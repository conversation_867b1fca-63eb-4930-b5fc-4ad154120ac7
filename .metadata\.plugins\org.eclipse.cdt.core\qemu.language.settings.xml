<?xml version="1.0" encoding="UTF-8" standalone="no"?><project><configuration id="ilg.gnumcueclipse.managedbuild.cross.riscv.config.elf.debug.**********" name="Release"><extension point="org.eclipse.cdt.core.LanguageSettingsProvider"><provider id="org.eclipse.embedcdt.managedbuild.cross.riscv.core.GCCBuiltinSpecsDetector"><language id="org.eclipse.cdt.core.gcc"><entry kind="includePath" name="/qemu/galaxy_sdk"><flag value="BUILTIN|READONLY|VALUE_WORKSPACE_PATH|RESOLVED"/></entry><entry kind="includePath" name="/qemu/galaxy_sdk/bsp/include/arch/riscv/n309"><flag value="BUILTIN|READONLY|VALUE_WORKSPACE_PATH|RESOLVED"/></entry><entry kind="includePath" name="/qemu/galaxy_sdk/bsp/include"><flag value="BUILTIN|READONLY|VALUE_WORKSPACE_PATH|RESOLVED"/></entry><entry kind="includePath" name="/qemu/galaxy_sdk/config/include"><flag value="BUILTIN|READONLY|VALUE_WORKSPACE_PATH|RESOLVED"/></entry><entry kind="includePath" name="/qemu/galaxy_sdk/drivers/include"><flag value="BUILTIN|READONLY|VALUE_WORKSPACE_PATH|RESOLVED"/></entry><entry kind="includePath" name="/qemu/galaxy_sdk/modules/external/riscv_dsp/include"><flag value="BUILTIN|READONLY|VALUE_WORKSPACE_PATH|RESOLVED"/></entry><entry kind="includePath" name="/qemu/galaxy_sdk/modules/external/nnom/inc/layers"><flag value="BUILTIN|READONLY|VALUE_WORKSPACE_PATH|RESOLVED"/></entry><entry kind="includePath" name="/qemu/galaxy_sdk/modules/external/nnom/port"><flag value="BUILTIN|READONLY|VALUE_WORKSPACE_PATH|RESOLVED"/></entry><entry kind="includePath" name="/qemu/galaxy_sdk/modules/external/nnom/inc"><flag value="BUILTIN|READONLY|VALUE_WORKSPACE_PATH|RESOLVED"/></entry><entry kind="includePath" name="/qemu/galaxy_sdk/modules/external/riscv_dsp/PrivateInclude"><flag value="BUILTIN|READONLY|VALUE_WORKSPACE_PATH|RESOLVED"/></entry><entry kind="includePath" name="/qemu/galaxy_sdk/modules/include"><flag value="BUILTIN|READONLY|VALUE_WORKSPACE_PATH|RESOLVED"/></entry><entry kind="includePath" name="/qemu/galaxy_sdk/os/include"><flag value="BUILTIN|READONLY|VALUE_WORKSPACE_PATH|RESOLVED"/></entry><entry kind="includePath" name="/qemu/galaxy_sdk/osal/include"><flag value="BUILTIN|READONLY|VALUE_WORKSPACE_PATH|RESOLVED"/></entry><entry kind="includePath" name="/qemu/galaxy_sdk/prebuilts/bluetooth/health/include"><flag value="BUILTIN|READONLY|VALUE_WORKSPACE_PATH|RESOLVED"/></entry><entry kind="includePath" name="D:/NucleiStudio/NucleiStudio/toolchain/gcc/lib/gcc/riscv64-unknown-elf/13.1.1/include"><flag value="BUILTIN|READONLY"/></entry><entry kind="includePath" name="D:/NucleiStudio/NucleiStudio/toolchain/gcc/lib/gcc/riscv64-unknown-elf/13.1.1/include-fixed"><flag value="BUILTIN|READONLY"/></entry><entry kind="includePath" name="D:/NucleiStudio/NucleiStudio/toolchain/gcc/riscv64-unknown-elf/include"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__ATOMIC_ACQUIRE" value="2"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__ATOMIC_ACQ_REL" value="4"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__ATOMIC_CONSUME" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__ATOMIC_RELAXED" value="0"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__ATOMIC_RELEASE" value="3"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__ATOMIC_SEQ_CST" value="5"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__BIGGEST_ALIGNMENT__" value="16"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__BYTE_ORDER__" value="__ORDER_LITTLE_ENDIAN__"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__CHAR16_TYPE__" value="short unsigned int"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__CHAR32_TYPE__" value="long unsigned int"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__CHAR_BIT__" value="8"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__CHAR_UNSIGNED__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__DBL_DECIMAL_DIG__" value="17"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__DBL_DENORM_MIN__" value="((double)4.94065645841246544176568792868221372e-324L)"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__DBL_DIG__" value="15"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__DBL_EPSILON__" value="((double)2.22044604925031308084726333618164062e-16L)"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__DBL_HAS_DENORM__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__DBL_HAS_INFINITY__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__DBL_HAS_QUIET_NAN__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__DBL_IS_IEC_60559__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__DBL_MANT_DIG__" value="53"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__DBL_MAX_10_EXP__" value="308"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__DBL_MAX_EXP__" value="1024"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__DBL_MAX__" value="((double)1.79769313486231570814527423731704357e+308L)"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__DBL_MIN_10_EXP__" value="(-307)"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__DBL_MIN_EXP__" value="(-1021)"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__DBL_MIN__" value="((double)2.22507385850720138309023271733240406e-308L)"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__DBL_NORM_MAX__" value="((double)1.79769313486231570814527423731704357e+308L)"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__DECIMAL_DIG__" value="36"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__DEC_EVAL_METHOD__" value="2"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__ELF__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FINITE_MATH_ONLY__" value="0"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLOAT_WORD_ORDER__" value="__ORDER_LITTLE_ENDIAN__"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT128_DECIMAL_DIG__" value="36"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT128_DENORM_MIN__" value="6.47517511943802511092443895822764655e-4966F128"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT128_DIG__" value="33"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT128_EPSILON__" value="1.92592994438723585305597794258492732e-34F128"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT128_HAS_DENORM__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT128_HAS_INFINITY__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT128_HAS_QUIET_NAN__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT128_IS_IEC_60559__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT128_MANT_DIG__" value="113"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT128_MAX_10_EXP__" value="4932"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT128_MAX_EXP__" value="16384"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT128_MAX__" value="1.18973149535723176508575932662800702e+4932F128"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT128_MIN_10_EXP__" value="(-4931)"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT128_MIN_EXP__" value="(-16381)"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT128_MIN__" value="3.36210314311209350626267781732175260e-4932F128"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT128_NORM_MAX__" value="1.18973149535723176508575932662800702e+4932F128"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT16_DECIMAL_DIG__" value="5"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT16_DENORM_MIN__" value="5.96046447753906250000000000000000000e-8F16"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT16_DIG__" value="3"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT16_EPSILON__" value="9.76562500000000000000000000000000000e-4F16"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT16_HAS_DENORM__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT16_HAS_INFINITY__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT16_HAS_QUIET_NAN__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT16_IS_IEC_60559__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT16_MANT_DIG__" value="11"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT16_MAX_10_EXP__" value="4"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT16_MAX_EXP__" value="16"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT16_MAX__" value="6.55040000000000000000000000000000000e+4F16"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT16_MIN_10_EXP__" value="(-4)"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT16_MIN_EXP__" value="(-13)"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT16_MIN__" value="6.10351562500000000000000000000000000e-5F16"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT16_NORM_MAX__" value="6.55040000000000000000000000000000000e+4F16"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT32X_DECIMAL_DIG__" value="17"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT32X_DENORM_MIN__" value="4.94065645841246544176568792868221372e-324F32x"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT32X_DIG__" value="15"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT32X_EPSILON__" value="2.22044604925031308084726333618164062e-16F32x"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT32X_HAS_DENORM__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT32X_HAS_INFINITY__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT32X_HAS_QUIET_NAN__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT32X_IS_IEC_60559__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT32X_MANT_DIG__" value="53"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT32X_MAX_10_EXP__" value="308"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT32X_MAX_EXP__" value="1024"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT32X_MAX__" value="1.79769313486231570814527423731704357e+308F32x"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT32X_MIN_10_EXP__" value="(-307)"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT32X_MIN_EXP__" value="(-1021)"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT32X_MIN__" value="2.22507385850720138309023271733240406e-308F32x"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT32X_NORM_MAX__" value="1.79769313486231570814527423731704357e+308F32x"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT32_DECIMAL_DIG__" value="9"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT32_DENORM_MIN__" value="1.40129846432481707092372958328991613e-45F32"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT32_DIG__" value="6"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT32_EPSILON__" value="1.19209289550781250000000000000000000e-7F32"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT32_HAS_DENORM__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT32_HAS_INFINITY__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT32_HAS_QUIET_NAN__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT32_IS_IEC_60559__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT32_MANT_DIG__" value="24"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT32_MAX_10_EXP__" value="38"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT32_MAX_EXP__" value="128"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT32_MAX__" value="3.40282346638528859811704183484516925e+38F32"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT32_MIN_10_EXP__" value="(-37)"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT32_MIN_EXP__" value="(-125)"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT32_MIN__" value="1.17549435082228750796873653722224568e-38F32"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT32_NORM_MAX__" value="3.40282346638528859811704183484516925e+38F32"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT64X_DECIMAL_DIG__" value="36"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT64X_DENORM_MIN__" value="6.47517511943802511092443895822764655e-4966F64x"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT64X_DIG__" value="33"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT64X_EPSILON__" value="1.92592994438723585305597794258492732e-34F64x"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT64X_HAS_DENORM__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT64X_HAS_INFINITY__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT64X_HAS_QUIET_NAN__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT64X_IS_IEC_60559__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT64X_MANT_DIG__" value="113"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT64X_MAX_10_EXP__" value="4932"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT64X_MAX_EXP__" value="16384"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT64X_MAX__" value="1.18973149535723176508575932662800702e+4932F64x"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT64X_MIN_10_EXP__" value="(-4931)"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT64X_MIN_EXP__" value="(-16381)"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT64X_MIN__" value="3.36210314311209350626267781732175260e-4932F64x"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT64X_NORM_MAX__" value="1.18973149535723176508575932662800702e+4932F64x"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT64_DECIMAL_DIG__" value="17"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT64_DENORM_MIN__" value="4.94065645841246544176568792868221372e-324F64"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT64_DIG__" value="15"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT64_EPSILON__" value="2.22044604925031308084726333618164062e-16F64"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT64_HAS_DENORM__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT64_HAS_INFINITY__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT64_HAS_QUIET_NAN__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT64_IS_IEC_60559__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT64_MANT_DIG__" value="53"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT64_MAX_10_EXP__" value="308"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT64_MAX_EXP__" value="1024"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT64_MAX__" value="1.79769313486231570814527423731704357e+308F64"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT64_MIN_10_EXP__" value="(-307)"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT64_MIN_EXP__" value="(-1021)"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT64_MIN__" value="2.22507385850720138309023271733240406e-308F64"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT64_NORM_MAX__" value="1.79769313486231570814527423731704357e+308F64"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT_DECIMAL_DIG__" value="9"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT_DENORM_MIN__" value="1.40129846432481707092372958328991613e-45F"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT_DIG__" value="6"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT_EPSILON__" value="1.19209289550781250000000000000000000e-7F"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT_EVAL_METHOD_TS_18661_3__" value="0"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT_EVAL_METHOD__" value="0"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT_HAS_DENORM__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT_HAS_INFINITY__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT_HAS_QUIET_NAN__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT_IS_IEC_60559__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT_MANT_DIG__" value="24"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT_MAX_10_EXP__" value="38"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT_MAX_EXP__" value="128"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT_MAX__" value="3.40282346638528859811704183484516925e+38F"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT_MIN_10_EXP__" value="(-37)"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT_MIN_EXP__" value="(-125)"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT_MIN__" value="1.17549435082228750796873653722224568e-38F"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT_NORM_MAX__" value="3.40282346638528859811704183484516925e+38F"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT_RADIX__" value="2"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FP_FAST_FMAF" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FP_FAST_FMAF32" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__GCC_ATOMIC_BOOL_LOCK_FREE" value="2"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__GCC_ATOMIC_CHAR16_T_LOCK_FREE" value="2"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__GCC_ATOMIC_CHAR32_T_LOCK_FREE" value="2"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__GCC_ATOMIC_CHAR_LOCK_FREE" value="2"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__GCC_ATOMIC_INT_LOCK_FREE" value="2"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__GCC_ATOMIC_LLONG_LOCK_FREE" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__GCC_ATOMIC_LONG_LOCK_FREE" value="2"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__GCC_ATOMIC_POINTER_LOCK_FREE" value="2"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__GCC_ATOMIC_SHORT_LOCK_FREE" value="2"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__GCC_ATOMIC_TEST_AND_SET_TRUEVAL" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__GCC_ATOMIC_WCHAR_T_LOCK_FREE" value="2"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__GCC_HAVE_DWARF2_CFI_ASM" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__GCC_HAVE_SYNC_COMPARE_AND_SWAP_1" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__GCC_HAVE_SYNC_COMPARE_AND_SWAP_2" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__GCC_HAVE_SYNC_COMPARE_AND_SWAP_4" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__GCC_IEC_559" value="0"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__GCC_IEC_559_COMPLEX" value="0"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__GNUC_EXECUTION_CHARSET_NAME" value="&quot;UTF-8&quot;"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__GNUC_MINOR__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__GNUC_PATCHLEVEL__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__GNUC_STDC_INLINE__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__GNUC_WIDE_EXECUTION_CHARSET_NAME" value="&quot;UTF-32LE&quot;"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__GNUC__" value="13"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__GXX_ABI_VERSION" value="1018"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__INT16_C(c)" value="c"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__INT16_MAX__" value="0x7fff"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__INT16_TYPE__" value="short int"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__INT32_C(c)" value="c ## L"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__INT32_MAX__" value="0x7fffffffL"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__INT32_TYPE__" value="long int"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__INT64_C(c)" value="c ## LL"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__INT64_MAX__" value="0x7fffffffffffffffLL"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__INT64_TYPE__" value="long long int"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__INT8_C(c)" value="c"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__INT8_MAX__" value="0x7f"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__INT8_TYPE__" value="signed char"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__INTMAX_C(c)" value="c ## LL"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__INTMAX_MAX__" value="0x7fffffffffffffffLL"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__INTMAX_TYPE__" value="long long int"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__INTMAX_WIDTH__" value="64"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__INTPTR_MAX__" value="0x7fffffff"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__INTPTR_TYPE__" value="int"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__INTPTR_WIDTH__" value="32"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__INT_FAST16_MAX__" value="0x7fffffff"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__INT_FAST16_TYPE__" value="int"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__INT_FAST16_WIDTH__" value="32"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__INT_FAST32_MAX__" value="0x7fffffff"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__INT_FAST32_TYPE__" value="int"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__INT_FAST32_WIDTH__" value="32"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__INT_FAST64_MAX__" value="0x7fffffffffffffffLL"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__INT_FAST64_TYPE__" value="long long int"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__INT_FAST64_WIDTH__" value="64"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__INT_FAST8_MAX__" value="0x7fffffff"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__INT_FAST8_TYPE__" value="int"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__INT_FAST8_WIDTH__" value="32"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__INT_LEAST16_MAX__" value="0x7fff"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__INT_LEAST16_TYPE__" value="short int"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__INT_LEAST16_WIDTH__" value="16"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__INT_LEAST32_MAX__" value="0x7fffffffL"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__INT_LEAST32_TYPE__" value="long int"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__INT_LEAST32_WIDTH__" value="32"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__INT_LEAST64_MAX__" value="0x7fffffffffffffffLL"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__INT_LEAST64_TYPE__" value="long long int"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__INT_LEAST64_WIDTH__" value="64"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__INT_LEAST8_MAX__" value="0x7f"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__INT_LEAST8_TYPE__" value="signed char"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__INT_LEAST8_WIDTH__" value="8"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__INT_MAX__" value="0x7fffffff"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__INT_WIDTH__" value="32"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__LDBL_DECIMAL_DIG__" value="36"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__LDBL_DENORM_MIN__" value="6.47517511943802511092443895822764655e-4966L"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__LDBL_DIG__" value="33"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__LDBL_EPSILON__" value="1.92592994438723585305597794258492732e-34L"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__LDBL_HAS_DENORM__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__LDBL_HAS_INFINITY__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__LDBL_HAS_QUIET_NAN__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__LDBL_IS_IEC_60559__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__LDBL_MANT_DIG__" value="113"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__LDBL_MAX_10_EXP__" value="4932"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__LDBL_MAX_EXP__" value="16384"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__LDBL_MAX__" value="1.18973149535723176508575932662800702e+4932L"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__LDBL_MIN_10_EXP__" value="(-4931)"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__LDBL_MIN_EXP__" value="(-16381)"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__LDBL_MIN__" value="3.36210314311209350626267781732175260e-4932L"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__LDBL_NORM_MAX__" value="1.18973149535723176508575932662800702e+4932L"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__LONG_LONG_MAX__" value="0x7fffffffffffffffLL"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__LONG_LONG_WIDTH__" value="64"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__LONG_MAX__" value="0x7fffffffL"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__LONG_WIDTH__" value="32"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__OPTIMIZE__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__ORDER_BIG_ENDIAN__" value="4321"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__ORDER_LITTLE_ENDIAN__" value="1234"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__ORDER_PDP_ENDIAN__" value="3412"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__PRAGMA_REDEFINE_EXTNAME" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__PTRDIFF_MAX__" value="0x7fffffff"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__PTRDIFF_TYPE__" value="int"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__PTRDIFF_WIDTH__" value="32"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__REGISTER_PREFIX__" value=""><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__SCHAR_MAX__" value="0x7f"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__SCHAR_WIDTH__" value="8"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__SHRT_MAX__" value="0x7fff"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__SHRT_WIDTH__" value="16"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__SIG_ATOMIC_MAX__" value="0x7fffffff"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__SIG_ATOMIC_MIN__" value="(-__SIG_ATOMIC_MAX__ - 1)"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__SIG_ATOMIC_TYPE__" value="int"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__SIG_ATOMIC_WIDTH__" value="32"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__SIZEOF_DOUBLE__" value="8"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__SIZEOF_FLOAT__" value="4"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__SIZEOF_INT__" value="4"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__SIZEOF_LONG_DOUBLE__" value="16"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__SIZEOF_LONG_LONG__" value="8"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__SIZEOF_LONG__" value="4"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__SIZEOF_POINTER__" value="4"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__SIZEOF_PTRDIFF_T__" value="4"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__SIZEOF_SHORT__" value="2"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__SIZEOF_SIZE_T__" value="4"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__SIZEOF_WCHAR_T__" value="4"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__SIZEOF_WINT_T__" value="4"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__SIZE_MAX__" value="0xffffffffU"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__SIZE_TYPE__" value="unsigned int"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__SIZE_WIDTH__" value="32"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__STDC_HOSTED__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__STDC_UTF_16__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__STDC_UTF_32__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__STDC_VERSION__" value="201112L"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__STDC__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UINT16_C(c)" value="c"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UINT16_MAX__" value="0xffff"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UINT16_TYPE__" value="short unsigned int"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UINT32_C(c)" value="c ## UL"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UINT32_MAX__" value="0xffffffffUL"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UINT32_TYPE__" value="long unsigned int"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UINT64_C(c)" value="c ## ULL"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UINT64_MAX__" value="0xffffffffffffffffULL"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UINT64_TYPE__" value="long long unsigned int"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UINT8_C(c)" value="c"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UINT8_MAX__" value="0xff"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UINT8_TYPE__" value="unsigned char"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UINTMAX_C(c)" value="c ## ULL"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UINTMAX_MAX__" value="0xffffffffffffffffULL"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UINTMAX_TYPE__" value="long long unsigned int"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UINTPTR_MAX__" value="0xffffffffU"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UINTPTR_TYPE__" value="unsigned int"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UINT_FAST16_MAX__" value="0xffffffffU"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UINT_FAST16_TYPE__" value="unsigned int"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UINT_FAST32_MAX__" value="0xffffffffU"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UINT_FAST32_TYPE__" value="unsigned int"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UINT_FAST64_MAX__" value="0xffffffffffffffffULL"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UINT_FAST64_TYPE__" value="long long unsigned int"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UINT_FAST8_MAX__" value="0xffffffffU"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UINT_FAST8_TYPE__" value="unsigned int"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UINT_LEAST16_MAX__" value="0xffff"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UINT_LEAST16_TYPE__" value="short unsigned int"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UINT_LEAST32_MAX__" value="0xffffffffUL"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UINT_LEAST32_TYPE__" value="long unsigned int"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UINT_LEAST64_MAX__" value="0xffffffffffffffffULL"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UINT_LEAST64_TYPE__" value="long long unsigned int"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UINT_LEAST8_MAX__" value="0xff"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UINT_LEAST8_TYPE__" value="unsigned char"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__USER_LABEL_PREFIX__" value=""><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__VERSION__" value="&quot;13.1.1 20230713&quot;"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__WCHAR_MAX__" value="0x7fffffff"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__WCHAR_MIN__" value="(-__WCHAR_MAX__ - 1)"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__WCHAR_TYPE__" value="int"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__WCHAR_WIDTH__" value="32"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__WINT_MAX__" value="0xffffffffU"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__WINT_MIN__" value="0U"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__WINT_TYPE__" value="unsigned int"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__WINT_WIDTH__" value="32"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__riscv" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__riscv_a" value="2000000"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__riscv_arch_test" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__riscv_atomic" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__riscv_c" value="2000000"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__riscv_cmodel_medlow" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__riscv_compressed" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__riscv_div" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__riscv_dsp" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__riscv_f" value="2000000"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__riscv_fdiv" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__riscv_flen" value="32"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__riscv_float_abi_single" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__riscv_fsqrt" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__riscv_i" value="2000000"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__riscv_m" value="2000000"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__riscv_mul" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__riscv_muldiv" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__riscv_unaligned_fast" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__riscv_xlen" value="32"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__riscv_xxldsp" value="1000000"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__riscv_zicsr" value="2000000"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__riscv_zifencei" value="2000000"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__riscv_zpn" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__riscv_zprv" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__riscv_zpsf" value="1"><flag value="BUILTIN|READONLY"/></entry></language><language id="org.eclipse.cdt.core.g++"><entry kind="includePath" name="/qemu/galaxy_sdk"><flag value="BUILTIN|READONLY|VALUE_WORKSPACE_PATH|RESOLVED"/></entry><entry kind="includePath" name="/qemu/galaxy_sdk/bsp/include/arch/riscv/n309"><flag value="BUILTIN|READONLY|VALUE_WORKSPACE_PATH|RESOLVED"/></entry><entry kind="includePath" name="/qemu/galaxy_sdk/bsp/include"><flag value="BUILTIN|READONLY|VALUE_WORKSPACE_PATH|RESOLVED"/></entry><entry kind="includePath" name="/qemu/galaxy_sdk/config/include"><flag value="BUILTIN|READONLY|VALUE_WORKSPACE_PATH|RESOLVED"/></entry><entry kind="includePath" name="/qemu/galaxy_sdk/drivers/include"><flag value="BUILTIN|READONLY|VALUE_WORKSPACE_PATH|RESOLVED"/></entry><entry kind="includePath" name="/qemu/galaxy_sdk/modules/external/riscv_dsp/include"><flag value="BUILTIN|READONLY|VALUE_WORKSPACE_PATH|RESOLVED"/></entry><entry kind="includePath" name="/qemu/galaxy_sdk/modules/external/nnom/inc/layers"><flag value="BUILTIN|READONLY|VALUE_WORKSPACE_PATH|RESOLVED"/></entry><entry kind="includePath" name="/qemu/galaxy_sdk/modules/external/nnom/port"><flag value="BUILTIN|READONLY|VALUE_WORKSPACE_PATH|RESOLVED"/></entry><entry kind="includePath" name="/qemu/galaxy_sdk/modules/external/nnom/inc"><flag value="BUILTIN|READONLY|VALUE_WORKSPACE_PATH|RESOLVED"/></entry><entry kind="includePath" name="/qemu/galaxy_sdk/modules/external/riscv_dsp/PrivateInclude"><flag value="BUILTIN|READONLY|VALUE_WORKSPACE_PATH|RESOLVED"/></entry><entry kind="includePath" name="/qemu/galaxy_sdk/modules/include"><flag value="BUILTIN|READONLY|VALUE_WORKSPACE_PATH|RESOLVED"/></entry><entry kind="includePath" name="/qemu/galaxy_sdk/os/include"><flag value="BUILTIN|READONLY|VALUE_WORKSPACE_PATH|RESOLVED"/></entry><entry kind="includePath" name="/qemu/galaxy_sdk/osal/include"><flag value="BUILTIN|READONLY|VALUE_WORKSPACE_PATH|RESOLVED"/></entry><entry kind="includePath" name="/qemu/galaxy_sdk/prebuilts/bluetooth/health/include"><flag value="BUILTIN|READONLY|VALUE_WORKSPACE_PATH|RESOLVED"/></entry><entry kind="includePath" name="D:/NucleiStudio/NucleiStudio/toolchain/gcc/riscv64-unknown-elf/include/c++/13.1.1"><flag value="BUILTIN|READONLY"/></entry><entry kind="includePath" name="D:/NucleiStudio/NucleiStudio/toolchain/gcc/riscv64-unknown-elf/include/c++/13.1.1/riscv64-unknown-elf/rv32imafc_xxldsp/ilp32f"><flag value="BUILTIN|READONLY"/></entry><entry kind="includePath" name="D:/NucleiStudio/NucleiStudio/toolchain/gcc/riscv64-unknown-elf/include/c++/13.1.1/backward"><flag value="BUILTIN|READONLY"/></entry><entry kind="includePath" name="D:/NucleiStudio/NucleiStudio/toolchain/gcc/lib/gcc/riscv64-unknown-elf/13.1.1/include"><flag value="BUILTIN|READONLY"/></entry><entry kind="includePath" name="D:/NucleiStudio/NucleiStudio/toolchain/gcc/lib/gcc/riscv64-unknown-elf/13.1.1/include-fixed"><flag value="BUILTIN|READONLY"/></entry><entry kind="includePath" name="D:/NucleiStudio/NucleiStudio/toolchain/gcc/riscv64-unknown-elf/include"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__ATOMIC_ACQUIRE" value="2"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__ATOMIC_ACQ_REL" value="4"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__ATOMIC_CONSUME" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__ATOMIC_RELAXED" value="0"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__ATOMIC_RELEASE" value="3"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__ATOMIC_SEQ_CST" value="5"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__BIGGEST_ALIGNMENT__" value="16"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__BYTE_ORDER__" value="__ORDER_LITTLE_ENDIAN__"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__CHAR16_TYPE__" value="short unsigned int"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__CHAR32_TYPE__" value="long unsigned int"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__CHAR_BIT__" value="8"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__CHAR_UNSIGNED__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__DBL_DECIMAL_DIG__" value="17"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__DBL_DENORM_MIN__" value="double(4.94065645841246544176568792868221372e-324L)"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__DBL_DIG__" value="15"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__DBL_EPSILON__" value="double(2.22044604925031308084726333618164062e-16L)"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__DBL_HAS_DENORM__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__DBL_HAS_INFINITY__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__DBL_HAS_QUIET_NAN__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__DBL_IS_IEC_60559__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__DBL_MANT_DIG__" value="53"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__DBL_MAX_10_EXP__" value="308"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__DBL_MAX_EXP__" value="1024"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__DBL_MAX__" value="double(1.79769313486231570814527423731704357e+308L)"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__DBL_MIN_10_EXP__" value="(-307)"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__DBL_MIN_EXP__" value="(-1021)"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__DBL_MIN__" value="double(2.22507385850720138309023271733240406e-308L)"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__DBL_NORM_MAX__" value="double(1.79769313486231570814527423731704357e+308L)"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__DECIMAL_DIG__" value="36"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__DEC_EVAL_METHOD__" value="2"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__DEPRECATED" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__ELF__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__EXCEPTIONS" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FINITE_MATH_ONLY__" value="0"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLOAT_WORD_ORDER__" value="__ORDER_LITTLE_ENDIAN__"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT128_DECIMAL_DIG__" value="36"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT128_DENORM_MIN__" value="6.47517511943802511092443895822764655e-4966F128"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT128_DIG__" value="33"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT128_EPSILON__" value="1.92592994438723585305597794258492732e-34F128"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT128_HAS_DENORM__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT128_HAS_INFINITY__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT128_HAS_QUIET_NAN__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT128_IS_IEC_60559__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT128_MANT_DIG__" value="113"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT128_MAX_10_EXP__" value="4932"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT128_MAX_EXP__" value="16384"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT128_MAX__" value="1.18973149535723176508575932662800702e+4932F128"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT128_MIN_10_EXP__" value="(-4931)"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT128_MIN_EXP__" value="(-16381)"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT128_MIN__" value="3.36210314311209350626267781732175260e-4932F128"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT128_NORM_MAX__" value="1.18973149535723176508575932662800702e+4932F128"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT16_DECIMAL_DIG__" value="5"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT16_DENORM_MIN__" value="5.96046447753906250000000000000000000e-8F16"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT16_DIG__" value="3"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT16_EPSILON__" value="9.76562500000000000000000000000000000e-4F16"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT16_HAS_DENORM__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT16_HAS_INFINITY__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT16_HAS_QUIET_NAN__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT16_IS_IEC_60559__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT16_MANT_DIG__" value="11"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT16_MAX_10_EXP__" value="4"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT16_MAX_EXP__" value="16"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT16_MAX__" value="6.55040000000000000000000000000000000e+4F16"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT16_MIN_10_EXP__" value="(-4)"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT16_MIN_EXP__" value="(-13)"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT16_MIN__" value="6.10351562500000000000000000000000000e-5F16"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT16_NORM_MAX__" value="6.55040000000000000000000000000000000e+4F16"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT32X_DECIMAL_DIG__" value="17"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT32X_DENORM_MIN__" value="4.94065645841246544176568792868221372e-324F32x"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT32X_DIG__" value="15"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT32X_EPSILON__" value="2.22044604925031308084726333618164062e-16F32x"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT32X_HAS_DENORM__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT32X_HAS_INFINITY__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT32X_HAS_QUIET_NAN__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT32X_IS_IEC_60559__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT32X_MANT_DIG__" value="53"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT32X_MAX_10_EXP__" value="308"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT32X_MAX_EXP__" value="1024"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT32X_MAX__" value="1.79769313486231570814527423731704357e+308F32x"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT32X_MIN_10_EXP__" value="(-307)"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT32X_MIN_EXP__" value="(-1021)"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT32X_MIN__" value="2.22507385850720138309023271733240406e-308F32x"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT32X_NORM_MAX__" value="1.79769313486231570814527423731704357e+308F32x"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT32_DECIMAL_DIG__" value="9"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT32_DENORM_MIN__" value="1.40129846432481707092372958328991613e-45F32"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT32_DIG__" value="6"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT32_EPSILON__" value="1.19209289550781250000000000000000000e-7F32"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT32_HAS_DENORM__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT32_HAS_INFINITY__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT32_HAS_QUIET_NAN__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT32_IS_IEC_60559__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT32_MANT_DIG__" value="24"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT32_MAX_10_EXP__" value="38"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT32_MAX_EXP__" value="128"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT32_MAX__" value="3.40282346638528859811704183484516925e+38F32"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT32_MIN_10_EXP__" value="(-37)"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT32_MIN_EXP__" value="(-125)"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT32_MIN__" value="1.17549435082228750796873653722224568e-38F32"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT32_NORM_MAX__" value="3.40282346638528859811704183484516925e+38F32"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT64X_DECIMAL_DIG__" value="36"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT64X_DENORM_MIN__" value="6.47517511943802511092443895822764655e-4966F64x"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT64X_DIG__" value="33"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT64X_EPSILON__" value="1.92592994438723585305597794258492732e-34F64x"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT64X_HAS_DENORM__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT64X_HAS_INFINITY__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT64X_HAS_QUIET_NAN__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT64X_IS_IEC_60559__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT64X_MANT_DIG__" value="113"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT64X_MAX_10_EXP__" value="4932"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT64X_MAX_EXP__" value="16384"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT64X_MAX__" value="1.18973149535723176508575932662800702e+4932F64x"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT64X_MIN_10_EXP__" value="(-4931)"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT64X_MIN_EXP__" value="(-16381)"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT64X_MIN__" value="3.36210314311209350626267781732175260e-4932F64x"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT64X_NORM_MAX__" value="1.18973149535723176508575932662800702e+4932F64x"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT64_DECIMAL_DIG__" value="17"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT64_DENORM_MIN__" value="4.94065645841246544176568792868221372e-324F64"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT64_DIG__" value="15"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT64_EPSILON__" value="2.22044604925031308084726333618164062e-16F64"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT64_HAS_DENORM__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT64_HAS_INFINITY__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT64_HAS_QUIET_NAN__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT64_IS_IEC_60559__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT64_MANT_DIG__" value="53"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT64_MAX_10_EXP__" value="308"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT64_MAX_EXP__" value="1024"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT64_MAX__" value="1.79769313486231570814527423731704357e+308F64"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT64_MIN_10_EXP__" value="(-307)"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT64_MIN_EXP__" value="(-1021)"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT64_MIN__" value="2.22507385850720138309023271733240406e-308F64"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT64_NORM_MAX__" value="1.79769313486231570814527423731704357e+308F64"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT_DECIMAL_DIG__" value="9"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT_DENORM_MIN__" value="1.40129846432481707092372958328991613e-45F"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT_DIG__" value="6"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT_EPSILON__" value="1.19209289550781250000000000000000000e-7F"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT_EVAL_METHOD_TS_18661_3__" value="0"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT_EVAL_METHOD__" value="0"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT_HAS_DENORM__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT_HAS_INFINITY__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT_HAS_QUIET_NAN__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT_IS_IEC_60559__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT_MANT_DIG__" value="24"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT_MAX_10_EXP__" value="38"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT_MAX_EXP__" value="128"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT_MAX__" value="3.40282346638528859811704183484516925e+38F"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT_MIN_10_EXP__" value="(-37)"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT_MIN_EXP__" value="(-125)"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT_MIN__" value="1.17549435082228750796873653722224568e-38F"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT_NORM_MAX__" value="3.40282346638528859811704183484516925e+38F"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT_RADIX__" value="2"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FP_FAST_FMAF" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FP_FAST_FMAF32" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__GCC_ATOMIC_BOOL_LOCK_FREE" value="2"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__GCC_ATOMIC_CHAR16_T_LOCK_FREE" value="2"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__GCC_ATOMIC_CHAR32_T_LOCK_FREE" value="2"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__GCC_ATOMIC_CHAR_LOCK_FREE" value="2"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__GCC_ATOMIC_INT_LOCK_FREE" value="2"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__GCC_ATOMIC_LLONG_LOCK_FREE" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__GCC_ATOMIC_LONG_LOCK_FREE" value="2"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__GCC_ATOMIC_POINTER_LOCK_FREE" value="2"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__GCC_ATOMIC_SHORT_LOCK_FREE" value="2"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__GCC_ATOMIC_TEST_AND_SET_TRUEVAL" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__GCC_ATOMIC_WCHAR_T_LOCK_FREE" value="2"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__GCC_CONSTRUCTIVE_SIZE" value="32"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__GCC_DESTRUCTIVE_SIZE" value="32"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__GCC_HAVE_DWARF2_CFI_ASM" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__GCC_HAVE_SYNC_COMPARE_AND_SWAP_1" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__GCC_HAVE_SYNC_COMPARE_AND_SWAP_2" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__GCC_HAVE_SYNC_COMPARE_AND_SWAP_4" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__GCC_IEC_559" value="0"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__GCC_IEC_559_COMPLEX" value="0"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__GNUC_EXECUTION_CHARSET_NAME" value="&quot;UTF-8&quot;"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__GNUC_MINOR__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__GNUC_PATCHLEVEL__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__GNUC_STDC_INLINE__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__GNUC_WIDE_EXECUTION_CHARSET_NAME" value="&quot;UTF-32LE&quot;"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__GNUC__" value="13"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__GNUG__" value="13"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__GXX_ABI_VERSION" value="1018"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__GXX_EXPERIMENTAL_CXX0X__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__GXX_RTTI" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__GXX_WEAK__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__INT16_C(c)" value="c"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__INT16_MAX__" value="0x7fff"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__INT16_TYPE__" value="short int"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__INT32_C(c)" value="c ## L"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__INT32_MAX__" value="0x7fffffffL"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__INT32_TYPE__" value="long int"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__INT64_C(c)" value="c ## LL"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__INT64_MAX__" value="0x7fffffffffffffffLL"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__INT64_TYPE__" value="long long int"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__INT8_C(c)" value="c"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__INT8_MAX__" value="0x7f"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__INT8_TYPE__" value="signed char"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__INTMAX_C(c)" value="c ## LL"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__INTMAX_MAX__" value="0x7fffffffffffffffLL"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__INTMAX_TYPE__" value="long long int"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__INTMAX_WIDTH__" value="64"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__INTPTR_MAX__" value="0x7fffffff"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__INTPTR_TYPE__" value="int"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__INTPTR_WIDTH__" value="32"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__INT_FAST16_MAX__" value="0x7fffffff"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__INT_FAST16_TYPE__" value="int"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__INT_FAST16_WIDTH__" value="32"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__INT_FAST32_MAX__" value="0x7fffffff"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__INT_FAST32_TYPE__" value="int"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__INT_FAST32_WIDTH__" value="32"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__INT_FAST64_MAX__" value="0x7fffffffffffffffLL"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__INT_FAST64_TYPE__" value="long long int"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__INT_FAST64_WIDTH__" value="64"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__INT_FAST8_MAX__" value="0x7fffffff"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__INT_FAST8_TYPE__" value="int"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__INT_FAST8_WIDTH__" value="32"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__INT_LEAST16_MAX__" value="0x7fff"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__INT_LEAST16_TYPE__" value="short int"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__INT_LEAST16_WIDTH__" value="16"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__INT_LEAST32_MAX__" value="0x7fffffffL"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__INT_LEAST32_TYPE__" value="long int"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__INT_LEAST32_WIDTH__" value="32"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__INT_LEAST64_MAX__" value="0x7fffffffffffffffLL"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__INT_LEAST64_TYPE__" value="long long int"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__INT_LEAST64_WIDTH__" value="64"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__INT_LEAST8_MAX__" value="0x7f"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__INT_LEAST8_TYPE__" value="signed char"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__INT_LEAST8_WIDTH__" value="8"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__INT_MAX__" value="0x7fffffff"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__INT_WIDTH__" value="32"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__LDBL_DECIMAL_DIG__" value="36"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__LDBL_DENORM_MIN__" value="6.47517511943802511092443895822764655e-4966L"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__LDBL_DIG__" value="33"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__LDBL_EPSILON__" value="1.92592994438723585305597794258492732e-34L"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__LDBL_HAS_DENORM__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__LDBL_HAS_INFINITY__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__LDBL_HAS_QUIET_NAN__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__LDBL_IS_IEC_60559__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__LDBL_MANT_DIG__" value="113"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__LDBL_MAX_10_EXP__" value="4932"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__LDBL_MAX_EXP__" value="16384"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__LDBL_MAX__" value="1.18973149535723176508575932662800702e+4932L"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__LDBL_MIN_10_EXP__" value="(-4931)"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__LDBL_MIN_EXP__" value="(-16381)"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__LDBL_MIN__" value="3.36210314311209350626267781732175260e-4932L"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__LDBL_NORM_MAX__" value="1.18973149535723176508575932662800702e+4932L"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__LONG_LONG_MAX__" value="0x7fffffffffffffffLL"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__LONG_LONG_WIDTH__" value="64"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__LONG_MAX__" value="0x7fffffffL"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__LONG_WIDTH__" value="32"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__OPTIMIZE__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__ORDER_BIG_ENDIAN__" value="4321"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__ORDER_LITTLE_ENDIAN__" value="1234"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__ORDER_PDP_ENDIAN__" value="3412"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__PRAGMA_REDEFINE_EXTNAME" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__PTRDIFF_MAX__" value="0x7fffffff"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__PTRDIFF_TYPE__" value="int"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__PTRDIFF_WIDTH__" value="32"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__REGISTER_PREFIX__" value=""><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__SCHAR_MAX__" value="0x7f"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__SCHAR_WIDTH__" value="8"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__SHRT_MAX__" value="0x7fff"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__SHRT_WIDTH__" value="16"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__SIG_ATOMIC_MAX__" value="0x7fffffff"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__SIG_ATOMIC_MIN__" value="(-__SIG_ATOMIC_MAX__ - 1)"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__SIG_ATOMIC_TYPE__" value="int"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__SIG_ATOMIC_WIDTH__" value="32"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__SIZEOF_DOUBLE__" value="8"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__SIZEOF_FLOAT__" value="4"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__SIZEOF_INT__" value="4"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__SIZEOF_LONG_DOUBLE__" value="16"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__SIZEOF_LONG_LONG__" value="8"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__SIZEOF_LONG__" value="4"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__SIZEOF_POINTER__" value="4"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__SIZEOF_PTRDIFF_T__" value="4"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__SIZEOF_SHORT__" value="2"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__SIZEOF_SIZE_T__" value="4"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__SIZEOF_WCHAR_T__" value="4"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__SIZEOF_WINT_T__" value="4"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__SIZE_MAX__" value="0xffffffffU"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__SIZE_TYPE__" value="unsigned int"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__SIZE_WIDTH__" value="32"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__STDC_HOSTED__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__STDC_UTF_16__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__STDC_UTF_32__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__STDC__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UINT16_C(c)" value="c"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UINT16_MAX__" value="0xffff"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UINT16_TYPE__" value="short unsigned int"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UINT32_C(c)" value="c ## UL"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UINT32_MAX__" value="0xffffffffUL"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UINT32_TYPE__" value="long unsigned int"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UINT64_C(c)" value="c ## ULL"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UINT64_MAX__" value="0xffffffffffffffffULL"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UINT64_TYPE__" value="long long unsigned int"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UINT8_C(c)" value="c"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UINT8_MAX__" value="0xff"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UINT8_TYPE__" value="unsigned char"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UINTMAX_C(c)" value="c ## ULL"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UINTMAX_MAX__" value="0xffffffffffffffffULL"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UINTMAX_TYPE__" value="long long unsigned int"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UINTPTR_MAX__" value="0xffffffffU"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UINTPTR_TYPE__" value="unsigned int"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UINT_FAST16_MAX__" value="0xffffffffU"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UINT_FAST16_TYPE__" value="unsigned int"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UINT_FAST32_MAX__" value="0xffffffffU"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UINT_FAST32_TYPE__" value="unsigned int"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UINT_FAST64_MAX__" value="0xffffffffffffffffULL"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UINT_FAST64_TYPE__" value="long long unsigned int"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UINT_FAST8_MAX__" value="0xffffffffU"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UINT_FAST8_TYPE__" value="unsigned int"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UINT_LEAST16_MAX__" value="0xffff"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UINT_LEAST16_TYPE__" value="short unsigned int"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UINT_LEAST32_MAX__" value="0xffffffffUL"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UINT_LEAST32_TYPE__" value="long unsigned int"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UINT_LEAST64_MAX__" value="0xffffffffffffffffULL"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UINT_LEAST64_TYPE__" value="long long unsigned int"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UINT_LEAST8_MAX__" value="0xff"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UINT_LEAST8_TYPE__" value="unsigned char"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__USER_LABEL_PREFIX__" value=""><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__VERSION__" value="&quot;13.1.1 20230713&quot;"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__WCHAR_MAX__" value="0x7fffffff"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__WCHAR_MIN__" value="(-__WCHAR_MAX__ - 1)"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__WCHAR_TYPE__" value="int"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__WCHAR_WIDTH__" value="32"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__WINT_MAX__" value="0xffffffffU"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__WINT_MIN__" value="0U"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__WINT_TYPE__" value="unsigned int"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__WINT_WIDTH__" value="32"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__cplusplus" value="201103L"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__cpp_alias_templates" value="200704L"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__cpp_attributes" value="200809L"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__cpp_binary_literals" value="201304L"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__cpp_constexpr" value="200704L"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__cpp_decltype" value="200707L"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__cpp_delegating_constructors" value="200604L"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__cpp_exceptions" value="199711L"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__cpp_hex_float" value="201603L"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__cpp_inheriting_constructors" value="201511L"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__cpp_initializer_lists" value="200806L"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__cpp_lambdas" value="200907L"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__cpp_nsdmi" value="200809L"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__cpp_range_based_for" value="200907L"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__cpp_raw_strings" value="200710L"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__cpp_ref_qualifiers" value="200710L"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__cpp_rtti" value="199711L"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__cpp_runtime_arrays" value="198712L"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__cpp_rvalue_reference" value="200610L"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__cpp_rvalue_references" value="200610L"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__cpp_static_assert" value="200410L"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__cpp_threadsafe_static_init" value="200806L"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__cpp_unicode_characters" value="200704L"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__cpp_unicode_literals" value="200710L"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__cpp_user_defined_literals" value="200809L"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__cpp_variadic_templates" value="200704L"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__riscv" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__riscv_a" value="2000000"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__riscv_arch_test" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__riscv_atomic" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__riscv_c" value="2000000"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__riscv_cmodel_medlow" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__riscv_compressed" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__riscv_div" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__riscv_dsp" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__riscv_f" value="2000000"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__riscv_fdiv" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__riscv_flen" value="32"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__riscv_float_abi_single" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__riscv_fsqrt" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__riscv_i" value="2000000"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__riscv_m" value="2000000"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__riscv_mul" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__riscv_muldiv" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__riscv_unaligned_fast" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__riscv_xlen" value="32"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__riscv_xxldsp" value="1000000"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__riscv_zicsr" value="2000000"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__riscv_zifencei" value="2000000"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__riscv_zpn" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__riscv_zprv" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__riscv_zpsf" value="1"><flag value="BUILTIN|READONLY"/></entry></language></provider></extension></configuration></project>