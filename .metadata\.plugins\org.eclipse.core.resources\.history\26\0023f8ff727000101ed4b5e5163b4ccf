/*
 * 声纹识别/说话人识别 - NNOM推理代码
 * 
 * 作者: Augment Agent
 * 日期: 2025-08-02
 * 
 * 说明: 
 * - 使用NNOM框架进行声纹识别推理
 * - 输入: 198x12的MFCC特征 (2秒音频，12维MFCC，去除C0)
 * - 输出: 16类说话人分类结果
 * - 目标说话人: <PERSON><PERSON><PERSON>(0), <PERSON><PERSON><PERSON>(1), <PERSON><PERSON><PERSON>(2), <PERSON><PERSON><PERSON>(3)
 * - 其他说话人: ID1-ID11 (4-14), others(15)
 */

#include <stdio.h>
#include <stdint.h>
#include <string.h>
#include <math.h>
#include "uart_printf.h"
#include "nnom.h"
#include "speaker_weights.h"
#include "speaker_inference.h"

// 模型配置 (使用头文件中的定义)
#define INPUT_HEIGHT    SPEAKER_INPUT_HEIGHT
#define INPUT_WIDTH     SPEAKER_INPUT_WIDTH
#define INPUT_CHANNELS  SPEAKER_INPUT_CHANNELS
#define NUM_CLASSES     SPEAKER_NUM_CLASSES

#define INPUT_SIZE      SPEAKER_INPUT_SIZE
#define OUTPUT_SIZE     NUM_CLASSES

// 说话人标签
static const char* speaker_names[NUM_CLASSES] = {
    "XiaoXin",   // 0 - 目标说话人
    "XiaoYuan",  // 1 - 目标说话人  
    "XiaoSi",    // 2 - 目标说话人
    "XiaoLai",   // 3 - 目标说话人
    "ID1",       // 4 - 其他说话人
    "ID2",       // 5 - 其他说话人
    "ID3",       // 6 - 其他说话人
    "ID4",       // 7 - 其他说话人
    "ID5",       // 8 - 其他说话人
    "ID6",       // 9 - 其他说话人
    "ID7",       // 10 - 其他说话人
    "ID8",       // 11 - 其他说话人
    "ID9",       // 12 - 其他说话人
    "ID10",      // 13 - 其他说话人
    "ID11",      // 14 - 其他说话人
    "others"     // 15 - 未知说话人
};

// 目标说话人标志 (前4个是目标说话人)
static const uint8_t is_target_speaker[NUM_CLASSES] = {
    1, 1, 1, 1,  // XiaoXin, XiaoYuan, XiaoSi, XiaoLai
    0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0  // 其他说话人
};

// 全局变量
static nnom_model_t *model;
static int8_t input_buffer[INPUT_SIZE];
static int8_t output_buffer[OUTPUT_SIZE];

// 浮点数打印函数 (SDK兼容)
void print_float(float value) {
    int integer_part = (int)value;
    int decimal_part = (int)((value - integer_part) * 1000);
    if (decimal_part < 0) decimal_part = -decimal_part;
    uart_printf("%d.%03d", integer_part, decimal_part);
}

// 使用speaker_weights.h中定义的权重和偏置tensor
// 这些已经在speaker_weights.h中定义好了

/**
 * 构建NNOM模型网络结构
 * 使用speaker_weights.h中预定义的模型
 */
static nnom_model_t* build_speaker_model(void) {
    // 直接使用speaker_weights.h中预定义的模型创建函数
    return nnom_model_create();
}

/**
 * 初始化NNOM模型
 * @return SPEAKER_OK: 成功, 其他: 错误码
 */
speaker_error_t speaker_model_init(void) {
    uart_printf("🚀 初始化声纹识别模型...\n");

    // 构建模型网络结构
    model = build_speaker_model();
    if (model == NULL) {
        uart_printf("❌ 模型创建失败\n");
        return SPEAKER_ERROR_INIT;
    }

    // 模型已经在nnom_model_create()中编译完成
    // 无需再次编译

    uart_printf("✅ 模型初始化成功\n");
    uart_printf("   输入尺寸: %dx%dx%d\n", SPEAKER_INPUT_HEIGHT, SPEAKER_INPUT_WIDTH, SPEAKER_INPUT_CHANNELS);
    uart_printf("   输出类别: %d\n", SPEAKER_NUM_CLASSES);

    return SPEAKER_OK;
}

/**
 * 预处理MFCC特征 - 与Python训练代码保持一致
 * @param mfcc_features: 输入的MFCC特征 (float格式)
 * @param quantized_input: 输出的量化输入 (int8格式)
 *
 * 量化过程（基于实际MFCC数据范围优化）：
 * 1. 实际MFCC范围: [-359, 60], 95%数据在±42.6
 * 2. Python训练时: clip(x, -64, 64) / 64 -> round(x * 128) / 128
 * 3. NNOM量化: INPUT_OUTPUT_DEC = 2, 即 quantized = round(x * 2^2)
 * 4. 组合效果: 原始MFCC -> 训练归一化 -> NNOM量化
 */
void preprocess_mfcc(const float *mfcc_features, int8_t *quantized_input) {
    // NNOM正确方式：直接量化原始MFCC数据，不进行预归一化
    // 使用权重文件中的量化参数，避免硬编码
    const int dec_bits = INPUT_OUTPUT_DEC;  // 从speaker_weights.h获取
    const float quantize_factor = (float)(1 << dec_bits);  // 2^INPUT_OUTPUT_DEC

    for (int i = 0; i < INPUT_SIZE; i++) {
        // 直接量化原始MFCC值，与NNOM训练时的处理方式一致
        // NNOM公式: quantized = round(float_value * 2^dec_bits)
        float scaled_value = mfcc_features[i] * quantize_factor;

        // 四舍五入
        int32_t quantized = (int32_t)(scaled_value + (scaled_value >= 0 ? 0.5f : -0.5f));

        // 限制到int8范围 [-128, 127]
        if (quantized > 127) quantized = 127;
        if (quantized < -128) quantized = -128;

        quantized_input[i] = (int8_t)quantized;
    }
}

/**
 * 测试量化函数的正确性
 * 验证C代码量化结果与Python训练时的量化是否一致
 */
void test_quantization_consistency(void) {
    uart_printf("\n🧪 测试量化一致性...\n");

    // 测试用例：模拟典型的MFCC值（基于实际数据范围，去除C0）
    static float test_values[] = {
        0.0f,     // 零值
        1.0f,     // 小正值
        -1.0f,    // 小负值
        12.2f,    // 95%分位数
        -12.2f,   // 95%负分位数
        46.657f,  // 实际最大值（会被clip到8.0）
        -56.072f, // 实际最小值（会被clip到-8.0）
        8.0f,     // 训练限制边界
        -8.0f,    // 负训练限制边界
        5.5f,     // 典型中等值
        -5.5f     // 典型中等负值
    };

    int num_tests = sizeof(test_values) / sizeof(test_values[0]);
    int8_t quantized_results[10];

    uart_printf("   测试值 -> 量化结果 (预期Python结果)\n");
    uart_printf("   ----------------------------------------\n");

    for (int i = 0; i < num_tests; i++) {
        // 使用我们的量化函数
        preprocess_mfcc(&test_values[i], &quantized_results[i]);

        // 计算预期的NNOM结果用于对比（直接量化原始值）
        const float quantize_factor = (float)(1 << INPUT_OUTPUT_DEC);  // 使用宏定义
        float scaled_value = test_values[i] * quantize_factor;
        int expected_nnom = (int)(scaled_value + (scaled_value >= 0 ? 0.5f : -0.5f));
        if (expected_nnom > 127) expected_nnom = 127;
        if (expected_nnom < -128) expected_nnom = -128;

        uart_printf("   %6.2f -> %4d (预期: %4d) %s\n",
                   test_values[i],
                   quantized_results[i],
                   expected_nnom,
                   (quantized_results[i] == expected_nnom) ? "✅" : "❌");
    }

    uart_printf("   量化一致性测试完成\n");
}

/**
 * 后处理模型输出
 * @param raw_output: 模型原始输出 (int8格式)
 * @param probabilities: 输出的概率 (float格式)
 */
void postprocess_output(const int8_t *raw_output, float *probabilities) {
    // 根据NNOM量化参数进行后处理
    // 输出层量化参数: dec_bit=7, 即scale = 1/2^7 = 1/128
    const float scale = 1.0f / (1 << 7);  // 2^7 = 128

    // 转换为浮点数 (Softmax输出已经是概率)
    float sum = 0.0f;
    for (int i = 0; i < NUM_CLASSES; i++) {
        // float_value = quantized_value * scale
        probabilities[i] = raw_output[i] * scale;
        sum += probabilities[i];
    }

    // 归一化确保概率和为1
    if (sum > 0.0f) {
        for (int i = 0; i < NUM_CLASSES; i++) {
            probabilities[i] /= sum;
        }
    }
}

/**
 * 执行声纹识别推理
 * @param mfcc_features: 输入的MFCC特征 [198x12]
 * @param result: 输出的识别结果
 * @return SPEAKER_OK: 成功, 其他: 错误码
 */
speaker_error_t speaker_inference(const float *mfcc_features, speaker_result_t *result) {
    if (model == NULL || mfcc_features == NULL || result == NULL) {
        uart_printf("❌ 参数错误\n");
        return SPEAKER_ERROR_INPUT;
    }

    // 1. 预处理输入
    preprocess_mfcc(mfcc_features, input_buffer);

    // 2. 执行推理
    memcpy(nnom_input_data, input_buffer, INPUT_SIZE);
    if (model_run(model) != NN_SUCCESS) {
        uart_printf("❌ 模型推理失败\n");
        return SPEAKER_ERROR_INFERENCE;
    }
    memcpy(output_buffer, nnom_output_data, OUTPUT_SIZE);

    // 3. 后处理输出
    float probabilities[NUM_CLASSES];
    postprocess_output(output_buffer, probabilities);

    // 4. 找到最大概率的类别
    int max_class = 0;
    float max_prob = probabilities[0];
    for (int i = 1; i < NUM_CLASSES; i++) {
        if (probabilities[i] > max_prob) {
            max_prob = probabilities[i];
            max_class = i;
        }
    }

    // 5. 填充结果结构体
    result->predicted_class = max_class;
    result->confidence = max_prob;
    result->speaker_name = speaker_names[max_class];
    result->is_target = is_target_speaker[max_class];
    
    // 6. 打印详细结果
    uart_printf("🎯 声纹识别结果:\n");
    uart_printf("   预测说话人: %s (类别 %d)\n", result->speaker_name, result->predicted_class);
    uart_printf("   置信度: ");
    print_float(result->confidence * 100.0f);
    uart_printf("%%\n");

    if (result->is_target) {
        uart_printf("   ✅ 目标说话人识别成功\n");
    } else {
        uart_printf("   ⚠️  非目标说话人\n");
    }

    // 打印前3个最高概率
    uart_printf("   Top-3 概率:\n");
    for (int rank = 0; rank < 3 && rank < NUM_CLASSES; rank++) {
        int best_idx = 0;
        float best_prob = -1.0f;

        // 找到第rank高的概率
        for (int i = 0; i < NUM_CLASSES; i++) {
            if (probabilities[i] > best_prob) {
                best_prob = probabilities[i];
                best_idx = i;
            }
        }

        uart_printf("     %d. %s: ", rank + 1, speaker_names[best_idx]);
        print_float(best_prob * 100.0f);
        uart_printf("%%\n");

        // 标记为已选择
        probabilities[best_idx] = -1.0f;
    }

    return SPEAKER_OK;
}

/**
 * 检查是否为目标说话人
 */
uint8_t speaker_is_target(int class_id) {
    if (class_id >= 0 && class_id < NUM_CLASSES) {
        return is_target_speaker[class_id];
    }
    return 0;
}

/**
 * 获取说话人姓名
 */
const char* speaker_get_name(int class_id) {
    if (class_id >= 0 && class_id < NUM_CLASSES) {
        return speaker_names[class_id];
    }
    return "Unknown";
}

/**
 * 测试函数 - 使用随机数据
 */
void speaker_test(void) {
    uart_printf("\n🧪 声纹识别测试\n");
    uart_printf("==================================================\n");

    // 1. 首先测试量化一致性
    test_quantization_consistency();

    // 2. 生成测试MFCC特征 (模拟真实数据范围)
    static float test_mfcc[INPUT_SIZE];

    // 简单的伪随机数生成 (线性同余)
    static uint32_t seed = 12345;
    for (int i = 0; i < INPUT_SIZE; i++) {
        seed = seed * 1103515245 + 12345;
        float random_val = ((float)(seed % 10000) / 10000.0f - 0.5f) * 100.0f;  // [-50, 50]
        test_mfcc[i] = random_val;
    }

    uart_printf("📊 生成测试MFCC特征: %dx%d\n", INPUT_HEIGHT, INPUT_WIDTH);
    uart_printf("   特征范围: [-50.0, 50.0]\n");

    // 执行推理
    speaker_result_t result;
    speaker_error_t error = speaker_inference(test_mfcc, &result);

    if (error == SPEAKER_OK) {
        uart_printf("\n✅ 推理测试完成\n");
    } else {
        uart_printf("\n❌ 推理测试失败，错误码: %d\n", error);
    }
}

///**
// * 主函数
// */
//int main(void) {
//    uart_printf("\n🎙️  声纹识别系统启动\n");
//    uart_printf("==================================================\n");
//
//    // 初始化模型
//    if (speaker_model_init() != SPEAKER_OK) {
//        uart_printf("❌ 系统初始化失败\n");
//        return -1;
//    }
//
//    // 运行测试
//    speaker_test();
//
//    uart_printf("\n🏁 程序结束\n");
//    return 0;
//}
