#include "nnom.h"

/* Weights, bias and Q format */
#define TENSOR_CONV2D_1_FUSED_KERNEL_0 {20, 13, 25, 9, 2, 8, -7, 22, -24, 10, 17, 20, 29, 22, 17, -21, 1, -28, -4, 15, -3, -16, -11, 5, -10, 0, 17, -4, -4, -6, 0, 7, 2, -8, 3, 8, -25, 8, 0, 20, 13, -33, 19, -8, -1, 20, -25, -7, -7, -2, -5, 4, 17, 19, 4, 5, -21, 39, -25, -5, 12, -21, 29, 14, -15, -15, 6, -15, -9, -15, 0, 25, -22, 10, -7, 28, -6, -9, 8, 15, 12, -18, 8, -6, 7, 10, -10, -1, -6, 3, 10, 0, 5, 2, 10, 4, 3, 10, 6, -2, 0, -14, -4, 14, -8, 7, 2, -5, -11, 4, 10, 17, 2, -20, 22, 16, 14, -2, -26, 3, 5, 6, 2, -20, 4, 8, -19, -2, 3, 12, -14, -32, -9, 31, 23, 45, 61, 13, -51, -74, -3, 17, 19, 4, 5, -31, -29, -14, 16, 38, -25, -29, -29, -11, 4, -18, -11, -18, -23, -10, -7, -8, 3, -6, -5, 18, 6, 16, 1, 13, 48, 36, 33, 31, 19, -7, -10, 15, -28, 23, 10, -4, -17, -13, 24, -7, -11, 15, 10, 6, -15, 6, 14, 6, -9, 7, 4, 12, -21, 9, -21, -11, -13, -2, -18, 0, 10, 12, 9, 1, -15, -2, -1, -7, -10, 6, 10, 25, 10, 5, -15, -1, 1, -5, -6, 5, 2, -7, -1, -14, -5, 7, -6, -6, -2, -4, 8, -10, 4, 18, -7, 12, 0, 11, 40, -10, -3, -6, -2, 27, 11, 6, 3, 5, -13, 5, 1, 4, -6, -4, -3, 0, 0, 23, 1, -19, 1, -5, 29, 7, -25, 8, 2, 17, 4, -11, 1, 6, 13, 11, -2, 13, 7, 16, 29, 14, -1, -6, -12, -14, -2, -17, -25, -30, -40, -17, -5, 4, -4, -9, -10, 1, 5, 13, 11, 2, -2, -5, 7, -17, 17, -5, 4, -17, -25, 14, -3, -4, -8, -19, 14, 4, -14, -12, 1, 5, -1, 6, 4, -12, 13, 4, 2, -2, -23, 13, 13, 4, -5, -17, 6, 7, 4, 1, -18, -4, -5, 5, 0, -3, 5, 2, -1, 4, 1, -4, 8, -24, 18, -18, -8, 9, -11, 19, -19, -9, 5, -6, 11, -12, -3, 5, 1, 9, -8, -2, 0, 7, -10, 8, -2, 6, 14, -24, 13, -4, 14, 0, -23, 15, 14, 4, -5, -17, 17, 8, 4, 1, -8, 11}

#define TENSOR_CONV2D_1_FUSED_KERNEL_0_DEC_BITS {8}

#define TENSOR_CONV2D_1_FUSED_BIAS_0 {25, -80, 39, -37, -115, -18, -4, 53, 24, 33, 30, -46, -64, -14, -7, -14}

#define TENSOR_CONV2D_1_FUSED_BIAS_0_DEC_BITS {7}

#define CONV2D_1_FUSED_BIAS_LSHIFT {2}

#define CONV2D_1_FUSED_OUTPUT_RSHIFT {6}

#define TENSOR_CONV2D_2_FUSED_KERNEL_0 {-7, -4, 2, 22, -5, -26, -16, -3, 16, -28, -6, 0, 6, -1, 6, 14, 6, 4, -3, 26, -13, -21, -7, 10, -6, -10, -7, 6, -14, -8, -20, -9, -7, 0, -8, 9, -6, -9, 7, -11, -6, 10, 16, -2, -9, 3, 4, 31, -2, 4, 2, 21, -14, -8, -14, -20, 18, -16, -4, 5, -1, -4, -17, -3, -5, 7, 0, 23, -5, -10, -14, 14, -1, -12, 7, -4, 0, -1, -22, -6, -10, -16, 1, 17, -2, -8, -5, -18, -2, -3, -2, -9, -19, -2, 1, 14, 1, 14, 20, 15, -12, -15, -19, -11, 3, 5, -13, -10, 5, -8, -9, -22, 4, 3, -1, 24, 4, -4, 5, 1, -25, -6, -3, -12, 1, 1, -11, -13, -3, -14, 7, 7, -4, -32, 3, -10, -9, 3, 2, -2, -4, 14, 16, 13, -39, -28, -8, -13, 10, -17, 38, -3, -25, -11, -53, -21, -1, -9, -5, 0, -14, 2, 19, 17, 9, -7, 2, 11, 27, -20, 6, 4, -16, -19, -10, 7, -35, -23, -21, -49, -38, -26, -21, 3, 0, 28, -33, 23, -18, -21, -18, -4, -16, -10, -14, 0, 9, -14, 17, 1, -13, -8, 12, -7, 7, 5, 1, -8, 0, 4, -5, 15, 8, -13, -7, 0, 4, 0, 6, -4, -22, -11, 5, 14, -9, -16, -12, -7, 35, 11, 3, -20, 7, -29, -25, 8, 1, -11, 13, 4, -4, -6, -21, 10, -1, 12, 6, 29, 12, 14, 33, 12, 7, 6, -21, -4, 24, -23, -15, 18, -1, -12, -5, -13, -1, 36, -5, -9, 21, 5, 1, 3, -40, 22, 5, 4, 44, -23, 22, -11, -17, 8, -16, -1, 11, -19, 26, 18, -2, -6, 23, -4, -6, 16, 37, 13, -29, -3, -43, 5, -10, -11, 7, 7, -59, 9, -8, -5, 18, 10, -27, 18, -2, -14, 12, 5, 22, 8, 11, -8, -6, 4, -9, 20, -35, 15, 21, 19, -12, 7, 1, 26, -52, -11, -17, -23, -9, 1, 3, 2, -7, -19, -17, -9, -16, 0, -9, 4, -9, -1, 3, -10, -49, 6, 18, 1, 14, 9, -39, -13, -11, -31, 12, 0, 8, 19, -4, 10, -17, 7, 8, -9, -71, -20, 3, 7, -4, 14, 18, -14, -74, 22, 1, -7, -10, 1, -28, -5, -4, 4, 12, -30, 0, -2, 13, 13, 0, 8, 9, -6, -15, -14, -2, -3, 32, -3, -4, 1, -3, 2, 8, 2, -4, 9, -8, 17, -25, 7, -17, -15, -58, 10, 3, 6, -25, -8, 9, -8, -31, -12, 13, 5, 4, 3, 19, 3, -14, -11, 4, -11, -21, 24, 7, -15, 10, 8, 4, -18, 28, 14, 6, -9, 1, -2, -18, -14, -29, 2, 21, -36, 7, 19, 8, 10, 27, 28, 6, 25, -27, 8, -15, 25, -28, -8, 16, -27, -20, 1, 22, -19, 0, 16, 15, 5, 1, -3, -7, 6, -10, -17, 13, -5, 22, 18, -10, -15, -6, -14, -23, -18, 30, -13, 10, -10, -7, 12, 20, -31, 15, 3, -5, 2, -11, 12, 8, -13, -2, 11, 1, 8, -12, -17, 31, -14, -6, -10, 13, -18, -40, -16, -15, 0, 1, 2, -27, 14, 6, 7, 4, -33, 2, 22, -17, -16, -28, -19, 34, -19, 15, -3, -17, 21, 8, -21, 24, -7, 21, 7, 3, -5, -17, 15, -19, 10, 12, -3, -23, 3, -4, -10, 0, 1, 31, 11, 8, -14, 17, -1, -33, -5, 3, 11, -2, -3, -34, 12, -6, 5, 33, 34, -15, 15, 23, -5, 24, -23, -52, 26, 16, 9, -1, -24, 40, -16, -32, -39, -27, -37, -14, 8, 21, -44, -54, 9, 7, -1, -22, 43, 57, -45, -26, -25, 6, 34, 21, 10, -26, -25, 17, -6, 16, -27, -10, 9, 8, 23, 23, 15, -7, -14, -14, 0, 17, 7, 8, 5, 13, 0, -14, -4, 13, -16, 35, 10, -13, -41, -18, 8, -13, -49, -50, 0, -3, 27, -3, -22, 8, -11, 22, 18, 10, -10, 15, 23, -9, -42, 9, -13, -39, -12, 12, 7, -7, -3, -31, -28, 59, 7, -29, -21, -7, -6, -43, -4, -16, -22, 10, 12, -16, 27, -12, 30, 30, 46, -5, 19, -45, -19, -19, 4, -1, -5, -19, -6, -41, 6, 29, -13, -10, -10, 10, -3, -13, -18, -10, -4, -28, -13, -6, -5, -10, 7, 21, 22, -9, -5, 2, -4, -7, -14, -1, -20, -7, -22, 6, -17, 0, 4, 2, 3, 22, -35, 7, -15, -3, -23, 15, -5, 6, -14, 18, -3, 14, 7, 11, -17, -10, 14, 5, 0, -11, -3, -7, 1, -47, -5, 4, 3, -4, 11, -9, 23, 1, -7, 6, -8, -8, -16, -11, -7, -6, 7, 7, 4, 13, -12, -5, -3, -1, -18, 18, 11, -15, -12, 2, -16, -10, -20, -3, 17, 2, -11, 5, 10, 2, 12, -16, -9, 2, -23, -19, -17, -27, 16, 11, 6, 5, 3, -6, 15, 15, 24, -2, 8, -10, -5, 8, -3, -25, 9, 3, 33, 16, -15, -1, -7, -2, 20, 9, 12, -9, -6, 6, -32, 8, -10, 2, 9, 27, -9, -7, 9, 18, 20, 1, -29, -2, -12, -64, 0, 3, -12, -11, 3, -1, 10, -18, 25, 18, -15, 14, 5, 10, 20, 24, 0, 21, 17, -28, -15, -7, -2, -13, -9, -1, 14, 10, -35, -1, 15, -4, 2, -17, 3, 2, 9, 10, -4, -45, -35, 1, 47, -3, -20, 12, -29, -24, -14, 6, 5, 12, 10, 11, 8, -9, -12, -10, 2, -4, 14, -14, -4, 16, 4, 2, 3, 4, -12, -30, 17, 0, -3, -4, -2, 2, -8, -6, -10, 2, -12, 1, 6, 5, 16, -2, 8, -12, -13, -15, 6, -18, 17, 5, 8, 14, 4, 22, 3, -2, -16, 11, -5, 18, 4, -11, 13, -22, -10, -7, -9, 17, -4, -22, -25, -20, -6, 5, 5, 2, -35, -2, 24, -16, 3, 1, -4, -40, -30, 29, 2, 11, -6, -17, -11, -16, -43, -6, -12, 17, 2, 5, 20, 13, -19, -17, -5, -3, 1, -1, -13, -3, 3, 11, 17, 4, 2, -1, 11, 7, -19, -6, -9, 25, -9, -11, -6, 8, -23, -5, 7, -14, -12, 20, 2, -21, -21, -54, 7, 3, -24, -5, -1, 14, -6, 10, 10, 16, -1, -9, 11, -1, -39, -32, -16, 3, -6, 7, -22, -9, 6, 11, 6, 3, 7, 1, 9, -6, -10, 7, -8, 8, -3, -2, -9, 5, -13, 6, -22, -19, -1, 22, -20, -30, -27, -57, 12, -8, -35, -14, -4, 8, -3, 0, 2, -1, 0, 8, 7, 6, -8, 1, 2, -3, -11, 6, -20, -6, 5, 19, 2, -20, -8, 6, -1, 8, 1, 1, -10, 15, -13, 9, -10, -1, -12, 3, -10, -20, 7, 17, -15, -30, -34, 9, 6, -4, 7, -10, -5, 4, 5, 4, 20, 1, 37, 21, 21, -8, -1, 0, 5, 14, 24, 10, -6, 1, 18, 23, -25, -12, 8, -5, -4, 11, -6, -22, -8, 14, 25, 1, -16, 35, 11, 10, -9, -26, 1, 15, -1, -31, -17, 28, -23, 12, 15, 11, -8, -16, 23, 25, -25, -6, -25, 3, -12, 16, -39, 6, 6, 14, -1, -10, -38, -4, 18, 2, -7, -31, -5, -10, -6, 18, -10, -2, 6, 15, 7, -16, -22, 4, 22, 6, -14, -24, 37, -4, -5, 5, -42, 1, 3, 8, 2, 4, -46, -24, 15, 2, -6, -15, -12, 9, -47, 19, 3, -12, -15, -6, -25, -7, -21, -8, 9, -3, -9, -13, 8, 4, -25, 15, 0, -18, 17, -26, -28, 9, -20, 7, -8, 5, -5, -44, 39, 26, -13, -3, -13, -6, 32, -2, -7, 19, -50, -20, 4, -1, -19, 5, -16, 4, -23, 2, 0, -14, -14, 14, 25, 30, -10, 1, 9, 7, -3, 18, -4, -2, -18, 0, 3, 9, 12, -8, 26, -11, -10, -10, 3, -15, 0, -18, 16, 22, -19, 20, -19, 2, -20, 11, -8, -13, -7, 14, -2, -2, 15, -8, -2, 6, -14, 10, 13, 3, -18, 16, 12, 11, -4, 17, 8, 2, 3, 10, -4, -26, -8, -11, 17, 15, 0, -31, 8, 2, -9, -3, -1, -15, -17, -14, -16, 6, -15, 30, -11, -33, -16, -1, -23, -3, -5, 11, -11, 1, 7, -5, -8, 19, -2, 25, -2, 10, 11, 11, 8, -7, 9, 15, 8, -5, 6, 7, -4, -25, -10, 1, 4, -4, -17, -4, 6, 18, -7, -11, -6, -29, -27, -13, -18, 25, -13, 7, -8, -8, -7, 29, -9, -16, 4, -8, -22, -6, 3, -1, 23, -4, -18, -6, 3, -1, -8, 2, -3, 11, -3, -19, 10, 0, 19, 14, -22, 9, -17, -19, -9, 13, -6, -9, 17, 23, 5, -18, -15, 13, 7, 11, -9, 2, 10, -27, 8, -16, -10, -20, 12, 4, -15, -37, -12, 0, -32, -1, 17, -9, -18, -11, -12, 3, -7, 0, -1, 0, 8, -17, -24, -21, 5, 6, 7, -5, 1, -35, 8, 3, 8, -13, 12, 0, 8, -7, -14, -6, 3, 3, -5, -6, -7, -43, 5, 10, 8, -20, 11, -3, 10, -23, -13, -1, -26, 8, 15, 9, -20, 4, -10, 9, 13, 5, 7, 12, 1, 10, -35, 6, 8, -17, 30, -5, 7, -10, 3, -14, 5, 5, 5, -17, 8, 13, 6, -17, 12, -1, 19, -10, 7, -10, -17, -19, 18, -5, 9, -11, 25, 8, -11, -9, -9, 0, 11, 24, -1, 41, 18, 23, -19, -6, -29, 23, 17, -2, 4, -7, -19, -3, -33, 16, 8, 20, 13, 15, 1, -19, -15, 30, -15, 3, -2, 5, -26, -25, 8, 24, -64, 22, 26, 28, -18, 10, -14, 18, 10, -8, 29, 12, -50, 9, -6, -14, 11, -11, 31, -17, 8, -33, -12, 19, 0, -27, 15, -1, -10, 1, -20, 2, -16, 15, 19, 13, -14, -16, -16, 3, 11, -29, -11, 0, -12, -21, -3, -1, -27, 10, 6, 28, 4, -9, -1, 4, 29, 10, -6, 8, -29, 16, 10, 15, -32, -48, -13, -16, 9, -10, -13, -2, -14, -30, 9, 11, 10, -15, 3, -3, 1, 12, 27, 12, -8, -19, -52, -20, -9, -33, -6, 3, 6, 4, 52, -5, 0, 23, 7, 9, 17, -9, -8, -2, 6, -46, -42, 9, 26, 1, -39, 7, -12, -1, 2, 1, 11, -9, 0, -20, 14, 0, -3, 21, 6, 18, 40, 16, 16, 6, -18, 8, -37, -10, 16, -22, -3, 19, 27, -30, -7, -10, 8, 17, 12, -42, -4, 5, -4, -22, -18, 2, 4, 11, -15, -25, 10, 0, 11, 0, -14, -8, 0, -12, 15, 0, 14, -23, -2, -2, -14, 5, 34, 16, 9, 6, 6, 17, -1, -4, 9, -17, 11, -4, 9, -9, 23, -16, -14, -24, 5, 24, 19, -45, 1, -16, 8, -5, -12, -11, -8, -8, 0, -30, -7, 2, 8, -10, -14, -2, -38, -3, -2, 2, -13, -15, 2, -1, -5, -12, 16, 1, -5, 15, 12, 25, 10, -6, 9, 20, -5, 11, 7, -35, -20, -11, 6, -17, -31, 5, 43, 20, -3, 13, -11, -9, 7, -14, 6, 18, 18, -15, -64, -4, 33, 14, -2, 5, -4, -3, -17, -5, 19, 9, 5, 4, 3, 12, 1, -21, -23, 19, -11, -3, -3, 23, 2, -6, -7, -14, -6, 4, 1, -20, 23, -24, 45, -15, 1, -4, -11, 11, 8, -1, -24, -34, 4, 8, 40, 3, 19, -16, -1, 20, 0, -25, -3, 13, -4, -12, 11, 4, 6, -9, -24, 12, -2, -1, -32, 11, 6, 12, -3, 9, -2, -14, -4, 12, -4, 5, 7, -6, -2, -22, 32, 3, 16, -24, 5, 1, -10, 5, -32, -25, 4, 12, 36, -3, -24, -7, -9, -10, -13, -18, -11, 13, -19, -15, 1, 5, 15, -13, -18, -15, 4, -3, -29, -16, 24, 11, -4, -6, 20, -11, 6, 5, 30, 4, 12, -21, -9, -26, 4, -5, -8, -2, -1, 22, -24, -15, 16, -1, -1, -21, 19, -9, -7, 2, -9, -2, 8, 6, 15, -21, 2, 23, -2, -1, -15, -6, -21, 3, -7, -3, -26, 11, -14, 22, -2, 7, 6, 8, -24, -8, 5, -5, -5, 12, -14, 4, 3, -13, -3, -11, -3, -1, 4, 15, 11, 5, -1, -4, -22, -5, -1, -9, -11, -7, 0, -14, 2, -5, -1, 18, -5, 12, -7, -1, -29, -6, -14, 14, 13, 7, -7, 29, -14, 4, 15, 0, -6, 1, 7, 22, 9, 8, 3, 1, 5, -7, 15, -3, 6, 2, 5, 8, 8, 0, -4, -4, -24, -16, -6, -6, -5, 8, -13, 3, 2, -17, 6, 8, -2, 3, 1, 7, -22, -2, 12, 14, 6, -24, 9, 26, -3, 4, -13, -2, 29, -1, 6, 22, 2, 0, 13, 1, -9, -3, 15, 17, 7, -5, 8, -8, 10, -5, -10, -30, -5, 3, 0, -6, -26, 13, 10, 10, 6, -5, 11, -25, 10, -32, -22, 36, -6, -41, 8, -23, 15, 6, 19, -4, -38, 1, -9, 26, 8, 27, 4, -5, 5, 8, -10, 2, 20, 0, 9, -19, 2, -2, 15, -32, 2, 15, -3, -8, 6, -25, 8, -4, -27, -12, 14, 25, -1, -19, -3, 12, 7, 11, -2, 16, 15, -3, 3, -45, 0, 0, -6, 5, -17, 13, -1, -10, 0, 32, -12, -17, -14, -16, 1, 13, 19, 18, 14, 29, 3, 2, 3, -21, -26, 10, 10, 10, 5, 6, 17, -6, -21, -39, 6, 24, -13, -36, -35, 12, 9, 42, 3, 9, 4, 35, -10, -28, -1, 26, 8, -22, 1, 14, -7, -11, -5, -21, 5, -15, -38, -26, -17, -16, 9, 22, 8, 23, -33, -2, -30, -11, -3, 8, -4, 11, 21, 47, -9, -11, -23, 9, 34, -17, 29, 4, 19, 9, -13, -1, -22, -6, -33, -10, 8, 34, -14, 30, 8, 28, 13, -8, 1, -8, 2, 13, -6, -4, -3, -9, 0, 18, 4, 24, 24, -5, -14, 2, -8, -26, -17, -11, 9, -1, 7, -37, 17, -38, -2, -3, -8, -21, -33, 2, -12, 17, -27, -8, -8, 1, -7, 14, 7, -4, 2, -12, 18, 22, -27, 1, 12, -9, -23, 3, 18, 21, 15, 23, 22, 19, 13, 2, 27, -14, -16, -3, -8, -2, 0, -18, 15, -11, -5, 10, -7, -40, 19, -23, 0, -22, -20, -15, -20, -3, -27, -24, -3, 18, -3, 30, -16, -5, 3, 7, 34, 17, -21, -13, -29, -22, -10, 16, 1, -4, 13, 17, 20, -2, -20, -8, 28, -30, -44, -6, 3, -11, 20, -36, 4, -36, -25, 15, 11, -29, -14, -12, -23, -1, 0, -3, -26, -34, 13, -39, -36, 13, -17, -10, -15, 10, 11, 13, -5, -1, 1, -23, -4, -17, -3, 3, -10, 14, -22, -11, 10, 38, 44, -7, -48, 22, 26, 4, -17, -15, 10, 56, -26, -66, 24, -2, 20, -13, -14, 3, -18, 24, 4, -19, -13, -13, 33, 27, 40, 2, 2, -16, 17, 20, -23, 2, 9, -22, -3, 10, -17, -27, 12, 20, 38, -19, -17, -19, -8, 30, 44, -5, -13, 31, 5, 29, -5, -15, 6, 5, 36, -56, 6, -43, -1, -70, 7, -4, 12, 45, -21, -23, -39, 30, 3, -8, -36, 3, 53, 18, -29, -37, 1, -13, 5, 5, -36, -25, -9, 24, -12, 24, -23, 22, -4, 48, 35, 4, -11, -7, 31, 58, -4, -32, -26, -29, -3, -22, 7, 6, 30, 17, 0, 10, -13, 21, -60, -34, -5, -36, -3, 7, 25, 23, -13, -11, -22, -4, -14, 14, -10, 8, 1, 3, 0, 4, -4, -25, 3, -5, -24, 37, 40, -3, 13, 20, 10, 26, -3, -45, -19, 9, 1, -16, -3, -12, -36, -1, -36, -20, 24, 1, -10, 0, 0, -7, -19, -25, 1, 2, -6, 2, 16, 35, -6, -11, 16, -20, 15, 6, 36, -21, -10, -5, 24, 10, -11, 4, -15, 30, 25, 12, -10, -6, 16, 9, -1, -22, -16, 29, 4, -7, 20, -14, -11, -34, -45, -18, -13, -2, 25, -13, 45, 2, -47, 4, 10, 7, -36, -30, -11, 46, 9, -50, -3, -42, -36, 9, 26, 5, -15, -34, 28, 41, 18, -12, -30, 8, -6, -10, -5, -10, 10, 31, -28, -48, -27, 12, -24, 35, 7, 11, 23, -8, 2, 9, -54, -12, 2, -34, 35, -6, -10, 1, 10, -5, 1, 13, -29, 17, 13, 11, -7, 4, -24, 9, 23, 19, -18, -1, 10, 1, 12, -5, 1, -11, 1, -14, 20, -8, 2, 27, -20, 2, -23, -16, 26, -22, -10, -31, -4, 14, 35, -4, -27, -3, 9, -20, 7, -3, -12, 0, -11, 3, -17, 16, -39, 14, 8, 14, 10, -1, -16, 13, -7, 26, -11, -8, -13, -11, -3, 21, 3, 3, 16, 6, 9, -6, -9, 9, -3, 12, -13, -17, 17, -15, 3, -7, 1, 18, 37, -5, -11, 10, 0, -12, 5, -14, -3, -1, -24, -18, 2, 20, 26, 23, 6, 8, 6, -4, -9, -3, -8, 19, 5, -1, -26, -18, -8, -3, -1, -10, 6, -8, 6, 5, -15, 10, -4, -1, -6, -11, -5, -2, -4, 11, -12, -23, 25, -1, -13, -1, -4, 11, -24, 3, 13, -7, 0, -4, -8, -2, 1, 1, 7, -24, -24, -5, 17, 1, -32, -21, 7, 4, -5, 10, 6, 4, 2, -5, 1, -3, 14, -1, -12, -26, -12, 18, 5, 19, 9, -9, -21, 1, 8, 2, 9, -27, -14, -12, 1, -2, -16, 11, -6, -4, 11, -11, -5, 3, 7, 1, -1, -19, 13, -14, 29, 0, -31, -22, 14, -2, 12, -5, -8, -3, -8, 11, 4, 1, -1, 26, -12, -5, -1, 16, 10, 14, -17, 0, -8, 0, 11, -3, 9, -36, -14, -11, -6, 3, -1, -15, -7, 5, 6, 4, 4, -10, 5, -12, 2, -19, -20, -16, 8, -4, -30, -13, 3, 10, -2, 9, 3, 0, -10, -16, 17, 13, 21, 19, -3, -27, -4, 3, 7, 13, 0, -23, 17, -6, -22, 0, 6, -36, 7, 2, 18, 8, 8, -21, -20, -14, -2, -9, 4, -4, -28, 14, -5, -39, 1, 2, -10, -15, -4, 26, 6, 22, -28, -15, 16, 6, 4, -2, 15, -5, 1, -4, -43, 1, -11, 27, 0, -2, -25, -16, 0, -1, 4, 34, 3, -20, -21, -13, 2, 18, 11, -3, -23, -3, -4, -18, -11, 19, -8, 12, -3, -49, -21, 5, -22, 24, -19, 33, -4, 17, -5, -5, 9, 16, -4, -5, -11, -11, 0, -6, -21, 2, -20, 20, 4, -6, -21, -9, 12, -2, 16, 30, -4, -17, 12, -7, 11, -21, 5, 4, 2, -16, -3, -26, 7, 34, -3, -4, 3, 21, -15, 12, -20, 12, 5, 16, 1, 25, 8, -14, -14, 1, 2, -1, -11, -4, 6, 16, -5, 5, 8, 15, -5, -9, -14, 16, 15, 10, 14, -17, -3, -7, 28, -2, -7, -31, 35, 0, -3, 29, 2, 18, 14, 12, -33, 1, -18, 2, 16, -35, -43, 6, -22, -2, 2, -7, 3, -1, -5, -8, -21, -1, 28, 21, 3, 15, -15, -2, 19, -43, 3, -11, 1, -20, -6, 22, -24, 1, 17, -16, 19, -41, 3, -7, 11, 4, 7, -5, 1, 7, 20, -3, 10, -3, -1, 0, 14, -51, -20, 15, -14, 17, -4, -5, -2, 0, 13, 1, 17, -3, 17, 5, 12, 5, -37, -2, -17, -34, -4, -33, 10, -14, -12, -15, 6, 18, 4, 10, -20, -24, 14, 7, 22, -9, 3, 11, -38, -34, -31, 1, 7, -10, -21, 7, 4, -11, -5, 0, 21, 10, -13, 11, 14, 6, -27, -28, 17, 6, 3, 12, 6, -18, -10, -6, -73, -22, 14, -10, 9, 2, -13, 3, -6, 12, 7, 8, -12, -12, 12, -21, 2, -19, 2, 19, 4, -14, 16, -2, 33, -6, -11, -3, 24, 8, -15, -23, -11, 19, -6, 8, -23, -10, 19, -6, 11, 15, -1, 7, -7, -6, 5, -15, 30, -11, -19, -33, -16, 5, 13, 11, -10, -8, 17, -35, -29, 3, 5, -42, 15, -2, -20, 11, 3, -16, 17, -25, 26, 17, 10, -11, -1, 22, 0, 0, -8, 14, -10, 0, 10, -4, 1, -12, 5, 4, 11, -9, 15, -4, -8, -22, 12, 15, -1, -25, -5, 6, 37, 18, -9, -18, 13, -49, 6, 20, -16, -48, 5, 9, 4, -4, 28, 5, 0, -7, 16, -6, 14, 4, -11, 12, -1, 3, -7, -3, -36, 20, 8, 18, 9, -3, -2, 5, -2, -15, 13, 7, -21, -10, -2, -43, -8, -24, 17, -5, 28, -22, -28, -19, 16, -57, -8, -27, 12, -3, -30, 4, 6, -10, 5, 12, 3, 4, -7, -19, 16, -19, -4, -1, 10, 6, 20, -8, -10, 0, 16, -7, 10, 12, -31, -11, -41, -21, 25, 2, -16, 15, 46, -2, 20, -14, -25, 19, -17, 5, -5, 1, 6, 13, -21, -4, -30, 24, -1, 3, 0, 2, 7, -9, -11, -17, -1, -10, -2, -5, 14, -4, 15, 20, 26, -12, 5, -12, -15, -4, -3, -7, -9, 12, 8, 17, -5, -6, -36, 13, 14, -6, 11, -6, 2, 9, -5, -5, -8, -21, 0, -5, 23, 4, -28, -6, -9, -13, -8, 21, -26, -21, -22, -2, 14, 2, 9, 11, -3, -3, 14, 8, -4, -64, 21, -26, 13, -24, -29, -5, 10, 8, 8, 16, -21, 6, -19, 9, 5, 2, -6, -6, -16, 4, 7, -15, 2, -23, -17, -4, 33, -4, 26, -3, 3, -8, -7, -1, 13, 32, -4, 2, -1, 7, 0, -13, 17, 27, -32, -10, -5, -12, 1, -6, 7, -5, -21, -37, 3, 8, -15, -23, 26, -14, 24, -2, -9, 17, -35, -34, 0, 0, -6, 18, -19, -20, 16, 7, 5, -8, 14, -14, 8, -4, 1, -4, 1, 37, -17, 21, -5, 13, -15, -9, 12, 15, -19, -15, -21, -18, -20, -2, -11, -5, -19, -23, -7, 1, -31, -10, 16, -9, 2, 1, -5, 15, -25, -2, -9, 10, -1, 6, -24, -17, 2, -26, 3, 13, 7, 10, 18, -3, -20, -7, 15, 30, -18, 16, -9, 10, -15, -9, 9, -18, -13, 9, -16, 10, -28, 4, 1, 1, -4, -13, 4, 10, -5, 13, 35, -15, 7, 12, 8, -39, 5, -18, -14, 1, 8, -23, -20, -5, 1, -22, -14, -4, -1, -4, -11, 0, -10, 2, 9, 17, -22, -5, -17, -1, 3, -13, 2, 5, -11, 2, 13, 0, -9, -19, 16, 14, 27, 9, -14, 9, 13, -35, 5, -8, -14, -7, -10, 18, -10, -27, -29, 14, 0, 3, -6, 26, 7, 6, -17, 5, -4, -9, 6, 15, -12, 2, 10, -14, -24, -1, -19, -8, 6, -10, -4, -6, -10, 8, 15, 3, 8, 2, 1, 2, 10, 9, -13, -3, 3, 7, -4, -3, -22, 6, -5, 6, -11, -2, -41, 23, -3, 5, -14, 13, 18, 9, -28, 4, -11, -7, 13, 17, 4, 6, 12, -11, -19, -3, -23, 2, 15, -25, 11, 15, -3, 5, 12, -1, -18, 7, -1, 15, -13, 12, -3, 2, -7, 7, -19, -6, -18, 10, -13, 16, -16, -10, -31, 23, 0, -8, -9, 24, 11, -7, -23, -1, 13, -1, 15, -2, 1, 15, -30, -11, 9, -10, -13, -28, -12, 16, 17, 2, -15, -3, 16, 3, -2, 5, -21, -9, -16, 17, -4, 2, -13, 10, -18, -22, 3, 29, -36, -5, 4, -13, -31, -30, -32, -24, -14, -30, 5, 37, 43, 20, 6, 23, 11, -10, -8, 22, -13, -3, -7, -12, 3, -6, -11, 3, 0, 4, -57, -13, -16, 5, 21, -3, -17, -28, 14, 13, -37, 12, -8, 0, -14, -8, -19, 30, -25, 5, -7, 20, -4, -4, 4, 6, -22, -25, 10, 14, 19, 3, 2, 39, 13, 12, 0, 4, 8, -9, -10, 8, 14, -1, -1, 6, -15, -20, 2, -18, -32, 27, -1, -8, 10, -39, -35, -10, -24, 28, 2, -10, 13, -19, 12, -5, -9, 19, 0, 7, -6, -7, 6, 16, -24, -50, -16, 23, 6, 28, -6, 26, -30, -34, 5, 15, -14, -29, -1, 18, 8, -28, -33, -16, -7, 12, 16, 7, 8, 19, 12, -4, 16, -18, -11, -25, -25, 7, -3, 4, 22, -12, -25, 28, -31, 1, -7, 16, 5, -4, -9, -32, 23, 14, -1, -17, -4, -4, 2, -22, -39, -3, 11, 6, 13, -11, -1, 18, -4, -1, 1, -10, -2, -12, 12, 8, 0, 2, -22, -17, 19, -4, -18, -29, -11, 3, 3, -3, -2, -38, 3, 16, 1, -20, -1, -10, -22, -2, -12, -31, 5, 26, -1, 1, -15, 0, 24, 19, -25, 37, -37, 5, 25, -8, 12, 12, -7, 12, 2, -17, 20, -34, 19, 3, -7, 14, -10, -14, 24, -5, 13, -2, -8, 6, 15, -17, 3, -29, -9, 8, -9, -14, -24, -10, -4, -3, -15, -6, -18, 16, 19, -13, 17, 15, 34, 17, 30, 11, 9, -18, -6, -29, -18, 21, 15, -5, -3, -6, 17, 3, -5, 2, -8, -1, -25, -2, -21, 15, -11, 8, 9, 26, -1, -1, -4, -35, -10, 5, 16, 22, -6, 7, 7, 21, 19, -17, -5, 24, 49, -15, -14, -10, 8, 4, 23, -11, 3, -4, -19, -8, -2, 13, -2, -4, -16, 23, -5, -2, -11, -14, -7, 25, -18, -16, -10, 12, 12, -20, -13, 27, 8, -4, 20, -36, 5, -7, -6, -21, -6, -13, 13, 8, 28, 2, -35, 13, 3, -15, -2, -8, -16, 17, 15, -31, 12, -6, -37, -20, 0, -17, -6, -19, -18, 5, -5, -2, -13, -20, -1, 28, 2, -9, -2, -17, -17, -7, -5, 27, -25, -4, 16, -22, 9, 11, 7, -44, 19, 30, 24, -8, 10, -16, -27, -39, -33, -38, 1, 26, 4, 3, 7, -27, 5, -19, -8, -30, 17, -24, 4, -27, -5, 9, 22, 14, -12, 3, 27, -1, 2, 16, 8, -6, 0, -25, 10, -16, -12, 4, 15, 7, 4, -8, 14, 26, -11, -5, 20, 10, -13, -26, 17, 12, -8, 13, 34, 6, 4, 29, 8, -36, -2, -8, 7, -26, -2, -25, 12, -4, -6, 17, 1, -3, 2, -9, 3, -20, -5, 26, -21, -1, -8, -28, 3, -5, -25, 1, 7, -8, -7, 2, -5, 24, -6, -2, 10, 6, -29, -38, 21, 12, -7, -2, 24, -12, 1, 16, -6, -28, -16, -10, 8, -24, 0, -30, -2, -10, -1, 6, 4, 12, -22, -1, -13, 14, 3, -2, -35, -10, 17, -3, 6, 8, -18, -3, 3, 5, -15, 2, 4, 12, -9, -2, -18, -4, -12, -14, 3, 27, -12, 7, -7, -2, 28, 28, 16, 13, -21, 5, -56, 49, -12, 1, 0, 16, 8, 12, 16, -15, 29, -36, 8, 27, -15, 13, 29, -19, -29, 20, -4, -13, -4, -19, 20, -15, -12, 1, -4, 51, -8, -33, 18, 11, -2, -22, 6, 10, -5, 19, 33, -36, 40, 10, 16, -18, 7, 9, -9, 8, 5, -31, -30, -14, -27, 10, -5, -11, -1, 5, -20, -4, -9, -12, -23, 2, -11, 36, -9, -35, -25, 5, 19, -9, 9, 1, 31, -15, -25, 1, -13, 7, -3, -13, -8, 10, -2, 15, 16, -23, -7, -11, -8, 15, 28, -1, 9, -26, -24, -30, -8, -15, -18, 21, 6, 12, -31, 8, -14, -30, 6, 17, -22, -23, 0, -10, -11, -16, -51, -3, -13, 5, -7, 13, 12, -48, -19, 7, 6, -6, -12, -12, -8, 7, -1, 5, -43}

#define TENSOR_CONV2D_2_FUSED_KERNEL_0_DEC_BITS {7}

#define TENSOR_CONV2D_2_FUSED_BIAS_0 {99, 39, 81, -15, 8, 32, 51, 40, -16, 2, 18, 10, 18, 90, 24, -9, 71, 67, -10, 6, 77, 30, 14, 8, 69, 6, 35, 78, 36, 52, 105, 62}

#define TENSOR_CONV2D_2_FUSED_BIAS_0_DEC_BITS {7}

#define CONV2D_2_FUSED_BIAS_LSHIFT {3}

#define CONV2D_2_FUSED_OUTPUT_RSHIFT {7}

#define TENSOR_CONV2D_3_FUSED_KERNEL_0 {7, -24, -17, -6, 21, -9, 13, -2, -19, 15, -24, 19, -5, -3, 20, 22, 4, -6, 27, 0, 14, 13, 12, 13, 4, 7, 10, 14, -5, -4, 13, 23, 11, 11, -1, -20, 12, -4, 5, 19, 17, 8, -2, 3, -9, 5, 15, 0, -28, -22, 2, -15, -4, 8, 0, 18, -5, -10, 7, 12, -14, -20, -3, -20, 34, -14, 9, -11, 12, 13, 12, 53, 14, 26, 2, -18, 0, 21, -15, -8, -2, 26, 17, -27, 3, 1, -6, 2, -8, -8, 0, -15, 4, 12, -18, -6, -8, -24, -13, 7, 22, -6, 9, -2, 3, 2, -20, 12, -1, -17, 0, 8, -7, -8, 28, -1, 16, 2, 1, 7, 16, -3, 6, 7, 0, -10, 8, 14, 9, 1, -3, -15, 6, -1, -1, 11, 8, 15, -4, 4, -4, 6, 11, -1, -5, -21, 2, -2, -11, 0, 0, 4, -9, -21, 13, 3, -2, -14, -4, -13, 26, -20, 13, -1, 11, 0, 16, 36, 7, 12, 9, -14, 9, 5, -15, -11, -7, 28, 9, -29, 5, 6, -1, 5, -9, -16, -4, -13, 17, 9, -10, -2, -13, -32, -22, 6, 25, -6, 19, 4, 5, -2, -20, 24, -7, -10, 9, 12, -2, -6, 36, 4, 15, 15, 6, 9, 18, 19, -5, 19, -9, -7, 13, 12, 18, -6, -4, -12, 15, 3, -11, 17, 4, -4, -9, 0, -11, -1, 10, -10, -20, -17, -1, -2, -1, 11, 9, 23, -10, -8, 24, 2, 0, -13, -3, -11, 37, -15, 13, -12, 13, 4, 22, 37, 15, 29, 10, -3, 9, -1, -17, 5, -5, 25, -14, -39, -3, 6, -6, 14, -4, -2, 6, -6, 9, 16, -6, -17, -15, -19, -2, 2, -5, 26, -24, -22, -18, -5, -20, 2, 24, 32, 27, -23, 21, -5, -3, -2, -5, 7, 17, 21, -18, -40, 2, 8, -34, 4, -23, 10, -17, -1, -2, 14, 4, -21, 8, 14, -6, 19, -3, -22, 4, -11, -7, 3, -6, -11, 13, -2, -5, -1, 8, 3, 0, -19, 3, -5, -18, 14, -7, 5, 3, 26, -12, 16, 18, 14, 13, -19, -31, 10, 0, -27, -26, 9, 31, 4, -8, -6, 10, 14, -2, -21, -10, 28, -12, -4, -12, 35, -7, -18, 26, 7, -2, -21, 3, 2, 2, 26, -27, -9, -5, -3, -16, 7, 16, 36, 25, -11, 10, -1, -6, 6, -3, -8, 6, 10, -1, -36, 9, 8, -32, -3, -19, 15, 3, -15, 2, 4, 7, -16, 7, 18, 12, 11, 2, -21, 8, 9, -6, 13, -1, -7, 16, 1, -8, -16, 5, 3, 8, -11, -13, -4, -15, 5, 4, 16, 4, 8, -10, 1, 13, 14, 16, -10, -20, 5, 3, -16, -9, 4, 18, 6, -15, 8, 13, 5, 4, -6, -13, 15, -9, -10, -6, 21, -12, -11, 10, 16, -8, -21, 12, 5, 8, 19, -35, -2, -11, 12, -17, -4, 18, 53, 29, -15, 9, -1, -7, 2, -6, -9, 7, 7, -15, -45, 4, 15, -27, -5, -35, 27, -16, -6, -1, 11, 7, -7, 3, 4, 15, 19, 1, -15, 5, -11, 6, 9, -2, 6, 9, -8, -5, -25, 10, 6, 22, -21, -30, 5, -9, 6, -1, 16, 3, 4, 7, -3, 2, -5, 20, -5, -18, -1, 5, -8, -12, -1, 14, 16, -11, 16, 11, 10, 8, -19, -21, 24, 0, -24, -31, 27, 2, -8, 23, 15, -11, 13, -25, 24, -4, 12, -9, -8, -19, 6, -8, 23, -3, 4, 19, 19, 21, -21, 30, 9, 2, -4, 2, 0, 30, -11, 3, 26, -30, -13, 16, 1, -15, 4, -42, 11, 8, 11, 5, -9, 7, -9, -2, 12, 14, -12, -14, -15, -18, -10, 4, -2, -11, -7, 13, 16, 22, -12, 4, 6, 1, -11, -16, -11, -15, 1, -6, 0, 6, 7, 17, -9, 20, -10, 19, -15, -5, 1, -26, 8, -5, 7, 11, 5, 6, -36, -17, 21, 11, -5, 4, -18, 7, -10, -14, -12, -11, 22, -19, 14, -1, 7, -11, -7, -12, -6, -14, 8, 2, 14, 25, 13, 5, -17, 26, 3, -5, -11, 8, -5, 17, -16, -11, 18, -27, -6, 6, 7, -2, 17, -33, 2, 3, 5, 3, -22, 13, -10, 3, 0, 6, 1, -5, -6, -27, -7, 4, 0, -3, -6, 3, 7, 14, -1, -1, -1, -7, -4, -17, -4, -7, 10, -3, -10, 8, 11, 8, -7, 22, 2, 14, -12, -7, -3, -23, 6, -1, 1, 4, -9, -2, -26, -8, 12, 13, 6, -5, -11, -1, -13, -21, -9, -13, 34, -12, 15, 3, -1, -6, -6, -14, -6, -39, 14, -9, 20, 17, 14, 2, -5, 27, 3, -9, -3, 14, -9, 22, -10, 10, 22, -21, -2, 24, 5, 5, 11, -28, 18, 1, 8, 4, -8, 8, -18, -18, 3, 2, 17, -8, -9, -27, -4, 4, 0, 13, -1, -4, 6, 26, 10, 7, 6, 5, -3, -15, -6, -7, -4, -13, 5, -12, 11, 5, -6, 37, 8, 10, -6, -5, 4, -32, 20, -11, 11, 3, -17, 3, -30, -11, 6, 10, -7, -3, -24, 13, -3, -8, -13, -14, 0, -14, -5, -9, 11, -1, 5, -11, -29, -2, 6, 32, -1, -2, -20, 31, 5, 11, 0, 12, -3, 36, -22, -3, -34, 15, 21, -29, 21, -24, -24, -22, -20, 12, 7, -10, -1, 2, -15, 14, -7, 3, -20, 11, 6, -28, -11, -1, 18, -9, -16, 14, 5, 16, 11, 4, -40, 15, 5, -12, 33, -1, 0, 7, 1, -7, -33, 9, -3, -14, 7, -13, 5, -15, -7, -9, 13, -4, 19, 0, 29, -4, 14, 18, -20, -6, 5, -16, 27, 0, -15, 12, -10, 13, 28, -2, 6, -10, -6, -12, 4, -11, 8, -20, -1, 5, -6, 14, 4, 4, -4, 21, 10, 9, -5, 5, -4, 15, -14, -18, -12, 12, 18, -23, 21, -17, -14, -12, -15, 17, 7, -14, -11, -2, -15, 6, 7, 0, -7, 1, 6, -22, -3, 9, 29, -4, -13, 9, -15, 4, 21, -7, -20, 0, 9, -1, 27, -3, 10, 14, 8, 0, -20, -11, 5, -2, 7, -14, 16, -22, 7, -12, 6, 4, 24, -3, 47, 6, 16, 13, -27, -3, -4, -25, 17, -18, -12, 10, -5, 7, 40, -10, 10, -14, -12, -19, 17, -15, 3, -18, -20, 11, -8, 31, 15, 15, -9, 45, 10, 5, -9, 33, -16, 15, -12, -23, -17, 9, 7, -17, 18, -30, -15, -18, -22, 13, 7, -15, -8, -2, -15, 19, 8, 16, -16, 3, 6, -23, -23, 17, 22, -7, -4, 8, -27, 20, 25, 5, -39, -10, 7, -13, 25, -4, -5, 8, 6, -1, -18, -11, -2, 8, 0, -6, 9, -17, 4, -26, 6, 0, 15, -9, 33, -3, 20, 9, -30, 6, 13, -32, 25, -37, -28, 9, 7, 2, 24, 16, 38, -2, 14, -15, 24, -28, -15, -8, -8, 10, 14, 9, 13, 13, -19, 16, -2, -1, -8, -20, -20, 10, 0, 9, -7, 19, 12, -2, 16, -11, -16, -20, 11, -22, 24, 6, 8, 2, 13, 19, 9, -10, 7, 4, -9, -24, -3, 0, 15, -3, -8, 0, 3, -1, 8, -2, -16, 0, -2, -5, 3, 1, -1, -22, 21, -8, -29, 19, 9, -18, -22, -7, -1, -8, -3, -1, -20, -18, 5, 16, -37, 4, 12, 6, -34, -19, 26, -17, 41, 15, 6, 2, 10, 2, 25, 16, 36, -6, -10, -9, 20, -14, -4, -15, 2, 8, 7, 8, 12, 14, -11, 7, 6, 5, -4, -17, -12, 16, -6, -10, -1, 12, 13, 2, 14, -3, -19, -20, 9, -11, 28, -3, 11, -4, 13, 9, 8, -1, 5, -1, 4, -20, 6, 10, 10, 5, -11, 5, 6, -2, 1, -9, -9, -6, -8, -4, -5, -4, 0, -8, 18, -13, -18, 2, 11, -17, -20, 7, 4, -1, -4, 9, -32, -16, -16, 17, -39, 10, 7, -2, -29, -16, 9, -14, 26, 15, 12, 13, 5, -3, 37, 11, 51, -1, -10, -12, 32, -12, -9, -16, 4, 13, 3, 7, 18, 14, -16, 8, 2, 1, -6, -14, -9, 21, -7, -1, -14, 15, 4, 1, 8, -5, -27, -18, 7, -12, 29, -7, -2, 2, 6, 1, 9, -5, 0, -11, -3, -15, -4, 3, 9, 10, -13, 12, 4, 5, -8, 10, -6, -5, -7, 10, 4, -5, -7, 1, 16, -20, -8, -2, 5, -15, -26, 3, 17, -11, 2, 4, -34, -18, -19, 18, -32, 19, 13, -10, -34, -21, 8, -11, 33, -6, 7, 10, 0, 1, 41, 14, 27, 10, -3, 9, -14, -18, 2, 24, 8, -2, 3, 15, -18, 1, -22, 21, -3, -7, 1, -19, 11, -31, -34, 0, 8, -7, 8, -26, -9, 20, 7, 16, -6, -28, 6, 17, -14, 7, -6, 0, 15, 10, -1, 5, -1, -3, -6, 13, 2, 30, -13, 8, 13, 14, -6, -9, -6, 2, -9, 19, 1, 10, 0, -31, 8, 8, -1, 25, -11, 25, -4, -6, -16, -40, 20, 13, 8, 1, -14, -10, 20, -26, -46, 12, -27, 15, 17, -13, 12, 17, -5, -24, -14, -22, 0, 7, 28, 6, -7, 15, -7, -15, 12, 16, 1, 2, 0, 13, -14, 4, -7, 12, -9, -8, 6, -20, -1, -15, -9, 2, 7, 1, 3, -13, -4, 2, -3, 16, 8, -26, 9, 27, -10, 8, -21, 15, 12, 7, -9, 10, -1, -2, -11, 8, -5, 19, -16, 8, -3, -5, 2, -4, -7, 13, -12, 31, -1, 2, -13, -26, 10, -3, 9, 25, 4, 16, -2, -2, -8, -37, 6, 15, 5, -10, -9, -6, 22, -14, -26, 7, -16, 16, 3, 0, 17, 29, -7, -15, -6, -16, 8, 2, 31, 19, -6, 7, -14, -13, 17, 17, 3, 15, 3, 20, -19, 11, -10, 28, -3, -5, 9, -18, -16, -30, -19, -10, 4, 10, 16, -2, 2, 9, -4, 28, 5, -13, 6, 29, -12, 17, -27, 18, 24, -2, 5, 13, -5, 10, -22, 11, -6, 26, -24, 4, -7, -3, 3, 4, -3, 14, -10, 26, -3, 2, -19, -16, 11, -5, -3, 19, -3, 19, -5, -4, 0, -43, 9, 14, 2, -11, -12, -16, 15, -18, -33, 10, -31, 10, 8, 1, 12, 34, -4, -25, 1, -16, -4, 7, 3, 1, 8, -17, -6, 11, 3, 8, 4, 9, -7, -2, -34, -14, -13, -3, 4, 24, -7, 10, -18, 3, -14, -33, 6, 8, 9, 35, 32, -2, -12, -6, 2, 15, -8, -16, -2, 5, 5, -18, -6, 9, 11, -17, -2, 0, 17, 31, 7, -6, -37, -7, -2, -17, -1, 2, -7, 11, 2, 26, 24, -10, -30, -2, 19, -4, -18, 12, -2, -3, 13, -13, -3, 4, 17, 1, -6, 28, 13, 45, -40, 4, 23, -8, -6, -5, 16, 18, 16, 4, 12, 10, 35, -6, -17, 2, 10, 1, 8, -6, -1, 21, 6, -3, -9, 8, -4, 7, -28, -8, -18, 3, 10, 18, -10, 8, -12, 0, -17, -28, 16, 0, 18, 22, 26, 1, -18, -6, -4, 7, 5, -13, 3, 3, 3, -25, -10, 3, 1, -13, -5, 2, 7, 17, 4, -12, -21, -5, 19, -7, -8, 0, -8, 15, 4, 23, 25, 6, -19, -3, 22, -6, -5, 7, 9, -7, 20, -5, 2, 4, 22, -7, -2, 22, 13, 20, -35, 1, 15, -2, 0, -17, 9, 23, 15, 14, 5, -4, 15, 2, -8, -19, 2, -6, 4, -11, -8, 31, 1, -10, 3, -2, 6, 1, -31, -3, -11, 8, 5, 16, -3, 34, -15, -5, -15, -14, 10, 8, 24, 27, 43, -9, -28, -10, -1, 12, 5, -1, 6, 6, 13, -37, -12, 5, -6, -21, -8, -7, 5, 25, 1, -3, -16, 4, 19, -9, 4, 0, -22, 11, 0, 26, 35, 10, -28, -21, 33, -6, -7, 2, 12, -3, 3, -20, 14, -3, 33, -5, 9, 18, 19, 14, -43, 6, 23, -22, 3, 0, 11, 23, 40, 14, -6, 12, 15, 6, -26, -6, -15, -38, -24, 7, 8, 15, 1, -12, -3, -13, 6, -8, 8, -16, 25, -19, 6, 12, 6, -1, 4, 23, 7, 10, -6, -3, 5, 15, 4, 6, 17, 8, 1, -9, -12, 16, 13, -7, -17, 19, 18, -18, 5, 3, -20, -1, 5, -9, -1, 3, 23, 7, 21, 14, -10, 19, 6, 5, 9, -24, -18, -4, -6, 28, -6, 14, -6, 15, -4, -9, 5, 30, 34, 8, 2, 21, -7, -29, -15, -15, 13, -12, 14, 16, 0, -20, -10, -4, 7, -1, -13, 8, 23, -3, 1, -9, 2, -19, -15, 3, 13, 16, -3, 3, -18, -10, 8, -16, 9, -1, 7, -23, -5, 12, 0, -3, 8, 12, 7, 2, -4, -7, -2, -8, -15, -2, 20, 10, 1, -5, -19, 6, 5, 2, -9, 18, 3, -16, 1, -2, -8, 8, 2, -8, 2, -5, 30, 1, 25, 17, -1, 13, -6, 0, -7, -13, -8, 4, -3, 20, -5, 16, -12, 12, -5, -16, 3, 15, 12, 4, -4, 10, -14, -15, -10, -16, 15, 4, 10, 3, -6, -8, -7, -12, 3, -11, -14, 14, 12, 9, -4, -1, 2, -22, -21, 10, 17, 29, 10, 7, -20, -4, 11, -22, 4, -13, 9, -17, -2, 17, -7, -4, 3, 6, 12, 0, 0, -11, -9, 9, -5, -23, 18, 11, 11, 2, -19, 15, 8, -6, -3, 20, 0, -21, 7, -9, -4, 4, 6, -10, -1, -9, 34, 13, 7, 19, -16, 2, 5, 7, -4, -10, -10, 11, 0, 26, 7, 13, -10, 23, 8, -11, 11, 28, 18, 0, 2, 14, -16, -17, 16, -17, 16, 2, 6, -7, -9, -10, -4, -13, 18, -17, -10, 28, 11, 6, -11, -12, -5, -8, 11, 14, 3, 15, 19, -7, 20, -16, 13, 1, 2, 11, -15, 33, 5, 11, -6, -13, 25, 9, 9, 5, 1, -6, 4, -46, -13, 16, 31, 1, -3, 11, -3, 1, -27, -4, 21, -48, 23, 1, 20, -4, 12, -2, -2, 14, 2, -5, -2, -14, -4, -20, 24, -10, -23, -22, -30, 11, -17, -1, -19, -11, 8, 0, 7, 17, -1, 7, -5, -42, 11, -12, -15, -51, 4, -13, -12, 10, -24, 1, -3, -16, -9, -3, 0, -6, -25, -12, 8, -16, -12, -2, -11, -10, 0, -4, -2, 19, 13, 16, 20, -11, 8, -17, 0, 16, 3, 11, -1, 16, -2, 5, -2, -12, 19, 6, 9, 7, -4, 6, 4, -33, -10, 15, 20, 4, 5, 7, 3, 3, -37, 3, 16, -18, 28, 8, 9, 0, 12, 5, -1, 19, 12, -8, 0, -5, 8, -8, 11, -8, -12, -2, -39, 16, -2, -13, -2, 4, 16, 5, 29, 15, -4, 15, -4, -41, 13, -5, -19, -28, 8, -10, -2, 16, -14, -1, 20, -9, -3, 0, 6, 9, -1, 5, 13, -11, -6, -13, -1, -6, -9, 3, 2, 38, 7, 33, 26, -18, 1, -41, 6, 13, 5, 27, -7, 13, -2, 0, -9, -30, 19, 3, 6, 12, 20, 4, 12, -46, -15, 18, 23, 21, -3, -1, 9, 2, -30, 4, 19, -26, 17, 5, 3, -3, 9, 10, -5, 17, 6, 8, -22, 7, 7, -14, 2, -4, -11, 13, -17, 0, -21, -10, 4, 12, 13, 7, 35, 5, -9, 24, -8, -41, 17, -7, -9, -27, 12, -1, -3, 11, -8, 4, -10, 12, -2, -14, 20, 0, -11, -9, 17, -36, -7, -7, 5, 7, 16, 2, -11, 25, -30, 3, -3, 4, 8, 9, -7, -5, -13, 0, -5, -28, 10, -18, 1, 26, -24, -8, -5, -31, -29, -11, -9, 7, 2, -21, -1, 34, -2, -15, -11, 26, -9, -21, 11, 11, 8, -1, -4, -36, 12, -2, 22, 14, -5, 15, 20, 10, -7, 14, -4, -15, -5, -12, -28, 22, 7, 15, -6, 16, 0, 11, 2, 21, -22, 5, -1, -15, -20, -26, -15, 14, -14, 20, 50, 12, 10, -17, 12, -21, -14, 19, -30, -13, -9, 24, 3, -3, -2, -9, 11, 7, 16, 6, -6, 16, -16, 6, -1, 13, 2, 7, -2, 1, -10, -5, 6, -7, 12, -20, 5, 23, -11, 5, 2, -33, -17, -7, -4, 6, 12, -9, -2, 18, 0, -12, -9, 33, -26, -7, 16, 15, 0, -4, 1, -25, 3, -5, 16, 6, -2, 10, 9, 15, -5, 3, 6, -10, -1, 1, -24, 11, 16, 13, -17, 6, -4, 2, 6, 28, -35, 5, -5, -11, -15, -2, -14, 27, -13, 11, 19, 12, 19, -16, 7, -13, -7, 10, -17, 0, 3, 19, 3, 7, 7, -13, 8, 16, 16, 10, -18, 17, -27, 10, 2, -1, 2, 29, -16, 1, -26, -22, -17, -10, 14, -28, 1, 28, -8, -11, 3, -28, -5, -11, -9, 2, 22, -12, -11, 25, 2, 1, -13, 40, -19, -1, -1, -8, -8, 9, 2, -30, 9, 13, 3, 4, -9, 18, 16, 19, 20, 0, 10, -20, -5, 5, -32, 10, 27, 15, -20, 12, -3, 2, 13, 16, -30, -2, -15, -21, -8, 5, -16, 22, -20, 26, 4, 5, 14, -4, 9, -16, 2, 0, -27, 2, -27, 21, 9, 10, 1, -4, 9, -4, 0, -27, -6, 5, -12, -33, -26, -6, -16, -25, -8, 21, -5, 5, -15, -22, 33, 31, -5, 13, 8, 33, 0, -3, -6, 12, -11, -22, 26, -8, -27, -8, 8, 19, 3, 5, 6, -10, -15, -24, -40, -1, -25, 27, 2, -15, -27, 16, -30, -6, -11, 11, 3, 33, 1, 2, -1, 7, 1, -25, -11, 10, -18, 8, -19, 7, 0, -1, -16, 21, 0, -19, -17, 18, 2, 15, 8, 2, 44, 5, 23, -8, 24, 12, -10, 23, 10, -2, 13, -19, -3, -27, 9, 1, 16, 1, 4, -13, 0, 4, -12, -5, -15, 3, -2, -29, -13, 12, -6, 3, -15, -22, 32, 14, -1, 9, 3, 35, 3, -8, -4, 4, -5, -11, 18, -2, -28, -13, -4, 12, -7, 5, 0, 3, -13, -3, -25, -12, -21, 11, 8, -12, -18, -2, -17, -16, 0, -8, 0, 32, 4, 10, 7, 15, 12, -23, 1, 3, -19, 2, -15, -1, -1, 9, -11, 7, 0, -12, -19, 15, 9, 14, 12, 3, 23, -2, 22, -17, 6, 5, 0, 36, -1, 7, 15, 1, 16, -23, -4, 0, 13, -2, -7, -14, 3, 16, -26, -18, -23, 14, -25, -33, -20, 12, 3, 15, -24, -26, 29, 20, -16, 3, 12, 22, 2, -7, -3, -1, -17, -10, 17, -6, -31, -24, -12, 12, -8, 9, 5, 2, -9, 0, -40, -2, -22, 17, 16, -10, -31, 5, -26, -2, 2, -7, 6, 36, 3, 1, 16, 29, 6, -17, 11, 2, -8, -1, -4, 1, -3, 7, -6, 21, 1, -20, -36, 33, 11, 32, 23, 3, 4, 1, 26, -25, 14, 18, 17, 28, 7, 0, 30, -3, 10, -13, 17, -28, -4, 19, 17, 45, 18, 13, 8, 1, 0, -28, 15, 21, -10, 5, 38, -11, 6, 26, -22, -15, -10, -9, 9, -8, -1, 1, -17, -10, 3, -1, -12, -35, 4, -6, 10, 7, 11, 25, -20, -7, -5, -21, 12, 5, 8, 10, -6, -1, 15, 8, -7, 10, 12, 10, 29, -15, -31, 1, -23, -24, 18, 43, -6, -8, 1, -22, 9, 6, 44, 17, -10, -16, -17, -35, 13, -21, -36, 6, -15, 3, -16, -1, -53, 13, 23, -16, 16, -7, -12, -4, -3, 4, 44, 19, 3, -21, 6, 8, 15, 36, -2, 8, 21, -11, 5, -33, 6, 6, -26, 4, 29, -4, 15, 12, -19, -10, -3, -6, 4, -3, 16, 5, -7, -6, -3, -7, -4, -13, -4, -4, 14, 10, 5, 29, 0, -7, 17, -33, 17, 14, 1, 1, 3, 6, 16, 15, -15, 12, 17, -3, 4, -33, -14, 9, -20, -17, 5, 24, -1, -3, 7, -13, 13, 7, 28, 4, -2, -1, 0, -44, 10, 3, -42, 17, -10, 11, -14, 3, -66, 6, 26, -3, 9, -14, -7, -10, -2, 9, 41, 29, -4, -1, 2, 0, 14, 40, 10, 0, -6, -15, 3, -18, 2, -21, -33, 5, 22, 1, 10, 10, -1, -15, -1, -17, -6, -6, 12, -2, 5, -29, -4, -20, -8, -20, 4, 4, 9, 19, 25, 29, -7, -1, 12, -41, 4, 0, 7, -8, -2, 20, 38, 8, -22, 13, 29, -12, 12, -20, -22, -6, -13, -26, 15, 14, 0, -1, -3, -17, 8, 16, 45, 14, -12, 9, -13, -60, 15, -13, -18, -3, -17, 20, -17, 19, -72, 13, 27, -15, 11, -14, -17, 2, -22, 16, 43, 12, 13, 4, 0, -12, 26, -18, -22, -20, -9, 14, -12, 26, -15, 15, -14, 21, -41, -26, -16, 14, -2, -5, 1, 11, -1, -11, -23, -13, -21, -15, 15, -15, 17, -19, 1, 4, 26, -2, 0, 10, 10, -4, -29, 16, 2, 35, -24, -14, -16, -25, -2, 3, -6, 7, 0, 7, -5, 13, -28, 0, -29, -11, -2, 1, 14, 4, -6, -18, -3, -5, -35, 0, 12, 1, -30, 0, -7, -4, 2, 31, -6, -5, 18, 9, 20, -1, 2, -7, -11, 13, -8, -6, -2, -2, -17, 10, 25, 13, 9, -7, 17, -21, -16, -25, -18, 7, -3, 24, 1, 8, 0, 16, -27, -20, -7, 8, 3, -3, 9, 10, -1, -9, -9, -10, -3, 2, 5, -12, 9, -17, -2, -3, 15, 1, 5, 5, -2, -10, -20, 21, 11, 20, -23, -14, -10, -19, -5, 2, -5, -2, 11, 16, 6, 19, -27, -6, -4, -1, 4, -7, 27, 7, -11, -23, -2, -4, -24, -1, 15, -9, -33, 16, -6, -15, 10, 32, 4, -5, 22, 9, 30, -5, -4, -5, -1, 17, -7, -3, 11, -5, -11, 5, 32, 12, 12, -13, 18, -22, -27, -23, 6, 7, -10, 46, -7, 30, -8, 31, -28, -12, -10, 6, 3, -11, -6, 16, 8, -2, -16, -8, 21, -16, -12, 0, 19, -26, -6, -16, 28, 10, -15, 7, 9, 4, -6, 33, 6, 41, -31, -21, -2, -20, -15, 11, -2, -7, 15, 11, -16, 11, -29, -10, -5, -5, -2, 0, 7, 18, -18, -11, 13, 4, -18, 0, 10, -8, -18, 26, -13, -14, 3, 24, 4, -3, 20, 0, 38, 4, 4, -15, 0, 16, -14, -2, 23, 3, -24, 6, 29, 14, 3, -17, -16, 18, -4, -9, -10, -3, 14, 5, -10, -5, -19, 20, -15, 18, 14, 24, 14, 1, 28, 25, 28, -6, 1, 12, 0, -30, -1, -4, -4, 3, 5, -46, -14, 10, -7, -9, 18, -17, -2, -1, -34, 4, -12, -5, 20, 17, -17, -3, -7, -34, -35, -9, 18, 16, -4, 10, 11, -5, -7, 22, -2, -3, -2, -17, 9, 6, 23, -9, -17, 17, 9, -20, -27, 36, -22, -11, 38, 8, 25, 9, 13, -24, -10, 3, 24, 8, -26, 10, -41, -12, -16, 22, -6, 0, 0, -21, -12, 12, -1, -16, -10, -5, -1, 4, 5, -13, -26, 17, -13, 15, 9, 14, 7, 12, 8, 21, 22, 1, -5, 8, 0, -24, -5, -10, -9, -1, 7, -14, -8, 15, -17, -11, 7, -7, -2, -2, -16, -7, -12, -6, 10, 7, -3, -16, 2, -9, -30, -18, 16, 18, 2, 9, 10, -8, 3, 2, -14, -11, -9, 9, 8, -2, 5, -15, -20, 26, 9, -17, 0, 21, -12, -39, 32, 5, 33, 4, -3, -7, -5, -3, 20, 16, -37, 14, -11, 0, -18, 0, -4, -2, 4, -1, -27, 1, -1, -14, -22, -1, 5, 9, 13, -21, -20, 27, -15, 24, 11, 24, -5, 8, 15, 40, 29, -5, -1, 0, -14, -18, 12, -12, -5, 8, 13, -21, -17, 11, -6, -20, -16, -9, 1, -2, -28, -5, 4, 11, 7, 3, -25, -11, 6, -4, -22, -23, 28, 9, 16, 11, 8, -26, 8, 6, 1, 3, 4, 16, -7, -2, 19, -11, -28, 11, -12, -7, -20, 32, -1, -29, 13, -2, 13, -7, -5, -5, -34, 5, 23, -5, -41, 18, -29, -26, -27, -12, -18, 13, 15, -7, -12, 7, 12, -13, -17, -6, 9, 15, -7, -2, 12, 15, -7, -6, -10, 11, 5, -9, -18, -21, -5, -15, -9, -16, 1, 1, 8, 11, -8, -8, 26, -7, -16, 3, 7, -21, 7, 22, 12, 21, 1, 21, 8, 24, 0, 6, 7, 33, 25, 2, 6, 12, 5, 0, -1, -19, -14, 32, -8, -19, 18, 2, 45, -10, 6, -10, -23, -1, -3, -2, 28, -2, -21, -19, 23, 15, 10, 20, -23, 5, 10, -2, -46, 12, 1, 14, -5, -3, 3, 14, 9, 0, -3, -2, 3, -2, -15, 3, 11, -6, -2, -8, 9, 3, -7, -14, 17, 6, -4, -7, -2, 8, -1, -7, -11, -16, 4, -21, 1, -21, 3, 6, 4, 17, -3, -9, 19, -7, -17, 8, 11, -16, 4, 12, 6, 2, 7, 11, 2, 27, -10, -4, 3, 28, 29, 3, -9, 7, 3, -10, 5, -5, -6, 24, 3, -12, 15, -2, 27, -13, 8, -9, -25, 5, -4, -17, 21, 3, -13, -25, 23, 5, 11, 6, -19, -2, 7, 1, -38, 7, 12, 0, -5, 1, -3, 16, 0, 0, -9, -4, 1, -1, -20, 5, 13, -12, -10, -12, 17, 8, -6, 0, 19, 7, -6, -12, -11, 25, 1, -8, -14, -16, 4, -23, -2, -21, -2, -3, 10, 22, -2, -17, 17, 6, -5, 7, 6, -28, 18, 20, 13, 5, 1, 9, -1, 37, -11, 0, -4, 22, 41, -8, -14, 11, 17, -10, 7, -13, 13, 17, -1, -15, 13, -5, 33, -10, 15, -7, -32, 0, -2, -13, 24, 1, -6, -22, 9, 7, 10, 11, -20, -14, 13, -10, -43, 10, 24, 16, -11, -10, 6, 20, 6, -6, -11, -14, 14, 7, 18, 5, 1, 2, -9, -14, -14, 8, 21, 9, 37, -19, -4, 18, 15, 8, 1, 14, 8, -7, -6, -16, -9, -10, -19, 10, -8, 31, -8, 50, 7, 17, 0, -14, 1, 2, 13, 2, 2, 7, 7, 2, 6, 2, 6, 11, -1, 2, 2, 15, 10, -3, -2, -12, 9, 6, -10, 6, -19, 6, 18, -2, 9, -8, 0, -1, -10, -1, 17, 5, 3, 8, -4, -6, 9, -4, -5, 8, -8, -17, -6, 36, 6, -11, -25, -17, 15, 9, -2, 30, -17, -15, 9, -8, 2, 8, 14, 1, 1, 9, -11, -10, -15, -7, 6, 4, 27, -29, -10, 13, 9, 8, 3, 8, 13, -1, -3, -12, -4, -12, -6, -2, -7, 26, -4, 34, -1, 6, 2, -12, 6, -1, 0, -2, 8, 11, 0, -4, -1, -2, 13, 7, 1, 2, -10, 21, 4, 8, 3, -20, 0, 10, -9, -2, -12, 9, -4, 5, 7, -14, 0, 1, 3, 6, 6, 1, 3, 11, -7, -4, 3, -11, -2, 8, -1, -4, -19, 31, 3, 2, -20, -20, 10, -1, 3, 6, -6, -7, 5, -11, 8, 20, 23, 2, 4, -5, -18, -6, -6, -9, 6, 0, 36, -37, -24, 5, 0, 12, 8, 14, 16, 8, -4, -16, -3, -5, -26, 11, 0, 15, -4, 56, -1, 0, -1, -27, 4, 1, -8, -9, 12, 8, -4, -6, 4, 2, 8, 2, 2, 6, 4, 27, 6, 7, 9, -11, 10, -2, -1, 4, -17, 16, -4, 11, 19, -8, -8, -15, 1, -10, 0, -2, 3, 24, -4, -7, -2, -12, -8, 3, -9, 2, -28, 31, 2, -2, -23, -17, 5, 16, -3, 13, -8, -3, -5, -4, 4, -1, 9, -33, -11, -3, 7, -1, -5, -42, -27, -1, -1, 7, 14, 6, 17, -7, 31, -16, 1, -19, -22, -2, 6, -22, -9, -15, -6, 22, 13, -9, -11, 0, 9, -3, 16, -4, 16, 0, -4, 20, 10, -20, 10, -34, -8, -16, -6, -11, -6, 7, 23, 3, -4, -4, 10, 8, 28, 4, -1, 16, 3, 14, 18, 38, 45, 0, -5, 11, 12, 2, -24, -20, -1, 17, -13, -16, 15, -5, 4, -23, 11, -13, 41, 10, 8, -16, -39, -2, 35, -5, 0, 2, -6, -2, -16, 10, 7, -20, -10, -9, 1, -2, -16, -15, -31, -8, 0, 7, 0, 5, 9, -15, 30, -19, 6, -23, -21, 14, 4, 0, 3, -6, 2, 16, 13, -13, 1, 3, 8, 4, 9, 2, 17, 9, 2, 7, 12, -9, 12, -16, 0, -15, -3, -1, -17, 1, 11, 9, -13, -6, 9, -1, 3, -3, -4, 8, -11, 15, 24, 23, 38, 1, -3, 14, 6, 5, -20, -5, -6, 13, -17, -3, 8, 0, -1, -11, 16, -14, 38, -3, 11, -21, -13, -9, 14, 6, -17, 3, 3, 2, -14, 11, 5, -23, -9, -13, 18, -7, -23, -34, -24, -15, -13, -4, 0, -10, -7, -24, 38, -18, 7, -21, -37, 7, 6, -13, -10, -6, -5, 21, 7, -8, 9, 15, 1, 8, 19, 0, 11, 12, -19, 1, 18, -14, 21, -27, 10, -3, 0, -2, -32, -2, 18, -6, -2, -19, -2, 1, 15, 1, -3, 25, -15, 20, 33, 28, 30, 7, -9, 21, 21, 20, -6, -10, -20, 18, -23, -4, 6, 14, -22, -7, 32, -11, 53, -5, 22, -24, -7, 3, 19, -17, 8, -3, 14, 3, -3, 9, 0, 8, 10, 5, 14, 14, 13, 1, 11, 3, 25, -6, 27, -1, -15, -19, -2, -25, 3, -38, -34, -23, 15, 12, -1, -8, -11, 11, 0, -10, 4, 15, 4, 0, -8, 18, -9, 19, 28, 21, 19, -8, -5, 27, -6, 9, 4, -1, 14, 8, 11, 7, -2, -7, -13, -6, -24, 9, -9, -16, -8, 1, 23, -7, 11, -14, -21, 1, -11, -12, 16, 3, -6, -9, 16, -23, 15, 2, -45, -24, 29, 20, -47, -30, 10, 0, 6, -4, -1, 2, 29, -3, -17, 12, 0, 11, 0, 8, -6, 9, 17, 4, 16, -3, -1, 9, 20, -5, 23, -3, -6, -22, 8, -20, 4, -19, -8, -8, 20, 15, 1, -1, -8, 4, -4, -3, 15, 7, -1, 2, -9, 9, -8, 19, 28, 3, 15, -4, -8, 27, 2, 8, -1, 5, 10, 13, 3, 3, -3, -7, -2, 3, -6, 7, -3, -15, -14, -8, 27, -12, 12, -7, -20, 3, -13, -16, 9, 8, -10, 3, 10, -15, 8, 3, -35, -24, 28, 26, -28, -14, 17, -9, 13, 2, -9, 1, 21, -3, -19, 10, 3, 13, -2, 10, -9, -1, 26, 9, 19, -9, 4, 12, 14, -12, 19, -16, -2, -12, 2, -19, 6, -45, -14, -7, 31, 16, 5, -2, -8, 8, -11, -2, 27, 1, -21, -2, -13, 12, 4, 8, 23, -1, 8, 1, 0, 25, -5, 4, 0, 13, 16, 7, -9, 19, 6, -13, -13, -8, -22, 3, -16, -19, -15, -2, 25, -10, 24, 4, -37, -12, -5, -20, 18, 3, -8, -5, 9, -28, 14, -4, -34, -20, 18, 31, -25, -26, 17, -2, 10, 5, -27, -2, 16, -7, -25, 7, 2, -7, -6, -18, -9, 2, 12, 27, -4, -9, -13, 4, 1, -15, -14, -7, 18, -7, 5, 23, 26, -1, -18, 9, 0, 17, 19, -6, -14, 12, 10, -10, 16, -18, 20, 0, 1, -6, -3, 14, -10, 5, 0, -30, 9, -19, -6, -14, -34, -4, -6, -12, -12, -9, 15, -27, 3, 18, 11, -19, 7, 30, 2, -4, -1, 15, 24, -3, -8, 12, 0, -9, -18, -1, -1, 19, 3, -2, -2, -22, -18, -11, 4, 23, -16, 1, 10, -1, -4, 10, 12, -17, -18, 11, -5, -1, 2, -2, 2, -16, -6, 0, 4, 23, -12, 7, -13, 15, 0, -17, -6, -4, 9, -17, 3, 25, 21, 1, -21, -3, 1, 11, 2, -14, -16, 7, -5, -3, 14, -6, 9, -6, 6, -18, 3, 0, -18, 11, -11, -18, 2, -21, 5, -7, -14, -2, -11, -8, 11, 0, 21, -3, -1, 20, 3, -14, 1, 31, -7, -11, 3, 12, 12, -6, -2, 9, 3, -14, -21, -13, -7, 18, 1, -6, 1, -10, -6, 2, -3, 10, 13, 4, 8, 9, -9, 8, 9, -18, -16, 2, -4, -8, -3, -2, 3, -13, -7, -2, -3, 35, -13, 12, -16, 3, 15, -28, -2, -15, 14, 3, 1, 27, 17, -11, -19, -12, 7, 15, 7, -31, -20, -11, 2, 1, 10, 1, 11, -7, 12, -18, 5, 4, -11, 19, -5, -29, 4, -15, 7, -6, -3, -6, -15, -16, 1, -1, 19, -5, -15, 24, 7, -15, 13, 30, -7, 1, 0, 23, 3, -10, 7, 8, -11, -10, -17, -15, -6, 11, 10, -10, 10, -13, -14, 4, -4, 18, 17, 7, 15, -8, 2, 19, 12, -25, -27, 5, 4, -1, 0, -1, 14, 32, 29, -12, 7, -19, 11, -12, 11, -6, -23, 15, 20, 4, 8, -10, -14, -23, -12, -21, -11, -8, 9, 18, -16, -27, 10, 18, 3, -22, 3, -34, 12, -10, 11, -9, 11, 14, -26, -7, -8, 7, 3, 2, 10, 10, 12, 2, 0, -6, 0, 1, -7, 1, 5, -4, -47, 3, 39, 15, 2, 9, 22, -39, 17, 5, 5, 5, 11, -17, -13, -9, -18, 19, -16, 23, 10, 1, 0, 17, -7, 12, 19, -16, 20, -16, 3, 17, 11, -21, 15, 17, 28, 2, -3, 4, 18, 29, 22, -12, -1, -26, 11, -14, 0, -2, -24, 13, 15, 6, 7, -5, -12, -22, -6, -10, -1, -11, 16, 10, -20, -6, 4, 4, -1, -17, 12, -13, 17, -15, 17, -5, 8, 14, -14, -17, 0, 11, -1, 8, 8, 10, 16, 3, -1, 0, 1, -9, -17, 17, 5, 2, -37, 1, 29, 6, 1, 4, 23, -17, 13, 5, -12, 3, 15, -10, -4, -11, -6, 15, -17, 16, 21, 3, 2, 10, -17, 5, 17, -2, 2, -21, -2, 21, 13, -10, 9, 18, 15, 3, 4, 6, 15, 26, 24, -13, -3, -38, 3, -16, -14, -4, -31, 12, 10, 1, -7, 11, 0, -24, 3, -9, -12, -22, 29, 7, -10, -7, 1, -1, -5, -26, 2, -13, 1, -3, 12, -8, 11, 16, -10, -27, 8, -1, -4, 8, 8, 0, 21, 10, -5, 1, 7, -8, -24, 7, 6, 10, -16, 0, 23, 12, -5, -4, 22, -19, 6, 2, -8, 13, 12, -13, -5, -20, -6, 13, -21, 17, 17, -1, -11, 9, -34, -5, 14, 9, -4, -14, 7, 21, 31, -8, -3, 28, 19, 6, -21, 1, -8, 3, -1, -8, -3, -3, -11, -22, -2, -7, 10, -1, 2, 21, 25, -36, 12, 1, -12, 12, -15, -1, 2, 12, -20, -17, 6, 35, 2, 0, 2, -1, -11, -14, 1, 0, 1, -16, -16, -15, -15, 13, -2, 7, 2, 17, 23, -3, 4, -3, 30, -16, -11, 20, 2, 9, 12, 2, 14, 3, 10, 6, 12, 11, -13, 3, -1, 7, 19, 8, 18, 19, -15, 10, -36, 32, -4, -1, 1, -39, -7, -12, 6, -10, 25, -18, -23, 5, 30, 4, 1, 20, -3, -1, -12, 5, -15, 6, 3, -9, 11, 2, -1, -13, -7, -4, 13, -10, -3, 13, 28, -37, 6, 0, -2, 17, -1, 4, -2, 10, -3, -16, 7, 34, 2, -8, 10, -3, -14, -22, -3, -5, 11, -9, -13, -11, -11, 6, 9, -4, -2, 7, 15, -6, 6, 8, 25, -6, -29, 12, -2, 20, 6, 6, 6, -6, 7, -3, 7, 0, -14, 17, 5, 6, 14, 13, 20, 10, -6, 10, -15, 20, 4, 0, -13, -27, -1, -13, 7, -2, 10, -18, -13, 2, 12, -3, -13, 26, -2, -5, -9, 10, -18, -6, 3, -10, -9, 12, -2, -19, -11, -10, 7, 1, -6, 5, 28, -41, 11, -3, 4, 21, 6, -2, -7, -4, -3, -12, 4, 48, 9, -3, 1, 18, -6, -21, 2, -3, 14, -15, -18, -10, -17, 3, 24, -3, 4, 12, 12, 3, -4, 8, 25, -11, -18, -2, -8, 6, 15, 1, -2, 8, -7, 4, 15, 1, -13, 20, 18, 4, 23, 10, 19, 4, -19, 17, -14, 24, 6, -1, -16, -22, 8, -28, 13, 2, 4, -27, -20, -6, 24, 5, -10, 31, -2, -3, 7, -1, -8, -25, -4, -5, -3, 46, -15, 0, 6, 6, -17, 0, -2, -9, 29, 28, -12, 10, 7, 2, 19, 7, 7, 8, -19, 8, 4, 17, 11, -22, 30, -7, -7, 7, 4, -12, -3, -3, -14, 24, 9, -6, 12, -10, 3, 3, 9, 23, 23, -11, 2, 9, 8, -25, -52, -3, 15, -15, -3, 6, 4, -2, -12, 8, -5, -4, -12, -5, 19, -14, -30, 19, 29, -20, 9, 10, -25, -5, 8, -13, -12, 37, -17, 2, 3, -8, -32, -3, -5, 7, 0, 19, 4, 0, 40, -7, -1, -1, -9, -7, -1, 40, -16, -5, -9, 16, -16, -5, -2, 2, 21, 7, -22, 13, 8, -9, 9, -1, 1, 4, -11, 0, 2, 32, 4, -13, 17, -6, -14, 3, -9, -9, -1, 3, -11, 12, -8, -11, 19, 3, -3, 8, 11, 14, 26, -8, 12, 7, 12, -10, -30, -3, 0, -13, -2, 10, 7, -7, -16, 2, -12, 3, -6, 4, 3, -9, -14, 12, 21, -8, 2, 5, -21, -3, 14, -5, -19, 23, 1, 1, 8, 0, -8, -19, -6, -5, -13, 15, 0, -8, 20, -3, -13, -18, -10, 11, -16, 58, -9, -10, -24, 27, -10, -4, -15, 17, 28, 12, -28, 8, 5, -6, 5, -5, -12, 1, -9, 5, -11, 19, 17, -16, 13, -9, 4, 6, -11, -14, 15, 3, -2, 19, -13, -24, 4, -4, -5, 13, 22, 23, 36, -24, 6, -7, 0, -10, -36, -4, 16, -12, 2, 14, 16, -1, -12, 11, 1, 2, -12, 4, -4, -10, -23, 4, 26, -25, 5, 2, -24, 8, 9, -15, -23, 34, -25, 1, 2, -7, -23, -9, -8, -10, -11, 3, 7, 4, 11, 30, -19, -20, -24, 18, -26, -16, -6, 17, 0, 8, -4, -6, -21, 1, -5, 7, 5, 2, 7, 17, 9, -20, 10, -20, -42, 1, -2, 9, 2, 5, 18, -14, -27, 15, 2, 28, -20, 3, -9, 9, -37, 11, -10, -6, -9, 9, 14, 22, 1, -4, -4, -1, 27, 16, -7, -21, 7, 14, 4, 15, -14, 2, 5, 10, 7, 15, -14, -18, -16, -14, 2, 9, -24, -7, 8, 8, 9, -15, -12, 8, 30, 4, -30, 6, 2, 13, 6, 2, -4, -35, -19, 10, 21, -21, 2, 15, -7, -7, -21, 13, -33, -13, -6, 27, 2, 7, 7, 6, -16, 6, 1, 14, 5, -1, 1, 13, 16, -8, 16, -14, -24, 5, -1, 7, -8, 1, 11, -4, -10, 9, 2, 22, -6, 1, -4, 2, -14, 1, 3, 3, -12, 9, 11, 6, -8, -5, -13, 9, 22, -2, 3, -2, 5, 9, 7, 16, -12, -3, 6, 14, 11, 0, -12, -11, -9, -11, 4, 7, -16, 5, 14, 5, 12, -7, -8, 19, 26, -2, -17, -8, 13, 2, -3, 12, 1, -13, -18, 6, 7, -17, 9, 26, -6, -22, -26, 28, -42, -4, -21, 27, 11, 24, 1, 4, -13, 21, 5, 12, 8, 12, 2, 0, 27, -16, 20, -17, -21, 10, 9, 5, -11, -7, 19, 10, -4, 4, 0, 19, -7, 7, -2, 5, -28, 3, 0, -3, -22, 15, -15, 1, -1, 18, -19, 7, 21, -12, 5, -7, 10, 15, 11, 13, -24, 4, 22, 12, 0, -1, -28, -11, -24, -10, 12, 15, -28, 4, 13, -1, 18, -8, -15, 13, 22, -10, -28, -7, 22, 7, 2, 0, 2, -10, -17, 14, -1, -26, 9, 3, 9, 0, -4, 17, 11, 17, 13, -15, 3, -2, 9, -15, -4, 0, -17, -15, 16, 9, -8, -16, 13, 29, 9, -24, 13, 6, -8, -31, 2, -28, -1, 17, 18, 11, 7, -4, -20, 21, 1, -13, 25, -13, 14, 24, -15, -9, -18, 32, -6, -8, -12, -12, 24, 9, -20, 14, 20, -26, -9, -1, -25, 0, 0, 29, 18, 11, 12, 1, -9, 21, -14, -25, 30, 6, 11, -44, -15, -8, 31, 12, -16, 8, 22, 5, -21, -21, -26, -30, 11, 9, 33, -28, 16, -23, 17, 3, 9, 4, -6, 25, 16, 16, 14, -8, 5, 3, 4, -10, -7, -3, -18, -8, 17, 13, -12, -26, 6, 29, 0, -18, -2, 11, -9, -4, -17, -12, -16, 11, -5, 3, 2, 4, -11, 12, 13, -4, 14, -8, 15, 17, 3, -16, -6, 26, -10, -3, 6, -19, 21, 6, -4, 7, 4, -15, -13, -11, -7, -10, -6, 14, 13, 14, -4, -9, -2, 9, -12, -20, 24, 14, 7, -18, -13, -5, 5, 9, -14, 7, 31, -2, -18, -18, -4, -34, 2, 14, 24, -14, 29, -22, 0, -20, 10, -1, -2, 34, 20, 16, 7, -13, -8, -11, 0, -18, -8, 11, -20, -8, 16, 2, -20, -43, 19, 34, 5, -11, 4, 4, -28, -18, -3, -21, -14, 3, 20, -4, 8, 3, -6, 4, 24, 2, 19, -15, 17, 19, 11, -13, -3, 19, -19, -11, 13, -12, 7, -1, -30, 3, -14, 1, -8, -5, -22, 1, 2, 25, 13, 19, 8, -5, 11, 12, -11, -23, 11, 12, 11, -29, 1, -1, 14, 25, -14, 12, 39, 9, -19, -12, 1, -31, 22, -2, 30, -31, 16, -15, -2, -11, 25, 18, -13, 0, 8, 32, -27, 7, -11, 29, -8, 8, -5, -25, -8, -34, 5, -13, 20, -20, -1, 1, -11, 0, 11, 7, -22, 2, 20, -13, 3, 8, 18, 3, -13, -11, -3, 2, -8, 12, 12, 5, -16, 2, 3, -10, -4, 20, 23, 8, 23, 0, -19, -10, -35, 6, -30, -8, 2, -16, 3, -2, -6, 5, 13, -13, 3, 3, -3, 2, -61, 19, 2, -1, 18, 7, -17, -3, -4, -14, -4, 33, 13, -2, -1, 28, -17, 5, -11, 0, 49, -3, 12, 29, 20, 1, 14, 0, -13, 0, 13, 28, -22, 8, 4, 32, -6, 8, -2, -14, -9, -13, 6, -10, 13, -25, -7, 1, 7, -5, 9, 13, -23, 4, 8, -9, 7, 9, 7, 4, -10, -10, -4, 9, -6, 15, 2, 10, -11, 7, 10, -1, -6, 23, 10, 6, 24, -1, -18, 4, -26, 12, -7, -6, -2, -26, 8, -5, 5, 6, 0, -3, 3, -3, -12, 1, -44, 21, -1, 9, 12, 14, -2, 8, 0, 6, 13, 29, 16, -4, -6, 21, -11, 6, -1, -5, 36, -3, -3, 15, 9, -14, 20, 13, -6, -11, 28, 47, -9, 11, 6, 60, -1, 5, 0, -14, -10, -24, 13, -15, 15, -40, -13, -2, 1, 14, 9, 5, -35, -7, 14, -11, 11, -8, 21, -3, -12, -7, 2, 4, 1, 20, 3, 26, -14, 4, 14, 13, -4, 18, 4, -6, 12, 7, -43, -2, -35, -5, 5, -19, -21, -25, -1, -9, 8, 4, 3, -2, -5, -1, -13, -7, -35, 25, -1, 13, 9, 4, 3, 17, -9, 10, 5, 28, 11, -13, -3, 18, -22, -3, -9, -22, 41, -9, -4, 3, 14, -17, 23, -14, 8, -11, -6, -25, -4, -7, -7, -17, 17, -5, 7, 12, 10, -5, 8, -4, -8, -26, 5, -14, -15, 7, -5, -7, 7, 36, 11, 24, -10, -9, 15, -16, 10, -5, 17, 6, 11, -1, 1, -5, 4, 0, 8, -12, 3, -19, -6, 3, -6, -8, -29, -12, 17, 0, 11, 21, 6, 7, 12, -3, 6, -1, 12, -16, -14, 10, 28, 13, 8, 9, 6, -1, -7, -15, -4, 5, 15, -11, 23, 8, 21, -5, -26, -26, 1, 16, 1, 34, 8, -13, 6, -13, -8, -11, 25, -13, 0, -12, -1, -23, -4, -12, -15, -13, 12, -3, -7, 9, 13, -11, 2, -1, -3, -17, 3, 2, -13, 0, 6, -10, 3, 37, 11, 16, -4, -8, 15, -17, 6, -8, 13, 3, 19, -9, 10, 2, 7, 2, 6, -14, 8, -7, -5, 8, -10, 2, -20, -6, -2, 4, 15, 10, 2, 14, 2, 12, 10, -6, 3, -22, -4, 15, 24, 17, 12, 1, 3, -2, -11, -8, 0, -2, 14, -6, 8, 9, 13, 1, -19, -26, 13, 13, -4, 12, -5, -7, 11, -8, 0, -9, 22, -9, -3, -23, 2, -17, 2, -26, -22, -15, 15, -11, -9, -10, -9, -12, 11, 3, 10, -13, 6, 5, -9, -4, -9, -11, 4, 22, 6, 37, -8, -10, 20, -21, 12, -9, 25, 1, 23, -9, 19, 5, 12, -1, 4, -7, -1, -8, -9, 12, 4, -11, -12, -3, 8, 9, 15, 8, 9, 28, 3, 17, 14, -4, 5, -18, -3, 16, 41, 20, 22, 6, -11, 4, -15, -27, 3, 4, 9, -9, 1, -2, 24, 0, -10, -27, 21, 17, -5, 21, 2, -14, 5, 8, -8, 9, 4, -14, -19, 33, -14, 3, -13, -11, 5, -2, -38, 20, 1, -2, 19, 4, -4, -3, -5, -6, 26, -11, -23, -8, -26, -18, 15, -18, -14, -8, 40, 4, -9, -5, 3, 28, -17, -1, -14, 14, 18, -8, 1, 1, 6, 3, 6, 9, 1, 13, 4, 4, 8, 2, -29, -11, 13, -12, 2, 12, -16, 3, 0, 11, -13, 22, 2, 1, 8, 20, 0, -14, 26, -17, 2, 17, -9, -17, 34, 18, 17, -21, -23, 11, 6, -5, -29, -12, 8, -3, -2, 12, -3, -22, 14, -5, 14, -6, -20, 30, -15, 15, -7, 3, 5, -4, -30, 17, -12, 2, 17, -1, -11, -6, -5, -29, 22, -7, -12, -7, -24, -9, 7, -11, -6, -7, 25, 1, 15, -6, -3, 27, -17, 1, -15, 20, 4, -8, 9, 6, 1, -7, 6, -9, -4, 17, 8, -2, 9, 1, -21, -12, -4, -5, -1, 23, -16, -1, -4, 6, 9, 21, -1, -2, 4, 14, 6, -8, 2, -4, -3, 9, -15, -19, 26, 15, 11, -21, -22, -4, 15, 8, 3, -15, -9, 11, -12, 10, -3, -12, 7, 25, 9, -11, -25, 33, -29, 25, -2, 3, -10, 4, -37, 10, -23, 3, 25, 0, -7, 8, 2, -11, 32, -12, 0, -13, -26, -15, 13, -34, -3, -12, 20, 26, 14, 11, -8, 35, -26, 12, -41, 20, -4, 2, 4, -2, 0, 4, 20, -4, -8, 8, -7, -6, -4, -2, -27, -12, 13, -2, 1, 27, -7, 9, -8, -7, 21, 24, -4, -1, 6, 14, -7, -2, 2, 2, -4, 27, -19, -19, 10, -6, 12, -16, -38, -3, 9, -4, -9, -13, 4, 12, -26, -7, 25, -12, 1, -8, 13, -4, 1, -8, 23, 15, 9, 8, -13, 9, 1, 10, -15, -14, -11, -2, -5, 10, 17, 20, 22, -18, 3, 15, 10, 24, 2, -13, 11, 7, -21, 3, 22, 9, -22, 2, 4, -7, -2, -13, -33, 16, -27, 1, 7, -11, -2, 9, -4, -4, -9, -1, -9, 29, -9, -1, -24, 4, 11, -24, -4, -9, -15, -2, -11, 19, -4, 9, -6, -10, -11, 14, 0, 21, -4, -14, 5, -8, 36, 1, 15, 15, 32, -22, -6, 1, 4, 8, -26, 1, 24, 13, 20, 13, -6, -5, 16, -6, 0, -2, 22, 13, 9, -5, 1, 15, -3, 10, -12, -9, -15, 12, -5, 3, 6, 25, 13, -1, 2, 21, 10, 12, 0, -14, 7, 13, -10, 5, 23, 1, -9, 4, 3, -12, -7, -4, -21, 9, -30, -11, 3, -2, -8, 4, -6, -4, 1, 5, -14, 30, -17, 2, -3, 7, 3, -15, -14, -12, -16, -16, -23, 1, 4, -2, -3, -8, -20, 11, -1, 19, 1, -5, 14, 3, 26, 15, 9, 21, 9, 7, 2, -9, -2, 7, -20, -5, 23, 9, 1, 12, -12, -10, 12, -2, -3, 0, 40, 12, 0, 8, -2, 24, -3, 19, -4, -8, -9, 7, -5, 7, 9, 28, 6, -2, 6, 26, 13, 17, -12, -14, 12, -3, -19, -14, 24, 2, -9, 1, 7, -6, -7, -1, -12, 17, -34, 0, 14, -3, -17, -18, -8, 0, 4, 12, -44, 19, -21, -1, -1, 11, -2, -8, 5, -13, -19, -6, -18, 0, 0, 5, -3, -3, -19, 21, 0, 12, 9, -3, 18, 1, 36, 17, 9, 22, 10, 9, -4, -4, -5, 2, -18, -10, 32, 22, 2, 16, -23, -10, -9, 15, 27, -12, 25, 12, 28, 10, 5, 2, 13, 9, 24, 3, -4, -5, -15, -18, -1, -1, -3, -4, 10, 30, -8, -7, 17, 15, -16, -15, 7, -13, 6, -8, -10, 3, 4, 4, 5, 4, 12, 12, -9, 28, -21, 24, 20, -2, 15, 1, 7, 0, 2, 17, 4, -4, -44, 12, 33, -29, -5, -15, 28, -20, -7, -17, -20, 4, 12, -8, 34, 37, 12, 12, -20, 2, 2, -2, -12, -8, 14, -7, -21, -21, 26, 2, 6, -3, -27, 4, 15, 32, -17, 11, -6, -1, 3, 7, 13, -9, 11, -12, 23, 2, 4, -1, 6, 5, 18, 2, 1, 2, -14, -13, -7, -5, -1, -8, 10, 23, -6, -2, 17, 22, -9, -9, 13, -10, -6, 4, -14, 0, -6, 1, -1, -5, 9, 10, -10, 20, 1, 15, 7, 6, 14, 8, -4, -2, -14, 14, -2, 7, -26, 6, 15, -28, 1, -9, 33, -13, -4, -8, -23, -4, 2, -11, 22, 25, 6, 15, -6, 6, 2, 1, -13, -5, -6, 13, -38, 5, 19, -13, -1, 0, -23, -11, 18, 34, -11, 21, -10, -3, -5, 4, 14, -7, 29, 2, 35, -5, -5, -1, 4, 0, 25, -8, 2, -4, -1, -13, 2, -9, 2, -9, 17, 24, 1, -14, 15, 36, -9, -17, 29, -11, -12, 4, -3, -12, 22, -3, 15, -7, 8, 11, -7, 39, 1, 18, 20, 7, 23, -2, -2, 8, -7, -8, -9, 0, -39, 8, 20, -34, -29, -6, 36, -15, 3, 3, -21, 5, 1, -17, 46, 15, 4, 10, -13, 2, -2, -13, -29, -9, -6, -10, -30, 8, 15, -13, 3, -1, -28, 3, 28, 46, -5, 32, -5, 17, 5, 16, 5, 6, 15, 5, 9, -5, 11, 27, 29, -3, 10, 1, -28, -3, -4, 26, -4, -13, 11, 22, 25, 16, 8, 32, -4, -10, -17, -8, 13, -16, 3, -15, -3, -5, -17, -3, -4, 4, -10, 3, 7, 12, -4, -6, -6, 16, 13, -26, -24, -34, 9, 15, -5, 1, -26, 0, -13, -22, -6, 1, -6, 9, 3, -14, -13, 7, -13, -21, 12, 15, 4, -5, -3, 11, -5, -38, 3, 25, -44, 5, 2, -1, 20, 3, 23, -10, -7, -26, 1, 14, 24, 2, -13, -1, 7, 11, 4, 9, 12, 13, 13, -1, 12, 12, 25, -6, 9, 7, -25, -6, -7, 22, -3, -9, 0, 15, 7, 16, 5, 21, -5, -19, -3, -10, -3, -1, -9, -7, 6, 13, -27, 3, -3, 5, -20, 3, 7, -1, -3, 0, 1, 15, -4, 1, -9, -17, 7, 23, -2, -4, -16, 2, -21, -14, -13, -6, 5, 11, -8, -22, -13, -10, -9, -6, 8, 13, 4, 1, -4, 6, -11, -23, 9, 18, -52, 6, -5, 10, 13, 12, 18, -3, -5, -15, 4, 8, 17, 8, -19, 4, 6, 14, 10, 13, 27, 22, 9, 21, 17, 15, 38, -5, 15, 21, -19, 3, -10, 21, -10, -17, -1, 6, 13, 33, 28, 23, -13, -20, 8, -10, -2, 12, -8, -10, 3, -1, -21, 4, 7, 7, -14, -7, 7, 19, -3, 3, -4, -1, -1, -1, -5, 3, 7, 15, -4, 13, -25, 7, -17, -4, -7, -15, 3, 4, -7, -22, -9, -8, -8, -5, 34, 25, 18, 6, 0, 16, -23, -27, 2, 21, -36, 2, -5, 6, -8, 13, 16, -9, -16, 0, 3, 16, 27, 8, -14, 9, -9, 12, 13, -19, -4, 1, 26, -12, 11, 12, 18, 2, -1, 14, -2, 15, -13, -4, -8, 9, -17, 0, 10, -26, -19, -8, 7, 20, 2, -23, -3, 0, -24, -8, -11, -2, -10, -24, -9, -8, -13, 18, 15, -10, -6, 9, 3, 15, 11, 14, 23, -26, 4, -16, 18, -4, -14, -23, -6, 25, 14, 2, 2, -17, -9, 4, -26, 10, -5, -33, 26, -1, -31, 8, -13, 14, -7, -4, -17, -9, -10, -2, -4, -20, 28, 29, -18, -5, 12, 5, -22, 42, 17, 5, -11, 2, -11, 18, 2, -11, -1, 7, 11, -3, 12, 1, 23, -8, 4, 9, -3, 10, -8, -4, -4, 0, -21, -6, 7, -16, -19, -2, 8, 9, 4, -23, 0, 2, -17, -3, -16, 2, -12, -21, -12, -2, -6, 8, 12, -5, 0, 18, 4, 17, 14, 19, 17, -15, 1, -16, 14, -5, -13, -16, -4, 6, 9, 3, -2, 1, -1, 5, -18, 9, -13, -23, 13, -8, -11, 4, -11, 1, 0, -4, -22, 0, -16, -5, -13, -6, 20, 37, -30, -12, 10, -3, -18, 26, 3, 11, -5, 3, -3, 10, 13, -10, 1, -1, 16, -5, 10, -8, 17, -18, 2, 9, 4, 13, -7, -4, 11, 5, -36, -10, 9, -25, -24, -1, 12, 9, 12, -28, -3, 1, -19, -8, -15, 6, -26, -21, -11, -7, -12, 3, 8, -12, 6, 11, -1, 21, 22, 13, 16, -15, -10, -16, 19, -1, -12, -18, -13, 11, 19, 9, -15, -3, -6, -6, -18, 16, -6, -12, 3, -14, -12, 2, -14, 9, -5, 0, -29, 5, -29, 14, -9, -8, 12, 38, -35, -9, 5, -2, -26, 43, -8, 20, -14, -9, 15, 1, 8, -9, 12, 10, -2, 4, 19, -13, -2, 9, -8, 35, 11, -19, -7, 5, 3, -17, 37, -17, 5, 24, 30, -6, -19, -17, 6, 32, 4, -13, 17, -7, -2, -18, 16, 26, 19, -16, -1, 4, -13, 35, 1, 11, 1, 3, -7, 8, -11, 0, -10, 2, -8, 10, 12, 1, 13, -13, 13, -20, 8, -9, 20, 3, 22, -1, 12, 25, -5, -26, -3, 32, -10, -7, 6, 12, 0, 14, -22, -6, 0, 25, -15, -23, -23, 21, 1, -3, 16, -31, -15, -14, 14, -6, 8, 3, 16, -17, -5, 2, -3, 3, -14, -7, -10, -3, -7, 19, 5, -15, -8, 0, -2, -3, 30, -5, 0, 15, 15, 4, -19, -16, 10, 21, -13, -11, 12, -3, 0, -9, 8, 15, 11, -8, 5, -4, -5, 10, 11, 1, -2, 15, -4, 5, -23, 10, -7, -3, -7, 4, 5, 5, 3, -3, 16, -23, 0, -21, 12, 16, 23, -10, 28, 27, 11, -27, 3, 19, -10, -3, 6, -5, -2, 11, -27, 15, -7, 19, -16, -28, -29, 22, -6, -9, 26, -25, -14, -11, 11, -1, -2, 7, 18, -14, -16, -9, -6, -2, 7, -15, -10, -1, -13, 18, -4, -28, 9, 16, 0, -12, 37, -4, 2, 8, 27, -2, -8, -10, 0, 28, -16, -4, 10, -14, -5, -18, 11, 6, 34, -15, 11, -17, -8, 23, 9, 11, 9, 9, -3, 4, -11, -2, 11, -1, -4, 7, 19, 1, 17, 2, 16, -25, 6, -4, -4, -4, 24, -17, 31, 34, 14, -21, -24, 19, -5, 3, 6, 5, 0, 4, -34, -3, 18, 20, -11, -20, -27, 34, 11, -5, 29, -14, -34, -4, 15}

#define TENSOR_CONV2D_3_FUSED_KERNEL_0_DEC_BITS {4}

#define TENSOR_CONV2D_3_FUSED_BIAS_0 {-51, -17, 12, 28, 37, 7, -33, 9, -12, 24, 19, 0, 67, 11, -5, -45, 7, -16, 26, -25, -26, 17, 16, 27, -24, -7, 42, -39, -45, -4, 43, -62}

#define TENSOR_CONV2D_3_FUSED_BIAS_0_DEC_BITS {3}

#define CONV2D_3_FUSED_BIAS_LSHIFT {4}

#define CONV2D_3_FUSED_OUTPUT_RSHIFT {6}

#define TENSOR_DENSE_KERNEL_0 {46, -55, -19, -47, -66, -17, 12, 5, 29, -66, 15, -61, 40, 7, 42, 29, -12, -29, -49, 19, 30, -27, -42, -36, -15, 1, 44, -21, 62, 12, -80, 6, 38, -25, 44, -7, 20, -37, 30, -40, 7, 35, 16, 55, -8, -21, 34, 27, -54, 19, -65, -36, 11, 31, -28, 2, 4, 2, -57, -29, 24, 32, -1, -72, 27, -14, -6, 17, -68, 2, -41, -10, -14, -47, -80, 15, -33, -20, -19, 51, -34, 25, 35, -13, -81, 19, -12, 10, 4, 39, 55, 21, -40, 12, 5, -100, -27, 41, 54, 52, 8, -56, -16, 44, -69, -34, -37, -9, -56, -34, 38, -36, -77, -36, -53, 18, -35, 43, -15, 49, -17, -14, -10, 14, 46, -46, 0, -53, -66, -42, -68, -71, 44, -12, -8, -2, -8, 16, 31, -16, -35, 26, 24, 16, 21, -72, 46, -69, -60, -58, 33, -34, -25, 42, -24, -43, -48, -23, -44, -80, -58, 0, 30, 6, 29, -4, -35, 58, 11, 66, 10, 22, 12, -30, -22, -35, 48, 8, 16, 2, 14, -53, -56, 28, -31, 15, -75, 4, 46, 63, -2, -11, 28, 20, -25, -78, -65, 29, 40, -14, 1, 31, 49, -26, -30, -60, -37, 3, -16, -6, 14, 29, -48, 48, 0, 37, -41, -24, 5, -5, 46, -46, -47, -11, 12, 23, -63, 32, 34, -37, -31, -15, -24, 29, 25, 25, -38, 6, 55, -26, 4, 3, 25, -48, -19, -23, 39, -22, 9, -93, -55, 0, 8, -46, -23, 68, 22, -38, -17, 5, 40, 7, 47, 22, -54, 7, -49, 27, -38, -60, -47, -67, -23, 43, -3, 37, 14, 18, 26, -10, 31, 41, -16, -45, -15, -29, 16, 12, 32, 7, -43, -53, -11, -48, -10, -17, -23, -24, 26, -35, -62, -17, -3, -9, -56, 10, 51, 37, -39, -35, -44, -36, -21, -34, 21, 24, -22, -52, 6, 47, -51, -4, -19, 8, -25, 22, -12, 36, 31, -1, -4, -26, -49, 17, 10, 18, -11, -56, 17, -9, 9, 18, -28, 45, -8, 28, -4, -14, -34, -9, -18, -46, -41, -12, -13, 14, -54, -20, -47, -7, -13, 36, -45, -53, 33, 2, 42, -23, 39, -42, 23, -16, 26, -4, -41, 23, 35, -48, -6, -22, 32, 11, 33, -59, 4, 8, 1, 22, 13, -19, -15, -6, -21, -33, 31, -21, -39, 47, -14, -13, 6, -24, 20, -25, -21, 22, -37, -35, -22, 30, 34, 11, -10, -52, 40, 2, -53, 28, 6, 19, -45, 20, -2, -55, 16, -9, -61, -65, -70, -34, -61, 43, 22, 13, 12, -41, 30, -37, 33, -7, -57, -14, 28, -4, -28, -11, -25, -17, 24, 44, -28, 49, -28, 19, 22, 1, -41, 28, -42, -15, 24, 34, -39, 13, 46, -37, -53, -46, -4, -17, 26, -78, 11, -91, -6, 40, 42, 9, -15, 25, -18, 27, -34, -25, 49, 37, 19, -47, 20, 35, -4, -6, -42, -3, 18, -3, -6, -64, 29, -38, 25, 18, -37, -5, -30, -26, -14, 29, 16, -4, -9, 20}

#define TENSOR_DENSE_KERNEL_0_DEC_BITS {5}

#define TENSOR_DENSE_BIAS_0 {4, 5, 6, 7, -1, 4, 7, 4, -2, -7, -2, 4, -13, 2, -4, -3}

#define TENSOR_DENSE_BIAS_0_DEC_BITS {5}

#define DENSE_BIAS_LSHIFT {0}

#define DENSE_OUTPUT_RSHIFT {-1}


/* output q format for each layer */
#define INPUT_OUTPUT_DEC 1
#define INPUT_OUTPUT_OFFSET 0
#define CONV2D_1_FUSED_OUTPUT_DEC 3
#define CONV2D_1_FUSED_OUTPUT_OFFSET 0
#define MAX_POOLING_1_OUTPUT_DEC 3
#define MAX_POOLING_1_OUTPUT_OFFSET 0
#define CONV2D_2_FUSED_OUTPUT_DEC 3
#define CONV2D_2_FUSED_OUTPUT_OFFSET 0
#define MAX_POOLING_2_OUTPUT_DEC 3
#define MAX_POOLING_2_OUTPUT_OFFSET 0
#define CONV2D_3_FUSED_OUTPUT_DEC 1
#define CONV2D_3_FUSED_OUTPUT_OFFSET 0
#define GLOBAL_AVERAGE_POOLING_OUTPUT_DEC 1
#define GLOBAL_AVERAGE_POOLING_OUTPUT_OFFSET 0
#define DENSE_OUTPUT_DEC 7
#define DENSE_OUTPUT_OFFSET 0

/* bias shift and output shift for none-weighted layer */

/* tensors and configurations for each layer */
static int8_t nnom_input_data[2376] = {0};

const nnom_shape_data_t tensor_input_dim[] = {198, 12, 1};
const nnom_qformat_param_t tensor_input_dec[] = {1};
const nnom_qformat_param_t tensor_input_offset[] = {0};
const nnom_tensor_t tensor_input = {
    .p_data = (void*)nnom_input_data,
    .dim = (nnom_shape_data_t*)tensor_input_dim,
    .q_dec = (nnom_qformat_param_t*)tensor_input_dec,
    .q_offset = (nnom_qformat_param_t*)tensor_input_offset,
    .qtype = NNOM_QTYPE_PER_TENSOR,
    .num_dim = 3,
    .bitwidth = 8
};

const nnom_io_config_t input_config = {
    .super = {.name = "input"},
    .tensor = (nnom_tensor_t*)&tensor_input
};
const int8_t tensor_conv2d_1_fused_kernel_0_data[] = TENSOR_CONV2D_1_FUSED_KERNEL_0;

const nnom_shape_data_t tensor_conv2d_1_fused_kernel_0_dim[] = {5, 5, 1, 16};
const nnom_qformat_param_t tensor_conv2d_1_fused_kernel_0_dec[] = TENSOR_CONV2D_1_FUSED_KERNEL_0_DEC_BITS;
const nnom_qformat_param_t tensor_conv2d_1_fused_kernel_0_offset[] = {0};
const nnom_tensor_t tensor_conv2d_1_fused_kernel_0 = {
    .p_data = (void*)tensor_conv2d_1_fused_kernel_0_data,
    .dim = (nnom_shape_data_t*)tensor_conv2d_1_fused_kernel_0_dim,
    .q_dec = (nnom_qformat_param_t*)tensor_conv2d_1_fused_kernel_0_dec,
    .q_offset = (nnom_qformat_param_t*)tensor_conv2d_1_fused_kernel_0_offset,
    .qtype = NNOM_QTYPE_PER_TENSOR,
    .num_dim = 4,
    .bitwidth = 8
};
const int8_t tensor_conv2d_1_fused_bias_0_data[] = TENSOR_CONV2D_1_FUSED_BIAS_0;

const nnom_shape_data_t tensor_conv2d_1_fused_bias_0_dim[] = {16};
const nnom_qformat_param_t tensor_conv2d_1_fused_bias_0_dec[] = TENSOR_CONV2D_1_FUSED_BIAS_0_DEC_BITS;
const nnom_qformat_param_t tensor_conv2d_1_fused_bias_0_offset[] = {0};
const nnom_tensor_t tensor_conv2d_1_fused_bias_0 = {
    .p_data = (void*)tensor_conv2d_1_fused_bias_0_data,
    .dim = (nnom_shape_data_t*)tensor_conv2d_1_fused_bias_0_dim,
    .q_dec = (nnom_qformat_param_t*)tensor_conv2d_1_fused_bias_0_dec,
    .q_offset = (nnom_qformat_param_t*)tensor_conv2d_1_fused_bias_0_offset,
    .qtype = NNOM_QTYPE_PER_TENSOR,
    .num_dim = 1,
    .bitwidth = 8
};

const nnom_qformat_param_t conv2d_1_fused_output_shift[] = CONV2D_1_FUSED_OUTPUT_RSHIFT;
const nnom_qformat_param_t conv2d_1_fused_bias_shift[] = CONV2D_1_FUSED_BIAS_LSHIFT;
const nnom_conv2d_config_t conv2d_1_fused_config = {
    .super = {.name = "conv2d_1_fused"},
    .qtype = NNOM_QTYPE_PER_TENSOR,
    .weight = (nnom_tensor_t*)&tensor_conv2d_1_fused_kernel_0,
    .bias = (nnom_tensor_t*)&tensor_conv2d_1_fused_bias_0,
    .output_shift = (nnom_qformat_param_t *)&conv2d_1_fused_output_shift, 
    .bias_shift = (nnom_qformat_param_t *)&conv2d_1_fused_bias_shift, 
    .filter_size = 16,
    .kernel_size = {5, 5},
    .stride_size = {1, 1},
    .padding_size = {0, 0},
    .dilation_size = {1, 1},
    .padding_type = PADDING_VALID
};

const nnom_pool_config_t max_pooling_1_config = {
    .super = {.name = "max_pooling_1"},
    .padding_type = PADDING_VALID,
    .output_shift = 0,
    .kernel_size = {2, 1},
    .stride_size = {2, 1},
    .num_dim = 2
};
const int8_t tensor_conv2d_2_fused_kernel_0_data[] = TENSOR_CONV2D_2_FUSED_KERNEL_0;

const nnom_shape_data_t tensor_conv2d_2_fused_kernel_0_dim[] = {3, 3, 16, 32};
const nnom_qformat_param_t tensor_conv2d_2_fused_kernel_0_dec[] = TENSOR_CONV2D_2_FUSED_KERNEL_0_DEC_BITS;
const nnom_qformat_param_t tensor_conv2d_2_fused_kernel_0_offset[] = {0};
const nnom_tensor_t tensor_conv2d_2_fused_kernel_0 = {
    .p_data = (void*)tensor_conv2d_2_fused_kernel_0_data,
    .dim = (nnom_shape_data_t*)tensor_conv2d_2_fused_kernel_0_dim,
    .q_dec = (nnom_qformat_param_t*)tensor_conv2d_2_fused_kernel_0_dec,
    .q_offset = (nnom_qformat_param_t*)tensor_conv2d_2_fused_kernel_0_offset,
    .qtype = NNOM_QTYPE_PER_TENSOR,
    .num_dim = 4,
    .bitwidth = 8
};
const int8_t tensor_conv2d_2_fused_bias_0_data[] = TENSOR_CONV2D_2_FUSED_BIAS_0;

const nnom_shape_data_t tensor_conv2d_2_fused_bias_0_dim[] = {32};
const nnom_qformat_param_t tensor_conv2d_2_fused_bias_0_dec[] = TENSOR_CONV2D_2_FUSED_BIAS_0_DEC_BITS;
const nnom_qformat_param_t tensor_conv2d_2_fused_bias_0_offset[] = {0};
const nnom_tensor_t tensor_conv2d_2_fused_bias_0 = {
    .p_data = (void*)tensor_conv2d_2_fused_bias_0_data,
    .dim = (nnom_shape_data_t*)tensor_conv2d_2_fused_bias_0_dim,
    .q_dec = (nnom_qformat_param_t*)tensor_conv2d_2_fused_bias_0_dec,
    .q_offset = (nnom_qformat_param_t*)tensor_conv2d_2_fused_bias_0_offset,
    .qtype = NNOM_QTYPE_PER_TENSOR,
    .num_dim = 1,
    .bitwidth = 8
};

const nnom_qformat_param_t conv2d_2_fused_output_shift[] = CONV2D_2_FUSED_OUTPUT_RSHIFT;
const nnom_qformat_param_t conv2d_2_fused_bias_shift[] = CONV2D_2_FUSED_BIAS_LSHIFT;
const nnom_conv2d_config_t conv2d_2_fused_config = {
    .super = {.name = "conv2d_2_fused"},
    .qtype = NNOM_QTYPE_PER_TENSOR,
    .weight = (nnom_tensor_t*)&tensor_conv2d_2_fused_kernel_0,
    .bias = (nnom_tensor_t*)&tensor_conv2d_2_fused_bias_0,
    .output_shift = (nnom_qformat_param_t *)&conv2d_2_fused_output_shift, 
    .bias_shift = (nnom_qformat_param_t *)&conv2d_2_fused_bias_shift, 
    .filter_size = 32,
    .kernel_size = {3, 3},
    .stride_size = {1, 1},
    .padding_size = {0, 0},
    .dilation_size = {1, 1},
    .padding_type = PADDING_VALID
};

const nnom_pool_config_t max_pooling_2_config = {
    .super = {.name = "max_pooling_2"},
    .padding_type = PADDING_VALID,
    .output_shift = 0,
    .kernel_size = {2, 1},
    .stride_size = {2, 1},
    .num_dim = 2
};
const int8_t tensor_conv2d_3_fused_kernel_0_data[] = TENSOR_CONV2D_3_FUSED_KERNEL_0;

const nnom_shape_data_t tensor_conv2d_3_fused_kernel_0_dim[] = {3, 3, 32, 32};
const nnom_qformat_param_t tensor_conv2d_3_fused_kernel_0_dec[] = TENSOR_CONV2D_3_FUSED_KERNEL_0_DEC_BITS;
const nnom_qformat_param_t tensor_conv2d_3_fused_kernel_0_offset[] = {0};
const nnom_tensor_t tensor_conv2d_3_fused_kernel_0 = {
    .p_data = (void*)tensor_conv2d_3_fused_kernel_0_data,
    .dim = (nnom_shape_data_t*)tensor_conv2d_3_fused_kernel_0_dim,
    .q_dec = (nnom_qformat_param_t*)tensor_conv2d_3_fused_kernel_0_dec,
    .q_offset = (nnom_qformat_param_t*)tensor_conv2d_3_fused_kernel_0_offset,
    .qtype = NNOM_QTYPE_PER_TENSOR,
    .num_dim = 4,
    .bitwidth = 8
};
const int8_t tensor_conv2d_3_fused_bias_0_data[] = TENSOR_CONV2D_3_FUSED_BIAS_0;

const nnom_shape_data_t tensor_conv2d_3_fused_bias_0_dim[] = {32};
const nnom_qformat_param_t tensor_conv2d_3_fused_bias_0_dec[] = TENSOR_CONV2D_3_FUSED_BIAS_0_DEC_BITS;
const nnom_qformat_param_t tensor_conv2d_3_fused_bias_0_offset[] = {0};
const nnom_tensor_t tensor_conv2d_3_fused_bias_0 = {
    .p_data = (void*)tensor_conv2d_3_fused_bias_0_data,
    .dim = (nnom_shape_data_t*)tensor_conv2d_3_fused_bias_0_dim,
    .q_dec = (nnom_qformat_param_t*)tensor_conv2d_3_fused_bias_0_dec,
    .q_offset = (nnom_qformat_param_t*)tensor_conv2d_3_fused_bias_0_offset,
    .qtype = NNOM_QTYPE_PER_TENSOR,
    .num_dim = 1,
    .bitwidth = 8
};

const nnom_qformat_param_t conv2d_3_fused_output_shift[] = CONV2D_3_FUSED_OUTPUT_RSHIFT;
const nnom_qformat_param_t conv2d_3_fused_bias_shift[] = CONV2D_3_FUSED_BIAS_LSHIFT;
const nnom_conv2d_config_t conv2d_3_fused_config = {
    .super = {.name = "conv2d_3_fused"},
    .qtype = NNOM_QTYPE_PER_TENSOR,
    .weight = (nnom_tensor_t*)&tensor_conv2d_3_fused_kernel_0,
    .bias = (nnom_tensor_t*)&tensor_conv2d_3_fused_bias_0,
    .output_shift = (nnom_qformat_param_t *)&conv2d_3_fused_output_shift, 
    .bias_shift = (nnom_qformat_param_t *)&conv2d_3_fused_bias_shift, 
    .filter_size = 32,
    .kernel_size = {3, 3},
    .stride_size = {1, 1},
    .padding_size = {0, 0},
    .dilation_size = {1, 1},
    .padding_type = PADDING_VALID
};

const nnom_global_pool_config_t global_average_pooling_config = {
    .super = {.name = "global_average_pooling"},
    .output_shift = 0,
};
const int8_t tensor_dense_kernel_0_data[] = TENSOR_DENSE_KERNEL_0;

const nnom_shape_data_t tensor_dense_kernel_0_dim[] = {32, 16};
const nnom_qformat_param_t tensor_dense_kernel_0_dec[] = TENSOR_DENSE_KERNEL_0_DEC_BITS;
const nnom_qformat_param_t tensor_dense_kernel_0_offset[] = {0};
const nnom_tensor_t tensor_dense_kernel_0 = {
    .p_data = (void*)tensor_dense_kernel_0_data,
    .dim = (nnom_shape_data_t*)tensor_dense_kernel_0_dim,
    .q_dec = (nnom_qformat_param_t*)tensor_dense_kernel_0_dec,
    .q_offset = (nnom_qformat_param_t*)tensor_dense_kernel_0_offset,
    .qtype = NNOM_QTYPE_PER_TENSOR,
    .num_dim = 2,
    .bitwidth = 8
};
const int8_t tensor_dense_bias_0_data[] = TENSOR_DENSE_BIAS_0;

const nnom_shape_data_t tensor_dense_bias_0_dim[] = {16};
const nnom_qformat_param_t tensor_dense_bias_0_dec[] = TENSOR_DENSE_BIAS_0_DEC_BITS;
const nnom_qformat_param_t tensor_dense_bias_0_offset[] = {0};
const nnom_tensor_t tensor_dense_bias_0 = {
    .p_data = (void*)tensor_dense_bias_0_data,
    .dim = (nnom_shape_data_t*)tensor_dense_bias_0_dim,
    .q_dec = (nnom_qformat_param_t*)tensor_dense_bias_0_dec,
    .q_offset = (nnom_qformat_param_t*)tensor_dense_bias_0_offset,
    .qtype = NNOM_QTYPE_PER_TENSOR,
    .num_dim = 1,
    .bitwidth = 8
};

const nnom_qformat_param_t dense_output_shift[] = DENSE_OUTPUT_RSHIFT;
const nnom_qformat_param_t dense_bias_shift[] = DENSE_BIAS_LSHIFT;
const nnom_dense_config_t dense_config = {
    .super = {.name = "dense"},
    .qtype = NNOM_QTYPE_PER_TENSOR,
    .weight = (nnom_tensor_t*)&tensor_dense_kernel_0,
    .bias = (nnom_tensor_t*)&tensor_dense_bias_0,
    .output_shift = (nnom_qformat_param_t *)&dense_output_shift,
    .bias_shift = (nnom_qformat_param_t *)&dense_bias_shift
};
static int8_t nnom_output_data[16] = {0};

const nnom_shape_data_t tensor_output0_dim[] = {16};
const nnom_qformat_param_t tensor_output0_dec[] = {DENSE_OUTPUT_DEC};
const nnom_qformat_param_t tensor_output0_offset[] = {0};
const nnom_tensor_t tensor_output0 = {
    .p_data = (void*)nnom_output_data,
    .dim = (nnom_shape_data_t*)tensor_output0_dim,
    .q_dec = (nnom_qformat_param_t*)tensor_output0_dec,
    .q_offset = (nnom_qformat_param_t*)tensor_output0_offset,
    .qtype = NNOM_QTYPE_PER_TENSOR,
    .num_dim = 1,
    .bitwidth = 8
};

const nnom_io_config_t output0_config = {
    .super = {.name = "output0"},
    .tensor = (nnom_tensor_t*)&tensor_output0
};
/* model version */
#define NNOM_MODEL_VERSION (10000*0 + 100*4 + 3)

/* nnom model */
static nnom_model_t* nnom_model_create(void)
{
	static nnom_model_t model;
	nnom_layer_t* layer[9];

	check_model_version(NNOM_MODEL_VERSION);
	new_model(&model);

	layer[0] = input_s(&input_config);
	layer[1] = model.hook(conv2d_s(&conv2d_1_fused_config), layer[0]);
	layer[2] = model.hook(maxpool_s(&max_pooling_1_config), layer[1]);
	layer[3] = model.hook(conv2d_s(&conv2d_2_fused_config), layer[2]);
	layer[4] = model.hook(maxpool_s(&max_pooling_2_config), layer[3]);
	layer[5] = model.hook(conv2d_s(&conv2d_3_fused_config), layer[4]);
	layer[6] = model.hook(global_avgpool_s(&global_average_pooling_config), layer[5]);
	layer[7] = model.hook(dense_s(&dense_config), layer[6]);
	layer[8] = model.hook(output_s(&output0_config), layer[7]);
	model_compile(&model, layer[0], layer[8]);
	return &model;
}
