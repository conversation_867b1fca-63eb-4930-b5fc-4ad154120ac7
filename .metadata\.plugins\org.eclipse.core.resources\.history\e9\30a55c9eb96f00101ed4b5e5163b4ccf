/*
 * 声纹识别/说话人识别 - 头文件
 * 
 * 作者: Augment Agent
 * 日期: 2025-08-02
 */

#ifndef SPEAKER_INFERENCE_H
#define SPEAKER_INFERENCE_H

#include <stdint.h>

#ifdef __cplusplus
extern "C" {
#endif

// 模型配置常量
#define SPEAKER_INPUT_HEIGHT    198     // 时间帧数
#define SPEAKER_INPUT_WIDTH     12      // MFCC维度
#define SPEAKER_INPUT_CHANNELS  1       // 单声道
#define SPEAKER_NUM_CLASSES     16      // 分类数量
#define SPEAKER_INPUT_SIZE      (SPEAKER_INPUT_HEIGHT * SPEAKER_INPUT_WIDTH * SPEAKER_INPUT_CHANNELS)

// 目标说话人ID
#define SPEAKER_XIAOXIN         0
#define SPEAKER_XIAOYUAN        1
#define SPEAKER_XIAOSI          2
#define SPEAKER_XIAOLAI         3

// 其他说话人ID范围
#define SPEAKER_OTHER_START     4
#define SPEAKER_OTHER_END       14
#define SPEAKER_UNKNOWN         15

// 识别结果结构体
typedef struct {
    int predicted_class;        // 预测的类别ID
    float confidence;           // 置信度 [0.0, 1.0]
    const char* speaker_name;   // 说话人姓名
    uint8_t is_target;          // 是否为目标说话人 (1: 是, 0: 否)
} speaker_result_t;

// 错误码定义
typedef enum {
    SPEAKER_OK = 0,             // 成功
    SPEAKER_ERROR_INIT = -1,    // 初始化失败
    SPEAKER_ERROR_INPUT = -2,   // 输入参数错误
    SPEAKER_ERROR_INFERENCE = -3 // 推理失败
} speaker_error_t;

/**
 * 初始化声纹识别模型
 * @return SPEAKER_OK: 成功, 其他: 错误码
 */
speaker_error_t speaker_model_init(void);

/**
 * 执行声纹识别推理
 * @param mfcc_features: 输入的MFCC特征 [198x12] (float格式)
 * @param result: 输出的识别结果
 * @return SPEAKER_OK: 成功, 其他: 错误码
 */
speaker_error_t speaker_inference(const float *mfcc_features, speaker_result_t *result);

/**
 * 检查是否为目标说话人
 * @param class_id: 类别ID
 * @return 1: 目标说话人, 0: 非目标说话人
 */
uint8_t speaker_is_target(int class_id);

/**
 * 获取说话人姓名
 * @param class_id: 类别ID
 * @return 说话人姓名字符串
 */
const char* speaker_get_name(int class_id);

/**
 * 测试声纹识别功能
 */
void speaker_test(void);

/**
 * 打印浮点数 (SDK兼容函数)
 * @param value: 要打印的浮点数
 */
void print_float(float value);

#ifdef __cplusplus
}
#endif

#endif // SPEAKER_INFERENCE_H
