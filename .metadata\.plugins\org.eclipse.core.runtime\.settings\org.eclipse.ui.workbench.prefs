//org.eclipse.ui.commands/state/org.eclipse.ui.navigator.resources.nested.changeProjectPresentation/org.eclipse.ui.commands.radioState=false
ColorsAndFontsPreferencePage.expandedCategories=Torg.eclipse.ui.workbenchMisc
ColorsAndFontsPreferencePage.selectedElement=Forg.eclipse.jface.textfont
ENABLED_DECORATORS=org.eclipse.ui.LinkedResourceDecorator\:true,org.eclipse.ui.SymlinkDecorator\:true,org.eclipse.ui.VirtualResourceDecorator\:true,org.eclipse.ui.ContentTypeDecorator\:true,org.eclipse.ui.ResourceFilterDecorator\:false,org.eclipse.mylyn.context.ui.decorator.interest\:true,org.eclipse.debug.ui.prototype.decorator\:true,org.eclipse.mylyn.tasks.ui.decorators.task\:true,org.eclipse.cdt.ui.indexedFiles\:false,org.eclipse.cdt.ui.translationUnit\:true,org.eclipse.cdt.managedbuilder.ui.excludedFile\:true,org.eclipse.cdt.managedbuilder.ui.includeFolder\:true,org.eclipse.cdt.internal.ui.CustomBuildSettingsDecorator\:true,org.eclipse.linuxtools.tmf.ui.trace_folder.decorator\:true,org.eclipse.linuxtools.tmf.ui.experiment_folder.decorator\:true,org.eclipse.linuxtools.tmf.ui.linked_trace.decorator\:true,org.eclipse.egit.ui.internal.decorators.GitLightweightDecorator\:true,org.eclipse.egit.ui.internal.repository.RepositoryTreeNodeDecorator\:true,org.eclipse.mylyn.team.ui.changeset.decorator\:true,
UIActivities.org.eclipse.cdt.debug.dsfgdbActivity=true
eclipse.preferences.version=1
org.eclipse.cdt.debug.ui.ModulesDetailPaneFont=1|Consolas|14.25|0|WINDOWS|1|-19|0|0|0|400|0|0|0|0|3|2|1|49|Consolas;
org.eclipse.cdt.internal.ui.compare.AsmMergeViewer=1|Consolas|14.25|0|WINDOWS|1|-19|0|0|0|400|0|0|0|0|3|2|1|49|Consolas;
org.eclipse.cdt.internal.ui.compare.CMergeViewer=1|Consolas|14.25|0|WINDOWS|1|-19|0|0|0|400|0|0|0|0|3|2|1|49|Consolas;
org.eclipse.cdt.make.internal.ui.compare.MakefileMergeViewer=1|Consolas|14.25|0|WINDOWS|1|-19|0|0|0|400|0|0|0|0|3|2|1|49|Consolas;
org.eclipse.cdt.ui.buildconsole.ConsoleFont=1|Consolas|14.25|0|WINDOWS|1|-19|0|0|0|400|0|0|0|0|3|2|1|49|Consolas;
org.eclipse.cdt.ui.editors.textfont=1|Consolas|14.25|0|WINDOWS|1|-19|0|0|0|400|0|0|0|0|3|2|1|49|Consolas;
org.eclipse.compare.contentmergeviewer.TextMergeViewer=1|Consolas|14.25|0|WINDOWS|1|-19|0|0|0|400|0|0|0|0|3|2|1|49|Consolas;
org.eclipse.debug.ui.DetailPaneFont=1|Consolas|14.25|0|WINDOWS|1|-19|0|0|0|400|0|0|0|0|3|2|1|49|Consolas;
org.eclipse.debug.ui.MemoryViewTableFont=1|Consolas|14.25|0|WINDOWS|1|-19|0|0|0|400|0|0|0|0|3|2|1|49|Consolas;
org.eclipse.debug.ui.consoleFont=1|Consolas|14.25|0|WINDOWS|1|-19|0|0|0|400|0|0|0|0|3|2|1|49|Consolas;
org.eclipse.egit.ui.CommitMessageEditorFont=1|Consolas|14.25|0|WINDOWS|1|-19|0|0|0|400|0|0|0|0|3|2|1|49|Consolas;
org.eclipse.egit.ui.CommitMessageFont=1|Consolas|14.25|0|WINDOWS|1|-19|0|0|0|400|0|0|0|0|3|2|1|49|Consolas;
org.eclipse.egit.ui.DiffHeadlineFont=1|Consolas|14.25|0|WINDOWS|1|-19|0|0|0|400|0|0|0|0|3|2|1|49|Consolas;
org.eclipse.jface.textfont=1|Consolas|14.25|0|WINDOWS|1|-19|0|0|0|400|0|0|0|0|3|2|1|49|Consolas;
org.eclipse.mylyn.wikitext.ui.presentation.textFont=1|Consolas|14.25|0|WINDOWS|1|-19|0|0|0|400|0|0|0|0|3|2|1|49|Consolas;
org.eclipse.tracecompass.tmf.ui.font.eventraw=1|Consolas|14.25|0|WINDOWS|1|-19|0|0|0|400|0|0|0|0|3|2|1|49|Consolas;
org.eclipse.ui.workbench.ACTIVE_NOFOCUS_TAB_BG_END=255,255,255
org.eclipse.ui.workbench.ACTIVE_NOFOCUS_TAB_BG_START=255,255,255
org.eclipse.ui.workbench.ACTIVE_NOFOCUS_TAB_TEXT_COLOR=16,16,16
org.eclipse.ui.workbench.ACTIVE_TAB_BG_END=255,255,255
org.eclipse.ui.workbench.ACTIVE_TAB_BG_START=255,255,255
org.eclipse.ui.workbench.INACTIVE_TAB_BG_START=242,242,242
org.eclipse.ui.workbench.texteditor.blockSelectionModeFont=1|Consolas|9.75|0|WINDOWS|1|-13|0|0|0|400|0|0|0|0|3|2|1|49|Consolas;
org.eclipse.wst.sse.ui.textfont=1|Consolas|14.25|0|WINDOWS|1|-19|0|0|0|400|0|0|0|0|3|2|1|49|Consolas;
terminal.views.view.font.definition=1|Consolas|14.25|0|WINDOWS|1|-19|0|0|0|400|0|0|0|0|3|2|1|49|Consolas;
