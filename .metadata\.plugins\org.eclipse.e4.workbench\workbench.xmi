<?xml version="1.0" encoding="ASCII"?>
<application:Application xmi:version="2.0" xmlns:xmi="http://www.omg.org/XMI" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:advanced="http://www.eclipse.org/ui/2010/UIModel/application/ui/advanced" xmlns:application="http://www.eclipse.org/ui/2010/UIModel/application" xmlns:basic="http://www.eclipse.org/ui/2010/UIModel/application/ui/basic" xmlns:menu="http://www.eclipse.org/ui/2010/UIModel/application/ui/menu" xmi:id="_rjq9YHHlEfCAZJ6UDgwwcw" elementId="org.eclipse.e4.legacy.ide.application" contributorURI="platform:/plugin/org.eclipse.platform" selectedElement="_rjq9YXHlEfCAZJ6UDgwwcw" bindingContexts="_rjrnj3HlEfCAZJ6UDgwwcw">
  <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;workbench>&#xD;&#xA;&lt;mruList>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;speaker_inference.c&quot; tooltip=&quot;qemu/galaxy_sdk/speaker_inference.c&quot;>&#xD;&#xA;&lt;persistable path=&quot;/qemu/galaxy_sdk/speaker_inference.c&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;nnom.c&quot; tooltip=&quot;qemu/galaxy_sdk/modules/external/nnom/src/core/nnom.c&quot;>&#xD;&#xA;&lt;persistable path=&quot;/qemu/galaxy_sdk/modules/external/nnom/src/core/nnom.c&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;nnom_output.c&quot; tooltip=&quot;qemu/galaxy_sdk/modules/external/nnom/src/layers/nnom_output.c&quot;>&#xD;&#xA;&lt;persistable path=&quot;/qemu/galaxy_sdk/modules/external/nnom/src/layers/nnom_output.c&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;nnom_tensor.c&quot; tooltip=&quot;qemu/galaxy_sdk/modules/external/nnom/src/core/nnom_tensor.c&quot;>&#xD;&#xA;&lt;persistable path=&quot;/qemu/galaxy_sdk/modules/external/nnom/src/core/nnom_tensor.c&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;nnom_local.c&quot; tooltip=&quot;qemu/galaxy_sdk/modules/external/nnom/src/backends/nnom_local.c&quot;>&#xD;&#xA;&lt;persistable path=&quot;/qemu/galaxy_sdk/modules/external/nnom/src/backends/nnom_local.c&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;nnom_softmax.c&quot; tooltip=&quot;qemu/galaxy_sdk/modules/external/nnom/src/layers/nnom_softmax.c&quot;>&#xD;&#xA;&lt;persistable path=&quot;/qemu/galaxy_sdk/modules/external/nnom/src/layers/nnom_softmax.c&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;nnom_dense.c&quot; tooltip=&quot;qemu/galaxy_sdk/modules/external/nnom/src/layers/nnom_dense.c&quot;>&#xD;&#xA;&lt;persistable path=&quot;/qemu/galaxy_sdk/modules/external/nnom/src/layers/nnom_dense.c&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;nnom_flatten.c&quot; tooltip=&quot;qemu/galaxy_sdk/modules/external/nnom/src/layers/nnom_flatten.c&quot;>&#xD;&#xA;&lt;persistable path=&quot;/qemu/galaxy_sdk/modules/external/nnom/src/layers/nnom_flatten.c&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;nnom_avgpool.c&quot; tooltip=&quot;qemu/galaxy_sdk/modules/external/nnom/src/layers/nnom_avgpool.c&quot;>&#xD;&#xA;&lt;persistable path=&quot;/qemu/galaxy_sdk/modules/external/nnom/src/layers/nnom_avgpool.c&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;nnom_activation.c&quot; tooltip=&quot;qemu/galaxy_sdk/modules/external/nnom/src/layers/nnom_activation.c&quot;>&#xD;&#xA;&lt;persistable path=&quot;/qemu/galaxy_sdk/modules/external/nnom/src/layers/nnom_activation.c&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;nnom_conv2d.c&quot; tooltip=&quot;qemu/galaxy_sdk/modules/external/nnom/src/layers/nnom_conv2d.c&quot;>&#xD;&#xA;&lt;persistable path=&quot;/qemu/galaxy_sdk/modules/external/nnom/src/layers/nnom_conv2d.c&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;nnom_maxpool.c&quot; tooltip=&quot;qemu/galaxy_sdk/modules/external/nnom/src/layers/nnom_maxpool.c&quot;>&#xD;&#xA;&lt;persistable path=&quot;/qemu/galaxy_sdk/modules/external/nnom/src/layers/nnom_maxpool.c&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;nnom_input.c&quot; tooltip=&quot;qemu/galaxy_sdk/modules/external/nnom/src/layers/nnom_input.c&quot;>&#xD;&#xA;&lt;persistable path=&quot;/qemu/galaxy_sdk/modules/external/nnom/src/layers/nnom_input.c&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;main.c&quot; tooltip=&quot;qemu/galaxy_sdk/main.c&quot;>&#xD;&#xA;&lt;persistable path=&quot;/qemu/galaxy_sdk/main.c&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;nnom.h&quot; tooltip=&quot;qemu/galaxy_sdk/modules/external/nnom/inc/nnom.h&quot;>&#xD;&#xA;&lt;persistable path=&quot;/qemu/galaxy_sdk/modules/external/nnom/inc/nnom.h&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;pdm_audio_02_data.h&quot; tooltip=&quot;qemu/galaxy_sdk/pdm_audio_02_data.h&quot;>&#xD;&#xA;&lt;persistable path=&quot;/qemu/galaxy_sdk/pdm_audio_02_data.h&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;pdm_audio_02_data.c&quot; tooltip=&quot;qemu/galaxy_sdk/pdm_audio_02_data.c&quot;>&#xD;&#xA;&lt;persistable path=&quot;/qemu/galaxy_sdk/pdm_audio_02_data.c&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;speaker_weights.h&quot; tooltip=&quot;qemu/galaxy_sdk/speaker_weights.h&quot;>&#xD;&#xA;&lt;persistable path=&quot;/qemu/galaxy_sdk/speaker_weights.h&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;real_mfcc_data.h&quot; tooltip=&quot;qemu/galaxy_sdk/real_mfcc_data.h&quot;>&#xD;&#xA;&lt;persistable path=&quot;/qemu/galaxy_sdk/real_mfcc_data.h&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;speaker_inference.h&quot; tooltip=&quot;qemu/galaxy_sdk/speaker_inference.h&quot;>&#xD;&#xA;&lt;persistable path=&quot;/qemu/galaxy_sdk/speaker_inference.h&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;speaker_weights1.h&quot; tooltip=&quot;qemu/galaxy_sdk/speaker_weights1.h&quot;>&#xD;&#xA;&lt;persistable path=&quot;/qemu/galaxy_sdk/speaker_weights1.h&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;nnom_port.h&quot; tooltip=&quot;qemu/galaxy_sdk/modules/external/nnom/port/nnom_port.h&quot;>&#xD;&#xA;&lt;persistable path=&quot;/qemu/galaxy_sdk/modules/external/nnom/port/nnom_port.h&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;nnom_conv2d.h&quot; tooltip=&quot;qemu/galaxy_sdk/modules/external/nnom/inc/layers/nnom_conv2d.h&quot;>&#xD;&#xA;&lt;persistable path=&quot;/qemu/galaxy_sdk/modules/external/nnom/inc/layers/nnom_conv2d.h&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.cdt.ui.ExternalEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;_stdint.h&quot; tooltip=&quot;D:\NucleiStudio\NucleiStudio\toolchain\gcc\riscv64-unknown-elf\include\sys\_stdint.h&quot;>&#xD;&#xA;&lt;persistable path=&quot;D:\NucleiStudio\NucleiStudio\toolchain\gcc\riscv64-unknown-elf\include\sys\_stdint.h&quot; project=&quot;qemu&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.cdt.ui.ExternalEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;_default_types.h&quot; tooltip=&quot;D:\NucleiStudio\NucleiStudio\toolchain\gcc\riscv64-unknown-elf\include\machine\_default_types.h&quot;>&#xD;&#xA;&lt;persistable path=&quot;D:\NucleiStudio\NucleiStudio\toolchain\gcc\riscv64-unknown-elf\include\machine\_default_types.h&quot; project=&quot;qemu&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.cdt.ui.ExternalEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;stddef.h&quot; tooltip=&quot;D:\NucleiStudio\NucleiStudio\toolchain\gcc\lib\gcc\riscv64-unknown-elf\13.1.1\include\stddef.h&quot;>&#xD;&#xA;&lt;persistable path=&quot;D:\NucleiStudio\NucleiStudio\toolchain\gcc\lib\gcc\riscv64-unknown-elf\13.1.1\include\stddef.h&quot; project=&quot;qemu&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;nnom_local_q15.c&quot; tooltip=&quot;qemu/galaxy_sdk/modules/external/nnom/src/backends/nnom_local_q15.c&quot;>&#xD;&#xA;&lt;persistable path=&quot;/qemu/galaxy_sdk/modules/external/nnom/src/backends/nnom_local_q15.c&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;nnom_utils.c&quot; tooltip=&quot;qemu/galaxy_sdk/modules/external/nnom/src/core/nnom_utils.c&quot;>&#xD;&#xA;&lt;persistable path=&quot;/qemu/galaxy_sdk/modules/external/nnom/src/core/nnom_utils.c&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;/mruList>&#xD;&#xA;&lt;/workbench>"/>
  <tags>activeSchemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
  <children xsi:type="basic:TrimmedWindow" xmi:id="_rjq9YXHlEfCAZJ6UDgwwcw" elementId="IDEWindow" contributorURI="platform:/plugin/org.eclipse.platform" selectedElement="_rjq9YnHlEfCAZJ6UDgwwcw" label="%trimmedwindow.label.eclipseSDK" x="2246" y="94" width="1024" height="768">
    <persistedState key="coolBarVisible" value="true"/>
    <persistedState key="perspectiveBarVisible" value="true"/>
    <persistedState key="isRestored" value="true"/>
    <persistedState key="show_in_time" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;show_in_time/>"/>
    <persistedState key="workingSets" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;workingSets/>"/>
    <persistedState key="aggregateWorkingSetId" value="Aggregate for window 1754150091289"/>
    <tags>topLevel</tags>
    <tags>shellMaximized</tags>
    <children xsi:type="basic:PartSashContainer" xmi:id="_rjq9YnHlEfCAZJ6UDgwwcw" selectedElement="_rjq9Y3HlEfCAZJ6UDgwwcw" horizontal="true">
      <children xsi:type="advanced:PerspectiveStack" xmi:id="_rjq9Y3HlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.ide.perspectivestack" containerData="7500" selectedElement="_rjq9eXHlEfCAZJ6UDgwwcw">
        <children xsi:type="advanced:Perspective" xmi:id="_rjq9ZHHlEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.CPerspective" selectedElement="_rjq9ZXHlEfCAZJ6UDgwwcw" label="C/C++" iconURI="platform:/plugin/org.eclipse.cdt.ui/icons/view16/c_pers.gif">
          <persistedState key="persp.hiddenItems" value="persp.hideToolbarSC:print,persp.hideToolbarSC:org.eclipse.ui.edit.undo,persp.hideToolbarSC:org.eclipse.ui.edit.redo,persp.hideToolbarSC:org.eclipse.ui.edit.text.toggleShowSelectedElementOnly,persp.hideToolbarSC:org.eclipse.debug.ui.commands.RunToLine,"/>
          <tags>persp.actionSet:org.eclipse.embedcdt.debug.gdbjtag.restart.ui.actionset</tags>
          <tags>persp.actionSet:org.eclipse.mylyn.doc.actionSet</tags>
          <tags>persp.actionSet:org.eclipse.mylyn.tasks.ui.navigation</tags>
          <tags>persp.actionSet:org.eclipse.ui.cheatsheets.actionSet</tags>
          <tags>persp.actionSet:org.eclipse.search.searchActionSet</tags>
          <tags>persp.actionSet:org.eclipse.text.quicksearch.actionSet</tags>
          <tags>persp.actionSet:org.eclipse.ui.edit.text.actionSet.annotationNavigation</tags>
          <tags>persp.actionSet:org.eclipse.ui.edit.text.actionSet.navigation</tags>
          <tags>persp.actionSet:org.eclipse.ui.edit.text.actionSet.convertLineDelimitersTo</tags>
          <tags>persp.actionSet:org.eclipse.ui.externaltools.ExternalToolsSet</tags>
          <tags>persp.actionSet:org.eclipse.ui.actionSet.keyBindings</tags>
          <tags>persp.actionSet:org.eclipse.ui.actionSet.openFiles</tags>
          <tags>persp.actionSet:org.eclipse.cdt.ui.SearchActionSet</tags>
          <tags>persp.actionSet:org.eclipse.cdt.ui.CElementCreationActionSet</tags>
          <tags>persp.actionSet:org.eclipse.ui.NavigateActionSet</tags>
          <tags>persp.viewSC:org.eclipse.ui.console.ConsoleView</tags>
          <tags>persp.viewSC:org.eclipse.search.ui.views.SearchView</tags>
          <tags>persp.viewSC:org.eclipse.ui.views.ContentOutline</tags>
          <tags>persp.viewSC:org.eclipse.ui.views.ProblemView</tags>
          <tags>persp.viewSC:org.eclipse.cdt.ui.CView</tags>
          <tags>persp.viewSC:org.eclipse.ui.views.PropertySheet</tags>
          <tags>persp.viewSC:org.eclipse.ui.views.TaskList</tags>
          <tags>persp.newWizSC:org.eclipse.cdt.ui.wizards.ConvertToMakeWizard</tags>
          <tags>persp.newWizSC:org.eclipse.cdt.ui.wizards.NewMakeFromExisting</tags>
          <tags>persp.newWizSC:org.eclipse.cdt.ui.wizard.project</tags>
          <tags>persp.newWizSC:myplugintest.wizards.SampleNewWizard</tags>
          <tags>persp.newWizSC:myplugintest.wizards.PackageWizard</tags>
          <tags>persp.newWizSC:org.eclipse.cdt.ui.wizards.NewSourceFolderCreationWizard</tags>
          <tags>persp.newWizSC:org.eclipse.cdt.ui.wizards.NewFolderCreationWizard</tags>
          <tags>persp.newWizSC:org.eclipse.cdt.ui.wizards.NewSourceFileCreationWizard</tags>
          <tags>persp.newWizSC:org.eclipse.cdt.ui.wizards.NewHeaderFileCreationWizard</tags>
          <tags>persp.newWizSC:org.eclipse.cdt.ui.wizards.NewFileCreationWizard</tags>
          <tags>persp.newWizSC:org.eclipse.cdt.ui.wizards.NewClassCreationWizard</tags>
          <tags>persp.actionSet:org.eclipse.debug.ui.profileActionSet</tags>
          <tags>persp.perspSC:org.eclipse.debug.ui.DebugPerspective</tags>
          <tags>persp.perspSC:org.eclipse.team.ui.TeamSynchronizingPerspective</tags>
          <tags>persp.actionSet:org.eclipse.debug.ui.launchActionSet</tags>
          <tags>persp.actionSet:org.eclipse.cdt.ui.buildConfigActionSet</tags>
          <tags>persp.actionSet:org.eclipse.cdt.ui.NavigationActionSet</tags>
          <tags>persp.actionSet:org.eclipse.cdt.ui.OpenActionSet</tags>
          <tags>persp.actionSet:org.eclipse.cdt.ui.CodingActionSet</tags>
          <tags>persp.actionSet:org.eclipse.ui.edit.text.actionSet.presentation</tags>
          <tags>persp.showIn:org.eclipse.cdt.ui.includeBrowser</tags>
          <tags>persp.showIn:org.eclipse.cdt.ui.CView</tags>
          <tags>persp.showIn:org.eclipse.ui.navigator.ProjectExplorer</tags>
          <tags>persp.viewSC:org.eclipse.ui.navigator.ProjectExplorer</tags>
          <tags>persp.viewSC:org.eclipse.cdt.ui.includeBrowser</tags>
          <tags>persp.viewSC:org.eclipse.mylyn.tasks.ui.views.tasks</tags>
          <tags>persp.newWizSC:org.eclipse.mylyn.tasks.ui.wizards.new.repository.task</tags>
          <tags>persp.showIn:org.eclipse.cdt.codan.internal.ui.views.ProblemDetails</tags>
          <tags>persp.viewSC:org.eclipse.cdt.codan.internal.ui.views.ProblemDetails</tags>
          <tags>persp.actionSet:org.eclipse.debug.ui.breakpointActionSet</tags>
          <tags>persp.viewSC:org.eclipse.cdt.make.ui.views.MakeView</tags>
          <tags>persp.actionSet:org.eclipse.cdt.make.ui.makeTargetActionSet</tags>
          <tags>persp.viewSC:org.eclipse.tm.terminal.view.ui.TerminalsView</tags>
          <tags>persp.showIn:org.eclipse.tm.terminal.view.ui.TerminalsView</tags>
          <tags>persp.viewSC:org.eclipse.embedcdt.internal.managedbuild.packs.ui.views.DocsView</tags>
          <children xsi:type="basic:PartSashContainer" xmi:id="_rjq9ZXHlEfCAZJ6UDgwwcw" selectedElement="_rjq9anHlEfCAZJ6UDgwwcw" horizontal="true">
            <children xsi:type="basic:PartStack" xmi:id="_rjq9ZnHlEfCAZJ6UDgwwcw" elementId="topLeft" containerData="1417" selectedElement="_rjq9Z3HlEfCAZJ6UDgwwcw">
              <children xsi:type="advanced:Placeholder" xmi:id="_rjq9Z3HlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.navigator.ProjectExplorer" ref="_rjq_43HlEfCAZJ6UDgwwcw" closeable="true">
                <tags>View</tags>
                <tags>categoryTag:General</tags>
              </children>
              <children xsi:type="advanced:Placeholder" xmi:id="_rjq9aHHlEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.CView" toBeRendered="false" ref="_rjq__3HlEfCAZJ6UDgwwcw" closeable="true">
                <tags>View</tags>
                <tags>categoryTag:C/C++</tags>
              </children>
              <children xsi:type="advanced:Placeholder" xmi:id="_rjq9aXHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.views.BookmarkView" toBeRendered="false" ref="_rjrAAHHlEfCAZJ6UDgwwcw" closeable="true">
                <tags>View</tags>
                <tags>categoryTag:General</tags>
              </children>
            </children>
            <children xsi:type="basic:PartSashContainer" xmi:id="_rjq9anHlEfCAZJ6UDgwwcw" containerData="8583" selectedElement="_rjq9c3HlEfCAZJ6UDgwwcw">
              <children xsi:type="basic:PartSashContainer" xmi:id="_rjq9a3HlEfCAZJ6UDgwwcw" containerData="6809" selectedElement="_rjq9bHHlEfCAZJ6UDgwwcw" horizontal="true">
                <children xsi:type="advanced:Placeholder" xmi:id="_rjq9bHHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.editorss" containerData="7500" ref="_rjq_qXHlEfCAZJ6UDgwwcw"/>
                <children xsi:type="basic:PartStack" xmi:id="_rjq9bXHlEfCAZJ6UDgwwcw" elementId="topRight" containerData="2500" selectedElement="_rjq9bnHlEfCAZJ6UDgwwcw">
                  <children xsi:type="advanced:Placeholder" xmi:id="_rjq9bnHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.views.ContentOutline" ref="_rjrANXHlEfCAZJ6UDgwwcw" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:General</tags>
                  </children>
                  <children xsi:type="advanced:Placeholder" xmi:id="_rjq9b3HlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.views.minimap.MinimapView" toBeRendered="false" ref="_rjrAOHHlEfCAZJ6UDgwwcw" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:General</tags>
                  </children>
                  <children xsi:type="advanced:Placeholder" xmi:id="_rjq9cHHlEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.tasks.ui.views.tasks" ref="_rjrAOXHlEfCAZJ6UDgwwcw" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:Mylyn</tags>
                  </children>
                  <children xsi:type="advanced:Placeholder" xmi:id="_rjq9cXHlEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.make.ui.views.MakeView" ref="_rjrAOnHlEfCAZJ6UDgwwcw" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:Make</tags>
                  </children>
                  <children xsi:type="advanced:Placeholder" xmi:id="_rjq9cnHlEfCAZJ6UDgwwcw" elementId="org.eclipse.embedcdt.internal.managedbuild.packs.ui.views.DocsView" ref="_rjrAPHHlEfCAZJ6UDgwwcw" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:CMSIS Packs</tags>
                  </children>
                </children>
              </children>
              <children xsi:type="basic:PartStack" xmi:id="_rjq9c3HlEfCAZJ6UDgwwcw" elementId="bottom" containerData="3191" selectedElement="_rjq9dnHlEfCAZJ6UDgwwcw">
                <children xsi:type="advanced:Placeholder" xmi:id="_rjq9dHHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.views.ProblemView" ref="_rjrAAXHlEfCAZJ6UDgwwcw" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:General</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_rjq9dXHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.views.TaskList" ref="_rjrACXHlEfCAZJ6UDgwwcw" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:General</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_rjq9dnHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.console.ConsoleView" ref="_rjrACnHlEfCAZJ6UDgwwcw" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:General</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_rjq9d3HlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.views.PropertySheet" ref="_rjrANHHlEfCAZJ6UDgwwcw" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:General</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_rjq9eHHlEfCAZJ6UDgwwcw" elementId="org.eclipse.tm.terminal.view.ui.TerminalsView" toBeRendered="false" ref="_rjrAO3HlEfCAZJ6UDgwwcw" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:Terminal</tags>
                </children>
              </children>
            </children>
          </children>
        </children>
        <children xsi:type="advanced:Perspective" xmi:id="_rjq9eXHlEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.DebugPerspective" selectedElement="_rjq9enHlEfCAZJ6UDgwwcw" label="Debug" iconURI="platform:/plugin/org.eclipse.debug.ui/$nl$/icons/full/eview16/debug_persp.png">
          <persistedState key="persp.hiddenItems" value="persp.hideToolbarSC:print,persp.hideToolbarSC:org.eclipse.ui.edit.undo,persp.hideToolbarSC:org.eclipse.ui.edit.redo,persp.hideToolbarSC:org.eclipse.ui.edit.text.toggleShowSelectedElementOnly,persp.hideToolbarSC:org.eclipse.debug.ui.commands.RunToLine,"/>
          <tags>persp.actionSet:org.eclipse.embedcdt.debug.gdbjtag.restart.ui.actionset</tags>
          <tags>persp.actionSet:org.eclipse.mylyn.doc.actionSet</tags>
          <tags>persp.actionSet:org.eclipse.mylyn.tasks.ui.navigation</tags>
          <tags>persp.actionSet:org.eclipse.ui.cheatsheets.actionSet</tags>
          <tags>persp.actionSet:org.eclipse.search.searchActionSet</tags>
          <tags>persp.actionSet:org.eclipse.text.quicksearch.actionSet</tags>
          <tags>persp.actionSet:org.eclipse.ui.edit.text.actionSet.annotationNavigation</tags>
          <tags>persp.actionSet:org.eclipse.ui.edit.text.actionSet.navigation</tags>
          <tags>persp.actionSet:org.eclipse.ui.edit.text.actionSet.convertLineDelimitersTo</tags>
          <tags>persp.actionSet:org.eclipse.ui.externaltools.ExternalToolsSet</tags>
          <tags>persp.actionSet:org.eclipse.ui.actionSet.keyBindings</tags>
          <tags>persp.actionSet:org.eclipse.ui.actionSet.openFiles</tags>
          <tags>persp.viewSC:org.eclipse.ui.views.ProgressView</tags>
          <tags>persp.viewSC:org.eclipse.ui.texteditor.TemplatesView</tags>
          <tags>persp.actionSet:org.eclipse.debug.ui.launchActionSet</tags>
          <tags>persp.actionSet:org.eclipse.debug.ui.debugActionSet</tags>
          <tags>persp.actionSet:org.eclipse.ui.NavigateActionSet</tags>
          <tags>persp.viewSC:org.eclipse.debug.ui.DebugView</tags>
          <tags>persp.viewSC:org.eclipse.debug.ui.VariableView</tags>
          <tags>persp.viewSC:org.eclipse.debug.ui.BreakpointView</tags>
          <tags>persp.viewSC:org.eclipse.debug.ui.ExpressionView</tags>
          <tags>persp.viewSC:org.eclipse.ui.views.ContentOutline</tags>
          <tags>persp.viewSC:org.eclipse.ui.console.ConsoleView</tags>
          <tags>persp.viewSC:org.eclipse.ui.views.ProblemView</tags>
          <tags>persp.viewSC:org.eclipse.ui.navigator.ProjectExplorer</tags>
          <tags>persp.viewSC:org.eclipse.cdt.visualizer.view</tags>
          <tags>persp.viewSC:org.eclipse.pde.runtime.LogView</tags>
          <tags>persp.actionSet:org.eclipse.debug.ui.breakpointActionSet</tags>
          <tags>persp.showIn:org.eclipse.ui.navigator.ProjectExplorer</tags>
          <tags>persp.perspSC:org.eclipse.cdt.ui.CPerspective</tags>
          <tags>persp.perspSC:org.eclipse.wst.xml.ui.perspective</tags>
          <tags>persp.showIn:org.eclipse.egit.ui.RepositoriesView</tags>
          <tags>persp.viewSC:org.eclipse.cdt.debug.ui.SignalsView</tags>
          <tags>persp.viewSC:org.eclipse.debug.ui.RegisterView</tags>
          <tags>persp.viewSC:org.eclipse.debug.ui.ModuleView</tags>
          <tags>persp.viewSC:org.eclipse.debug.ui.MemoryView</tags>
          <tags>persp.viewSC:org.eclipse.cdt.debug.ui.executablesView</tags>
          <tags>persp.actionSet:org.eclipse.cdt.debug.ui.debugActionSet</tags>
          <tags>persp.viewSC:org.eclipse.cdt.dsf.debug.ui.disassembly.view</tags>
          <tags>persp.viewSC:org.eclipse.cdt.debug.ui.memory.memorybrowser.MemoryBrowser</tags>
          <tags>persp.viewSC:org.eclipse.tm.terminal.view.ui.TerminalsView</tags>
          <tags>persp.showIn:org.eclipse.tm.terminal.view.ui.TerminalsView</tags>
          <tags>persp.viewSC:org.eclipse.cdt.dsf.gdb.ui.tracecontrol.view</tags>
          <tags>persp.viewSC:org.eclipse.cdt.debug.ui.debuggerConsoleView</tags>
          <tags>persp.viewSC:org.eclipse.cdt.dsf.gdb.ui.debugsources.view</tags>
          <tags>persp.viewSC:org.eclipse.embedcdt.internal.debug.gdbjtag.ui.views.PeripheralsView</tags>
          <tags>persp.editorOnboardingImageUri:platform:/plugin/org.eclipse.debug.ui/icons/full/onboarding_debug_persp.png</tags>
          <tags>persp.editorOnboardingText:Go hunt your bugs here.</tags>
          <tags>persp.editorOnboardingCommand:Find Actions$$$Ctrl+3</tags>
          <tags>persp.editorOnboardingCommand:Step Into$$$F5</tags>
          <tags>persp.editorOnboardingCommand:Step Over$$$F6</tags>
          <tags>persp.editorOnboardingCommand:Step Return$$$F7</tags>
          <tags>persp.editorOnboardingCommand:Resume$$$F8</tags>
          <children xsi:type="basic:PartSashContainer" xmi:id="_rjq9enHlEfCAZJ6UDgwwcw" selectedElement="_rjq9gXHlEfCAZJ6UDgwwcw" horizontal="true">
            <children xsi:type="basic:PartSashContainer" xmi:id="_rjq9e3HlEfCAZJ6UDgwwcw" containerData="1453" selectedElement="_rjq9fHHlEfCAZJ6UDgwwcw" horizontal="true">
              <children xsi:type="basic:PartStack" xmi:id="_rjq9fHHlEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.internal.ui.NavigatorFolderView" containerData="5000" selectedElement="_rjq9fnHlEfCAZJ6UDgwwcw">
                <tags>org.eclipse.e4.primaryNavigationStack</tags>
                <children xsi:type="advanced:Placeholder" xmi:id="_rjq9fXHlEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.DebugView" ref="_rjrAPXHlEfCAZJ6UDgwwcw" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:Debug</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_rjq9fnHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.navigator.ProjectExplorer" ref="_rjq_43HlEfCAZJ6UDgwwcw" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:General</tags>
                </children>
              </children>
              <children xsi:type="basic:PartStack" xmi:id="_rjq9f3HlEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.dsf.gdb.ui.tracecontrol.viewMStack" toBeRendered="false" containerData="5000">
                <children xsi:type="advanced:Placeholder" xmi:id="_rjq9gHHlEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.dsf.gdb.ui.tracecontrol.view" toBeRendered="false" ref="_rjrk9HHlEfCAZJ6UDgwwcw" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:Debug</tags>
                </children>
              </children>
            </children>
            <children xsi:type="basic:PartSashContainer" xmi:id="_rjq9gXHlEfCAZJ6UDgwwcw" containerData="8547" selectedElement="_rjq9gnHlEfCAZJ6UDgwwcw">
              <children xsi:type="basic:PartSashContainer" xmi:id="_rjq9gnHlEfCAZJ6UDgwwcw" containerData="6383" selectedElement="_rjq9g3HlEfCAZJ6UDgwwcw" horizontal="true">
                <children xsi:type="advanced:Placeholder" xmi:id="_rjq9g3HlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.editorss" containerData="6500" ref="_rjq_qXHlEfCAZJ6UDgwwcw"/>
                <children xsi:type="basic:PartStack" xmi:id="_rjq9hHHlEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.internal.ui.OutlineFolderView" containerData="3500" selectedElement="_rjq9hXHlEfCAZJ6UDgwwcw">
                  <tags>org.eclipse.e4.secondaryNavigationStack</tags>
                  <tags>noFocus</tags>
                  <children xsi:type="advanced:Placeholder" xmi:id="_rjq9hXHlEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.VariableView" ref="_rjrkk3HlEfCAZJ6UDgwwcw" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:Debug</tags>
                  </children>
                  <children xsi:type="advanced:Placeholder" xmi:id="_rjq9hnHlEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.BreakpointView" ref="_rjrks3HlEfCAZJ6UDgwwcw" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:Debug</tags>
                  </children>
                  <children xsi:type="advanced:Placeholder" xmi:id="_rjq9h3HlEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.ExpressionView" ref="_rjrkzHHlEfCAZJ6UDgwwcw" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:Debug</tags>
                  </children>
                  <children xsi:type="advanced:Placeholder" xmi:id="_rjq9iHHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.views.ContentOutline" toBeRendered="false" ref="_rjrANXHlEfCAZJ6UDgwwcw" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:General</tags>
                  </children>
                  <children xsi:type="advanced:Placeholder" xmi:id="_rjq9iXHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.views.PropertySheet" toBeRendered="false" ref="_rjrANHHlEfCAZJ6UDgwwcw" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:General</tags>
                  </children>
                  <children xsi:type="advanced:Placeholder" xmi:id="_rjq9inHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.views.minimap.MinimapView" toBeRendered="false" ref="_rjrAOHHlEfCAZJ6UDgwwcw" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:General</tags>
                  </children>
                  <children xsi:type="advanced:Placeholder" xmi:id="_rjq9i3HlEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.visualizer.view" ref="_rjrk53HlEfCAZJ6UDgwwcw" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:Debug</tags>
                  </children>
                  <children xsi:type="advanced:Placeholder" xmi:id="_rjq9jHHlEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.debug.ui.SignalsView" toBeRendered="false" ref="_rjrk6XHlEfCAZJ6UDgwwcw" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:Debug</tags>
                  </children>
                  <children xsi:type="advanced:Placeholder" xmi:id="_rjq9jXHlEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.ModuleView" toBeRendered="false" ref="_rjrk6nHlEfCAZJ6UDgwwcw" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:Debug</tags>
                  </children>
                  <children xsi:type="advanced:Placeholder" xmi:id="_rjq9jnHlEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.dsf.debug.ui.disassembly.view" ref="_rjrk7nHlEfCAZJ6UDgwwcw" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:Debug</tags>
                  </children>
                  <children xsi:type="advanced:Placeholder" xmi:id="_rjq9j3HlEfCAZJ6UDgwwcw" elementId="org.eclipse.embedcdt.internal.debug.gdbjtag.ui.views.PeripheralsView" ref="_rjrk_3HlEfCAZJ6UDgwwcw" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:Debug</tags>
                  </children>
                </children>
              </children>
              <children xsi:type="basic:PartStack" xmi:id="_rjq9kHHlEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.internal.ui.ToolsFolderView" containerData="3617" selectedElement="_rjq9kXHlEfCAZJ6UDgwwcw">
                <tags>Debug</tags>
                <tags>General</tags>
                <tags>noFocus</tags>
                <children xsi:type="advanced:Placeholder" xmi:id="_rjq9kXHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.console.ConsoleView" ref="_rjrACnHlEfCAZJ6UDgwwcw" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:General</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_rjq9knHlEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.RegisterView" toBeRendered="false" ref="_rjrkj3HlEfCAZJ6UDgwwcw" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:Debug</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_rjq9k3HlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.views.BookmarkView" toBeRendered="false" ref="_rjrAAHHlEfCAZJ6UDgwwcw" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:General</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_rjq9lHHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.views.ProgressView" toBeRendered="false" ref="_rjrkknHlEfCAZJ6UDgwwcw" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:General</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_rjq9lXHlEfCAZJ6UDgwwcw" elementId="org.eclipse.pde.runtime.LogView" toBeRendered="false" ref="_rjrk6HHlEfCAZJ6UDgwwcw" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:General</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_rjq9lnHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.views.ProblemView" ref="_rjrAAXHlEfCAZJ6UDgwwcw" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:General</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_rjq9l3HlEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.debug.ui.executablesView" ref="_rjrk63HlEfCAZJ6UDgwwcw" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:Debug</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_rjq9mHHlEfCAZJ6UDgwwcw" elementId="org.eclipse.tm.terminal.view.ui.TerminalsView" toBeRendered="false" ref="_rjrAO3HlEfCAZJ6UDgwwcw" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:Terminal</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_rjq9mXHlEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.debug.ui.debuggerConsoleView" ref="_rjrk9XHlEfCAZJ6UDgwwcw" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:Debug</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_rjq9mnHlEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.dsf.gdb.ui.debugsources.view" toBeRendered="false" ref="_rjrk_nHlEfCAZJ6UDgwwcw" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:Debug</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_rjq9m3HlEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.MemoryView" ref="_rjrlEHHlEfCAZJ6UDgwwcw" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:Debug</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_rjq9nHHlEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.VariableView:PIN_CLONE_VIEW_1" toBeRendered="false" ref="_rjrlJXHlEfCAZJ6UDgwwcw" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:Debug</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_rjq9nXHlEfCAZJ6UDgwwcw" elementId="org.eclipse.search.ui.views.SearchView" ref="_rjrlKHHlEfCAZJ6UDgwwcw" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:General</tags>
                </children>
              </children>
            </children>
          </children>
          <windows xsi:type="basic:TrimmedWindow" xmi:id="_rjq9nnHlEfCAZJ6UDgwwcw" toBeRendered="false" x="1805" y="140" width="1648" height="361">
            <tags>shellMaximized</tags>
            <children xsi:type="basic:PartStack" xmi:id="_rjq9n3HlEfCAZJ6UDgwwcw" elementId="PartStack@523685f6" toBeRendered="false">
              <children xsi:type="advanced:Placeholder" xmi:id="_rjq9oHHlEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.debug.ui.memory.memorybrowser.MemoryBrowser" toBeRendered="false" ref="_rjrk8XHlEfCAZJ6UDgwwcw" closeable="true">
                <tags>View</tags>
                <tags>categoryTag:Debug</tags>
              </children>
            </children>
          </windows>
        </children>
      </children>
      <children xsi:type="basic:PartStack" xmi:id="_rjq9oXHlEfCAZJ6UDgwwcw" elementId="stickyFolderRight" toBeRendered="false" containerData="2500">
        <children xsi:type="advanced:Placeholder" xmi:id="_rjq9onHlEfCAZJ6UDgwwcw" elementId="org.eclipse.help.ui.HelpView" toBeRendered="false" ref="_rjq_pHHlEfCAZJ6UDgwwcw" closeable="true">
          <tags>View</tags>
          <tags>categoryTag:Help</tags>
        </children>
        <children xsi:type="advanced:Placeholder" xmi:id="_rjq9o3HlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.internal.introview" toBeRendered="false" ref="_rjq_pXHlEfCAZJ6UDgwwcw" closeable="true">
          <tags>View</tags>
          <tags>categoryTag:General</tags>
        </children>
        <children xsi:type="advanced:Placeholder" xmi:id="_rjq9pHHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.cheatsheets.views.CheatSheetView" toBeRendered="false" ref="_rjq_qHHlEfCAZJ6UDgwwcw" closeable="true">
          <tags>View</tags>
          <tags>categoryTag:Help</tags>
        </children>
      </children>
    </children>
    <sharedElements xsi:type="basic:Part" xmi:id="_rjq_pHHlEfCAZJ6UDgwwcw" elementId="org.eclipse.help.ui.HelpView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Help" iconURI="platform:/plugin/org.eclipse.help.ui/icons/view16/help_view.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.help.ui.internal.views.HelpView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.help.ui"/>
      <tags>View</tags>
      <tags>categoryTag:Help</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_rjq_pXHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.internal.introview" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Welcome" iconURI="platform:/plugin/org.eclipse.ui/icons/full/eview16/defaultview_misc.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.ViewIntroAdapterPart"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view>&#xD;&#xA;&lt;presentation currentPage=&quot;qroot&quot; restore=&quot;true&quot;/>&#xD;&#xA;&lt;standbyPart/>&#xD;&#xA;&lt;/view>"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
      <menus xmi:id="_rjq_pnHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.internal.introview">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_rjq_p3HlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.internal.introview" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_rjq_qHHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.cheatsheets.views.CheatSheetView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Cheat Sheets" iconURI="platform:/plugin/org.eclipse.ui.cheatsheets/icons/view16/cheatsheet_view.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.cheatsheets.views.CheatSheetView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.cheatsheets"/>
      <tags>View</tags>
      <tags>categoryTag:Help</tags>
    </sharedElements>
    <sharedElements xsi:type="advanced:Area" xmi:id="_rjq_qXHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.editorss" selectedElement="_rjq_qnHlEfCAZJ6UDgwwcw">
      <children xsi:type="basic:PartStack" xmi:id="_rjq_qnHlEfCAZJ6UDgwwcw" elementId="org.eclipse.e4.primaryDataStack" selectedElement="_rjq_r3HlEfCAZJ6UDgwwcw">
        <tags>org.eclipse.e4.primaryDataStack</tags>
        <tags>EditorStack</tags>
        <tags>active</tags>
        <tags>noFocus</tags>
        <children xsi:type="basic:Part" xmi:id="_rjq_q3HlEfCAZJ6UDgwwcw" elementId="org.eclipse.e4.ui.compatibility.editor" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor" label="main.c" iconURI="platform:/plugin/org.eclipse.cdt.ui/icons/obj16/c_file_obj.gif" closeable="true">
          <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;editor id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;main.c&quot; partName=&quot;main.c&quot; title=&quot;main.c&quot; tooltip=&quot;qemu/galaxy_sdk/main.c&quot;>&#xD;&#xA;&lt;input factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; path=&quot;/qemu/galaxy_sdk/main.c&quot;/>&#xD;&#xA;&lt;editorState selectionHorizontalPixel=&quot;0&quot; selectionLength=&quot;0&quot; selectionOffset=&quot;3300&quot; selectionTopPixel=&quot;2090&quot;/>&#xD;&#xA;&lt;/editor>"/>
          <tags>Editor</tags>
          <tags>removeOnHide</tags>
          <tags>org.eclipse.cdt.ui.editor.CEditor</tags>
        </children>
        <children xsi:type="basic:Part" xmi:id="_rjq_r3HlEfCAZJ6UDgwwcw" elementId="org.eclipse.e4.ui.compatibility.editor" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor" label="speaker_inference.c" iconURI="platform:/plugin/org.eclipse.cdt.ui/icons/obj16/c_file_obj.gif" closeable="true">
          <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;editor id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;speaker_inference.c&quot; partName=&quot;speaker_inference.c&quot; title=&quot;speaker_inference.c&quot; tooltip=&quot;qemu/galaxy_sdk/speaker_inference.c&quot;>&#xD;&#xA;&lt;input factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; path=&quot;/qemu/galaxy_sdk/speaker_inference.c&quot;/>&#xD;&#xA;&lt;editorState selectionHorizontalPixel=&quot;0&quot; selectionLength=&quot;0&quot; selectionOffset=&quot;4305&quot; selectionTopPixel=&quot;0&quot;/>&#xD;&#xA;&lt;/editor>"/>
          <tags>Editor</tags>
          <tags>removeOnHide</tags>
          <tags>org.eclipse.cdt.ui.editor.CEditor</tags>
          <tags>active</tags>
        </children>
        <children xsi:type="basic:Part" xmi:id="_rjq_s3HlEfCAZJ6UDgwwcw" elementId="org.eclipse.e4.ui.compatibility.editor" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor" label="nnom.c" iconURI="platform:/plugin/org.eclipse.cdt.ui/icons/obj16/c_file_obj.gif" closeable="true">
          <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;editor id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;nnom.c&quot; partName=&quot;nnom.c&quot; title=&quot;nnom.c&quot; tooltip=&quot;qemu/galaxy_sdk/modules/external/nnom/src/core/nnom.c&quot;>&#xD;&#xA;&lt;input factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; path=&quot;/qemu/galaxy_sdk/modules/external/nnom/src/core/nnom.c&quot;/>&#xD;&#xA;&lt;editorState selectionHorizontalPixel=&quot;0&quot; selectionLength=&quot;0&quot; selectionOffset=&quot;28295&quot; selectionTopPixel=&quot;22836&quot;/>&#xD;&#xA;&lt;/editor>"/>
          <tags>Editor</tags>
          <tags>removeOnHide</tags>
          <tags>org.eclipse.cdt.ui.editor.CEditor</tags>
        </children>
        <children xsi:type="basic:Part" xmi:id="_rjq_t3HlEfCAZJ6UDgwwcw" elementId="org.eclipse.e4.ui.compatibility.editor" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor" label="nnom_input.c" iconURI="platform:/plugin/org.eclipse.cdt.ui/icons/obj16/c_file_obj.gif" closeable="true">
          <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;editor id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;nnom_input.c&quot; partName=&quot;nnom_input.c&quot; title=&quot;nnom_input.c&quot; tooltip=&quot;qemu/galaxy_sdk/modules/external/nnom/src/layers/nnom_input.c&quot;>&#xD;&#xA;&lt;input factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; path=&quot;/qemu/galaxy_sdk/modules/external/nnom/src/layers/nnom_input.c&quot;/>&#xD;&#xA;&lt;editorState selectionHorizontalPixel=&quot;0&quot; selectionLength=&quot;0&quot; selectionOffset=&quot;4507&quot; selectionTopPixel=&quot;2662&quot;/>&#xD;&#xA;&lt;/editor>"/>
          <tags>Editor</tags>
          <tags>removeOnHide</tags>
          <tags>org.eclipse.cdt.ui.editor.CEditor</tags>
        </children>
        <children xsi:type="basic:Part" xmi:id="_rjq_u3HlEfCAZJ6UDgwwcw" elementId="org.eclipse.e4.ui.compatibility.editor" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor" label="nnom_tensor.c" iconURI="platform:/plugin/org.eclipse.cdt.ui/icons/obj16/c_file_obj.gif" closeable="true">
          <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;editor id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;nnom_tensor.c&quot; partName=&quot;nnom_tensor.c&quot; title=&quot;nnom_tensor.c&quot; tooltip=&quot;qemu/galaxy_sdk/modules/external/nnom/src/core/nnom_tensor.c&quot;>&#xD;&#xA;&lt;input factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; path=&quot;/qemu/galaxy_sdk/modules/external/nnom/src/core/nnom_tensor.c&quot;/>&#xD;&#xA;&lt;editorState selectionHorizontalPixel=&quot;0&quot; selectionLength=&quot;0&quot; selectionOffset=&quot;573&quot; selectionTopPixel=&quot;330&quot;/>&#xD;&#xA;&lt;/editor>"/>
          <tags>Editor</tags>
          <tags>removeOnHide</tags>
          <tags>org.eclipse.cdt.ui.editor.CEditor</tags>
        </children>
        <children xsi:type="basic:Part" xmi:id="_rjq_v3HlEfCAZJ6UDgwwcw" elementId="org.eclipse.e4.ui.compatibility.editor" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor" label="nnom_conv2d.c" iconURI="platform:/plugin/org.eclipse.cdt.ui/icons/obj16/c_file_obj.gif" closeable="true">
          <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;editor id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;nnom_conv2d.c&quot; partName=&quot;nnom_conv2d.c&quot; title=&quot;nnom_conv2d.c&quot; tooltip=&quot;qemu/galaxy_sdk/modules/external/nnom/src/layers/nnom_conv2d.c&quot;>&#xD;&#xA;&lt;input factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; path=&quot;/qemu/galaxy_sdk/modules/external/nnom/src/layers/nnom_conv2d.c&quot;/>&#xD;&#xA;&lt;editorState selectionHorizontalPixel=&quot;0&quot; selectionLength=&quot;0&quot; selectionOffset=&quot;18880&quot; selectionTopPixel=&quot;9020&quot;/>&#xD;&#xA;&lt;/editor>"/>
          <tags>Editor</tags>
          <tags>removeOnHide</tags>
          <tags>org.eclipse.cdt.ui.editor.CEditor</tags>
        </children>
        <children xsi:type="basic:Part" xmi:id="_rjq_w3HlEfCAZJ6UDgwwcw" elementId="org.eclipse.e4.ui.compatibility.editor" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor" label="nnom_local.c" iconURI="platform:/plugin/org.eclipse.cdt.ui/icons/obj16/c_file_obj.gif" closeable="true">
          <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;editor id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;nnom_local.c&quot; partName=&quot;nnom_local.c&quot; title=&quot;nnom_local.c&quot; tooltip=&quot;qemu/galaxy_sdk/modules/external/nnom/src/backends/nnom_local.c&quot;>&#xD;&#xA;&lt;input factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; path=&quot;/qemu/galaxy_sdk/modules/external/nnom/src/backends/nnom_local.c&quot;/>&#xD;&#xA;&lt;editorState selectionHorizontalPixel=&quot;0&quot; selectionLength=&quot;0&quot; selectionOffset=&quot;51325&quot; selectionTopPixel=&quot;29458&quot;/>&#xD;&#xA;&lt;/editor>"/>
          <tags>Editor</tags>
          <tags>removeOnHide</tags>
          <tags>org.eclipse.cdt.ui.editor.CEditor</tags>
        </children>
        <children xsi:type="basic:Part" xmi:id="_rjq_x3HlEfCAZJ6UDgwwcw" elementId="org.eclipse.e4.ui.compatibility.editor" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor" label="nnom_activation.c" iconURI="platform:/plugin/org.eclipse.cdt.ui/icons/obj16/c_file_obj.gif" closeable="true">
          <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;editor id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;nnom_activation.c&quot; partName=&quot;nnom_activation.c&quot; title=&quot;nnom_activation.c&quot; tooltip=&quot;qemu/galaxy_sdk/modules/external/nnom/src/layers/nnom_activation.c&quot;>&#xD;&#xA;&lt;input factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; path=&quot;/qemu/galaxy_sdk/modules/external/nnom/src/layers/nnom_activation.c&quot;/>&#xD;&#xA;&lt;editorState selectionHorizontalPixel=&quot;0&quot; selectionLength=&quot;0&quot; selectionOffset=&quot;3598&quot; selectionTopPixel=&quot;3058&quot;/>&#xD;&#xA;&lt;/editor>"/>
          <tags>Editor</tags>
          <tags>removeOnHide</tags>
          <tags>org.eclipse.cdt.ui.editor.CEditor</tags>
        </children>
        <children xsi:type="basic:Part" xmi:id="_rjq_y3HlEfCAZJ6UDgwwcw" elementId="org.eclipse.e4.ui.compatibility.editor" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor" label="nnom_maxpool.c" iconURI="platform:/plugin/org.eclipse.cdt.ui/icons/obj16/c_file_obj.gif" closeable="true">
          <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;editor id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;nnom_maxpool.c&quot; partName=&quot;nnom_maxpool.c&quot; title=&quot;nnom_maxpool.c&quot; tooltip=&quot;qemu/galaxy_sdk/modules/external/nnom/src/layers/nnom_maxpool.c&quot;>&#xD;&#xA;&lt;input factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; path=&quot;/qemu/galaxy_sdk/modules/external/nnom/src/layers/nnom_maxpool.c&quot;/>&#xD;&#xA;&lt;editorState selectionHorizontalPixel=&quot;0&quot; selectionLength=&quot;0&quot; selectionOffset=&quot;5121&quot; selectionTopPixel=&quot;3674&quot;/>&#xD;&#xA;&lt;/editor>"/>
          <tags>Editor</tags>
          <tags>removeOnHide</tags>
          <tags>org.eclipse.cdt.ui.editor.CEditor</tags>
        </children>
        <children xsi:type="basic:Part" xmi:id="_rjq_z3HlEfCAZJ6UDgwwcw" elementId="org.eclipse.e4.ui.compatibility.editor" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor" label="nnom_avgpool.c" iconURI="platform:/plugin/org.eclipse.cdt.ui/icons/obj16/c_file_obj.gif" closeable="true">
          <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;editor id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;nnom_avgpool.c&quot; partName=&quot;nnom_avgpool.c&quot; title=&quot;nnom_avgpool.c&quot; tooltip=&quot;qemu/galaxy_sdk/modules/external/nnom/src/layers/nnom_avgpool.c&quot;>&#xD;&#xA;&lt;input factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; path=&quot;/qemu/galaxy_sdk/modules/external/nnom/src/layers/nnom_avgpool.c&quot;/>&#xD;&#xA;&lt;editorState selectionHorizontalPixel=&quot;0&quot; selectionLength=&quot;0&quot; selectionOffset=&quot;4218&quot; selectionTopPixel=&quot;3121&quot;/>&#xD;&#xA;&lt;/editor>"/>
          <tags>Editor</tags>
          <tags>removeOnHide</tags>
          <tags>org.eclipse.cdt.ui.editor.CEditor</tags>
        </children>
        <children xsi:type="basic:Part" xmi:id="_rjq_03HlEfCAZJ6UDgwwcw" elementId="org.eclipse.e4.ui.compatibility.editor" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor" label="nnom_flatten.c" iconURI="platform:/plugin/org.eclipse.cdt.ui/icons/obj16/c_file_obj.gif" closeable="true">
          <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;editor id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;nnom_flatten.c&quot; partName=&quot;nnom_flatten.c&quot; title=&quot;nnom_flatten.c&quot; tooltip=&quot;qemu/galaxy_sdk/modules/external/nnom/src/layers/nnom_flatten.c&quot;>&#xD;&#xA;&lt;input factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; path=&quot;/qemu/galaxy_sdk/modules/external/nnom/src/layers/nnom_flatten.c&quot;/>&#xD;&#xA;&lt;editorState selectionHorizontalPixel=&quot;0&quot; selectionLength=&quot;0&quot; selectionOffset=&quot;2137&quot; selectionTopPixel=&quot;1317&quot;/>&#xD;&#xA;&lt;/editor>"/>
          <tags>Editor</tags>
          <tags>removeOnHide</tags>
          <tags>org.eclipse.cdt.ui.editor.CEditor</tags>
        </children>
        <children xsi:type="basic:Part" xmi:id="_rjq_13HlEfCAZJ6UDgwwcw" elementId="org.eclipse.e4.ui.compatibility.editor" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor" label="nnom_dense.c" iconURI="platform:/plugin/org.eclipse.cdt.ui/icons/obj16/c_file_obj.gif" closeable="true">
          <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;editor id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;nnom_dense.c&quot; partName=&quot;nnom_dense.c&quot; title=&quot;nnom_dense.c&quot; tooltip=&quot;qemu/galaxy_sdk/modules/external/nnom/src/layers/nnom_dense.c&quot;>&#xD;&#xA;&lt;input factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; path=&quot;/qemu/galaxy_sdk/modules/external/nnom/src/layers/nnom_dense.c&quot;/>&#xD;&#xA;&lt;editorState selectionHorizontalPixel=&quot;0&quot; selectionLength=&quot;0&quot; selectionOffset=&quot;7044&quot; selectionTopPixel=&quot;4287&quot;/>&#xD;&#xA;&lt;/editor>"/>
          <tags>Editor</tags>
          <tags>removeOnHide</tags>
          <tags>org.eclipse.cdt.ui.editor.CEditor</tags>
        </children>
        <children xsi:type="basic:Part" xmi:id="_rjq_23HlEfCAZJ6UDgwwcw" elementId="org.eclipse.e4.ui.compatibility.editor" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor" label="nnom_softmax.c" iconURI="platform:/plugin/org.eclipse.cdt.ui/icons/obj16/c_file_obj.gif" closeable="true">
          <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;editor id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;nnom_softmax.c&quot; partName=&quot;nnom_softmax.c&quot; title=&quot;nnom_softmax.c&quot; tooltip=&quot;qemu/galaxy_sdk/modules/external/nnom/src/layers/nnom_softmax.c&quot;>&#xD;&#xA;&lt;input factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; path=&quot;/qemu/galaxy_sdk/modules/external/nnom/src/layers/nnom_softmax.c&quot;/>&#xD;&#xA;&lt;editorState selectionHorizontalPixel=&quot;0&quot; selectionLength=&quot;0&quot; selectionOffset=&quot;2129&quot; selectionTopPixel=&quot;1364&quot;/>&#xD;&#xA;&lt;/editor>"/>
          <tags>Editor</tags>
          <tags>removeOnHide</tags>
          <tags>org.eclipse.cdt.ui.editor.CEditor</tags>
        </children>
        <children xsi:type="basic:Part" xmi:id="_rjq_33HlEfCAZJ6UDgwwcw" elementId="org.eclipse.e4.ui.compatibility.editor" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor" label="nnom_output.c" iconURI="platform:/plugin/org.eclipse.cdt.ui/icons/obj16/c_file_obj.gif" closeable="true">
          <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;editor id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;nnom_output.c&quot; partName=&quot;nnom_output.c&quot; title=&quot;nnom_output.c&quot; tooltip=&quot;qemu/galaxy_sdk/modules/external/nnom/src/layers/nnom_output.c&quot;>&#xD;&#xA;&lt;input factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; path=&quot;/qemu/galaxy_sdk/modules/external/nnom/src/layers/nnom_output.c&quot;/>&#xD;&#xA;&lt;editorState selectionHorizontalPixel=&quot;0&quot; selectionLength=&quot;0&quot; selectionOffset=&quot;1263&quot; selectionTopPixel=&quot;704&quot;/>&#xD;&#xA;&lt;/editor>"/>
          <tags>Editor</tags>
          <tags>removeOnHide</tags>
          <tags>org.eclipse.cdt.ui.editor.CEditor</tags>
        </children>
      </children>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_rjq_43HlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.navigator.ProjectExplorer" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Project Explorer" iconURI="platform:/plugin/org.eclipse.ui.navigator.resources/icons/full/eview16/resource_persp.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.navigator.resources.ProjectExplorer"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.navigator.resources"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view CommonNavigator.LINKING_ENABLED=&quot;0&quot; currentWorkingSetName=&quot;Aggregate for window 1754150091289&quot; org.eclipse.cdt.ui.cview.groupincludes=&quot;false&quot; org.eclipse.cdt.ui.cview.groupmacros=&quot;false&quot; org.eclipse.cdt.ui.editor.CUChildren=&quot;true&quot; org.eclipse.ui.navigator.resources.workingSets.showTopLevelWorkingSets=&quot;0&quot;>&#xD;&#xA;&lt;lastRecentlyUsedFilters/>&#xD;&#xA;&lt;/view>"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
      <menus xmi:id="_rjq_5HHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.navigator.ProjectExplorer">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_rjq_-HHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.navigator.ProjectExplorer"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_rjq__3HlEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.CView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="C/C++ Projects" iconURI="platform:/plugin/org.eclipse.cdt.ui/icons/view16/cview.gif" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.internal.ui.cview.CView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.ui"/>
      <tags>View</tags>
      <tags>categoryTag:C/C++</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_rjrAAHHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.views.BookmarkView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Bookmarks" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/bkmrk_nav.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.markers.BookmarksView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_rjrAAXHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.views.ProblemView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Problems" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/problems_view.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.markers.ProblemsView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view PRIMARY_SORT_FIELD=&quot;org.eclipse.ui.ide.severityAndDescriptionField&quot; categoryGroup=&quot;org.eclipse.ui.ide.severity&quot; markerContentGenerator=&quot;org.eclipse.ui.ide.problemsGenerator&quot;>&#xD;&#xA;&lt;columnWidths org.eclipse.ui.ide.locationField=&quot;90&quot; org.eclipse.ui.ide.markerType=&quot;90&quot; org.eclipse.ui.ide.pathField=&quot;120&quot; org.eclipse.ui.ide.resourceField=&quot;90&quot; org.eclipse.ui.ide.severityAndDescriptionField=&quot;300&quot;/>&#xD;&#xA;&lt;visible IMemento.internal.id=&quot;org.eclipse.ui.ide.severityAndDescriptionField&quot;/>&#xD;&#xA;&lt;visible IMemento.internal.id=&quot;org.eclipse.ui.ide.resourceField&quot;/>&#xD;&#xA;&lt;visible IMemento.internal.id=&quot;org.eclipse.ui.ide.pathField&quot;/>&#xD;&#xA;&lt;visible IMemento.internal.id=&quot;org.eclipse.ui.ide.locationField&quot;/>&#xD;&#xA;&lt;visible IMemento.internal.id=&quot;org.eclipse.ui.ide.markerType&quot;/>&#xD;&#xA;&lt;/view>"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
      <menus xmi:id="_rjrAAnHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.views.ProblemView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_rjrABHHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.views.ProblemView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_rjrACXHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.views.TaskList" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Tasks" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/tasks_tsk.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.markers.TasksView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_rjrACnHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.console.ConsoleView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Console" iconURI="platform:/plugin/org.eclipse.ui.console/icons/full/cview16/console_view.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.console.ConsoleView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.console"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
      <menus xmi:id="_rjrAC3HlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.console.ConsoleView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_rjrAD3HlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.console.ConsoleView"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_rjrANHHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.views.PropertySheet" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Properties" iconURI="platform:/plugin/org.eclipse.ui.views/icons/full/eview16/prop_ps.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.views.properties.PropertySheet"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.views"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_rjrANXHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.views.ContentOutline" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Outline" iconURI="platform:/plugin/org.eclipse.ui.views/icons/full/eview16/outline_co.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.views.contentoutline.ContentOutline"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.views"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
      <menus xmi:id="_rjrANnHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.views.ContentOutline">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_rjrAN3HlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.views.ContentOutline" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_rjrAOHHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.views.minimap.MinimapView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Minimap" iconURI="platform:/plugin/org.eclipse.ui.workbench.texteditor/icons/full/eview16/minimap.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.minimap.MinimapView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.workbench.texteditor"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_rjrAOXHlEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.tasks.ui.views.tasks" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Task List" iconURI="platform:/plugin/org.eclipse.mylyn.tasks.ui/icons/eview16/task-list.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.mylyn.internal.tasks.ui.views.TaskListView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.mylyn.tasks.ui"/>
      <tags>View</tags>
      <tags>categoryTag:Mylyn</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_rjrAOnHlEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.make.ui.views.MakeView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Build Targets" iconURI="platform:/plugin/org.eclipse.cdt.make.ui/icons/view16/make_target.gif" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.make.ui.views.MakeView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.make.ui"/>
      <tags>View</tags>
      <tags>categoryTag:Make</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_rjrAO3HlEfCAZJ6UDgwwcw" elementId="org.eclipse.tm.terminal.view.ui.TerminalsView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Terminal" iconURI="platform:/plugin/org.eclipse.tm.terminal.view.ui/icons/eview16/terminal_view.gif" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tm.terminal.view.ui.view.TerminalsView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tm.terminal.view.ui"/>
      <tags>View</tags>
      <tags>categoryTag:Terminal</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_rjrAPHHlEfCAZJ6UDgwwcw" elementId="org.eclipse.embedcdt.internal.managedbuild.packs.ui.views.DocsView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Documents" iconURI="platform:/plugin/org.eclipse.embedcdt.managedbuild.packs.ui/icons/pdficon_small.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.embedcdt.internal.managedbuild.packs.ui.views.DocsView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.embedcdt.managedbuild.packs.ui"/>
      <tags>View</tags>
      <tags>categoryTag:CMSIS Packs</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_rjrAPXHlEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.DebugView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Debug" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/debug_view.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.launch.LaunchView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:Debug</tags>
      <menus xmi:id="_rjrAPnHlEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.DebugView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_rjrkd3HlEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.DebugView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_rjrkj3HlEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.RegisterView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Registers" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/register_view.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.registers.RegistersView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:Debug</tags>
      <menus xmi:id="_rjrkkHHlEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.RegisterView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_rjrkkXHlEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.RegisterView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_rjrkknHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.views.ProgressView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Progress" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/pview.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.progress.ProgressView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_rjrkk3HlEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.VariableView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Variables" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/variable_view.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.variables.VariablesView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:Debug</tags>
      <menus xmi:id="_rjrklHHlEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.VariableView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_rjrkqHHlEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.VariableView"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_rjrks3HlEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.BreakpointView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Breakpoints" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/breakpoint_view.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.breakpoints.BreakpointsView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:Debug</tags>
      <menus xmi:id="_rjrktHHlEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.BreakpointView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_rjrkvnHlEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.BreakpointView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_rjrkzHHlEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.ExpressionView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Expressions" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/watchlist_view.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.expression.ExpressionView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:Debug</tags>
      <menus xmi:id="_rjrkzXHlEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.ExpressionView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_rjrk13HlEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.ExpressionView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_rjrk53HlEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.visualizer.view" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Visualizer" iconURI="platform:/plugin/org.eclipse.cdt.visualizer.ui/icons/full/view16/visualizer_view.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.visualizer.ui.VisualizerView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.visualizer.ui"/>
      <tags>View</tags>
      <tags>categoryTag:Debug</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_rjrk6HHlEfCAZJ6UDgwwcw" elementId="org.eclipse.pde.runtime.LogView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Error Log" iconURI="platform:/plugin/org.eclipse.ui.views.log/icons/eview16/error_log.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.log.LogView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.views.log"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_rjrk6XHlEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.debug.ui.SignalsView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Signals" iconURI="platform:/plugin/org.eclipse.cdt.debug.ui/icons/view16/signals_view.gif" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.debug.internal.ui.views.signals.FlexibleSignalsView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.debug.ui"/>
      <tags>View</tags>
      <tags>categoryTag:Debug</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_rjrk6nHlEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.ModuleView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Modules" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/module_view.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.modules.ModulesView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
      <tags>View</tags>
      <tags>categoryTag:Debug</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_rjrk63HlEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.debug.ui.executablesView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Executables" iconURI="platform:/plugin/org.eclipse.cdt.debug.ui/icons/obj16/exec_view_obj.gif" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.debug.internal.ui.views.executables.ExecutablesView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.debug.ui"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:Debug</tags>
      <menus xmi:id="_rjrk7HHlEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.debug.ui.executablesView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_rjrk7XHlEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.debug.ui.executablesView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_rjrk7nHlEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.dsf.debug.ui.disassembly.view" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Disassembly" iconURI="platform:/plugin/org.eclipse.cdt.dsf.ui/icons/disassembly.gif" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.dsf.debug.internal.ui.disassembly.DisassemblyView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.dsf.ui"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view disassembly.syncActiveContext=&quot;true&quot; disassembly.trackExpression=&quot;false&quot;/>"/>
      <tags>View</tags>
      <tags>categoryTag:Debug</tags>
      <menus xmi:id="_rjrk73HlEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.dsf.debug.ui.disassembly.view">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_rjrk8HHlEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.dsf.debug.ui.disassembly.view" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_rjrk8XHlEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.debug.ui.memory.memorybrowser.MemoryBrowser" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Memory Browser" iconURI="platform:/plugin/org.eclipse.cdt.debug.ui.memory.memorybrowser/icons/memorybrowser_view.gif" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.debug.ui.memory.memorybrowser.MemoryBrowser"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.debug.ui.memory.memorybrowser"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:Debug</tags>
      <menus xmi:id="_rjrk8nHlEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.debug.ui.memory.memorybrowser.MemoryBrowser">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_rjrk83HlEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.debug.ui.memory.memorybrowser.MemoryBrowser" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_rjrk9HHlEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.dsf.gdb.ui.tracecontrol.view" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Trace Control" iconURI="platform:/plugin/org.eclipse.cdt.dsf.gdb.ui/icons/full/view16/tracecontrol_view.gif" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.dsf.gdb.internal.ui.tracepoints.TraceControlView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.dsf.gdb.ui"/>
      <tags>View</tags>
      <tags>categoryTag:Debug</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_rjrk9XHlEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.debug.ui.debuggerConsoleView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Debugger Console" iconURI="platform:/plugin/org.eclipse.cdt.debug.ui/icons/view16/debugger_console_view.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.debug.internal.ui.views.debuggerconsole.DebuggerConsoleView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.debug.ui"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:Debug</tags>
      <menus xmi:id="_rjrk9nHlEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.debug.ui.debuggerConsoleView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_rjrk-HHlEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.debug.ui.debuggerConsoleView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_rjrk_nHlEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.dsf.gdb.ui.debugsources.view" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Debug Sources" iconURI="platform:/plugin/org.eclipse.cdt.dsf.gdb.ui/icons/full/view16/debugsources_view.gif" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.dsf.gdb.internal.ui.debugsources.DebugSourcesView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.dsf.gdb.ui"/>
      <tags>View</tags>
      <tags>categoryTag:Debug</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_rjrk_3HlEfCAZJ6UDgwwcw" elementId="org.eclipse.embedcdt.internal.debug.gdbjtag.ui.views.PeripheralsView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Peripherals" iconURI="platform:/plugin/org.eclipse.embedcdt.debug.gdbjtag.ui/icons/peripheral.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.embedcdt.internal.debug.gdbjtag.ui.render.peripherals.PeripheralsView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.embedcdt.debug.gdbjtag.ui"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:Debug</tags>
      <menus xmi:id="_rjrlAHHlEfCAZJ6UDgwwcw" elementId="org.eclipse.embedcdt.internal.debug.gdbjtag.ui.views.PeripheralsView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_rjrlCnHlEfCAZJ6UDgwwcw" elementId="org.eclipse.embedcdt.internal.debug.gdbjtag.ui.views.PeripheralsView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_rjrlEHHlEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.MemoryView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Memory" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/memory_view.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.memory.MemoryView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:Debug</tags>
      <menus xmi:id="_rjrlEXHlEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.MemoryView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_rjrlGHHlEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.MemoryView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_rjrlJXHlEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.VariableView:PIN_CLONE_VIEW_1" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Variables &lt;1>" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/variable_view.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.variables.VariablesView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:Debug</tags>
      <menus xmi:id="_rjrlJnHlEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.VariableView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_rjrlJ3HlEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.VariableView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_rjrlKHHlEfCAZJ6UDgwwcw" elementId="org.eclipse.search.ui.views.SearchView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Search" iconURI="platform:/plugin/org.eclipse.search/icons/full/eview16/searchres.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.search2.internal.ui.SearchView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.search"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view isPinned=&quot;false&quot;>&#xD;&#xA;&lt;view IMemento.internal.id=&quot;&quot; org.eclipse.search.lastActivation=&quot;0&quot;/>&#xD;&#xA;&lt;view IMemento.internal.id=&quot;org.eclipse.search.text.FileSearchResultPage&quot; org.eclipse.search.lastActivation=&quot;1&quot; org.eclipse.search.resultpage.layout=&quot;2&quot; org.eclipse.search.resultpage.limit=&quot;1000&quot; org.eclipse.search.resultpage.sorting=&quot;2&quot;/>&#xD;&#xA;&lt;/view>"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
      <menus xmi:id="_rjrlKXHlEfCAZJ6UDgwwcw" elementId="org.eclipse.search.ui.views.SearchView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_rjrlOnHlEfCAZJ6UDgwwcw" elementId="org.eclipse.search.ui.views.SearchView" visible="false"/>
    </sharedElements>
    <trimBars xmi:id="_rjrlYXHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.main.toolbar" contributorURI="platform:/plugin/org.eclipse.platform">
      <children xsi:type="menu:ToolControl" xmi:id="_rjrlYnHlEfCAZJ6UDgwwcw" elementId="org.eclipse.launchbar" contributionURI="bundleclass://org.eclipse.launchbar.ui.controls/org.eclipse.launchbar.ui.controls.internal.LaunchBarControl"/>
      <children xsi:type="menu:ToolBar" xmi:id="_rjrlY3HlEfCAZJ6UDgwwcw" elementId="group.file" toBeRendered="false">
        <tags>toolbarSeparator</tags>
        <children xsi:type="menu:ToolBarSeparator" xmi:id="_rjrlZHHlEfCAZJ6UDgwwcw" elementId="group.file" toBeRendered="false"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_rjrlZXHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.workbench.file">
        <tags>Draggable</tags>
        <children xsi:type="menu:HandledToolItem" xmi:id="_rjrlbXHlEfCAZJ6UDgwwcw" elementId="print" visible="false" iconURI="platform:/plugin/org.eclipse.ui/icons/full/etool16/print_edit.png" tooltip="Print" command="_rjtcS3HlEfCAZJ6UDgwwcw"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_rjrlc3HlEfCAZJ6UDgwwcw" elementId="group.edit" toBeRendered="false">
        <tags>toolbarSeparator</tags>
        <children xsi:type="menu:ToolBarSeparator" xmi:id="_rjrldHHlEfCAZJ6UDgwwcw" elementId="group.edit" toBeRendered="false"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_rjrldXHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.workbench.edit">
        <tags>Draggable</tags>
        <children xsi:type="menu:HandledToolItem" xmi:id="_rjrld3HlEfCAZJ6UDgwwcw" elementId="undo" iconURI="platform:/plugin/org.eclipse.ui/icons/full/etool16/undo_edit.png" tooltip="Undo" enabled="false" command="_rjtapXHlEfCAZJ6UDgwwcw"/>
        <children xsi:type="menu:HandledToolItem" xmi:id="_rjrleHHlEfCAZJ6UDgwwcw" elementId="redo" iconURI="platform:/plugin/org.eclipse.ui/icons/full/etool16/redo_edit.png" tooltip="Redo" enabled="false" command="_rjtbGnHlEfCAZJ6UDgwwcw"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_rjrleXHlEfCAZJ6UDgwwcw" elementId="additions" toBeRendered="false">
        <tags>toolbarSeparator</tags>
        <children xsi:type="menu:ToolBarSeparator" xmi:id="_rjrlenHlEfCAZJ6UDgwwcw" elementId="additions" toBeRendered="false"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_rjrlvXHlEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.CElementCreationActionSet" visible="false">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_rjrlvnHlEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.launchActionSet">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_rjrlw3HlEfCAZJ6UDgwwcw" elementId="org.eclipse.search.searchActionSet">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_rjrlyHHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.actionSet.presentation">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_rjrlznHlEfCAZJ6UDgwwcw" elementId="group.nav" toBeRendered="false">
        <tags>toolbarSeparator</tags>
        <children xsi:type="menu:ToolBarSeparator" xmi:id="_rjrlz3HlEfCAZJ6UDgwwcw" elementId="group.nav" toBeRendered="false"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_rjrl0HHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.workbench.navigate">
        <tags>Draggable</tags>
        <children xsi:type="menu:HandledToolItem" xmi:id="_rjrl1nHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.window.pinEditor" iconURI="platform:/plugin/org.eclipse.ui/icons/full/etool16/pin_editor.png" tooltip="Pin Editor" type="Check" command="_rjtb_nHlEfCAZJ6UDgwwcw"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_rjrl23HlEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.editor.CEditor" visible="false">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_rjrl3HHlEfCAZJ6UDgwwcw" elementId="group.editor" toBeRendered="false">
        <tags>toolbarSeparator</tags>
        <children xsi:type="menu:ToolBarSeparator" xmi:id="_rjrl3XHlEfCAZJ6UDgwwcw" elementId="group.editor" toBeRendered="false"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_rjrl3nHlEfCAZJ6UDgwwcw" elementId="group.help" toBeRendered="false">
        <tags>toolbarSeparator</tags>
        <children xsi:type="menu:ToolBarSeparator" xmi:id="_rjrl33HlEfCAZJ6UDgwwcw" elementId="group.help" toBeRendered="false"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_rjrl4HHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.workbench.help" visible="false">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolControl" xmi:id="_rjrl43HlEfCAZJ6UDgwwcw" elementId="PerspectiveSpacer" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.renderers.swt/org.eclipse.e4.ui.workbench.renderers.swt.LayoutModifierToolControl">
        <tags>stretch</tags>
        <tags>SHOW_RESTORE_MENU</tags>
      </children>
      <children xsi:type="menu:ToolControl" xmi:id="_rjrl53HlEfCAZJ6UDgwwcw" elementId="PerspectiveSwitcher" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.e4.ui.workbench.addons.perspectiveswitcher.PerspectiveSwitcher">
        <tags>Draggable</tags>
        <tags>HIDEABLE</tags>
        <tags>SHOW_RESTORE_MENU</tags>
      </children>
    </trimBars>
    <trimBars xmi:id="_rjrl7nHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.trim.status" contributorURI="platform:/plugin/org.eclipse.platform" side="Bottom">
      <children xsi:type="menu:ToolControl" xmi:id="_rjrl73HlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.StatusLine" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.StandardTrim">
        <tags>stretch</tags>
      </children>
      <children xsi:type="menu:ToolControl" xmi:id="_rjrl8HHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.HeapStatus" contributorURI="platform:/plugin/org.eclipse.platform" toBeRendered="false" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.StandardTrim">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolControl" xmi:id="_rjrl8XHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.ProgressBar" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.StandardTrim">
        <tags>Draggable</tags>
      </children>
    </trimBars>
    <trimBars xmi:id="_rjrl-XHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.trim.vertical1" contributorURI="platform:/plugin/org.eclipse.platform" toBeRendered="false" side="Left">
      <children xsi:type="menu:ToolControl" xmi:id="_rjrl-nHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.ide.perspectivestack(minimized)" toBeRendered="false" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.addons.swt/org.eclipse.e4.ui.workbench.addons.minmax.TrimStack">
        <tags>TrimStack</tags>
        <tags>Draggable</tags>
      </children>
    </trimBars>
    <trimBars xmi:id="_rjrl-3HlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.trim.vertical2" contributorURI="platform:/plugin/org.eclipse.platform" side="Right"/>
  </children>
  <bindingTables xmi:id="_rjrl_HHlEfCAZJ6UDgwwcw" contributorURI="platform:/plugin/org.eclipse.platform" bindingContext="_rjrnj3HlEfCAZJ6UDgwwcw">
    <bindings xmi:id="_rjrl_XHlEfCAZJ6UDgwwcw" keySequence="CTRL+1" command="_rjtaa3HlEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_rjrl_nHlEfCAZJ6UDgwwcw" keySequence="CTRL+SHIFT+L" command="_rjtcgHHlEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_rjrl_3HlEfCAZJ6UDgwwcw" keySequence="CTRL+V" command="_rjtZ13HlEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_rjrmAHHlEfCAZJ6UDgwwcw" keySequence="CTRL+A" command="_rjta_3HlEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_rjrmAXHlEfCAZJ6UDgwwcw" keySequence="CTRL+C" command="_rjtbSHHlEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_rjrmAnHlEfCAZJ6UDgwwcw" keySequence="CTRL+X" command="_rjtarXHlEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_rjrmA3HlEfCAZJ6UDgwwcw" keySequence="CTRL+Y" command="_rjtbGnHlEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_rjrmBHHlEfCAZJ6UDgwwcw" keySequence="CTRL+Z" command="_rjtapXHlEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_rjrmBXHlEfCAZJ6UDgwwcw" keySequence="ALT+PAGE_UP" command="_rjtbJHHlEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_rjrmBnHlEfCAZJ6UDgwwcw" keySequence="ALT+PAGE_DOWN" command="_rjtbzXHlEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_rjrmB3HlEfCAZJ6UDgwwcw" keySequence="SHIFT+INSERT" command="_rjtZ13HlEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_rjrmCHHlEfCAZJ6UDgwwcw" keySequence="ALT+F11" command="_rjtaE3HlEfCAZJ6UDgwwcw">
      <tags>platform:win32</tags>
    </bindings>
    <bindings xmi:id="_rjrmCXHlEfCAZJ6UDgwwcw" keySequence="CTRL+F10" command="_rjtZ-HHlEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_rjrmCnHlEfCAZJ6UDgwwcw" keySequence="CTRL+INSERT" command="_rjtbSHHlEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_rjrmC3HlEfCAZJ6UDgwwcw" keySequence="CTRL+PAGE_UP" command="_rjtcYHHlEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_rjrmDHHlEfCAZJ6UDgwwcw" keySequence="CTRL+PAGE_DOWN" command="_rjtac3HlEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_rjrmDXHlEfCAZJ6UDgwwcw" keySequence="ALT+SHIFT+F3" command="_rjtcVXHlEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_rjrmDnHlEfCAZJ6UDgwwcw" keySequence="SHIFT+DEL" command="_rjtarXHlEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_rjrmD3HlEfCAZJ6UDgwwcw" keySequence="ALT+/" command="_rjtcJXHlEfCAZJ6UDgwwcw">
      <tags>locale:zh</tags>
    </bindings>
  </bindingTables>
  <bindingTables xmi:id="_rjrmEHHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.textEditorScope" bindingContext="_rjrnlHHlEfCAZJ6UDgwwcw">
    <bindings xmi:id="_rjrmEXHlEfCAZJ6UDgwwcw" keySequence="CTRL+SHIFT+CR" command="_rjtcVHHlEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_rjrmEnHlEfCAZJ6UDgwwcw" keySequence="CTRL+BS" command="_rjtZsHHlEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_rjrmE3HlEfCAZJ6UDgwwcw" keySequence="CTRL+SHIFT+Q" command="_rjtaWHHlEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_rjrmFHHlEfCAZJ6UDgwwcw" keySequence="ALT+CTRL+C" command="_rjtZ7HHlEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_rjrmFXHlEfCAZJ6UDgwwcw" keySequence="CTRL+SHIFT+J" command="_rjtaSXHlEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_rjrmFnHlEfCAZJ6UDgwwcw" keySequence="CTRL++" command="_rjtbr3HlEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_rjrmF3HlEfCAZJ6UDgwwcw" keySequence="CTRL+-" command="_rjta6HHlEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_rjrmGHHlEfCAZJ6UDgwwcw" keySequence="ALT+CTRL+P" command="_rjtb7nHlEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_rjrmGXHlEfCAZJ6UDgwwcw" keySequence="ALT+CTRL+V" command="_rjtaA3HlEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_rjrmGnHlEfCAZJ6UDgwwcw" keySequence="ALT+CTRL+J" command="_rjtaZHHlEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_rjrmG3HlEfCAZJ6UDgwwcw" keySequence="ALT+SHIFT+A" command="_rjtbZnHlEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_rjrmHHHlEfCAZJ6UDgwwcw" keySequence="CTRL+J" command="_rjtZ_nHlEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_rjrmHXHlEfCAZJ6UDgwwcw" keySequence="CTRL+L" command="_rjtcOHHlEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_rjrmHnHlEfCAZJ6UDgwwcw" keySequence="CTRL+D" command="_rjtaB3HlEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_rjrmH3HlEfCAZJ6UDgwwcw" keySequence="CTRL+=" command="_rjtbr3HlEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_rjrmIHHlEfCAZJ6UDgwwcw" keySequence="ALT+CTRL+/" command="_rjtckXHlEfCAZJ6UDgwwcw">
      <tags>locale:zh</tags>
    </bindings>
    <bindings xmi:id="_rjrmIXHlEfCAZJ6UDgwwcw" keySequence="ALT+SHIFT+Y" command="_rjtZp3HlEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_rjrmInHlEfCAZJ6UDgwwcw" keySequence="CTRL+SHIFT+DEL" command="_rjtcKnHlEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_rjrmI3HlEfCAZJ6UDgwwcw" keySequence="CTRL+SHIFT+X" command="_rjtbUHHlEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_rjrmJHHlEfCAZJ6UDgwwcw" keySequence="CTRL+SHIFT+Y" command="_rjta5nHlEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_rjrmJXHlEfCAZJ6UDgwwcw" keySequence="CTRL+DEL" command="_rjtaoXHlEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_rjrmJnHlEfCAZJ6UDgwwcw" keySequence="ALT+ARROW_UP" command="_rjtc3nHlEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_rjrmJ3HlEfCAZJ6UDgwwcw" keySequence="ALT+ARROW_DOWN" command="_rjtb2HHlEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_rjrmKHHlEfCAZJ6UDgwwcw" keySequence="SHIFT+END" command="_rjta7nHlEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_rjrmKXHlEfCAZJ6UDgwwcw" keySequence="SHIFT+HOME" command="_rjta1XHlEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_rjrmKnHlEfCAZJ6UDgwwcw" keySequence="END" command="_rjtcbHHlEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_rjrmK3HlEfCAZJ6UDgwwcw" keySequence="INSERT" command="_rjtbh3HlEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_rjrmLHHlEfCAZJ6UDgwwcw" keySequence="F2" command="_rjtadXHlEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_rjrmLXHlEfCAZJ6UDgwwcw" keySequence="HOME" command="_rjtcjHHlEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_rjrmLnHlEfCAZJ6UDgwwcw" keySequence="ALT+CTRL+ARROW_UP" command="_rjtcuHHlEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_rjrmL3HlEfCAZJ6UDgwwcw" keySequence="ALT+CTRL+ARROW_DOWN" command="_rjtbAnHlEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_rjrmMHHlEfCAZJ6UDgwwcw" keySequence="CTRL+SHIFT+INSERT" command="_rjtaNHHlEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_rjrmMXHlEfCAZJ6UDgwwcw" keySequence="CTRL+SHIFT+ARROW_LEFT" command="_rjta8HHlEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_rjrmMnHlEfCAZJ6UDgwwcw" keySequence="CTRL+SHIFT+ARROW_RIGHT" command="_rjtaO3HlEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_rjrmM3HlEfCAZJ6UDgwwcw" keySequence="CTRL+F10" command="_rjtcUHHlEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_rjrmNHHlEfCAZJ6UDgwwcw" keySequence="CTRL+END" command="_rjtb2nHlEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_rjrmNXHlEfCAZJ6UDgwwcw" keySequence="CTRL+ARROW_UP" command="_rjtaI3HlEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_rjrmNnHlEfCAZJ6UDgwwcw" keySequence="CTRL+ARROW_DOWN" command="_rjtc7nHlEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_rjrmN3HlEfCAZJ6UDgwwcw" keySequence="CTRL+ARROW_LEFT" command="_rjtbQnHlEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_rjrmOHHlEfCAZJ6UDgwwcw" keySequence="CTRL+ARROW_RIGHT" command="_rjtaVXHlEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_rjrmOXHlEfCAZJ6UDgwwcw" keySequence="CTRL+HOME" command="_rjtZ1nHlEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_rjrmOnHlEfCAZJ6UDgwwcw" keySequence="CTRL+NUMPAD_MULTIPLY" command="_rjtb7XHlEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_rjrmO3HlEfCAZJ6UDgwwcw" keySequence="CTRL+NUMPAD_ADD" command="_rjtcp3HlEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_rjrmPHHlEfCAZJ6UDgwwcw" keySequence="CTRL+NUMPAD_SUBTRACT" command="_rjtcUnHlEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_rjrmPXHlEfCAZJ6UDgwwcw" keySequence="CTRL+NUMPAD_DIVIDE" command="_rjtaJXHlEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_rjrmPnHlEfCAZJ6UDgwwcw" keySequence="CTRL+SHIFT+NUMPAD_MULTIPLY" command="_rjtb9nHlEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_rjrmP3HlEfCAZJ6UDgwwcw" keySequence="CTRL+SHIFT+NUMPAD_DIVIDE" command="_rjtbiHHlEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_rjrmQHHlEfCAZJ6UDgwwcw" keySequence="SHIFT+CR" command="_rjtciXHlEfCAZJ6UDgwwcw"/>
  </bindingTables>
  <bindingTables xmi:id="_rjrmQXHlEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.cEditorScope" bindingContext="_rjrnqXHlEfCAZJ6UDgwwcw">
    <bindings xmi:id="_rjrmQnHlEfCAZJ6UDgwwcw" keySequence="ALT+CTRL+SHIFT+C" command="_rjtc8XHlEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_rjrmQ3HlEfCAZJ6UDgwwcw" keySequence="CTRL+TAB" command="_rjtc6nHlEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_rjrmRHHlEfCAZJ6UDgwwcw" keySequence="CTRL+SHIFT+P" command="_rjtaynHlEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_rjrmRXHlEfCAZJ6UDgwwcw" keySequence="CTRL+SHIFT+T" command="_rjtahnHlEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_rjrmRnHlEfCAZJ6UDgwwcw" keySequence="CTRL+7" command="_rjtZ2nHlEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_rjrmR3HlEfCAZJ6UDgwwcw" keySequence="CTRL+SHIFT+H" command="_rjtbmnHlEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_rjrmSHHlEfCAZJ6UDgwwcw" keySequence="CTRL+SHIFT+N" command="_rjtaVHHlEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_rjrmSXHlEfCAZJ6UDgwwcw" keySequence="CTRL+/" command="_rjtZ2nHlEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_rjrmSnHlEfCAZJ6UDgwwcw" keySequence="CTRL+SHIFT+O" command="_rjtavnHlEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_rjrmS3HlEfCAZJ6UDgwwcw" keySequence="CTRL+SHIFT+A" command="_rjtcVnHlEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_rjrmTHHlEfCAZJ6UDgwwcw" keySequence="ALT+CTRL+S" command="_rjtczHHlEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_rjrmTXHlEfCAZJ6UDgwwcw" keySequence="CTRL+#" command="_rjtcDHHlEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_rjrmTnHlEfCAZJ6UDgwwcw" keySequence="CTRL+SHIFT+C" command="_rjtZ2nHlEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_rjrmT3HlEfCAZJ6UDgwwcw" keySequence="CTRL+SHIFT+F" command="_rjtc63HlEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_rjrmUHHlEfCAZJ6UDgwwcw" keySequence="CTRL+SHIFT+G" command="_rjtZq3HlEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_rjrmUXHlEfCAZJ6UDgwwcw" keySequence="ALT+CTRL+H" command="_rjtabnHlEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_rjrmUnHlEfCAZJ6UDgwwcw" keySequence="ALT+CTRL+I" command="_rjtbc3HlEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_rjrmU3HlEfCAZJ6UDgwwcw" keySequence="CTRL+T" command="_rjtb3XHlEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_rjrmVHHlEfCAZJ6UDgwwcw" keySequence="CTRL+I" command="_rjtaRnHlEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_rjrmVXHlEfCAZJ6UDgwwcw" keySequence="CTRL+O" command="_rjtbWXHlEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_rjrmVnHlEfCAZJ6UDgwwcw" keySequence="CTRL+SHIFT+/" command="_rjtcpHHlEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_rjrmV3HlEfCAZJ6UDgwwcw" keySequence="ALT+SHIFT+R" command="_rjtcL3HlEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_rjrmWHHlEfCAZJ6UDgwwcw" keySequence="ALT+SHIFT+S" command="_rjtZ-nHlEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_rjrmWXHlEfCAZJ6UDgwwcw" keySequence="ALT+SHIFT+T" command="_rjtcEnHlEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_rjrmWnHlEfCAZJ6UDgwwcw" keySequence="CTRL+G" command="_rjtcw3HlEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_rjrmW3HlEfCAZJ6UDgwwcw" keySequence="ALT+SHIFT+L" command="_rjtbBHHlEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_rjrmXHHlEfCAZJ6UDgwwcw" keySequence="ALT+SHIFT+M" command="_rjtZzXHlEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_rjrmXXHlEfCAZJ6UDgwwcw" keySequence="CTRL+=" command="_rjtcDHHlEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_rjrmXnHlEfCAZJ6UDgwwcw" keySequence="ALT+SHIFT+O" command="_rjtas3HlEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_rjrmX3HlEfCAZJ6UDgwwcw" keySequence="ALT+SHIFT+Z" command="_rjtcQ3HlEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_rjrmYHHlEfCAZJ6UDgwwcw" keySequence="CTRL+SHIFT+\" command="_rjtbb3HlEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_rjrmYXHlEfCAZJ6UDgwwcw" keySequence="F3" command="_rjtc9nHlEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_rjrmYnHlEfCAZJ6UDgwwcw" keySequence="F4" command="_rjtcn3HlEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_rjrmY3HlEfCAZJ6UDgwwcw" keySequence="CTRL+SHIFT+ARROW_UP" command="_rjtcNHHlEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_rjrmZHHlEfCAZJ6UDgwwcw" keySequence="CTRL+SHIFT+ARROW_DOWN" command="_rjtb0HHlEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_rjrmZXHlEfCAZJ6UDgwwcw" keySequence="ALT+SHIFT+ARROW_UP" command="_rjta23HlEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_rjrmZnHlEfCAZJ6UDgwwcw" keySequence="ALT+SHIFT+ARROW_DOWN" command="_rjtc7HHlEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_rjrmZ3HlEfCAZJ6UDgwwcw" keySequence="ALT+SHIFT+ARROW_LEFT" command="_rjtcJnHlEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_rjrmaHHlEfCAZJ6UDgwwcw" keySequence="ALT+SHIFT+ARROW_RIGHT" command="_rjtce3HlEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_rjrmaXHlEfCAZJ6UDgwwcw" keySequence="ALT+C" command="_rjtbcHHlEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_rjrmanHlEfCAZJ6UDgwwcw" keySequence="SHIFT+TAB" command="_rjtbm3HlEfCAZJ6UDgwwcw"/>
  </bindingTables>
  <bindingTables xmi:id="_rjrma3HlEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.tasks.ui.views.tasks" bindingContext="_rjrntXHlEfCAZJ6UDgwwcw">
    <bindings xmi:id="_rjrmbHHlEfCAZJ6UDgwwcw" keySequence="CTRL+CR" command="_rjtalHHlEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_rjrmbXHlEfCAZJ6UDgwwcw" keySequence="ALT+SHIFT+C" command="_rjtbNXHlEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_rjrmbnHlEfCAZJ6UDgwwcw" keySequence="ALT+SHIFT+R" command="_rjta73HlEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_rjrmb3HlEfCAZJ6UDgwwcw" keySequence="ALT+SHIFT+U" command="_rjtb5XHlEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_rjrmcHHlEfCAZJ6UDgwwcw" keySequence="ALT+SHIFT+I" command="_rjta6XHlEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_rjrmcXHlEfCAZJ6UDgwwcw" keySequence="ALT+ARROW_UP" command="_rjtbvHHlEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_rjrmcnHlEfCAZJ6UDgwwcw" keySequence="ALT+ARROW_DOWN" command="_rjtamXHlEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_rjrmc3HlEfCAZJ6UDgwwcw" keySequence="SHIFT+INSERT" command="_rjtaGHHlEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_rjrmdHHlEfCAZJ6UDgwwcw" keySequence="INSERT" command="_rjta5HHlEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_rjrmdXHlEfCAZJ6UDgwwcw" keySequence="F4" command="_rjtZ9HHlEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_rjrmdnHlEfCAZJ6UDgwwcw" keySequence="ALT+SHIFT+ARROW_UP" command="_rjtcFHHlEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_rjrmd3HlEfCAZJ6UDgwwcw" keySequence="ALT+SHIFT+ARROW_DOWN" command="_rjta63HlEfCAZJ6UDgwwcw"/>
  </bindingTables>
  <bindingTables xmi:id="_rjrmeHHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.contexts.window" bindingContext="_rjrnkHHlEfCAZJ6UDgwwcw">
    <bindings xmi:id="_rjrmeXHlEfCAZJ6UDgwwcw" keySequence="ALT+CTRL+SHIFT+T" command="_rjtZ93HlEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_rjrmenHlEfCAZJ6UDgwwcw" keySequence="ALT+CTRL+SHIFT+L" command="_rjtbenHlEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_rjrme3HlEfCAZJ6UDgwwcw" keySequence="ALT+SHIFT+Q O" command="_rjtbvXHlEfCAZJ6UDgwwcw">
      <parameters xmi:id="_rjrmfHHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.ui.views.ContentOutline"/>
    </bindings>
    <bindings xmi:id="_rjrmfXHlEfCAZJ6UDgwwcw" keySequence="ALT+CTRL+B" command="_rjtbx3HlEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_rjrmfnHlEfCAZJ6UDgwwcw" keySequence="CTRL+SHIFT+R" command="_rjtc83HlEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_rjrmf3HlEfCAZJ6UDgwwcw" keySequence="ALT+SHIFT+Q Q" command="_rjtbvXHlEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_rjrmgHHlEfCAZJ6UDgwwcw" keySequence="CTRL+SHIFT+S" command="_rjtbqXHlEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_rjrmgXHlEfCAZJ6UDgwwcw" keySequence="CTRL+3" command="_rjtadHHlEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_rjrmgnHlEfCAZJ6UDgwwcw" keySequence="ALT+SHIFT+Q S" command="_rjtbvXHlEfCAZJ6UDgwwcw">
      <parameters xmi:id="_rjrmg3HlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.search.ui.views.SearchView"/>
    </bindings>
    <bindings xmi:id="_rjrmhHHlEfCAZJ6UDgwwcw" keySequence="CTRL+6" command="_rjtciHHlEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_rjrmhXHlEfCAZJ6UDgwwcw" keySequence="ALT+SHIFT+Q V" command="_rjtbvXHlEfCAZJ6UDgwwcw">
      <parameters xmi:id="_rjrmhnHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.debug.ui.VariableView"/>
    </bindings>
    <bindings xmi:id="_rjrmh3HlEfCAZJ6UDgwwcw" keySequence="ALT+CTRL+G" command="_rjtbtXHlEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_rjrmiHHlEfCAZJ6UDgwwcw" keySequence="CTRL+SHIFT+W" command="_rjtarHHlEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_rjrmiXHlEfCAZJ6UDgwwcw" keySequence="ALT+SHIFT+Q H" command="_rjtbvXHlEfCAZJ6UDgwwcw">
      <parameters xmi:id="_rjrminHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.ui.cheatsheets.views.CheatSheetView"/>
    </bindings>
    <bindings xmi:id="_rjrmi3HlEfCAZJ6UDgwwcw" keySequence="CTRL+SHIFT+K" command="_rjtaH3HlEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_rjrmjHHlEfCAZJ6UDgwwcw" keySequence="ALT+SHIFT+Q K" command="_rjtbvXHlEfCAZJ6UDgwwcw">
      <parameters xmi:id="_rjrmjXHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.mylyn.tasks.ui.views.tasks"/>
    </bindings>
    <bindings xmi:id="_rjrmjnHlEfCAZJ6UDgwwcw" keySequence="CTRL+," command="_rjtZ23HlEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_rjrmj3HlEfCAZJ6UDgwwcw" keySequence="ALT+SHIFT+Q L" command="_rjtbvXHlEfCAZJ6UDgwwcw">
      <parameters xmi:id="_rjrmkHHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.pde.runtime.LogView"/>
    </bindings>
    <bindings xmi:id="_rjrmkXHlEfCAZJ6UDgwwcw" keySequence="CTRL+." command="_rjtcunHlEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_rjrmknHlEfCAZJ6UDgwwcw" keySequence="ALT+CTRL+P" command="_rjta1nHlEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_rjrmk3HlEfCAZJ6UDgwwcw" keySequence="CTRL+SHIFT+B" command="_rjtaIHHlEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_rjrmlHHlEfCAZJ6UDgwwcw" keySequence="CTRL+#" command="_rjtZ-XHlEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_rjrmlXHlEfCAZJ6UDgwwcw" keySequence="ALT+CTRL+T" command="_rjtbRHHlEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_rjrmlnHlEfCAZJ6UDgwwcw" keySequence="CTRL+SHIFT+E" command="_rjtaMHHlEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_rjrml3HlEfCAZJ6UDgwwcw" keySequence="ALT+SHIFT+Q X" command="_rjtbvXHlEfCAZJ6UDgwwcw">
      <parameters xmi:id="_rjrmmHHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.ui.views.ProblemView"/>
    </bindings>
    <bindings xmi:id="_rjrmmXHlEfCAZJ6UDgwwcw" keySequence="ALT+SHIFT+Q Y" command="_rjtbvXHlEfCAZJ6UDgwwcw">
      <parameters xmi:id="_rjrmmnHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.team.sync.views.SynchronizeView"/>
    </bindings>
    <bindings xmi:id="_rjrmm3HlEfCAZJ6UDgwwcw" keySequence="ALT+SHIFT+Q Z" command="_rjtbvXHlEfCAZJ6UDgwwcw">
      <parameters xmi:id="_rjrmnHHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.team.ui.GenericHistoryView"/>
    </bindings>
    <bindings xmi:id="_rjrmnXHlEfCAZJ6UDgwwcw" keySequence="CTRL+P" command="_rjtcS3HlEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_rjrmnnHlEfCAZJ6UDgwwcw" keySequence="CTRL+Q" command="_rjtcW3HlEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_rjrmn3HlEfCAZJ6UDgwwcw" keySequence="CTRL+S" command="_rjta6nHlEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_rjrmoHHlEfCAZJ6UDgwwcw" keySequence="CTRL+W" command="_rjtbH3HlEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_rjrmoXHlEfCAZJ6UDgwwcw" keySequence="CTRL+H" command="_rjtcJHHlEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_rjrmonHlEfCAZJ6UDgwwcw" keySequence="CTRL+K" command="_rjtbynHlEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_rjrmo3HlEfCAZJ6UDgwwcw" keySequence="CTRL+M" command="_rjtcH3HlEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_rjrmpHHlEfCAZJ6UDgwwcw" keySequence="CTRL+N" command="_rjtcznHlEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_rjrmpXHlEfCAZJ6UDgwwcw" keySequence="ALT+SHIFT+?" command="_rjtah3HlEfCAZJ6UDgwwcw">
      <tags>locale:zh</tags>
    </bindings>
    <bindings xmi:id="_rjrmpnHlEfCAZJ6UDgwwcw" keySequence="CTRL+B" command="_rjtZ4HHlEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_rjrmp3HlEfCAZJ6UDgwwcw" keySequence="ALT+SHIFT+Q B" command="_rjtbvXHlEfCAZJ6UDgwwcw">
      <parameters xmi:id="_rjrmqHHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.debug.ui.BreakpointView"/>
    </bindings>
    <bindings xmi:id="_rjrmqXHlEfCAZJ6UDgwwcw" keySequence="ALT+SHIFT+Q C" command="_rjtbvXHlEfCAZJ6UDgwwcw">
      <parameters xmi:id="_rjrmqnHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.ui.console.ConsoleView"/>
    </bindings>
    <bindings xmi:id="_rjrmq3HlEfCAZJ6UDgwwcw" keySequence="CTRL+E" command="_rjtannHlEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_rjrmrHHlEfCAZJ6UDgwwcw" keySequence="CTRL+F" command="_rjtaDnHlEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_rjrmrXHlEfCAZJ6UDgwwcw" keySequence="ALT+SHIFT+W" command="_rjtct3HlEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_rjrmrnHlEfCAZJ6UDgwwcw" keySequence="ALT+SHIFT+H" command="_rjtalnHlEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_rjrmr3HlEfCAZJ6UDgwwcw" keySequence="ALT+SHIFT+N" command="_rjtaqXHlEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_rjrmsHHlEfCAZJ6UDgwwcw" keySequence="CTRL+_" command="_rjtainHlEfCAZJ6UDgwwcw">
      <parameters xmi:id="_rjrmsXHlEfCAZJ6UDgwwcw" elementId="Splitter.isHorizontal" name="Splitter.isHorizontal" value="true"/>
    </bindings>
    <bindings xmi:id="_rjrmsnHlEfCAZJ6UDgwwcw" keySequence="CTRL+{" command="_rjtainHlEfCAZJ6UDgwwcw">
      <parameters xmi:id="_rjrms3HlEfCAZJ6UDgwwcw" elementId="Splitter.isHorizontal" name="Splitter.isHorizontal" value="false"/>
    </bindings>
    <bindings xmi:id="_rjrmtHHlEfCAZJ6UDgwwcw" keySequence="SHIFT+F9" command="_rjtavXHlEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_rjrmtXHlEfCAZJ6UDgwwcw" keySequence="ALT+ARROW_LEFT" command="_rjtZ_HHlEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_rjrmtnHlEfCAZJ6UDgwwcw" keySequence="ALT+ARROW_RIGHT" command="_rjtawHHlEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_rjrmt3HlEfCAZJ6UDgwwcw" keySequence="SHIFT+F5" command="_rjtbCXHlEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_rjrmuHHlEfCAZJ6UDgwwcw" keySequence="ALT+F7" command="_rjtbbXHlEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_rjrmuXHlEfCAZJ6UDgwwcw" keySequence="F9" command="_rjtazXHlEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_rjrmunHlEfCAZJ6UDgwwcw" keySequence="F11" command="_rjtcm3HlEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_rjrmu3HlEfCAZJ6UDgwwcw" keySequence="F12" command="_rjtcKHHlEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_rjrmvHHlEfCAZJ6UDgwwcw" keySequence="F2" command="_rjtZ33HlEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_rjrmvXHlEfCAZJ6UDgwwcw" keySequence="F5" command="_rjtax3HlEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_rjrmvnHlEfCAZJ6UDgwwcw" keySequence="CTRL+SHIFT+F7" command="_rjtcnXHlEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_rjrmv3HlEfCAZJ6UDgwwcw" keySequence="CTRL+SHIFT+F8" command="_rjtaiXHlEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_rjrmwHHlEfCAZJ6UDgwwcw" keySequence="CTRL+SHIFT+F9" command="_rjta-XHlEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_rjrmwXHlEfCAZJ6UDgwwcw" keySequence="ALT+CTRL+ARROW_LEFT" command="_rjtcW3HlEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_rjrmwnHlEfCAZJ6UDgwwcw" keySequence="ALT+CTRL+ARROW_RIGHT" command="_rjtaKHHlEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_rjrmw3HlEfCAZJ6UDgwwcw" keySequence="CTRL+SHIFT+F12" command="_rjtZwHHlEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_rjrmxHHlEfCAZJ6UDgwwcw" keySequence="CTRL+SHIFT+F4" command="_rjtarHHlEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_rjrmxXHlEfCAZJ6UDgwwcw" keySequence="CTRL+SHIFT+F6" command="_rjtbrnHlEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_rjrmxnHlEfCAZJ6UDgwwcw" keySequence="CTRL+F7" command="_rjtbSXHlEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_rjrmx3HlEfCAZJ6UDgwwcw" keySequence="CTRL+F8" command="_rjtabHHlEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_rjrmyHHlEfCAZJ6UDgwwcw" keySequence="CTRL+F9" command="_rjtaM3HlEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_rjrmyXHlEfCAZJ6UDgwwcw" keySequence="CTRL+F11" command="_rjtcb3HlEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_rjrmynHlEfCAZJ6UDgwwcw" keySequence="CTRL+F12" command="_rjtaInHlEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_rjrmy3HlEfCAZJ6UDgwwcw" keySequence="CTRL+F4" command="_rjtbH3HlEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_rjrmzHHlEfCAZJ6UDgwwcw" keySequence="CTRL+F6" command="_rjtaFnHlEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_rjrmzXHlEfCAZJ6UDgwwcw" keySequence="ALT+SHIFT+F7" command="_rjtb3HHlEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_rjrmznHlEfCAZJ6UDgwwcw" keySequence="ALT+CTRL+SHIFT+ARROW_UP" command="_rjtblXHlEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_rjrmz3HlEfCAZJ6UDgwwcw" keySequence="ALT+CTRL+SHIFT+ARROW_DOWN" command="_rjtc2nHlEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_rjrm0HHlEfCAZJ6UDgwwcw" keySequence="ALT+CTRL+SHIFT+ARROW_RIGHT" command="_rjtbg3HlEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_rjrm0XHlEfCAZJ6UDgwwcw" keySequence="CTRL+SHIFT+NUMPAD_MULTIPLY" command="_rjtbqHHlEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_rjrm0nHlEfCAZJ6UDgwwcw" keySequence="CTRL+SHIFT+NUMPAD_DIVIDE" command="_rjtajHHlEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_rjrm03HlEfCAZJ6UDgwwcw" keySequence="ALT+CTRL+SHIFT+F12" command="_rjtcqXHlEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_rjrm1HHlEfCAZJ6UDgwwcw" keySequence="DEL" command="_rjtaG3HlEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_rjrm1XHlEfCAZJ6UDgwwcw" keySequence="ALT+?" command="_rjtah3HlEfCAZJ6UDgwwcw">
      <tags>locale:zh</tags>
    </bindings>
    <bindings xmi:id="_rjrm1nHlEfCAZJ6UDgwwcw" keySequence="ALT+-" command="_rjtbXHHlEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_rjrm13HlEfCAZJ6UDgwwcw" keySequence="ALT+CR" command="_rjtcD3HlEfCAZJ6UDgwwcw"/>
  </bindingTables>
  <bindingTables xmi:id="_rjrm2HHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.genericeditor.genericEditorContext" bindingContext="_rjrnl3HlEfCAZJ6UDgwwcw">
    <bindings xmi:id="_rjrm2XHlEfCAZJ6UDgwwcw" keySequence="CTRL+SHIFT+P" command="_rjta4XHlEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_rjrm2nHlEfCAZJ6UDgwwcw" keySequence="CTRL+SHIFT+G" command="_rjtca3HlEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_rjrm23HlEfCAZJ6UDgwwcw" keySequence="F3" command="_rjtcXHHlEfCAZJ6UDgwwcw"/>
  </bindingTables>
  <bindingTables xmi:id="_rjrm3HHlEfCAZJ6UDgwwcw" elementId="org.eclipse.wst.sse.ui.structuredTextEditorScope" bindingContext="_rjrnnXHlEfCAZJ6UDgwwcw">
    <bindings xmi:id="_rjrm3XHlEfCAZJ6UDgwwcw" keySequence="CTRL+SHIFT+P" command="_rjtbZ3HlEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_rjrm3nHlEfCAZJ6UDgwwcw" keySequence="CTRL+SHIFT+A" command="_rjtc73HlEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_rjrm33HlEfCAZJ6UDgwwcw" keySequence="CTRL+SHIFT+C" command="_rjtchHHlEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_rjrm4HHlEfCAZJ6UDgwwcw" keySequence="CTRL+SHIFT+F" command="_rjtcynHlEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_rjrm4XHlEfCAZJ6UDgwwcw" keySequence="CTRL+SHIFT+>" command="_rjtcOnHlEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_rjrm4nHlEfCAZJ6UDgwwcw" keySequence="CTRL+I" command="_rjtcTXHlEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_rjrm43HlEfCAZJ6UDgwwcw" keySequence="CTRL+O" command="_rjtbdXHlEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_rjrm5HHlEfCAZJ6UDgwwcw" keySequence="CTRL+SHIFT+/" command="_rjtbW3HlEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_rjrm5XHlEfCAZJ6UDgwwcw" keySequence="CTRL+SHIFT+\" command="_rjtbtnHlEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_rjrm5nHlEfCAZJ6UDgwwcw" keySequence="F3" command="_rjtbZHHlEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_rjrm53HlEfCAZJ6UDgwwcw" keySequence="CTRL+SHIFT+ARROW_UP" command="_rjtaRXHlEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_rjrm6HHlEfCAZJ6UDgwwcw" keySequence="CTRL+SHIFT+ARROW_DOWN" command="_rjtbEXHlEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_rjrm6XHlEfCAZJ6UDgwwcw" keySequence="ALT+SHIFT+ARROW_UP" command="_rjtb1HHlEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_rjrm6nHlEfCAZJ6UDgwwcw" keySequence="ALT+SHIFT+ARROW_DOWN" command="_rjtb6XHlEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_rjrm63HlEfCAZJ6UDgwwcw" keySequence="ALT+SHIFT+ARROW_LEFT" command="_rjtZ7nHlEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_rjrm7HHlEfCAZJ6UDgwwcw" keySequence="ALT+SHIFT+ARROW_RIGHT" command="_rjtbnHHlEfCAZJ6UDgwwcw"/>
  </bindingTables>
  <bindingTables xmi:id="_rjrm7XHlEfCAZJ6UDgwwcw" elementId="org.eclipse.compare.compareEditorScope" bindingContext="_rjrnrHHlEfCAZJ6UDgwwcw">
    <bindings xmi:id="_rjrm7nHlEfCAZJ6UDgwwcw" keySequence="ALT+CTRL+C" command="_rjtZ7HHlEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_rjrm73HlEfCAZJ6UDgwwcw" keySequence="ALT+CTRL+P" command="_rjtb7nHlEfCAZJ6UDgwwcw"/>
  </bindingTables>
  <bindingTables xmi:id="_rjrm8HHlEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.cViewScope" bindingContext="_rjrntnHlEfCAZJ6UDgwwcw">
    <bindings xmi:id="_rjrm8XHlEfCAZJ6UDgwwcw" keySequence="CTRL+SHIFT+T" command="_rjtahnHlEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_rjrm8nHlEfCAZJ6UDgwwcw" keySequence="CTRL+SHIFT+H" command="_rjtbmnHlEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_rjrm83HlEfCAZJ6UDgwwcw" keySequence="CTRL+SHIFT+G" command="_rjtZq3HlEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_rjrm9HHlEfCAZJ6UDgwwcw" keySequence="ALT+CTRL+H" command="_rjtabnHlEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_rjrm9XHlEfCAZJ6UDgwwcw" keySequence="ALT+CTRL+I" command="_rjtbc3HlEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_rjrm9nHlEfCAZJ6UDgwwcw" keySequence="ALT+SHIFT+R" command="_rjtcL3HlEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_rjrm93HlEfCAZJ6UDgwwcw" keySequence="CTRL+G" command="_rjtcw3HlEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_rjrm-HHlEfCAZJ6UDgwwcw" keySequence="F3" command="_rjtc9nHlEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_rjrm-XHlEfCAZJ6UDgwwcw" keySequence="F4" command="_rjtcn3HlEfCAZJ6UDgwwcw"/>
  </bindingTables>
  <bindingTables xmi:id="_rjrm-nHlEfCAZJ6UDgwwcw" elementId="org.eclipse.tm.terminal.EditContext" bindingContext="_rjrnknHlEfCAZJ6UDgwwcw">
    <bindings xmi:id="_rjrm-3HlEfCAZJ6UDgwwcw" keySequence="CTRL+SHIFT+V" command="_rjta33HlEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_rjrm_HHlEfCAZJ6UDgwwcw" keySequence="CTRL+SHIFT+C" command="_rjtcBHHlEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_rjrm_XHlEfCAZJ6UDgwwcw" keySequence="ALT+ARROW_UP" command="_rjtZrnHlEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_rjrm_nHlEfCAZJ6UDgwwcw" keySequence="ALT+ARROW_RIGHT" command="_rjtcmXHlEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_rjrm_3HlEfCAZJ6UDgwwcw" keySequence="SHIFT+INSERT" command="_rjta33HlEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_rjrnAHHlEfCAZJ6UDgwwcw" keySequence="CTRL+INSERT" command="_rjtcBHHlEfCAZJ6UDgwwcw"/>
  </bindingTables>
  <bindingTables xmi:id="_rjrnAXHlEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.tasks.ui.editors.task" bindingContext="_rjrnmHHlEfCAZJ6UDgwwcw">
    <bindings xmi:id="_rjrnAnHlEfCAZJ6UDgwwcw" keySequence="CTRL+SHIFT+M" command="_rjtZynHlEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_rjrnA3HlEfCAZJ6UDgwwcw" keySequence="ALT+SHIFT+C" command="_rjtbNXHlEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_rjrnBHHlEfCAZJ6UDgwwcw" keySequence="CTRL+O" command="_rjtcsHHlEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_rjrnBXHlEfCAZJ6UDgwwcw" keySequence="ALT+SHIFT+R" command="_rjta73HlEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_rjrnBnHlEfCAZJ6UDgwwcw" keySequence="ALT+SHIFT+S" command="_rjtasnHlEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_rjrnB3HlEfCAZJ6UDgwwcw" keySequence="ALT+SHIFT+U" command="_rjtb5XHlEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_rjrnCHHlEfCAZJ6UDgwwcw" keySequence="ALT+SHIFT+I" command="_rjta6XHlEfCAZJ6UDgwwcw"/>
  </bindingTables>
  <bindingTables xmi:id="_rjrnCXHlEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.make.ui.makefileEditorScope" bindingContext="_rjrnmXHlEfCAZJ6UDgwwcw">
    <bindings xmi:id="_rjrnCnHlEfCAZJ6UDgwwcw" keySequence="CTRL+/" command="_rjtbwnHlEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_rjrnC3HlEfCAZJ6UDgwwcw" keySequence="F3" command="_rjtbonHlEfCAZJ6UDgwwcw"/>
  </bindingTables>
  <bindingTables xmi:id="_rjrnDHHlEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.memoryview" bindingContext="_rjrnrnHlEfCAZJ6UDgwwcw">
    <bindings xmi:id="_rjrnDXHlEfCAZJ6UDgwwcw" keySequence="ALT+CTRL+M" command="_rjta_XHlEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_rjrnDnHlEfCAZJ6UDgwwcw" keySequence="ALT+CTRL+N" command="_rjtcqnHlEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_rjrnD3HlEfCAZJ6UDgwwcw" keySequence="CTRL+T" command="_rjtaeHHlEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_rjrnEHHlEfCAZJ6UDgwwcw" keySequence="CTRL+W" command="_rjtbjXHlEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_rjrnEXHlEfCAZJ6UDgwwcw" keySequence="CTRL+N" command="_rjtbsnHlEfCAZJ6UDgwwcw"/>
  </bindingTables>
  <bindingTables xmi:id="_rjrnEnHlEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.debugging" bindingContext="_rjrnr3HlEfCAZJ6UDgwwcw">
    <bindings xmi:id="_rjrnE3HlEfCAZJ6UDgwwcw" keySequence="CTRL+R" command="_rjtbTnHlEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_rjrnFHHlEfCAZJ6UDgwwcw" keySequence="F7" command="_rjtcv3HlEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_rjrnFXHlEfCAZJ6UDgwwcw" keySequence="F8" command="_rjtbgnHlEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_rjrnFnHlEfCAZJ6UDgwwcw" keySequence="F5" command="_rjtZ63HlEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_rjrnF3HlEfCAZJ6UDgwwcw" keySequence="F6" command="_rjta8XHlEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_rjrnGHHlEfCAZJ6UDgwwcw" keySequence="CTRL+F2" command="_rjtcK3HlEfCAZJ6UDgwwcw"/>
  </bindingTables>
  <bindingTables xmi:id="_rjrnGXHlEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.memory.abstractasynctablerendering" bindingContext="_rjrnsHHlEfCAZJ6UDgwwcw">
    <bindings xmi:id="_rjrnGnHlEfCAZJ6UDgwwcw" keySequence="CTRL+SHIFT+," command="_rjtcXnHlEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_rjrnG3HlEfCAZJ6UDgwwcw" keySequence="CTRL+SHIFT+." command="_rjtcHHHlEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_rjrnHHHlEfCAZJ6UDgwwcw" keySequence="CTRL+G" command="_rjtcHXHlEfCAZJ6UDgwwcw"/>
  </bindingTables>
  <bindingTables xmi:id="_rjrnHXHlEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.DiffViewer" bindingContext="_rjrnlXHlEfCAZJ6UDgwwcw">
    <bindings xmi:id="_rjrnHnHlEfCAZJ6UDgwwcw" keySequence="CTRL+O" command="_rjtbDXHlEfCAZJ6UDgwwcw"/>
  </bindingTables>
  <bindingTables xmi:id="_rjrnH3HlEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.wikitext.ui.editor.markupSourceContext" bindingContext="_rjrnm3HlEfCAZJ6UDgwwcw">
    <bindings xmi:id="_rjrnIHHlEfCAZJ6UDgwwcw" keySequence="CTRL+O" command="_rjtZ0XHlEfCAZJ6UDgwwcw"/>
  </bindingTables>
  <bindingTables xmi:id="_rjrnIXHlEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.RepositoriesView" bindingContext="_rjrnt3HlEfCAZJ6UDgwwcw">
    <bindings xmi:id="_rjrnInHlEfCAZJ6UDgwwcw" keySequence="CTRL+C" command="_rjtafHHlEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_rjrnI3HlEfCAZJ6UDgwwcw" keySequence="CTRL+ARROW_LEFT" command="_rjtaAnHlEfCAZJ6UDgwwcw"/>
  </bindingTables>
  <bindingTables xmi:id="_rjrnJHHlEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.ReflogView" bindingContext="_rjrns3HlEfCAZJ6UDgwwcw">
    <bindings xmi:id="_rjrnJXHlEfCAZJ6UDgwwcw" keySequence="CTRL+C" command="_rjtaF3HlEfCAZJ6UDgwwcw"/>
  </bindingTables>
  <bindingTables xmi:id="_rjrnJnHlEfCAZJ6UDgwwcw" elementId="org.eclipse.tracecompass.tmf.ui.view.timegraph.context" bindingContext="_rjrnv3HlEfCAZJ6UDgwwcw">
    <bindings xmi:id="_rjrnJ3HlEfCAZJ6UDgwwcw" keySequence="CTRL+D" command="_rjtbaXHlEfCAZJ6UDgwwcw"/>
  </bindingTables>
  <bindingTables xmi:id="_rjrnKHHlEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.dsf.debug.ui.disassembly.context" bindingContext="_rjrnsXHlEfCAZJ6UDgwwcw">
    <bindings xmi:id="_rjrnKXHlEfCAZJ6UDgwwcw" keySequence="CTRL+G" command="_rjtcy3HlEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_rjrnKnHlEfCAZJ6UDgwwcw" keySequence="HOME" command="_rjtad3HlEfCAZJ6UDgwwcw"/>
  </bindingTables>
  <bindingTables xmi:id="_rjrnK3HlEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.console" bindingContext="_rjrnqnHlEfCAZJ6UDgwwcw">
    <bindings xmi:id="_rjrnLHHlEfCAZJ6UDgwwcw" keySequence="CTRL+Z" command="_rjtcs3HlEfCAZJ6UDgwwcw">
      <tags>platform:win32</tags>
    </bindings>
  </bindingTables>
  <bindingTables xmi:id="_rjrnLXHlEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.debug.ui.debugging" bindingContext="_rjrnsnHlEfCAZJ6UDgwwcw">
    <bindings xmi:id="_rjrnLnHlEfCAZJ6UDgwwcw" keySequence="SHIFT+F7" command="_rjtboXHlEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_rjrnL3HlEfCAZJ6UDgwwcw" keySequence="SHIFT+F8" command="_rjtcGnHlEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_rjrnMHHlEfCAZJ6UDgwwcw" keySequence="SHIFT+F5" command="_rjta8nHlEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_rjrnMXHlEfCAZJ6UDgwwcw" keySequence="SHIFT+F6" command="_rjtZ43HlEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_rjrnMnHlEfCAZJ6UDgwwcw" keySequence="CTRL+F5" command="_rjtck3HlEfCAZJ6UDgwwcw"/>
  </bindingTables>
  <bindingTables xmi:id="_rjrnM3HlEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.macroExpansionHoverScope" bindingContext="_rjrnunHlEfCAZJ6UDgwwcw">
    <bindings xmi:id="_rjrnNHHlEfCAZJ6UDgwwcw" keySequence="ALT+ARROW_LEFT" command="_rjtb3nHlEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_rjrnNXHlEfCAZJ6UDgwwcw" keySequence="ALT+ARROW_RIGHT" command="_rjtb8nHlEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_rjrnNnHlEfCAZJ6UDgwwcw" keySequence="F3" command="_rjtc9nHlEfCAZJ6UDgwwcw"/>
  </bindingTables>
  <bindingTables xmi:id="_rjrnN3HlEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.internal.wikitext.ui.editor.basicMarkupSourceContext" bindingContext="_rjrnmnHlEfCAZJ6UDgwwcw">
    <bindings xmi:id="_rjrnOHHlEfCAZJ6UDgwwcw" keySequence="F1" command="_rjtZuHHlEfCAZJ6UDgwwcw"/>
  </bindingTables>
  <bindingTables xmi:id="_rjrnOXHlEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.RepositoriesView.SingleRepository" bindingContext="_rjrnuHHlEfCAZJ6UDgwwcw">
    <bindings xmi:id="_rjrnOnHlEfCAZJ6UDgwwcw" keySequence="F2" command="_rjtaHXHlEfCAZJ6UDgwwcw"/>
  </bindingTables>
  <bindingTables xmi:id="_rjrnO3HlEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.asmEditorScope" bindingContext="_rjrnlnHlEfCAZJ6UDgwwcw">
    <bindings xmi:id="_rjrnPHHlEfCAZJ6UDgwwcw" keySequence="F3" command="_rjtc9nHlEfCAZJ6UDgwwcw"/>
  </bindingTables>
  <bindingTables xmi:id="_rjrnPXHlEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.tmf.ui.view.uml2sd.context" bindingContext="_rjrnvHHlEfCAZJ6UDgwwcw">
    <bindings xmi:id="_rjrnPnHlEfCAZJ6UDgwwcw" keySequence="ALT+SHIFT+ARROW_UP" command="_rjtawXHlEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_rjrnP3HlEfCAZJ6UDgwwcw" keySequence="ALT+SHIFT+ARROW_DOWN" command="_rjtZyHHlEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_rjrnQHHlEfCAZJ6UDgwwcw" keySequence="ALT+SHIFT+ARROW_LEFT" command="_rjtbN3HlEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_rjrnQXHlEfCAZJ6UDgwwcw" keySequence="ALT+SHIFT+ARROW_RIGHT" command="_rjtbkHHlEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_rjrnQnHlEfCAZJ6UDgwwcw" keySequence="ALT+SHIFT+HOME" command="_rjtctnHlEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_rjrnQ3HlEfCAZJ6UDgwwcw" keySequence="ALT+SHIFT+END" command="_rjtanXHlEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_rjrnRHHlEfCAZJ6UDgwwcw" keySequence="ALT+R" command="_rjtcI3HlEfCAZJ6UDgwwcw"/>
  </bindingTables>
  <bindingTables xmi:id="_rjrnRXHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.console.ConsoleView" bindingContext="_rjrnrXHlEfCAZJ6UDgwwcw">
    <bindings xmi:id="_rjrnRnHlEfCAZJ6UDgwwcw" keySequence="CTRL+INSERT" command="_rjtbpnHlEfCAZJ6UDgwwcw"/>
  </bindingTables>
  <bindingTables xmi:id="_rjrnR3HlEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.changelog.core.changelogEditorScope" bindingContext="_rjrnnHHlEfCAZJ6UDgwwcw">
    <bindings xmi:id="_rjrnSHHlEfCAZJ6UDgwwcw" keySequence="ESC CTRL+F" command="_rjtbfXHlEfCAZJ6UDgwwcw"/>
  </bindingTables>
  <bindingTables xmi:id="_rjrnSXHlEfCAZJ6UDgwwcw" elementId="org.eclipse.tracecompass.tmf.ui.view.context" bindingContext="_rjrnvnHlEfCAZJ6UDgwwcw">
    <bindings xmi:id="_rjrnSnHlEfCAZJ6UDgwwcw" keySequence="Z" command="_rjtbBXHlEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_rjrnS3HlEfCAZJ6UDgwwcw" keySequence="+" command="_rjtZrHHlEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_rjrnTHHlEfCAZJ6UDgwwcw" keySequence="-" command="_rjtZ8HHlEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_rjrnTXHlEfCAZJ6UDgwwcw" keySequence="/" command="_rjtaTHHlEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_rjrnTnHlEfCAZJ6UDgwwcw" keySequence="S" command="_rjtar3HlEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_rjrnT3HlEfCAZJ6UDgwwcw" keySequence="W" command="_rjtbEnHlEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_rjrnUHHlEfCAZJ6UDgwwcw" keySequence="A" command="_rjtaenHlEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_rjrnUXHlEfCAZJ6UDgwwcw" keySequence="D" command="_rjtbF3HlEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_rjrnUnHlEfCAZJ6UDgwwcw" keySequence="=" command="_rjtZrHHlEfCAZJ6UDgwwcw"/>
  </bindingTables>
  <bindingTables xmi:id="_rjrnU3HlEfCAZJ6UDgwwcw" elementId="org.eclipse.tm.terminal.TerminalContext" bindingContext="_rjrntHHlEfCAZJ6UDgwwcw">
    <bindings xmi:id="_rjrnVHHlEfCAZJ6UDgwwcw" keySequence="ALT+Y" command="_rjtbV3HlEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_rjrnVXHlEfCAZJ6UDgwwcw" keySequence="ALT+A" command="_rjtbV3HlEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_rjrnVnHlEfCAZJ6UDgwwcw" keySequence="ALT+B" command="_rjtbV3HlEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_rjrnV3HlEfCAZJ6UDgwwcw" keySequence="ALT+C" command="_rjtbV3HlEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_rjrnWHHlEfCAZJ6UDgwwcw" keySequence="ALT+D" command="_rjtbV3HlEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_rjrnWXHlEfCAZJ6UDgwwcw" keySequence="ALT+E" command="_rjtbV3HlEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_rjrnWnHlEfCAZJ6UDgwwcw" keySequence="ALT+F" command="_rjtbV3HlEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_rjrnW3HlEfCAZJ6UDgwwcw" keySequence="ALT+G" command="_rjtbV3HlEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_rjrnXHHlEfCAZJ6UDgwwcw" keySequence="ALT+P" command="_rjtbV3HlEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_rjrnXXHlEfCAZJ6UDgwwcw" keySequence="ALT+R" command="_rjtbV3HlEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_rjrnXnHlEfCAZJ6UDgwwcw" keySequence="ALT+S" command="_rjtbV3HlEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_rjrnX3HlEfCAZJ6UDgwwcw" keySequence="ALT+T" command="_rjtbV3HlEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_rjrnYHHlEfCAZJ6UDgwwcw" keySequence="ALT+V" command="_rjtbV3HlEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_rjrnYXHlEfCAZJ6UDgwwcw" keySequence="ALT+W" command="_rjtbV3HlEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_rjrnYnHlEfCAZJ6UDgwwcw" keySequence="ALT+H" command="_rjtbV3HlEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_rjrnY3HlEfCAZJ6UDgwwcw" keySequence="ALT+L" command="_rjtbV3HlEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_rjrnZHHlEfCAZJ6UDgwwcw" keySequence="ALT+N" command="_rjtbV3HlEfCAZJ6UDgwwcw"/>
  </bindingTables>
  <bindingTables xmi:id="_rjrnZXHlEfCAZJ6UDgwwcw" bindingContext="_rjrnwHHlEfCAZJ6UDgwwcw"/>
  <bindingTables xmi:id="_rjrnZnHlEfCAZJ6UDgwwcw" bindingContext="_rjrnwXHlEfCAZJ6UDgwwcw"/>
  <bindingTables xmi:id="_rjrnZ3HlEfCAZJ6UDgwwcw" bindingContext="_rjrnwnHlEfCAZJ6UDgwwcw"/>
  <bindingTables xmi:id="_rjrnaHHlEfCAZJ6UDgwwcw" bindingContext="_rjrnw3HlEfCAZJ6UDgwwcw"/>
  <bindingTables xmi:id="_rjrnaXHlEfCAZJ6UDgwwcw" bindingContext="_rjrnxHHlEfCAZJ6UDgwwcw"/>
  <bindingTables xmi:id="_rjrnanHlEfCAZJ6UDgwwcw" bindingContext="_rjrnxXHlEfCAZJ6UDgwwcw"/>
  <bindingTables xmi:id="_rjrna3HlEfCAZJ6UDgwwcw" bindingContext="_rjrnxnHlEfCAZJ6UDgwwcw"/>
  <bindingTables xmi:id="_rjrnbHHlEfCAZJ6UDgwwcw" bindingContext="_rjrnx3HlEfCAZJ6UDgwwcw"/>
  <bindingTables xmi:id="_rjrnbXHlEfCAZJ6UDgwwcw" bindingContext="_rjrnyHHlEfCAZJ6UDgwwcw"/>
  <bindingTables xmi:id="_rjrnbnHlEfCAZJ6UDgwwcw" bindingContext="_rjrnyXHlEfCAZJ6UDgwwcw"/>
  <bindingTables xmi:id="_rjrnb3HlEfCAZJ6UDgwwcw" bindingContext="_rjrnynHlEfCAZJ6UDgwwcw"/>
  <bindingTables xmi:id="_rjrncHHlEfCAZJ6UDgwwcw" bindingContext="_rjrny3HlEfCAZJ6UDgwwcw"/>
  <bindingTables xmi:id="_rjrncXHlEfCAZJ6UDgwwcw" bindingContext="_rjrnzHHlEfCAZJ6UDgwwcw"/>
  <bindingTables xmi:id="_rjrncnHlEfCAZJ6UDgwwcw" bindingContext="_rjrnzXHlEfCAZJ6UDgwwcw"/>
  <bindingTables xmi:id="_rjrnc3HlEfCAZJ6UDgwwcw" bindingContext="_rjrnznHlEfCAZJ6UDgwwcw"/>
  <bindingTables xmi:id="_rjrndHHlEfCAZJ6UDgwwcw" bindingContext="_rjrnz3HlEfCAZJ6UDgwwcw"/>
  <bindingTables xmi:id="_rjrndXHlEfCAZJ6UDgwwcw" bindingContext="_rjrn0HHlEfCAZJ6UDgwwcw"/>
  <bindingTables xmi:id="_rjrndnHlEfCAZJ6UDgwwcw" bindingContext="_rjrn0XHlEfCAZJ6UDgwwcw"/>
  <bindingTables xmi:id="_rjrnd3HlEfCAZJ6UDgwwcw" bindingContext="_rjrn0nHlEfCAZJ6UDgwwcw"/>
  <bindingTables xmi:id="_rjrneHHlEfCAZJ6UDgwwcw" bindingContext="_rjrn03HlEfCAZJ6UDgwwcw"/>
  <bindingTables xmi:id="_rjrneXHlEfCAZJ6UDgwwcw" bindingContext="_rjrn1HHlEfCAZJ6UDgwwcw"/>
  <bindingTables xmi:id="_rjrnenHlEfCAZJ6UDgwwcw" bindingContext="_rjrn1XHlEfCAZJ6UDgwwcw"/>
  <bindingTables xmi:id="_rjrne3HlEfCAZJ6UDgwwcw" bindingContext="_rjrn1nHlEfCAZJ6UDgwwcw"/>
  <bindingTables xmi:id="_rjrnfHHlEfCAZJ6UDgwwcw" bindingContext="_rjrn13HlEfCAZJ6UDgwwcw"/>
  <bindingTables xmi:id="_rjrnfXHlEfCAZJ6UDgwwcw" bindingContext="_rjrn2HHlEfCAZJ6UDgwwcw"/>
  <bindingTables xmi:id="_rjrnfnHlEfCAZJ6UDgwwcw" bindingContext="_rjrn2XHlEfCAZJ6UDgwwcw"/>
  <bindingTables xmi:id="_rjrnf3HlEfCAZJ6UDgwwcw" bindingContext="_rjrn2nHlEfCAZJ6UDgwwcw"/>
  <bindingTables xmi:id="_rjrngHHlEfCAZJ6UDgwwcw" bindingContext="_rjrn23HlEfCAZJ6UDgwwcw"/>
  <bindingTables xmi:id="_rjrngXHlEfCAZJ6UDgwwcw" bindingContext="_rjrn3HHlEfCAZJ6UDgwwcw"/>
  <bindingTables xmi:id="_rjrngnHlEfCAZJ6UDgwwcw" bindingContext="_rjrn3XHlEfCAZJ6UDgwwcw"/>
  <bindingTables xmi:id="_rjrng3HlEfCAZJ6UDgwwcw" bindingContext="_rjrn3nHlEfCAZJ6UDgwwcw"/>
  <bindingTables xmi:id="_rjrnhHHlEfCAZJ6UDgwwcw" bindingContext="_rjrn33HlEfCAZJ6UDgwwcw"/>
  <bindingTables xmi:id="_rjrnhXHlEfCAZJ6UDgwwcw" bindingContext="_rjrn4HHlEfCAZJ6UDgwwcw"/>
  <bindingTables xmi:id="_rjrnhnHlEfCAZJ6UDgwwcw" bindingContext="_rjrn4XHlEfCAZJ6UDgwwcw"/>
  <bindingTables xmi:id="_rjrnh3HlEfCAZJ6UDgwwcw" bindingContext="_rjrn4nHlEfCAZJ6UDgwwcw"/>
  <bindingTables xmi:id="_rjrniHHlEfCAZJ6UDgwwcw" bindingContext="_rjrn43HlEfCAZJ6UDgwwcw"/>
  <bindingTables xmi:id="_rjrniXHlEfCAZJ6UDgwwcw" bindingContext="_rjrn5HHlEfCAZJ6UDgwwcw"/>
  <bindingTables xmi:id="_rjrninHlEfCAZJ6UDgwwcw" bindingContext="_rjrn5XHlEfCAZJ6UDgwwcw"/>
  <bindingTables xmi:id="_rjrni3HlEfCAZJ6UDgwwcw" bindingContext="_rjrn5nHlEfCAZJ6UDgwwcw"/>
  <bindingTables xmi:id="_rjrnjHHlEfCAZJ6UDgwwcw" bindingContext="_rjrn53HlEfCAZJ6UDgwwcw"/>
  <bindingTables xmi:id="_rjrnjXHlEfCAZJ6UDgwwcw" bindingContext="_rjrn6HHlEfCAZJ6UDgwwcw"/>
  <bindingTables xmi:id="_rjrnjnHlEfCAZJ6UDgwwcw" bindingContext="_rjrn6XHlEfCAZJ6UDgwwcw"/>
  <rootContext xmi:id="_rjrnj3HlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.contexts.dialogAndWindow" contributorURI="platform:/plugin/org.eclipse.platform" name="In Dialogs and Windows" description="Either a dialog or a window is open">
    <children xmi:id="_rjrnkHHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.contexts.window" contributorURI="platform:/plugin/org.eclipse.platform" name="In Windows" description="A window is open">
      <children xmi:id="_rjrnkXHlEfCAZJ6UDgwwcw" elementId="org.eclipse.e4.ui.contexts.views" contributorURI="platform:/plugin/org.eclipse.platform" name="%bindingcontext.name.bindingView"/>
      <children xmi:id="_rjrnknHlEfCAZJ6UDgwwcw" elementId="org.eclipse.tm.terminal.EditContext" name="Terminal Control in Focus" description="Show modified keyboard shortcuts in context menu"/>
      <children xmi:id="_rjrnk3HlEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.BreakpointView" name="In Breakpoints View" description="The breakpoints view context"/>
      <children xmi:id="_rjrnlHHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.textEditorScope" name="Editing Text" description="Editing Text Context">
        <children xmi:id="_rjrnlXHlEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.DiffViewer" name="In Diff Viewer"/>
        <children xmi:id="_rjrnlnHlEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.asmEditorScope" name="Assembly Editor" description="Editor for Assembly Source Files"/>
        <children xmi:id="_rjrnl3HlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.genericeditor.genericEditorContext" name="in Generic Code Editor" description="When editing in the Generic Code Editor"/>
        <children xmi:id="_rjrnmHHlEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.tasks.ui.editors.task" name="In Tasks Editor"/>
        <children xmi:id="_rjrnmXHlEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.make.ui.makefileEditorScope" name="Makefile Editor" description="Editor for makefiles"/>
        <children xmi:id="_rjrnmnHlEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.internal.wikitext.ui.editor.basicMarkupSourceContext" name="WikiText Markup Source Context" description="WikiText markup editing context">
          <children xmi:id="_rjrnm3HlEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.wikitext.ui.editor.markupSourceContext" name="WikiText Markup Source Context" description="WikiText markup editing context"/>
        </children>
        <children xmi:id="_rjrnnHHlEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.changelog.core.changelogEditorScope" name="ChangeLog Editor"/>
        <children xmi:id="_rjrnnXHlEfCAZJ6UDgwwcw" elementId="org.eclipse.wst.sse.ui.structuredTextEditorScope" name="Editing in Structured Text Editors" description="Editing in Structured Text Editors">
          <children xmi:id="_rjrnnnHlEfCAZJ6UDgwwcw" elementId="org.eclipse.wst.xml.navigation" name="XML Source Navigation" description="XML Source Navigation"/>
          <children xmi:id="_rjrnn3HlEfCAZJ6UDgwwcw" elementId="org.eclipse.wst.xml.cleanup" name="XML Source Cleanup" description="XML Source Cleanup"/>
          <children xmi:id="_rjrnoHHlEfCAZJ6UDgwwcw" elementId="org.eclipse.wst.sse.comments" name="Source Comments in Structured Text Editors" description="Source Comments in Structured Text Editors"/>
          <children xmi:id="_rjrnoXHlEfCAZJ6UDgwwcw" elementId="org.eclipse.core.runtime.xml" name="Editing XML Source" description="Editing XML Source"/>
          <children xmi:id="_rjrnonHlEfCAZJ6UDgwwcw" elementId="org.eclipse.wst.xml.occurrences" name="XML Source Occurrences" description="XML Source Occurrences"/>
          <children xmi:id="_rjrno3HlEfCAZJ6UDgwwcw" elementId="org.eclipse.wst.xml.grammar" name="XML Source Grammar" description="XML Source Grammar"/>
          <children xmi:id="_rjrnpHHlEfCAZJ6UDgwwcw" elementId="org.eclipse.wst.xml.comments" name="XML Source Comments" description="XML Source Comments"/>
          <children xmi:id="_rjrnpXHlEfCAZJ6UDgwwcw" elementId="org.eclipse.wst.xml.expand" name="XML Source Expand/Collapse" description="XML Source Expand/Collapse"/>
          <children xmi:id="_rjrnpnHlEfCAZJ6UDgwwcw" elementId="org.eclipse.wst.sse.hideFormat" name="Editing in Structured Text Editors" description="Editing in Structured Text Editors"/>
          <children xmi:id="_rjrnp3HlEfCAZJ6UDgwwcw" elementId="org.eclipse.wst.xml.selection" name="XML Source Selection" description="XML Source Selection"/>
          <children xmi:id="_rjrnqHHlEfCAZJ6UDgwwcw" elementId="org.eclipse.wst.xml.dependencies" name="XML Source Dependencies" description="XML Source Dependencies"/>
        </children>
        <children xmi:id="_rjrnqXHlEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.cEditorScope" name="C/C++ Editor" description="Editor for C/C++ Source Files"/>
      </children>
      <children xmi:id="_rjrnqnHlEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.console" name="In I/O Console" description="In I/O console"/>
      <children xmi:id="_rjrnq3HlEfCAZJ6UDgwwcw" elementId="org.eclipse.tm.terminal.view.ui.TerminalsView" name="In Terminal View" description="Show modified keyboard shortcuts in context menu"/>
      <children xmi:id="_rjrnrHHlEfCAZJ6UDgwwcw" elementId="org.eclipse.compare.compareEditorScope" name="Comparing in an Editor" description="Comparing in an Editor"/>
      <children xmi:id="_rjrnrXHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.console.ConsoleView" name="In Console View" description="In Console View"/>
      <children xmi:id="_rjrnrnHlEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.memoryview" name="In Memory View" description="In memory view"/>
      <children xmi:id="_rjrnr3HlEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.debugging" name="Debugging" description="Debugging programs">
        <children xmi:id="_rjrnsHHlEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.memory.abstractasynctablerendering" name="In Table Memory Rendering" description="In Table Memory Rendering"/>
        <children xmi:id="_rjrnsXHlEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.dsf.debug.ui.disassembly.context" name="In Disassembly" description="When debugging in assembly mode"/>
        <children xmi:id="_rjrnsnHlEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.debug.ui.debugging" name="Debugging C/C++" description="Debugging C/C++ Programs"/>
      </children>
      <children xmi:id="_rjrns3HlEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.ReflogView" name="In Git Reflog View"/>
      <children xmi:id="_rjrntHHlEfCAZJ6UDgwwcw" elementId="org.eclipse.tm.terminal.TerminalContext" name="Terminal Typing Connected" description="Override ALT+x menu access keys while typing into the Terminal"/>
      <children xmi:id="_rjrntXHlEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.tasks.ui.views.tasks" name="In Tasks View"/>
      <children xmi:id="_rjrntnHlEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.cViewScope" name="In C/C++ Views" description="In C/C++ Views"/>
      <children xmi:id="_rjrnt3HlEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.RepositoriesView" name="In Git Repositories View">
        <children xmi:id="_rjrnuHHlEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.RepositoriesView.SingleRepository" name="In Git Repositories View"/>
      </children>
    </children>
    <children xmi:id="_rjrnuXHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.contexts.dialog" contributorURI="platform:/plugin/org.eclipse.platform" name="In Dialogs" description="A dialog is open"/>
    <children xmi:id="_rjrnunHlEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.macroExpansionHoverScope" name="In Macro Expansion Hover" description="In Macro Expansion Hover"/>
  </rootContext>
  <rootContext xmi:id="_rjrnu3HlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.contexts.actionSet" name="Action Set" description="Parent context for action sets"/>
  <rootContext xmi:id="_rjrnvHHlEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.tmf.ui.view.uml2sd.context" name="UML2 Sequence Diagram Viewer"/>
  <rootContext xmi:id="_rjrnvXHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.contexts.workbenchMenu" name="Workbench Menu" description="When no Workbench windows are active"/>
  <rootContext xmi:id="_rjrnvnHlEfCAZJ6UDgwwcw" elementId="org.eclipse.tracecompass.tmf.ui.view.context" name="In Time-Based View"/>
  <rootContext xmi:id="_rjrnv3HlEfCAZJ6UDgwwcw" elementId="org.eclipse.tracecompass.tmf.ui.view.timegraph.context" name="In Time Graph"/>
  <rootContext xmi:id="_rjrnwHHlEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.debug.ui.debugActionSet" name="Auto::org.eclipse.cdt.debug.ui.debugActionSet"/>
  <rootContext xmi:id="_rjrnwXHlEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.debug.ui.reverseDebuggingActionSet" name="Auto::org.eclipse.cdt.debug.ui.reverseDebuggingActionSet"/>
  <rootContext xmi:id="_rjrnwnHlEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.debug.ui.tracepointActionSet" name="Auto::org.eclipse.cdt.debug.ui.tracepointActionSet"/>
  <rootContext xmi:id="_rjrnw3HlEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.debug.ui.debugViewLayoutActionSet" name="Auto::org.eclipse.cdt.debug.ui.debugViewLayoutActionSet"/>
  <rootContext xmi:id="_rjrnxHHlEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.dsf.debug.ui.updateModes" name="Auto::org.eclipse.cdt.dsf.debug.ui.updateModes"/>
  <rootContext xmi:id="_rjrnxXHlEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.make.ui.updateActionSet" name="Auto::org.eclipse.cdt.make.ui.updateActionSet"/>
  <rootContext xmi:id="_rjrnxnHlEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.make.ui.makeTargetActionSet" name="Auto::org.eclipse.cdt.make.ui.makeTargetActionSet"/>
  <rootContext xmi:id="_rjrnx3HlEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.cdt.ui.actionSet" name="Auto::org.eclipse.mylyn.cdt.ui.actionSet"/>
  <rootContext xmi:id="_rjrnyHHlEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.CodingActionSet" name="Auto::org.eclipse.cdt.ui.CodingActionSet"/>
  <rootContext xmi:id="_rjrnyXHlEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.SearchActionSet" name="Auto::org.eclipse.cdt.ui.SearchActionSet"/>
  <rootContext xmi:id="_rjrnynHlEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.NavigationActionSet" name="Auto::org.eclipse.cdt.ui.NavigationActionSet"/>
  <rootContext xmi:id="_rjrny3HlEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.OpenActionSet" name="Auto::org.eclipse.cdt.ui.OpenActionSet"/>
  <rootContext xmi:id="_rjrnzHHlEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.buildConfigActionSet" name="Auto::org.eclipse.cdt.ui.buildConfigActionSet"/>
  <rootContext xmi:id="_rjrnzXHlEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.CElementCreationActionSet" name="Auto::org.eclipse.cdt.ui.CElementCreationActionSet"/>
  <rootContext xmi:id="_rjrnznHlEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.text.c.actionSet.presentation" name="Auto::org.eclipse.cdt.ui.text.c.actionSet.presentation"/>
  <rootContext xmi:id="_rjrnz3HlEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.breakpointActionSet" name="Auto::org.eclipse.debug.ui.breakpointActionSet"/>
  <rootContext xmi:id="_rjrn0HHlEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.debugActionSet" name="Auto::org.eclipse.debug.ui.debugActionSet"/>
  <rootContext xmi:id="_rjrn0XHlEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.launchActionSet" name="Auto::org.eclipse.debug.ui.launchActionSet"/>
  <rootContext xmi:id="_rjrn0nHlEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.profileActionSet" name="Auto::org.eclipse.debug.ui.profileActionSet"/>
  <rootContext xmi:id="_rjrn03HlEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.gitaction" name="Auto::org.eclipse.egit.ui.gitaction"/>
  <rootContext xmi:id="_rjrn1HHlEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.navigation" name="Auto::org.eclipse.egit.ui.navigation"/>
  <rootContext xmi:id="_rjrn1XHlEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.SearchActionSet" name="Auto::org.eclipse.egit.ui.SearchActionSet"/>
  <rootContext xmi:id="_rjrn1nHlEfCAZJ6UDgwwcw" elementId="org.eclipse.embedcdt.debug.gdbjtag.restart.ui.actionset" name="Auto::org.eclipse.embedcdt.debug.gdbjtag.restart.ui.actionset"/>
  <rootContext xmi:id="_rjrn13HlEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.context.ui.actionSet" name="Auto::org.eclipse.mylyn.context.ui.actionSet"/>
  <rootContext xmi:id="_rjrn2HHlEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.doc.actionSet" name="Auto::org.eclipse.mylyn.doc.actionSet"/>
  <rootContext xmi:id="_rjrn2XHlEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.tasks.ui.navigation" name="Auto::org.eclipse.mylyn.tasks.ui.navigation"/>
  <rootContext xmi:id="_rjrn2nHlEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.tasks.ui.navigation.additions" name="Auto::org.eclipse.mylyn.tasks.ui.navigation.additions"/>
  <rootContext xmi:id="_rjrn23HlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.cheatsheets.actionSet" name="Auto::org.eclipse.ui.cheatsheets.actionSet"/>
  <rootContext xmi:id="_rjrn3HHlEfCAZJ6UDgwwcw" elementId="org.eclipse.search.searchActionSet" name="Auto::org.eclipse.search.searchActionSet"/>
  <rootContext xmi:id="_rjrn3XHlEfCAZJ6UDgwwcw" elementId="org.eclipse.team.ui.actionSet" name="Auto::org.eclipse.team.ui.actionSet"/>
  <rootContext xmi:id="_rjrn3nHlEfCAZJ6UDgwwcw" elementId="org.eclipse.text.quicksearch.actionSet" name="Auto::org.eclipse.text.quicksearch.actionSet"/>
  <rootContext xmi:id="_rjrn33HlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.actionSet.annotationNavigation" name="Auto::org.eclipse.ui.edit.text.actionSet.annotationNavigation"/>
  <rootContext xmi:id="_rjrn4HHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.actionSet.navigation" name="Auto::org.eclipse.ui.edit.text.actionSet.navigation"/>
  <rootContext xmi:id="_rjrn4XHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.actionSet.convertLineDelimitersTo" name="Auto::org.eclipse.ui.edit.text.actionSet.convertLineDelimitersTo"/>
  <rootContext xmi:id="_rjrn4nHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.externaltools.ExternalToolsSet" name="Auto::org.eclipse.ui.externaltools.ExternalToolsSet"/>
  <rootContext xmi:id="_rjrn43HlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.NavigateActionSet" name="Auto::org.eclipse.ui.NavigateActionSet"/>
  <rootContext xmi:id="_rjrn5HHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.actionSet.keyBindings" name="Auto::org.eclipse.ui.actionSet.keyBindings"/>
  <rootContext xmi:id="_rjrn5XHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.WorkingSetModificationActionSet" name="Auto::org.eclipse.ui.WorkingSetModificationActionSet"/>
  <rootContext xmi:id="_rjrn5nHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.WorkingSetActionSet" name="Auto::org.eclipse.ui.WorkingSetActionSet"/>
  <rootContext xmi:id="_rjrn53HlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.actionSet.openFiles" name="Auto::org.eclipse.ui.actionSet.openFiles"/>
  <rootContext xmi:id="_rjrn6HHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.actionSet.presentation" name="Auto::org.eclipse.ui.edit.text.actionSet.presentation"/>
  <rootContext xmi:id="_rjrn6XHlEfCAZJ6UDgwwcw" elementId="org.riscvstudio.debug.gdbjtag.qemu.riscv.launchConfigurationType.internal.org.eclipse.debug.ui.DebugPerspective" name="Auto::org.riscvstudio.debug.gdbjtag.qemu.riscv.launchConfigurationType.internal.org.eclipse.debug.ui.DebugPerspective"/>
  <descriptors xmi:id="_rjrn6nHlEfCAZJ6UDgwwcw" elementId="org.eclipse.e4.ui.compatibility.editor" allowMultiple="true" category="org.eclipse.e4.primaryDataStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor">
    <tags>Editor</tags>
    <tags>removeOnHide</tags>
  </descriptors>
  <descriptors xmi:id="_rjrn63HlEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.codan.internal.ui.views.ProblemDetails" label="Problem Details" iconURI="platform:/plugin/org.eclipse.cdt.codan.ui/icons/edit_bug.gif" tooltip="" category="C/C++" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.codan.internal.ui.views.ProblemDetails"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.codan.ui"/>
    <tags>View</tags>
    <tags>categoryTag:C/C++</tags>
  </descriptors>
  <descriptors xmi:id="_rjrn7HHlEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.debug.ui.executablesView" label="Executables" iconURI="platform:/plugin/org.eclipse.cdt.debug.ui/icons/obj16/exec_view_obj.gif" tooltip="" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.debug.internal.ui.views.executables.ExecutablesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_rjrn7XHlEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.debug.ui.SignalsView" label="Signals" iconURI="platform:/plugin/org.eclipse.cdt.debug.ui/icons/view16/signals_view.gif" tooltip="" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.debug.internal.ui.views.signals.FlexibleSignalsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_rjrn7nHlEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.debug.ui.debuggerConsoleView" label="Debugger Console" iconURI="platform:/plugin/org.eclipse.cdt.debug.ui/icons/view16/debugger_console_view.png" tooltip="" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.debug.internal.ui.views.debuggerconsole.DebuggerConsoleView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_rjrn73HlEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.debug.ui.memory.memorybrowser.MemoryBrowser" label="Memory Browser" iconURI="platform:/plugin/org.eclipse.cdt.debug.ui.memory.memorybrowser/icons/memorybrowser_view.gif" tooltip="" allowMultiple="true" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.debug.ui.memory.memorybrowser.MemoryBrowser"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.debug.ui.memory.memorybrowser"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_rjrn8HHlEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.dsf.gdb.ui.tracecontrol.view" label="Trace Control" iconURI="platform:/plugin/org.eclipse.cdt.dsf.gdb.ui/icons/full/view16/tracecontrol_view.gif" tooltip="" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.dsf.gdb.internal.ui.tracepoints.TraceControlView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.dsf.gdb.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_rjrn8XHlEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.dsf.gdb.ui.osresources.view" label="OS Resources" iconURI="platform:/plugin/org.eclipse.cdt.dsf.gdb.ui/icons/full/view16/osresources_view.gif" tooltip="" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.dsf.gdb.internal.ui.osview.OSResourcesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.dsf.gdb.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_rjrn8nHlEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.dsf.gdb.ui.debugsources.view" label="Debug Sources" iconURI="platform:/plugin/org.eclipse.cdt.dsf.gdb.ui/icons/full/view16/debugsources_view.gif" tooltip="" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.dsf.gdb.internal.ui.debugsources.DebugSourcesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.dsf.gdb.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_rjrn83HlEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.dsf.debug.ui.disassembly.view" label="Disassembly" iconURI="platform:/plugin/org.eclipse.cdt.dsf.ui/icons/disassembly.gif" tooltip="" allowMultiple="true" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.dsf.debug.internal.ui.disassembly.DisassemblyView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.dsf.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_rjrn9HHlEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.make.ui.views.MakeView" label="Build Targets" iconURI="platform:/plugin/org.eclipse.cdt.make.ui/icons/view16/make_target.gif" tooltip="" category="Make" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.make.ui.views.MakeView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.make.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Make</tags>
  </descriptors>
  <descriptors xmi:id="_rjrn9XHlEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.testsrunner.resultsview" label="C/C++ Unit" iconURI="platform:/plugin/org.eclipse.cdt.testsrunner/icons/eview16/cppunit.gif" tooltip="" category="C/C++" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.testsrunner.internal.ui.view.ResultsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.testsrunner"/>
    <tags>View</tags>
    <tags>categoryTag:C/C++</tags>
  </descriptors>
  <descriptors xmi:id="_rjrn9nHlEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.CView" label="C/C++ Projects" iconURI="platform:/plugin/org.eclipse.cdt.ui/icons/view16/cview.gif" tooltip="" category="C/C++" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.internal.ui.cview.CView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.ui"/>
    <tags>View</tags>
    <tags>categoryTag:C/C++</tags>
  </descriptors>
  <descriptors xmi:id="_rjrn93HlEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.IndexView" label="C/C++ Index" iconURI="platform:/plugin/org.eclipse.cdt.ui/icons/view16/types.gif" tooltip="" category="C/C++" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.internal.ui.indexview.IndexView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.ui"/>
    <tags>View</tags>
    <tags>categoryTag:C/C++</tags>
  </descriptors>
  <descriptors xmi:id="_rjrn-HHlEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.includeBrowser" label="Include Browser" iconURI="platform:/plugin/org.eclipse.cdt.ui/icons/view16/includeBrowser.gif" tooltip="" category="C/C++" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.internal.ui.includebrowser.IBViewPart"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.ui"/>
    <tags>View</tags>
    <tags>categoryTag:C/C++</tags>
  </descriptors>
  <descriptors xmi:id="_rjrn-XHlEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.callHierarchy" label="Call Hierarchy" iconURI="platform:/plugin/org.eclipse.cdt.ui/icons/view16/call_hierarchy.gif" tooltip="" allowMultiple="true" category="C/C++" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.internal.ui.callhierarchy.CHViewPart"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.ui"/>
    <tags>View</tags>
    <tags>categoryTag:C/C++</tags>
  </descriptors>
  <descriptors xmi:id="_rjrn-nHlEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.typeHierarchy" label="Type Hierarchy" iconURI="platform:/plugin/org.eclipse.cdt.ui/icons/view16/class_hi.gif" tooltip="" category="C/C++" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.internal.ui.typehierarchy.THViewPart"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.ui"/>
    <tags>View</tags>
    <tags>categoryTag:C/C++</tags>
  </descriptors>
  <descriptors xmi:id="_rjrn-3HlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.texteditor.TemplatesView" label="Templates" iconURI="platform:/plugin/org.eclipse.cdt.ui/icons/view16/templates.gif" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.texteditor.templates.TemplatesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.ui"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_rjrn_HHlEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.visualizer.view" label="Visualizer" iconURI="platform:/plugin/org.eclipse.cdt.visualizer.ui/icons/full/view16/visualizer_view.png" tooltip="" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.visualizer.ui.VisualizerView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.visualizer.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_rjrn_XHlEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.DebugView" label="Debug" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/debug_view.png" tooltip="" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.launch.LaunchView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_rjrn_nHlEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.BreakpointView" label="Breakpoints" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/breakpoint_view.png" tooltip="" allowMultiple="true" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.breakpoints.BreakpointsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_rjrn_3HlEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.VariableView" label="Variables" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/variable_view.png" tooltip="" allowMultiple="true" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.variables.VariablesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_rjsLgHHlEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.ExpressionView" label="Expressions" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/watchlist_view.png" tooltip="" allowMultiple="true" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.expression.ExpressionView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_rjsLgXHlEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.RegisterView" label="Registers" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/register_view.png" tooltip="" allowMultiple="true" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.registers.RegistersView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_rjsLgnHlEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.ModuleView" label="Modules" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/module_view.png" tooltip="" allowMultiple="true" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.modules.ModulesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_rjsLg3HlEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.MemoryView" label="Memory" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/memory_view.png" tooltip="" allowMultiple="true" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.memory.MemoryView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_rjsLhHHlEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.launchView" label="Launch Configurations" iconURI="platform:/plugin/org.eclipse.debug.ui.launchview/icons/run_exc.png" tooltip="" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.debug.ui.launchview/org.eclipse.debug.ui.launchview.internal.view.LaunchViewImpl">
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_rjsLhXHlEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.RepositoriesView" label="Git Repositories" iconURI="platform:/plugin/org.eclipse.egit.ui/icons/eview16/repo_rep.png" tooltip="" category="Git" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.egit.ui.internal.repository.RepositoriesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.egit.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Git</tags>
  </descriptors>
  <descriptors xmi:id="_rjsLhnHlEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.StagingView" label="Git Staging" iconURI="platform:/plugin/org.eclipse.egit.ui/icons/eview16/staging.png" tooltip="" category="Git" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.egit.ui.internal.staging.StagingView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.egit.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Git</tags>
  </descriptors>
  <descriptors xmi:id="_rjsLh3HlEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.InteractiveRebaseView" label="Git Interactive Rebase" iconURI="platform:/plugin/org.eclipse.egit.ui/icons/eview16/rebase_interactive.png" tooltip="" category="Git" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.egit.ui.internal.rebase.RebaseInteractiveView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.egit.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Git</tags>
  </descriptors>
  <descriptors xmi:id="_rjsLiHHlEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.CompareTreeView" label="Git Tree Compare" iconURI="platform:/plugin/org.eclipse.egit.ui/icons/obj16/gitrepository.png" tooltip="" category="Git" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.egit.ui.internal.dialogs.CompareTreeView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.egit.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Git</tags>
    <tags>NoRestore</tags>
  </descriptors>
  <descriptors xmi:id="_rjsLiXHlEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.ReflogView" label="Git Reflog" iconURI="platform:/plugin/org.eclipse.egit.ui/icons/eview16/reflog.png" tooltip="" category="Git" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.egit.ui.internal.reflog.ReflogView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.egit.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Git</tags>
  </descriptors>
  <descriptors xmi:id="_rjsLinHlEfCAZJ6UDgwwcw" elementId="org.eclipse.embedcdt.internal.debug.gdbjtag.ui.views.PeripheralsView" label="Peripherals" iconURI="platform:/plugin/org.eclipse.embedcdt.debug.gdbjtag.ui/icons/peripheral.png" tooltip="" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.embedcdt.internal.debug.gdbjtag.ui.render.peripherals.PeripheralsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.embedcdt.debug.gdbjtag.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_rjsLi3HlEfCAZJ6UDgwwcw" elementId="org.eclipse.embedcdt.internal.managedbuild.packs.ui.views.DocsView" label="Documents" iconURI="platform:/plugin/org.eclipse.embedcdt.managedbuild.packs.ui/icons/pdficon_small.png" tooltip="" category="CMSIS Packs" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.embedcdt.internal.managedbuild.packs.ui.views.DocsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.embedcdt.managedbuild.packs.ui"/>
    <tags>View</tags>
    <tags>categoryTag:CMSIS Packs</tags>
  </descriptors>
  <descriptors xmi:id="_rjsLjHHlEfCAZJ6UDgwwcw" elementId="org.eclipse.embedcdt.internal.packs.ui.views.DevicesView" label="Devices" iconURI="platform:/plugin/org.eclipse.embedcdt.packs.ui/icons/hardware_chip.png" tooltip="" category="CMSIS Packs" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.embedcdt.internal.packs.ui.views.DevicesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.embedcdt.packs.ui"/>
    <tags>View</tags>
    <tags>categoryTag:CMSIS Packs</tags>
  </descriptors>
  <descriptors xmi:id="_rjsLjXHlEfCAZJ6UDgwwcw" elementId="org.eclipse.embedcdt.internal.packs.ui.views.BoardsView" label="Boards" iconURI="platform:/plugin/org.eclipse.embedcdt.packs.ui/icons/board.png" tooltip="" category="CMSIS Packs" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.embedcdt.internal.packs.ui.views.BoardsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.embedcdt.packs.ui"/>
    <tags>View</tags>
    <tags>categoryTag:CMSIS Packs</tags>
  </descriptors>
  <descriptors xmi:id="_rjsLjnHlEfCAZJ6UDgwwcw" elementId="org.eclipse.embedcdt.internal.packs.ui.views.KeywordsView" label="Keywords" iconURI="platform:/plugin/org.eclipse.embedcdt.packs.ui/icons/info_obj.png" tooltip="" category="CMSIS Packs" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.embedcdt.internal.packs.ui.views.KeywordsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.embedcdt.packs.ui"/>
    <tags>View</tags>
    <tags>categoryTag:CMSIS Packs</tags>
  </descriptors>
  <descriptors xmi:id="_rjsLj3HlEfCAZJ6UDgwwcw" elementId="org.eclipse.embedcdt.internal.packs.ui.views.PacksView" label="CMSIS Packs" iconURI="platform:/plugin/org.eclipse.embedcdt.packs.ui/icons/packages.png" tooltip="" category="CMSIS Packs" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.embedcdt.internal.packs.ui.views.PacksView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.embedcdt.packs.ui"/>
    <tags>View</tags>
    <tags>categoryTag:CMSIS Packs</tags>
  </descriptors>
  <descriptors xmi:id="_rjsLkHHlEfCAZJ6UDgwwcw" elementId="org.eclipse.embedcdt.internal.packs.ui.views.OutlineView" label="Outline" iconURI="platform:/plugin/org.eclipse.embedcdt.packs.ui/icons/outline_co.png" tooltip="" category="CMSIS Packs" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.embedcdt.internal.packs.ui.views.OutlineView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.embedcdt.packs.ui"/>
    <tags>View</tags>
    <tags>categoryTag:CMSIS Packs</tags>
  </descriptors>
  <descriptors xmi:id="_rjsLkXHlEfCAZJ6UDgwwcw" elementId="org.eclipse.help.ui.HelpView" label="Help" iconURI="platform:/plugin/org.eclipse.help.ui/icons/view16/help_view.png" tooltip="" category="Help" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.help.ui.internal.views.HelpView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.help.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Help</tags>
  </descriptors>
  <descriptors xmi:id="_rjsLknHlEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.dataviewers.charts.view" label="Chart View" iconURI="platform:/plugin/org.eclipse.linuxtools.dataviewers.charts/icons/chart_icon.png" tooltip="" allowMultiple="true" category="Charts" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.linuxtools.internal.dataviewers.charts.view.ChartView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.linuxtools.dataviewers.charts"/>
    <tags>View</tags>
    <tags>categoryTag:Charts</tags>
  </descriptors>
  <descriptors xmi:id="_rjsLk3HlEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.gcov.view" label="gcov" iconURI="platform:/plugin/org.eclipse.linuxtools.gcov.core/icons/toggle.gif" tooltip="" allowMultiple="true" category="Profiling" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.linuxtools.internal.gcov.view.CovView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.linuxtools.gcov.core"/>
    <tags>View</tags>
    <tags>categoryTag:Profiling</tags>
    <tags>NoRestore</tags>
  </descriptors>
  <descriptors xmi:id="_rjsLlHHlEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.gprof.view" label="gprof" iconURI="platform:/plugin/org.eclipse.linuxtools.gprof/icons/toggle.gif" tooltip="Gprof view displays the profiling information contained in a gmon.out file" allowMultiple="true" category="Profiling" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.linuxtools.internal.gprof.view.GmonView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.linuxtools.gprof"/>
    <tags>View</tags>
    <tags>categoryTag:Profiling</tags>
    <tags>NoRestore</tags>
  </descriptors>
  <descriptors xmi:id="_rjsLlXHlEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.valgrind.ui.valgrindview" label="Valgrind" iconURI="platform:/plugin/org.eclipse.linuxtools.valgrind.ui/icons/valgrind-icon.png" tooltip="" category="Profiling" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.linuxtools.internal.valgrind.ui.ValgrindViewPart"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.linuxtools.valgrind.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Profiling</tags>
  </descriptors>
  <descriptors xmi:id="_rjsLlnHlEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.builds.navigator.builds" label="Builds" iconURI="platform:/plugin/org.eclipse.mylyn.builds.ui/icons/eview16/build-view.png" tooltip="" category="Mylyn" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.mylyn.internal.builds.ui.view.BuildsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.mylyn.builds.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Mylyn</tags>
  </descriptors>
  <descriptors xmi:id="_rjsLl3HlEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.commons.identity.ui.navigator.People" label="People" iconURI="platform:/plugin/org.eclipse.mylyn.commons.identity.ui/icons/obj16/people.gif" tooltip="" category="Mylyn" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.mylyn.internal.commons.identity.ui.PeopleView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.mylyn.commons.identity.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Mylyn</tags>
  </descriptors>
  <descriptors xmi:id="_rjsLmHHlEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.commons.repositories.ui.navigator.Repositories" label="Team Repositories" iconURI="platform:/plugin/org.eclipse.mylyn.commons.repositories.ui/icons/eview16/repositories.gif" tooltip="" category="Mylyn" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.mylyn.internal.commons.repositories.ui.RepositoriesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.mylyn.commons.repositories.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Mylyn</tags>
  </descriptors>
  <descriptors xmi:id="_rjsLmXHlEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.reviews.Explorer" label="Review" iconURI="platform:/plugin/org.eclipse.mylyn.reviews.ui/icons/obj16/review.png" tooltip="View artifacts and comments associated with reviews." category="Mylyn" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.mylyn.internal.reviews.ui.views.ReviewExplorer"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.mylyn.reviews.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Mylyn</tags>
  </descriptors>
  <descriptors xmi:id="_rjsLmnHlEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.tasks.ui.views.tasks" label="Task List" iconURI="platform:/plugin/org.eclipse.mylyn.tasks.ui/icons/eview16/task-list.png" tooltip="" allowMultiple="true" category="Mylyn" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.mylyn.internal.tasks.ui.views.TaskListView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.mylyn.tasks.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Mylyn</tags>
  </descriptors>
  <descriptors xmi:id="_rjsLm3HlEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.tasks.ui.views.repositories" label="Task Repositories" iconURI="platform:/plugin/org.eclipse.mylyn.tasks.ui/icons/eview16/repositories.png" tooltip="" category="Mylyn" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.mylyn.internal.tasks.ui.views.TaskRepositoriesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.mylyn.tasks.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Mylyn</tags>
  </descriptors>
  <descriptors xmi:id="_rjsLnHHlEfCAZJ6UDgwwcw" elementId="org.eclipse.oomph.p2.ui.RepositoryExplorer" label="Repository Explorer" iconURI="platform:/plugin/org.eclipse.oomph.p2.ui/icons/obj16/repository.gif" tooltip="" category="Oomph" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.oomph.p2.internal.ui.RepositoryExplorer"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.oomph.p2.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Oomph</tags>
  </descriptors>
  <descriptors xmi:id="_rjsLnXHlEfCAZJ6UDgwwcw" elementId="org.eclipse.remote.ui.view.connections" label="Connections" iconURI="platform:/plugin/org.eclipse.remote.ui/icons/connection.gif" tooltip="" category="Connections" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.remote.internal.ui.views.RemoteConnectionsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.remote.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Connections</tags>
  </descriptors>
  <descriptors xmi:id="_rjsLnnHlEfCAZJ6UDgwwcw" elementId="org.eclipse.search.ui.views.SearchView" label="Search" iconURI="platform:/plugin/org.eclipse.search/icons/full/eview16/searchres.png" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.search2.internal.ui.SearchView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.search"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_rjsLn3HlEfCAZJ6UDgwwcw" elementId="org.eclipse.team.sync.views.SynchronizeView" label="Synchronize" iconURI="platform:/plugin/org.eclipse.team.ui/icons/full/eview16/synch_synch.png" tooltip="" allowMultiple="true" category="Version Control (Team)" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.team.internal.ui.synchronize.SynchronizeView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.team.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Version Control (Team)</tags>
  </descriptors>
  <descriptors xmi:id="_rjsLoHHlEfCAZJ6UDgwwcw" elementId="org.eclipse.team.ui.GenericHistoryView" label="History" iconURI="platform:/plugin/org.eclipse.team.ui/icons/full/eview16/history_view.png" tooltip="" allowMultiple="true" category="Version Control (Team)" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.team.internal.ui.history.GenericHistoryView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.team.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Version Control (Team)</tags>
  </descriptors>
  <descriptors xmi:id="_rjsLoXHlEfCAZJ6UDgwwcw" elementId="org.eclipse.tm.terminal.view.ui.TerminalsView" label="Terminal" iconURI="platform:/plugin/org.eclipse.tm.terminal.view.ui/icons/eview16/terminal_view.gif" tooltip="" allowMultiple="true" category="Terminal" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tm.terminal.view.ui.view.TerminalsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tm.terminal.view.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Terminal</tags>
  </descriptors>
  <descriptors xmi:id="_rjsLonHlEfCAZJ6UDgwwcw" elementId="org.eclipse.tcf.te.ui.terminals.TerminalsView" label="Terminals (Old)" iconURI="platform:/plugin/org.eclipse.tm.terminal.view.ui/icons/eview16/terminal_view.gif" tooltip="" allowMultiple="true" category="Other" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tm.terminal.view.ui.view.OldTerminalsViewHandler"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tm.terminal.view.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Other</tags>
  </descriptors>
  <descriptors xmi:id="_rjsLo3HlEfCAZJ6UDgwwcw" elementId="org.eclipse.tracecompass.analysis.counters.ui.views.countersview" label="Counters" iconURI="platform:/plugin/org.eclipse.tracecompass.analysis.counters.ui/icons/counter.png" tooltip="" category="LTTng" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.analysis.counters.ui.views.CounterView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.analysis.counters.ui"/>
    <tags>View</tags>
    <tags>categoryTag:LTTng</tags>
  </descriptors>
  <descriptors xmi:id="_rjsLpHHlEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.tmf.analysis.graph.ui.criticalpath.view.criticalpathview" label="Critical Flow View" iconURI="platform:/plugin/org.eclipse.tracecompass.analysis.graph.ui/icons/eview16/critical-path.png" tooltip="" category="LTTng" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.internal.analysis.graph.ui.criticalpath.view.CriticalPathView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.analysis.graph.ui"/>
    <tags>View</tags>
    <tags>categoryTag:LTTng</tags>
  </descriptors>
  <descriptors xmi:id="_rjsLpXHlEfCAZJ6UDgwwcw" elementId="org.eclipse.tracecompass.analysis.lami.views.reportview" label="Analysis Report" iconURI="platform:/plugin/org.eclipse.ui/icons/full/eview16/defaultview_misc.png" tooltip="" allowMultiple="true" category="Tracing" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.internal.provisional.analysis.lami.ui.views.LamiReportView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.analysis.lami.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Tracing</tags>
    <tags>NoRestore</tags>
  </descriptors>
  <descriptors xmi:id="_rjsLpnHlEfCAZJ6UDgwwcw" elementId="org.eclipse.tracecompass.analysis.timing.ui.segstore.table:org.eclipse.tracecompass.analysis.os.linux.core.swslatency.sws" label="Sched_Wakeup/Switch Latencies" iconURI="platform:/plugin/org.eclipse.tracecompass.analysis.os.linux.ui/icons/obj16/latency.png" tooltip="" category="LTTng" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.analysis.timing.ui.views.segmentstore.table.SegmentStoreTableView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.analysis.os.linux.ui"/>
    <tags>View</tags>
    <tags>categoryTag:LTTng</tags>
  </descriptors>
  <descriptors xmi:id="_rjsLp3HlEfCAZJ6UDgwwcw" elementId="org.eclipse.tracecompass.internal.analysis.os.linux.ui.views.swslatency.scatter" label="Sched_Wakeup/Switch Latency vs Time" iconURI="platform:/plugin/org.eclipse.tracecompass.analysis.os.linux.ui/icons/obj16/scatter.png" tooltip="" category="LTTng" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.internal.analysis.os.linux.ui.views.swslatency.SWSLatencyScatterView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.analysis.os.linux.ui"/>
    <tags>View</tags>
    <tags>categoryTag:LTTng</tags>
  </descriptors>
  <descriptors xmi:id="_rjsLqHHlEfCAZJ6UDgwwcw" elementId="org.eclipse.tracecompass.analysis.timing.ui.segstore.statistics:org.eclipse.tracecompass.analysis.os.linux.core.swslatency.sws" label="Sched_Wakeup/Switch Latency Statistics" iconURI="platform:/plugin/org.eclipse.tracecompass.analysis.os.linux.ui/icons/obj16/statistics_view.gif" tooltip="" category="LTTng" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.analysis.timing.ui.views.segmentstore.statistics.SegmentStoreStatisticsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.analysis.os.linux.ui"/>
    <tags>View</tags>
    <tags>categoryTag:LTTng</tags>
  </descriptors>
  <descriptors xmi:id="_rjsLqXHlEfCAZJ6UDgwwcw" elementId="org.eclipse.tracecompass.analysis.os.linux.ui.segmentstore.statistics.prioname" label="Priority/Thread name Statistics" iconURI="platform:/plugin/org.eclipse.tracecompass.analysis.os.linux.ui/icons/eview16/statistics_view.gif" tooltip="" category="Tracing" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.internal.analysis.os.linux.ui.segmentstore.statistics.PriorityThreadNameStatisticsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.analysis.os.linux.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Tracing</tags>
  </descriptors>
  <descriptors xmi:id="_rjsLqnHlEfCAZJ6UDgwwcw" elementId="org.eclipse.tracecompass.analysis.os.linux.ui.segmentstore.statistics.prioname:org.eclipse.tracecompass.analysis.os.linux.core.swslatency.sws" label="Sched_Wakeup/Switch Latency Priority/Thread name Statistics" iconURI="platform:/plugin/org.eclipse.tracecompass.analysis.os.linux.ui/icons/obj16/statistics_view.gif" tooltip="" category="LTTng" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.internal.analysis.os.linux.ui.segmentstore.statistics.PriorityThreadNameStatisticsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.analysis.os.linux.ui"/>
    <tags>View</tags>
    <tags>categoryTag:LTTng</tags>
  </descriptors>
  <descriptors xmi:id="_rjsLq3HlEfCAZJ6UDgwwcw" elementId="org.eclipse.tracecompass.analysis.os.linux.ui.segmentstore.statistics.priority" label="Priority Statistics" iconURI="platform:/plugin/org.eclipse.tracecompass.analysis.os.linux.ui/icons/eview16/statistics_view.gif" tooltip="" category="Tracing" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.internal.analysis.os.linux.ui.segmentstore.statistics.PriorityStatisticsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.analysis.os.linux.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Tracing</tags>
  </descriptors>
  <descriptors xmi:id="_rjsLrHHlEfCAZJ6UDgwwcw" elementId="org.eclipse.tracecompass.analysis.os.linux.ui.segmentstore.statistics.priority:org.eclipse.tracecompass.analysis.os.linux.core.swslatency.sws" label="Sched_Wakeup/Switch Latency Priority Statistics" iconURI="platform:/plugin/org.eclipse.tracecompass.analysis.os.linux.ui/icons/obj16/statistics_view.gif" tooltip="" category="LTTng" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.internal.analysis.os.linux.ui.segmentstore.statistics.PriorityStatisticsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.analysis.os.linux.ui"/>
    <tags>View</tags>
    <tags>categoryTag:LTTng</tags>
  </descriptors>
  <descriptors xmi:id="_rjsLrXHlEfCAZJ6UDgwwcw" elementId="org.eclipse.tracecompass.internal.analysis.os.linux.ui.views.swslatency.density" label="Sched_Wakeup/Switch Density" iconURI="platform:/plugin/org.eclipse.tracecompass.analysis.os.linux.ui/icons/obj16/density.png" tooltip="" category="LTTng" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.internal.analysis.os.linux.ui.views.swslatency.SWSLatencyDensityView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.analysis.os.linux.ui"/>
    <tags>View</tags>
    <tags>categoryTag:LTTng</tags>
  </descriptors>
  <descriptors xmi:id="_rjsLrnHlEfCAZJ6UDgwwcw" elementId="org.eclipse.tracecompass.analysis.os.linux.views.controlflow" label="Control Flow" iconURI="platform:/plugin/org.eclipse.tracecompass.analysis.os.linux.ui/icons/eview16/control_flow_view.gif" tooltip="" category="LTTng" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.internal.analysis.os.linux.ui.views.controlflow.ControlFlowView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.analysis.os.linux.ui"/>
    <tags>View</tags>
    <tags>categoryTag:LTTng</tags>
  </descriptors>
  <descriptors xmi:id="_rjsLr3HlEfCAZJ6UDgwwcw" elementId="org.eclipse.tracecompass.analysis.os.linux.views.resources" label="Resources" iconURI="platform:/plugin/org.eclipse.tracecompass.analysis.os.linux.ui/icons/eview16/resources_view.gif" tooltip="" category="LTTng" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.internal.analysis.os.linux.ui.views.resources.ResourcesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.analysis.os.linux.ui"/>
    <tags>View</tags>
    <tags>categoryTag:LTTng</tags>
  </descriptors>
  <descriptors xmi:id="_rjsLsHHlEfCAZJ6UDgwwcw" elementId="org.eclipse.tracecompass.analysis.os.linux.views.cpuusage" label="CPU Usage" iconURI="platform:/plugin/org.eclipse.tracecompass.analysis.os.linux.ui/icons/eview16/cpu-usage.png" tooltip="" category="LTTng" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.internal.analysis.os.linux.ui.views.cpuusage.CpuUsageView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.analysis.os.linux.ui"/>
    <tags>View</tags>
    <tags>categoryTag:LTTng</tags>
  </descriptors>
  <descriptors xmi:id="_rjsLsXHlEfCAZJ6UDgwwcw" elementId="org.eclipse.tracecompass.analysis.timing.ui.segstore.table:org.eclipse.tracecompass.analysis.os.linux.latency.syscall" label="System Call Latencies" iconURI="platform:/plugin/org.eclipse.tracecompass.analysis.os.linux.ui/icons/eview16/latency.png" tooltip="" category="LTTng" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.analysis.timing.ui.views.segmentstore.table.SegmentStoreTableView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.analysis.os.linux.ui"/>
    <tags>View</tags>
    <tags>categoryTag:LTTng</tags>
  </descriptors>
  <descriptors xmi:id="_rjsLsnHlEfCAZJ6UDgwwcw" elementId="org.eclipse.tracecompass.analysis.os.linux.views.latency.scatter" label="System Call Latency vs Time" iconURI="platform:/plugin/org.eclipse.tracecompass.analysis.os.linux.ui/icons/eview16/scatter.png" tooltip="" category="LTTng" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.internal.analysis.os.linux.ui.views.latency.SystemCallLatencyScatterView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.analysis.os.linux.ui"/>
    <tags>View</tags>
    <tags>categoryTag:LTTng</tags>
  </descriptors>
  <descriptors xmi:id="_rjsLs3HlEfCAZJ6UDgwwcw" elementId="org.eclipse.tracecompass.analysis.timing.ui.segstore.statistics:org.eclipse.tracecompass.analysis.os.linux.latency.syscall" label="System Call Latency Statistics" iconURI="platform:/plugin/org.eclipse.tracecompass.analysis.os.linux.ui/icons/eview16/statistics_view.gif" tooltip="" category="LTTng" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.analysis.timing.ui.views.segmentstore.statistics.SegmentStoreStatisticsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.analysis.os.linux.ui"/>
    <tags>View</tags>
    <tags>categoryTag:LTTng</tags>
  </descriptors>
  <descriptors xmi:id="_rjsLtHHlEfCAZJ6UDgwwcw" elementId="org.eclipse.tracecompass.analysis.os.linux.views.latency.density" label="System Call Density" iconURI="platform:/plugin/org.eclipse.tracecompass.analysis.os.linux.ui/icons/eview16/density.png" tooltip="" category="LTTng" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.internal.analysis.os.linux.ui.views.latency.SystemCallLatencyDensityView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.analysis.os.linux.ui"/>
    <tags>View</tags>
    <tags>categoryTag:LTTng</tags>
  </descriptors>
  <descriptors xmi:id="_rjsLtXHlEfCAZJ6UDgwwcw" elementId="org.eclipse.tracecompass.analysis.os.linux.ui.kernelmemoryusageview" label="Kernel Memory Usage View" iconURI="platform:/plugin/org.eclipse.tracecompass.analysis.os.linux.ui/icons/eview16/memory-usage.png" tooltip="" category="LTTng" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.internal.analysis.os.linux.ui.views.kernelmemoryusage.KernelMemoryUsageView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.analysis.os.linux.ui"/>
    <tags>View</tags>
    <tags>categoryTag:LTTng</tags>
  </descriptors>
  <descriptors xmi:id="_rjsLtnHlEfCAZJ6UDgwwcw" elementId="org.eclipse.tracecompass.analysis.os.linux.views.diskioactivity" label="Disk I/O Activity" iconURI="platform:/plugin/org.eclipse.tracecompass.analysis.os.linux.ui/icons/eview16/resources_view.gif" tooltip="" category="LTTng" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.internal.analysis.os.linux.ui.views.io.diskioactivity.DiskIOActivityView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.analysis.os.linux.ui"/>
    <tags>View</tags>
    <tags>categoryTag:LTTng</tags>
  </descriptors>
  <descriptors xmi:id="_rjsLt3HlEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.tmf.ui.views.callstack" label="Flame Chart" iconURI="platform:/plugin/org.eclipse.tracecompass.analysis.profiling.ui/icons/eview16/callstack_view.gif" tooltip="" category="Tracing" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.analysis.profiling.ui.views.flamechart.FlameChartView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.analysis.profiling.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Tracing</tags>
  </descriptors>
  <descriptors xmi:id="_rjsLuHHlEfCAZJ6UDgwwcw" elementId="org.eclipse.tracecompass.internal.analysis.timing.ui.callgraph.callgraphDensity" label="Function Durations Distribution" iconURI="platform:/plugin/org.eclipse.tracecompass.analysis.profiling.ui/icons/eview16/funcdensity.png" tooltip="" category="Tracing" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.internal.analysis.profiling.ui.callgraph.CallGraphDensityView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.analysis.profiling.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Tracing</tags>
  </descriptors>
  <descriptors xmi:id="_rjsLuXHlEfCAZJ6UDgwwcw" elementId="org.eclipse.tracecompass.internal.analysis.timing.ui.flamegraph.flamegraphView" label="Flame Graph" iconURI="platform:/plugin/org.eclipse.tracecompass.analysis.profiling.ui/icons/eview16/flame.png" tooltip="" category="Tracing" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.internal.analysis.profiling.ui.flamegraph.FlameGraphView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.analysis.profiling.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Tracing</tags>
  </descriptors>
  <descriptors xmi:id="_rjsLunHlEfCAZJ6UDgwwcw" elementId="org.eclipse.tracecompass.internal.analysis.timing.ui.callgraph.statistics.callgraphstatistics" label="Function Duration Statistics" iconURI="platform:/plugin/org.eclipse.tracecompass.analysis.profiling.ui/icons/eview16/statistics_view.gif" tooltip="" category="Tracing" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.internal.analysis.profiling.ui.callgraph.statistics.CallGraphStatisticsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.analysis.profiling.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Tracing</tags>
  </descriptors>
  <descriptors xmi:id="_rjsLu3HlEfCAZJ6UDgwwcw" elementId="org.eclipse.tracecompass.analysis.timing.ui.segstore.table" label="Segment Store Table" iconURI="platform:/plugin/org.eclipse.tracecompass.analysis.timing.ui/icons/eview16/latency.png" tooltip="" category="Tracing" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.analysis.timing.ui.views.segmentstore.table.SegmentStoreTableView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.analysis.timing.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Tracing</tags>
  </descriptors>
  <descriptors xmi:id="_rjsLvHHlEfCAZJ6UDgwwcw" elementId="org.eclipse.tracecompass.analysis.timing.ui.segstore.statistics" label="Descriptive Statistics" iconURI="platform:/plugin/org.eclipse.tracecompass.analysis.timing.ui/icons/eview16/statistics_view.gif" tooltip="" category="Tracing" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.analysis.timing.ui.views.segmentstore.statistics.SegmentStoreStatisticsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.analysis.timing.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Tracing</tags>
  </descriptors>
  <descriptors xmi:id="_rjsLvXHlEfCAZJ6UDgwwcw" elementId="org.eclipse.tracecompass.analysis.timing.ui.segstore.scatter2" label="Segments vs Time" iconURI="platform:/plugin/org.eclipse.tracecompass.analysis.timing.ui/icons/eview16/latency.png" tooltip="" category="Tracing" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.analysis.timing.ui.views.segmentstore.scatter.SegmentStoreScatterView2"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.analysis.timing.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Tracing</tags>
  </descriptors>
  <descriptors xmi:id="_rjsLvnHlEfCAZJ6UDgwwcw" elementId="org.eclipse.tracecompass.analysis.timing.ui.segstore.table:org.eclipse.tracecompass.internal.analysis.timing.core.event.matching" label="Event Match Latencies" iconURI="platform:/plugin/org.eclipse.tracecompass.analysis.timing.ui/icons/eview16/latency.png" tooltip="" category="LTTng" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.analysis.timing.ui.views.segmentstore.table.SegmentStoreTableView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.analysis.timing.ui"/>
    <tags>View</tags>
    <tags>categoryTag:LTTng</tags>
  </descriptors>
  <descriptors xmi:id="_rjsLv3HlEfCAZJ6UDgwwcw" elementId="org.eclipse.tracecompass.analysis.timing.ui.segstore.statistics:org.eclipse.tracecompass.internal.analysis.timing.core.event.matching" label="Event Match Latency Statistics" iconURI="platform:/plugin/org.eclipse.tracecompass.analysis.timing.ui/icons/eview16/statistics_view.gif" tooltip="" category="LTTng" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.analysis.timing.ui.views.segmentstore.statistics.SegmentStoreStatisticsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.analysis.timing.ui"/>
    <tags>View</tags>
    <tags>categoryTag:LTTng</tags>
  </descriptors>
  <descriptors xmi:id="_rjsLwHHlEfCAZJ6UDgwwcw" elementId="org.eclipse.tracecompass.analysis.timing.ui.segstore.scatter2:org.eclipse.tracecompass.internal.analysis.timing.core.event.matching" label="Event Matches Scatter Graph" iconURI="platform:/plugin/org.eclipse.tracecompass.analysis.timing.ui/icons/eview16/statistics_view.gif" tooltip="" category="LTTng" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.analysis.timing.ui.views.segmentstore.scatter.SegmentStoreScatterView2"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.analysis.timing.ui"/>
    <tags>View</tags>
    <tags>categoryTag:LTTng</tags>
  </descriptors>
  <descriptors xmi:id="_rjsLwXHlEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.internal.lttng2.ui.views.control" label="Control" iconURI="platform:/plugin/org.eclipse.tracecompass.lttng2.control.ui/icons/eview16/control_view.gif" tooltip="" category="LTTng" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.internal.lttng2.control.ui.views.ControlView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.lttng2.control.ui"/>
    <tags>View</tags>
    <tags>categoryTag:LTTng</tags>
  </descriptors>
  <descriptors xmi:id="_rjsLwnHlEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.lttng2.ust.memoryusage" label="UST Memory Usage" iconURI="platform:/plugin/org.eclipse.tracecompass.lttng2.ust.ui/icons/eview16/memory-usage.png" tooltip="" category="LTTng" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.internal.lttng2.ust.ui.views.memusage.UstMemoryUsageView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.lttng2.ust.ui"/>
    <tags>View</tags>
    <tags>categoryTag:LTTng</tags>
  </descriptors>
  <descriptors xmi:id="_rjsLw3HlEfCAZJ6UDgwwcw" elementId="org.eclipse.tracecompass.analysis.timing.ui.segstore.table:org.eclipse.linuxtools.lttng2.ust.analysis.memory" label="Potential Leaks" iconURI="platform:/plugin/org.eclipse.tracecompass.lttng2.ust.ui/icons/eview16/memory-usage.png" tooltip="" category="LTTng" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.analysis.timing.ui.views.segmentstore.table.SegmentStoreTableView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.lttng2.ust.ui"/>
    <tags>View</tags>
    <tags>categoryTag:LTTng</tags>
  </descriptors>
  <descriptors xmi:id="_rjsLxHHlEfCAZJ6UDgwwcw" elementId="org.eclipse.tracecompass.analysis.timing.ui.segstore.scatter2:org.eclipse.linuxtools.lttng2.ust.analysis.memory" label="Potential Leaks vs Time" iconURI="platform:/plugin/org.eclipse.tracecompass.lttng2.ust.ui/icons/eview16/memory-usage.png" tooltip="" category="LTTng" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.analysis.timing.ui.views.segmentstore.scatter.SegmentStoreScatterView2"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.lttng2.ust.ui"/>
    <tags>View</tags>
    <tags>categoryTag:LTTng</tags>
  </descriptors>
  <descriptors xmi:id="_rjsLxXHlEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.tmf.analysis.xml.ui.views.timegraph" label="XML Time Graph View" iconURI="platform:/plugin/org.eclipse.tracecompass.tmf.analysis.xml.ui/icons/eview16/ganttxml.png" tooltip="" allowMultiple="true" category="LTTng" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.internal.tmf.analysis.xml.ui.views.timegraph.XmlTimeGraphView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.tmf.analysis.xml.ui"/>
    <tags>View</tags>
    <tags>categoryTag:LTTng</tags>
  </descriptors>
  <descriptors xmi:id="_rjsLxnHlEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.internal.tmf.analysis.xml.ui.views.xyview" label="XML XY Chart View" iconURI="platform:/plugin/org.eclipse.tracecompass.tmf.analysis.xml.ui/icons/eview16/linechartxml.png" tooltip="" allowMultiple="true" category="LTTng" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.internal.tmf.analysis.xml.ui.views.xychart.XmlXYView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.tmf.analysis.xml.ui"/>
    <tags>View</tags>
    <tags>categoryTag:LTTng</tags>
  </descriptors>
  <descriptors xmi:id="_rjsLx3HlEfCAZJ6UDgwwcw" elementId="org.eclipse.tracecompass.internal.tmf.analysis.xml.ui.views.latencytable" label="Latency Table" iconURI="platform:/plugin/org.eclipse.tracecompass.tmf.analysis.xml.ui/icons/eview16/latency.png" tooltip="" category="Tracing" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.internal.tmf.analysis.xml.ui.views.latency.PatternLatencyTableView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.tmf.analysis.xml.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Tracing</tags>
  </descriptors>
  <descriptors xmi:id="_rjsLyHHlEfCAZJ6UDgwwcw" elementId="org.eclipse.tracecompass.internal.tmf.analysis.xml.ui.views.scattergraph" label="Latency vs Time" iconURI="platform:/plugin/org.eclipse.tracecompass.tmf.analysis.xml.ui/icons/eview16/scatter.png" tooltip="" category="Tracing" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.internal.tmf.analysis.xml.ui.views.latency.PatternScatterGraphView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.tmf.analysis.xml.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Tracing</tags>
  </descriptors>
  <descriptors xmi:id="_rjsLyXHlEfCAZJ6UDgwwcw" elementId="org.eclipse.tracecompass.internal.tmf.analysis.xml.ui.views.density" label="Latency vs Count" iconURI="platform:/plugin/org.eclipse.tracecompass.tmf.analysis.xml.ui/icons/eview16/density.png" tooltip="" category="Tracing" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.internal.tmf.analysis.xml.ui.views.latency.PatternDensityView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.tmf.analysis.xml.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Tracing</tags>
  </descriptors>
  <descriptors xmi:id="_rjsLynHlEfCAZJ6UDgwwcw" elementId="org.eclipse.tracecompass.internal.tmf.analysis.xml.ui.views.statistics" label="Latency Statistics" iconURI="platform:/plugin/org.eclipse.tracecompass.tmf.analysis.xml.ui/icons/eview16/statistics_view.gif" tooltip="" category="Tracing" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.internal.tmf.analysis.xml.ui.views.latency.PatternStatisticsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.tmf.analysis.xml.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Tracing</tags>
  </descriptors>
  <descriptors xmi:id="_rjsLy3HlEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.tmf.ui.views.timechart" label="Time Chart" iconURI="platform:/plugin/org.eclipse.tracecompass.tmf.ui/icons/eview16/timechart_view.gif" tooltip="" category="Tracing" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.tmf.ui.views.timechart.TimeChartView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.tmf.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Tracing</tags>
  </descriptors>
  <descriptors xmi:id="_rjsLzHHlEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.tmf.ui.views.ssvisualizer" label="State System Explorer" iconURI="platform:/plugin/org.eclipse.tracecompass.tmf.ui/icons/eview16/events_view.gif" tooltip="" category="Tracing" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.tmf.ui.views.statesystem.TmfStateSystemExplorer"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.tmf.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Tracing</tags>
  </descriptors>
  <descriptors xmi:id="_rjsLzXHlEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.tmf.ui.views.colors" label="Colors" iconURI="platform:/plugin/org.eclipse.tracecompass.tmf.ui/icons/eview16/colors_view.gif" tooltip="" category="Tracing" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.tmf.ui.views.colors.ColorsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.tmf.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Tracing</tags>
  </descriptors>
  <descriptors xmi:id="_rjsLznHlEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.tmf.ui.views.filter" label="Filters" iconURI="platform:/plugin/org.eclipse.tracecompass.tmf.ui/icons/eview16/filters_view.gif" tooltip="" category="Tracing" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.tmf.ui.views.filter.FilterView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.tmf.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Tracing</tags>
  </descriptors>
  <descriptors xmi:id="_rjsLz3HlEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.tmf.ui.tmfUml2SDSyncView" label="Sequence Diagram" iconURI="platform:/plugin/org.eclipse.tracecompass.tmf.ui/icons/eview16/sequencediagram_view.gif" tooltip="" category="Tracing" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.tmf.ui.views.uml2sd.SDView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.tmf.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Tracing</tags>
  </descriptors>
  <descriptors xmi:id="_rjsL0HHlEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.tmf.ui.views.statistics" label="Statistics" iconURI="platform:/plugin/org.eclipse.tracecompass.tmf.ui/icons/eview16/statistics_view.gif" tooltip="" category="Tracing" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.internal.tmf.ui.views.statistics.TmfStatisticsViewImpl"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.tmf.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Tracing</tags>
  </descriptors>
  <descriptors xmi:id="_rjsL0XHlEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.tmf.ui.views.histogram" label="Histogram" iconURI="platform:/plugin/org.eclipse.tracecompass.tmf.ui/icons/eview16/histogram.gif" tooltip="" category="Tracing" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.tmf.ui.views.histogram.HistogramView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.tmf.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Tracing</tags>
  </descriptors>
  <descriptors xmi:id="_rjsL0nHlEfCAZJ6UDgwwcw" elementId="org.eclipse.tracecompass.tmf.ui.views.eventdensity" label="Event Density" iconURI="platform:/plugin/org.eclipse.tracecompass.tmf.ui/icons/eview16/histogram.gif" tooltip="" category="Tracing" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.internal.tmf.ui.views.eventdensity.EventDensityView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.tmf.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Tracing</tags>
  </descriptors>
  <descriptors xmi:id="_rjsL03HlEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.tmf.ui.views.synchronization" label="Synchronization" iconURI="platform:/plugin/org.eclipse.tracecompass.tmf.ui/icons/eview16/synced.gif" tooltip="" category="Tracing" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.tmf.ui.views.synchronization.TmfSynchronizationView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.tmf.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Tracing</tags>
  </descriptors>
  <descriptors xmi:id="_rjsL1HHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.internal.introview" label="Welcome" iconURI="platform:/plugin/org.eclipse.ui/icons/full/eview16/defaultview_misc.png" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.ViewIntroAdapterPart"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_rjsL1XHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.browser.view" label="Internal Web Browser" iconURI="platform:/plugin/org.eclipse.ui.browser/icons/obj16/internal_browser.png" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.browser.WebBrowserView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.browser"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_rjsL1nHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.cheatsheets.views.CheatSheetView" label="Cheat Sheets" iconURI="platform:/plugin/org.eclipse.ui.cheatsheets/icons/view16/cheatsheet_view.png" tooltip="" category="Help" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.cheatsheets.views.CheatSheetView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.cheatsheets"/>
    <tags>View</tags>
    <tags>categoryTag:Help</tags>
  </descriptors>
  <descriptors xmi:id="_rjsL13HlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.console.ConsoleView" label="Console" iconURI="platform:/plugin/org.eclipse.ui.console/icons/full/cview16/console_view.png" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.console.ConsoleView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.console"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_rjsL2HHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.views.ProgressView" label="Progress" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/pview.png" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.progress.ProgressView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_rjsL2XHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.views.BookmarkView" label="Bookmarks" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/bkmrk_nav.png" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.markers.BookmarksView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_rjsL2nHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.views.TaskList" label="Tasks" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/tasks_tsk.png" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.markers.TasksView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_rjsL23HlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.views.ProblemView" label="Problems" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/problems_view.png" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.markers.ProblemsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_rjsL3HHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.views.AllMarkersView" label="Markers" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/problems_view.png" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.markers.AllMarkersView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_rjsL3XHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.navigator.ProjectExplorer" label="Project Explorer" iconURI="platform:/plugin/org.eclipse.ui.navigator.resources/icons/full/eview16/resource_persp.png" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.navigator.resources.ProjectExplorer"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.navigator.resources"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_rjsL3nHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.views.PropertySheet" label="Properties" iconURI="platform:/plugin/org.eclipse.ui.views/icons/full/eview16/prop_ps.png" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.views.properties.PropertySheet"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.views"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_rjsL33HlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.views.ContentOutline" label="Outline" iconURI="platform:/plugin/org.eclipse.ui.views/icons/full/eview16/outline_co.png" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.views.contentoutline.ContentOutline"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.views"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_rjsL4HHlEfCAZJ6UDgwwcw" elementId="org.eclipse.pde.runtime.LogView" label="Error Log" iconURI="platform:/plugin/org.eclipse.ui.views.log/icons/eview16/error_log.png" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.log.LogView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.views.log"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_rjsL4XHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.views.minimap.MinimapView" label="Minimap" iconURI="platform:/plugin/org.eclipse.ui.workbench.texteditor/icons/full/eview16/minimap.png" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.minimap.MinimapView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.workbench.texteditor"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_rjsL4nHlEfCAZJ6UDgwwcw" elementId="org.eclipse.wst.xml.ui.views.annotations.XMLAnnotationsView" label="Documentation" iconURI="platform:/plugin/org.eclipse.wst.xml.ui/icons/full/obj16/comment_obj.gif" tooltip="" category="XML" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.wst.xml.ui.internal.views.annotations.XMLAnnotationsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.wst.xml.ui"/>
    <tags>View</tags>
    <tags>categoryTag:XML</tags>
  </descriptors>
  <descriptors xmi:id="_rjsL43HlEfCAZJ6UDgwwcw" elementId="org.eclipse.wst.xml.ui.contentmodel.view" label="Content Model" iconURI="platform:/plugin/org.eclipse.wst.xml.ui/icons/full/view16/hierarchy.gif" tooltip="" category="XML" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.wst.xml.ui.internal.views.contentmodel.ContentModelView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.wst.xml.ui"/>
    <tags>View</tags>
    <tags>categoryTag:XML</tags>
  </descriptors>
  <descriptors xmi:id="_rjsL5HHlEfCAZJ6UDgwwcw" elementId="org.riscvstudio.ide.tools.trace.ui.TabTrace" label="Trace" iconURI="platform:/plugin/org.riscvstudio.ide.tools.trace/icons/trace/trace.png" tooltip="" category="RV Trace" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.riscvstudio.ide.tools.trace.ui.TabTrace"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.riscvstudio.ide.tools.trace"/>
    <tags>View</tags>
    <tags>categoryTag:RV Trace</tags>
  </descriptors>
  <trimContributions xmi:id="_rjs1t3HlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.ide.application.trimcontribution.QuickAccess" contributorURI="platform:/plugin/org.eclipse.ui.ide.application" toBeRendered="false" parentId="org.eclipse.ui.main.toolbar" positionInParent="last">
    <children xsi:type="menu:ToolControl" xmi:id="_rjs1uHHlEfCAZJ6UDgwwcw" elementId="Spacer Glue" contributorURI="platform:/plugin/org.eclipse.ui.ide.application" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.renderers.swt/org.eclipse.e4.ui.workbench.renderers.swt.LayoutModifierToolControl">
      <tags>glue</tags>
      <tags>move_after:PerspectiveSpacer</tags>
      <tags>SHOW_RESTORE_MENU</tags>
    </children>
    <children xsi:type="menu:ToolControl" xmi:id="_rjs1uXHlEfCAZJ6UDgwwcw" elementId="SearchField" contributorURI="platform:/plugin/org.eclipse.ui.ide.application" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.quickaccess.SearchField">
      <tags>move_after:Spacer Glue</tags>
      <tags>HIDEABLE</tags>
      <tags>SHOW_RESTORE_MENU</tags>
    </children>
    <children xsi:type="menu:ToolControl" xmi:id="_rjs1unHlEfCAZJ6UDgwwcw" elementId="Search-PS Glue" contributorURI="platform:/plugin/org.eclipse.ui.ide.application" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.renderers.swt/org.eclipse.e4.ui.workbench.renderers.swt.LayoutModifierToolControl">
      <tags>glue</tags>
      <tags>move_after:SearchField</tags>
      <tags>SHOW_RESTORE_MENU</tags>
    </children>
  </trimContributions>
  <commands xmi:id="_rjtZpnHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.select.pageUp" commandName="Select Page Up" description="Select to the top of the page" category="_rjtddHHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtZp3HlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.toggleWordWrap" commandName="Toggle Word Wrap" description="Toggle word wrap in the current text editor" category="_rjtdcXHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtZqHHlEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.history.ResetQuickdiffBaseline" commandName="Reset quickdiff baseline" category="_rjtdi3HlEfCAZJ6UDgwwcw">
    <parameters xmi:id="_rjtZqXHlEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.history.ResetQuickdiffBaselineTarget" name="Reset target (HEAD, HEAD^1)" optional="false"/>
  </commands>
  <commands xmi:id="_rjtZqnHlEfCAZJ6UDgwwcw" elementId="org.eclipse.oomph.p2.ui.SearchRequirements" commandName="Search Requirements" category="_rjtdhXHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtZq3HlEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.search.findrefs" commandName="References" description="Searches for references to the selected element in the workspace" category="_rjtdkHHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtZrHHlEfCAZJ6UDgwwcw" elementId="org.eclipse.tracecompass.tmf.ui.command.zoomin.selection" commandName="Zoom in (selection)" category="_rjtdh3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtZrXHlEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.FetchGitLabMergeRequest" commandName="Fetch GitLab Merge Request" category="_rjtdonHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtZrnHlEfCAZJ6UDgwwcw" elementId="org.eclipse.tm.terminal.maximize" commandName="Maximize Active View or Editor" category="_rjtdnnHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtZr3HlEfCAZJ6UDgwwcw" elementId="org.eclipse.oomph.setup.editor.openDiscoveredType" commandName="Open Discovered Type" category="_rjtdk3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtZsHHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.deletePreviousWord" commandName="Delete Previous Word" description="Delete the previous word" category="_rjtddHHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtZsXHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.select.stopMultiSelection" commandName="End multi-selection" description="Unselects all multi-selections returning to a single cursor " category="_rjtddHHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtZsnHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.delimiter.unix" commandName="Convert Line Delimiters to Unix (LF, \n, 0A, &#xb6;)" description="Converts the line delimiters to Unix (LF, \n, 0A, &#xb6;)" category="_rjtde3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtZs3HlEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.history.Edit" commandName="Edit Commit" category="_rjtdi3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtZtHHlEfCAZJ6UDgwwcw" elementId="org.eclipse.epp.mpc.ui.command.showMarketplaceWizard" commandName="Eclipse Marketplace" description="Show the Eclipse Marketplace wizard" category="_rjtdi3HlEfCAZJ6UDgwwcw">
    <parameters xmi:id="_rjtZtXHlEfCAZJ6UDgwwcw" elementId="trigger" name="trigger"/>
  </commands>
  <commands xmi:id="_rjtZtnHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.navigate.addToWorkingSet" commandName="Add to Working Set" description="Adds the selected object to a working set." category="_rjtdcXHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtZt3HlEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.team.Revert" commandName="Revert Commit" category="_rjtdonHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtZuHHlEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.wikitext.ui.editor.showCheatSheetCommand" commandName="Show Markup Cheat Sheet" category="_rjtdi3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtZuXHlEfCAZJ6UDgwwcw" elementId="org.eclipse.team.ui.TeamSynchronizingPerspective" commandName="Team Synchronizing" description="Open the Team Synchronizing Perspective" category="_rjtdpHHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtZunHlEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.search.findrefs.workingset" commandName="References in Working Set" description="Searches for references to the selected element in a working set" category="_rjtdkHHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtZu3HlEfCAZJ6UDgwwcw" elementId="org.eclipse.epp.mpc.ui.command.showInstalled" commandName="Manage installed plug-ins" description="Update or uninstall plug-ins installed from the Marketplace" category="_rjtdi3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtZvHHlEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.debug.application.command.debugNewExecutable" commandName="Debug New Executable" description="Debug a new executable" category="_rjtdnXHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtZvXHlEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.debug.ui.command.ungroupDebugContexts" commandName="Ungroup" description="Ungroups the selected debug contexts" category="_rjtdnXHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtZvnHlEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.context.ui.commands.task.clearContext" commandName="Clear Context" category="_rjtddnHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtZv3HlEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.commands.ToggleLineBreakpoint" commandName="Toggle Line Breakpoint" description="Creates or removes a line breakpoint" category="_rjtdgnHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtZwHHlEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.tasks.ui.command.searchForTask" commandName="Search Repository for Task" category="_rjtdlnHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtZwXHlEfCAZJ6UDgwwcw" elementId="org.eclipse.tracecompass.tmf.ui.copy_to_clipboard" commandName="Copy to Clipboard" description="Copy to Clipboard" category="_rjtdh3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtZwnHlEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.debug.application.command.debugRemoteExecutable" commandName="Debug Remote Executable" description="Debug a Remote executable" category="_rjtdnXHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtZw3HlEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.debug.ui.command.castToArray" commandName="Cast To Type..." category="_rjtdiXHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtZxHHlEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.commit.UnifiedDiffCommand" commandName="Show Unified Diff" category="_rjtdi3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtZxXHlEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.internal.lttng2.ui.commands.control.disableLogger" commandName="Disable Logger" description="Disable Logger" category="_rjtdgXHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtZxnHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.move" commandName="Move..." description="Move the selected item" category="_rjtde3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtZx3HlEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.internal.lttng2.ui.commands.control.delete" commandName="Delete" description="Delete Target Node" category="_rjtdgXHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtZyHHlEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.tmf.ui.views.uml2sd.handlers.MoveSDDown" commandName="Scroll down" description="Scroll down the sequence diagram" category="_rjtdo3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtZyXHlEfCAZJ6UDgwwcw" elementId="org.eclipse.tracecompass.tmf.analysis.xml.ui.managexmlanalyses" commandName="Manage XML analyses..." description="Manage XML files containing analysis information" category="_rjtdh3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtZynHlEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.tasks.ui.command.maximizePart" commandName="Maximize Part" description="Maximize Part" category="_rjtdkXHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtZy3HlEfCAZJ6UDgwwcw" elementId="org.eclipse.compare.ignoreWhiteSpace" commandName="Ignore White Space" description="Ignore white space where applicable" category="_rjtdmHHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtZzHHlEfCAZJ6UDgwwcw" elementId="org.eclipse.oomph.setup.editor.importProjects" commandName="Import Projects" category="_rjtdk3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtZzXHlEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.refactor.extract.function" commandName="Extract Function - Refactoring " description="Extracts a function for the selected list of expressions or statements" category="_rjtdfnHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtZznHlEfCAZJ6UDgwwcw" elementId="org.eclipse.wst.xml.ui.disable.grammar.constraints" commandName="Turn off Grammar Constraints" description="Turn off grammar Constraints" category="_rjtdi3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtZz3HlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.navigate.goToResource" commandName="Go to Resource" description="Go to a particular resource in the active view" category="_rjtdlnHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtZ0HHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.window.resetPerspective" commandName="Reset Perspective" description="Reset the current perspective to its default state" category="_rjtdfXHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtZ0XHlEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.wikitext.ui.quickOutlineCommand" commandName="Quick Outline" description="Open a popup dialog with a quick outline of the current document" category="_rjtdlnHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtZ0nHlEfCAZJ6UDgwwcw" elementId="AnsiConsole.command.enable_disable" commandName="Enable / Disable ANSI Support" description="Enable / disable ANSI Support" category="_rjtdeXHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtZ03HlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.project.buildLast" commandName="Repeat Working Set Build" description="Repeat the last working set build" category="_rjtdm3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtZ1HHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.project.buildProject" commandName="Build Project" description="Build the selected project" category="_rjtdm3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtZ1XHlEfCAZJ6UDgwwcw" elementId="org.eclipse.compare.switchLeftAndRight" commandName="Swap Left and Right View" description="Switch the left and right sides in the compare editor" category="_rjtdmHHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtZ1nHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.goto.textStart" commandName="Text Start" description="Go to the beginning of the text" category="_rjtddHHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtZ13HlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.paste" commandName="Paste" description="Paste from the clipboard" category="_rjtdcXHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtZ2HHlEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.menu.updateUnresolvedIncludes" commandName="Re-resolve Unresolved Includes" category="_rjtdm3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtZ2XHlEfCAZJ6UDgwwcw" elementId="org.eclipse.oomph.setup.editor.refreshCache" commandName="Refresh Remote Cache" category="_rjtdk3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtZ2nHlEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.edit.text.c.toggle.comment" commandName="Toggle Comment" description="Toggle comment the selected lines" category="_rjtdkHHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtZ23HlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.navigate.previous" commandName="Previous" description="Navigate to the previous item" category="_rjtdlnHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtZ3HHlEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.team.clean" commandName="Clean..." category="_rjtdonHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtZ3XHlEfCAZJ6UDgwwcw" elementId="org.eclipse.tracecompass.tmf.ui.command.open_as_experiment" commandName="Open As Experiment..." description="Open selected traces as an experiment" category="_rjtde3HlEfCAZJ6UDgwwcw">
    <parameters xmi:id="_rjtZ3nHlEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.tmf.ui.commandparameter.select_trace_type.type" name="Trace Type" optional="false"/>
  </commands>
  <commands xmi:id="_rjtZ33HlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.rename" commandName="Rename" description="Rename the selected item" category="_rjtde3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtZ4HHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.project.buildAll" commandName="Build All" description="Build all projects" category="_rjtdm3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtZ4XHlEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.history.OpenInTextEditorCommand" commandName="Open in Text Editor" category="_rjtdi3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtZ4nHlEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.valgrind.launch.clearMarkersCommand" commandName="Remove Valgrind Markers" description="Removes all Valgrind markers" category="_rjtdi3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtZ43HlEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.debug.ui.command.reverseStepOver" commandName="Reverse Step Over" description="Perform Reverse Step Over" category="_rjtdpnHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtZ5HHlEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.internal.lttng2.ui.commands.control.save" commandName="Save..." description="Save session(s)" category="_rjtdgXHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtZ5XHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.cut.line.to.beginning" commandName="Cut to Beginning of Line" description="Cut to the beginning of a line of text" category="_rjtddHHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtZ5nHlEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.debug.application.command.debugAttachedExecutable" commandName="Debug Attached Executable" description="Debug an attached executable" category="_rjtdnXHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtZ53HlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.editors.revisions.rendering.cycle" commandName="Cycle Revision Coloring Mode" description="Cycles through the available coloring modes for revisions" category="_rjtddHHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtZ6HHlEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.search.findrefs.project" commandName="References in Project" description="Searches for references to the selected element in the enclosing project" category="_rjtdkHHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtZ6XHlEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.search.finddecl.project" commandName="Declaration in Project" description="Searches for declarations of the selected element in the enclosing project" category="_rjtdkHHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtZ6nHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ltk.ui.refactoring.commands.moveResources" commandName="Move Resources" description="Move the selected resources and notify LTK participants." category="_rjtdpXHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtZ63HlEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.commands.StepInto" commandName="Step Into" description="Step into" category="_rjtdgnHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtZ7HHlEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.changelog.core.actions.KeyActionCommand" commandName="Insert ChangeLog entry" description="Insert a ChangeLog entry" category="_rjtdd3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtZ7XHlEfCAZJ6UDgwwcw" elementId="org.eclipse.e4.ui.importer.openDirectory" commandName="Open Projects from File System..." category="_rjtdi3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtZ7nHlEfCAZJ6UDgwwcw" elementId="org.eclipse.wst.sse.ui.structure.select.previous" commandName="Select Previous Element" description="Expand selection to include previous sibling" category="_rjtdcXHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtZ73HlEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.RepositoriesViewClearCredentials" commandName="Clear Credentials" category="_rjtdonHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtZ8HHlEfCAZJ6UDgwwcw" elementId="org.eclipse.tracecompass.tmf.ui.command.zoomout.selection" commandName="Zoom out (selection)" category="_rjtdh3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtZ8XHlEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.debug.ui.memory.memorybrowser.jumpToMemory" commandName="Jump to Memory" description="Open memory view and add memory monitor for address" category="_rjtdgnHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtZ8nHlEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.wikitext.ui.convertToMarkupCommand" commandName="Generate Markup" category="_rjtdi3HlEfCAZJ6UDgwwcw">
    <parameters xmi:id="_rjtZ83HlEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.wikitext.ui.targetLanguage" name="TargetLanguage" optional="false"/>
  </commands>
  <commands xmi:id="_rjtZ9HHlEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.tasks.ui.command.showToolTip" commandName="Show Tooltip Description" category="_rjtdeHHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtZ9XHlEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.internal.lttng2.ui.commands.control.addContextOnChannel" commandName="Add Context..." description="Add Context to Channel(s) and/or Event(s)" category="_rjtdgXHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtZ9nHlEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.context.ui.commands.task.copyContext" commandName="Copy Context" category="_rjtddnHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtZ93HlEfCAZJ6UDgwwcw" elementId="org.eclipse.tm.terminal.view.ui.command.launchToolbar" commandName="Open Local Terminal on Selection" category="_rjtdl3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtZ-HHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.window.showViewMenu" commandName="Show View Menu" description="Show the view menu" category="_rjtdfXHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtZ-XHlEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.team.Commit" commandName="Commit..." category="_rjtdonHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtZ-nHlEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.edit.text.c.source.quickMenu" commandName="Show Source Quick Menu" description="Shows the source quick menu" category="_rjtdkHHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtZ-3HlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.shiftRight" commandName="Shift Right" description="Shift a block of text to the right" category="_rjtdcXHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtZ_HHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.navigate.backwardHistory" commandName="Backward History" description="Move backward in the editor navigation history" category="_rjtdlnHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtZ_XHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.removeTrailingWhitespace" commandName="Remove Trailing Whitespace" description="Removes the trailing whitespace of each line" category="_rjtde3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtZ_nHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.findIncremental" commandName="Incremental Find" description="Incremental find" category="_rjtdcXHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtZ_3HlEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.ImportChangedProjectsCommandId" commandName="Import Changed Projects" description="Import or create in local Git repository" category="_rjtdonHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtaAHHlEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.navigate.open.element.in.call.hierarchy" commandName="Open Element in Call Hierarchy" description="Open an element in the call hierarchy view" category="_rjtdlnHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtaAXHlEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.builds.ui.command.ShowBuildOutput" commandName="Show Build Output" category="_rjtdc3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtaAnHlEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.RepositoriesViewCollapseWorkingTree" commandName="Collapse Working Tree" category="_rjtdonHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtaA3HlEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.changelog.core.prepareCommit" commandName="Prepare Commit" description="Copies latest changelog entry to clipboard" category="_rjtdd3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtaBHHlEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.RepositoriesViewNewRemote" commandName="Create Remote..." category="_rjtdonHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtaBXHlEfCAZJ6UDgwwcw" elementId="org.eclipse.embedcdt.packs.ui.commands.updateCommand" commandName="Refresh" category="_rjtdf3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtaBnHlEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.commands.Restart" commandName="Restart" description="Restart a process or debug target without terminating and re-launching" category="_rjtdgnHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtaB3HlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.delete.line" commandName="Delete Line" description="Delete a line of text" category="_rjtddHHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtaCHHlEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.internal.lttng2.ui.commands.control.executeScript" commandName="Execute Command Script..." description="Execute Command Script" category="_rjtdgXHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtaCXHlEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.tmf.ui.command.remove" commandName="Remove" category="_rjtdi3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtaCnHlEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.internal.lttng2.ui.commands.control.enableEventOnChannel" commandName="Enable Event..." description="Enable Event" category="_rjtdgXHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtaC3HlEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.gdbtrace.ui.command.project.trace.selectexecutable" commandName="Select Trace Executable..." description="Select executable binary file for a GDB trace" category="_rjtdi3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtaDHHlEfCAZJ6UDgwwcw" elementId="org.eclipse.oomph.setup.editor.performDropdown" commandName="Perform Dropdown" category="_rjtdk3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtaDXHlEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.internal.lttng2.ui.commands.control.start" commandName="Start" description="Start Trace Session" category="_rjtdgXHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtaDnHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.findReplace" commandName="Find and Replace" description="Find and replace text" category="_rjtdcXHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtaD3HlEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.tmf.ui.openFile" commandName="Open File" description="Opens a file" category="_rjtde3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtaEHHlEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.team.MergeTool" commandName="Merge Tool" category="_rjtdonHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtaEXHlEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.command.shareProject" commandName="Share with Git" description="Share the project using Git" category="_rjtdi3HlEfCAZJ6UDgwwcw">
    <parameters xmi:id="_rjtaEnHlEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.command.projectNameParameter" name="Project" optional="false"/>
  </commands>
  <commands xmi:id="_rjtaE3HlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.window.fullscreenmode" commandName="Toggle Full Screen" description="Toggles the window between full screen and normal" category="_rjtdfXHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtaFHHlEfCAZJ6UDgwwcw" elementId="org.eclipse.equinox.p2.ui.sdk.installationDetails" commandName="Installation Details" category="_rjtdi3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtaFXHlEfCAZJ6UDgwwcw" elementId="org.eclipse.team.ui.synchronizeAll" commandName="Synchronize..." description="Synchronize resources in the workspace with another location" category="_rjtdjnHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtaFnHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.window.nextEditor" commandName="Next Editor" description="Switch to the next editor" category="_rjtdfXHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtaF3HlEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.internal.reflog.CopyCommand" commandName="Copy Commit Id" category="_rjtdi3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtaGHHlEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.tasks.ui.command.new.subtask" commandName="New Subtask" category="_rjtdeHHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtaGXHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.help.helpContents" commandName="Help Contents" description="Open the help contents" category="_rjtdmnHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtaGnHlEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.team.Reset" commandName="Reset..." category="_rjtdonHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtaG3HlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.delete" commandName="Delete" description="Delete the selection" category="_rjtdcXHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtaHHHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.delete.line.to.beginning" commandName="Delete to Beginning of Line" description="Delete to the beginning of a line of text" category="_rjtddHHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtaHXHlEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.team.RenameBranch" commandName="Rename Branch..." category="_rjtdonHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtaHnHlEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.team.Synchronize" commandName="Synchronize" category="_rjtdonHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtaH3HlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.findPrevious" commandName="Find Previous" description="Find previous item" category="_rjtdcXHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtaIHHlEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.commands.ToggleBreakpoint" commandName="Toggle Breakpoint" description="Creates or removes a breakpoint" category="_rjtdgnHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtaIXHlEfCAZJ6UDgwwcw" elementId="org.eclipse.launchbar.ui.command.buildActive" commandName="Build Active Launch Configuration" category="_rjtdoXHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtaInHlEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.tasks.ui.command.openTask" commandName="Open Task" category="_rjtdlnHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtaI3HlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.scroll.lineUp" commandName="Scroll Line Up" description="Scroll up one line of text" category="_rjtddHHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtaJHHlEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.menu.findUnresolvedIncludes" commandName="Search for Unresolved Includes" category="_rjtdm3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtaJXHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.folding.toggle" commandName="Toggle Folding" description="Toggles folding in the current editor" category="_rjtddHHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtaJnHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.toggleShowWhitespaceCharacters" commandName="Show Whitespace Characters" description="Shows whitespace characters in current text editor" category="_rjtddHHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtaJ3HlEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.internal.lttng2.ui.commands.control.disableChannel" commandName="Disable Channel" description="Disable a Trace Channel" category="_rjtdgXHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtaKHHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.gotoNextEditPosition" commandName="Next Edit Location" description="Next edit location" category="_rjtdlnHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtaKXHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.file.revert" commandName="Revert" description="Revert to the last saved state" category="_rjtde3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtaKnHlEfCAZJ6UDgwwcw" elementId="org.eclipse.oomph.ui.ToggleOfflineMode" commandName="Toggle Offline Mode" category="_rjtdinHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtaK3HlEfCAZJ6UDgwwcw" elementId="org.eclipse.oomph.setup.editor.openLog" commandName="Open Setup Log" category="_rjtdk3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtaLHHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.project.buildAutomatically" commandName="Build Automatically" description="Toggle the workspace build automatically function" category="_rjtdm3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtaLXHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.file.import" commandName="Import" description="Import" category="_rjtde3HlEfCAZJ6UDgwwcw">
    <parameters xmi:id="_rjtaLnHlEfCAZJ6UDgwwcw" elementId="importWizardId" name="Import Wizard"/>
  </commands>
  <commands xmi:id="_rjtaL3HlEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.history.Merge" commandName="Merge" category="_rjtdi3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtaMHHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.window.switchToEditor" commandName="Switch to Editor" description="Switch to an editor" category="_rjtdfXHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtaMXHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.help.dynamicHelp" commandName="Show Context Help" description="Open the contextual help" category="_rjtdmnHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtaMnHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.toMultiSelection" commandName="To multi-selection" description="Turn current selection into multiple text selections" category="_rjtdcXHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtaM3HlEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.tasks.ui.command.activateTask" commandName="Activate Task" category="_rjtdlnHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtaNHHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.toggleInsertMode" commandName="Toggle Insert Mode" description="Toggle insert mode" category="_rjtdcXHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtaNXHlEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.RepositoriesViewDelete" commandName="Delete Repository" category="_rjtdonHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtaNnHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.file.closePart" commandName="Close Part" description="Close the active workbench part" category="_rjtdfXHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtaN3HlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.editors.revisions.id.toggle" commandName="Toggle Revision Id Display" description="Toggles the display of the revision id" category="_rjtddHHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtaOHHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.project.cleanAction" commandName="Build Clean" description="Discard old built state" category="_rjtdm3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtaOXHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.select.multiCaretDown" commandName="Multi caret down" description="Add a new caret/multi selection below the current line, or remove the first caret/multi selection " category="_rjtddHHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtaOnHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.navigate.back" commandName="Back" description="Navigate back" category="_rjtdlnHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtaO3HlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.select.wordNext" commandName="Select Next Word" description="Select the next word" category="_rjtddHHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtaPHHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.goto.pageDown" commandName="Page Down" description="Go down one page" category="_rjtddHHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtaPXHlEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.builds.ui.commands.OpenBuildElement" commandName="Open Build Element" category="_rjtdc3HlEfCAZJ6UDgwwcw">
    <parameters xmi:id="_rjtaPnHlEfCAZJ6UDgwwcw" elementId="element" name="Element"/>
  </commands>
  <commands xmi:id="_rjtaP3HlEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.refactoring.command.ExtractConstant" commandName="Extract Constant..." category="_rjtdfnHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtaQHHlEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.team.ui.commands.CopyCommitMessage" commandName="Copy Commit Message for Task" description="Copies a commit message for the currently selected task to the clipboard." category="_rjtdeHHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtaQXHlEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.team.GarbageCollect" commandName="Collect Garbage" category="_rjtdonHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtaQnHlEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.history.CompareWithWorkingTree" commandName="Compare with Working Tree" category="_rjtdi3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtaQ3HlEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.team.Branch" commandName="Branch" description="Check out, rename, create, or delete a branch in a git repository" category="_rjtdonHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtaRHHlEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.debug.ui.command.loadAllSymbols" commandName="Load Symbols For All" category="_rjtdi3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtaRXHlEfCAZJ6UDgwwcw" elementId="org.eclipse.wst.xml.ui.previousSibling" commandName="Previous Sibling" description="Go to Previous Sibling" category="_rjtdcXHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtaRnHlEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.edit.text.c.indent" commandName="Indent Line" description="Indents the current line" category="_rjtdkHHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtaR3HlEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.RepositoriesViewConfigurePush" commandName="Configure Push..." category="_rjtdonHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtaSHHlEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.menu.createParserLog" commandName="Create Parser Log File" category="_rjtdm3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtaSXHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.findIncrementalReverse" commandName="Incremental Find Reverse" description="Incremental find reverse" category="_rjtdcXHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtaSnHlEfCAZJ6UDgwwcw" elementId="org.eclipse.epp.mpc.ui.command.importFavoritesWizard" commandName="Import Marketplace Favorites" description="Import another user's Marketplace Favorites List" category="_rjtdi3HlEfCAZJ6UDgwwcw">
    <parameters xmi:id="_rjtaS3HlEfCAZJ6UDgwwcw" elementId="favoritesUrl" name="favoritesUrl"/>
  </commands>
  <commands xmi:id="_rjtaTHHlEfCAZJ6UDgwwcw" elementId="org.eclipse.tracecompass.tmf.ui.filter" commandName="Filter Time Graph events" category="_rjtdh3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtaTXHlEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.team.stash.apply" commandName="Apply Stashed Changes" category="_rjtdonHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtaTnHlEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.edit.text.c.goto.next.bookmark" commandName="Next Bookmark" description="Goes to the next bookmark of the selected file" category="_rjtdkHHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtaT3HlEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.debug.ui.command.addRegisterGroup" commandName="Add RegisterGroup" description="Adds a Register Group" category="_rjtddXHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtaUHHlEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.debug.ui.command.resumeWithoutSignal" commandName="Resume Without Signal" description="Resume Without Signal" category="_rjtdlXHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtaUXHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.goto.columnNext" commandName="Next Column" description="Go to the next column" category="_rjtddHHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtaUnHlEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.history.Squash" commandName="Squash Commits" category="_rjtdi3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtaU3HlEfCAZJ6UDgwwcw" elementId="org.eclipse.search.ui.performTextSearchWorkingSet" commandName="Find Text in Working Set" description="Searches the files in the working set for specific text." category="_rjtdoHHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtaVHHlEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.edit.text.c.add.include" commandName="Add Include" description="Create include statement on selection" category="_rjtdkHHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtaVXHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.goto.wordNext" commandName="Next Word" description="Go to the next word" category="_rjtddHHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtaVnHlEfCAZJ6UDgwwcw" elementId="org.eclipse.oomph.setup.donate" commandName="Sponsor" description="Sponsor to the development of the Eclipse IDE" category="_rjtdi3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtaV3HlEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.internal.lttng2.ui.commands.control.assign.logger" commandName="Enable Logger..." description="Assign Logger to Session and Enable Logger" category="_rjtdgXHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtaWHHlEfCAZJ6UDgwwcw" elementId="org.eclipse.quickdiff.toggle" commandName="Quick Diff Toggle" description="Toggles quick diff information display on the line number ruler" category="_rjtdcXHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtaWXHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.deleteNext" commandName="Delete Next" description="Delete the next character" category="_rjtddHHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtaWnHlEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.FetchGerritChange" commandName="Fetch From Gerrit" category="_rjtdonHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtaW3HlEfCAZJ6UDgwwcw" elementId="org.eclipse.tracecompass.tmf.remote.ui.command.fetchlog" commandName="Fetch Remote Traces..." category="_rjtdh3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtaXHHlEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.team.ReplaceWithTheirs" commandName="Replace Conflicting Files with Their Revision" category="_rjtdonHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtaXXHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.toggleShowKeys" commandName="Toggle Show Key Bindings" description="Shows key binding when command is invoked" category="_rjtdfXHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtaXnHlEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.tasks.ui.command.SynchronizeAll" commandName="Synchronize Changed" category="_rjtdeHHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtaX3HlEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.history.Reword" commandName="Reword Commit" category="_rjtdi3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtaYHHlEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.team.OpenCommit" commandName="Open Git Commit" category="_rjtdonHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtaYXHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.help.displayHelp" commandName="Display Help" description="Display a Help topic" category="_rjtdmnHlEfCAZJ6UDgwwcw">
    <parameters xmi:id="_rjtaYnHlEfCAZJ6UDgwwcw" elementId="href" name="Help topic href"/>
  </commands>
  <commands xmi:id="_rjtaY3HlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.select.pageDown" commandName="Select Page Down" description="Select to the bottom of the page" category="_rjtddHHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtaZHHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.join.lines" commandName="Join Lines" description="Join lines of text" category="_rjtddHHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtaZXHlEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.dsf.ui.addRegistersExpression" commandName="Add Expression Group > Registers" category="_rjtdgnHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtaZnHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.window.showContextMenu" commandName="Show Context Menu" description="Show the context menu" category="_rjtdfXHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtaZ3HlEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.debug.ui.command.restoreRegisterGroups" commandName="Restore Default Register Groups" description="Restores the Default Register Groups" category="_rjtddXHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtaaHHlEfCAZJ6UDgwwcw" elementId="org.eclipse.oomph.p2.ui.ExploreRepository" commandName="Explore Repository" category="_rjtdhXHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtaaXHlEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.internal.lttng2.ui.commands.control.disableEvent" commandName="Disable Event" description="Disable Event" category="_rjtdgXHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtaanHlEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.team.InstallLfsLocal" commandName="Enable LFS locally" category="_rjtdonHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtaa3HlEfCAZJ6UDgwwcw" elementId="org.eclipse.jdt.ui.edit.text.java.correction.assist.proposals" commandName="Quick Fix" description="Suggest possible fixes for a problem" category="_rjtdcXHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtabHHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.window.nextPerspective" commandName="Next Perspective" description="Switch to the next perspective" category="_rjtdfXHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtabXHlEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.tasks.ui.command.UpdateRepositoryConfiguration" commandName="Update Repository Configuration" category="_rjtdi3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtabnHlEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.edit.open.call.hierarchy" commandName="Open Call Hierarchy" description="Opens the call hierarchy for the selected element" category="_rjtdlnHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtab3HlEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.commands.console.clear" commandName="Clear Console" description="Clear Console" category="_rjtdcXHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtacHHlEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.dsf.ui.addLocalsExpression" commandName="Add Expression Group > Local Variables" category="_rjtdgnHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtacXHlEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.history.ShowVersions" commandName="Open this Version" category="_rjtdi3HlEfCAZJ6UDgwwcw">
    <parameters xmi:id="_rjtacnHlEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.history.CompareMode" name="Compare mode"/>
  </commands>
  <commands xmi:id="_rjtac3HlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.navigate.nextTab" commandName="Next Tab" description="Switch to the next tab" category="_rjtdlnHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtadHHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.window.quickAccess" commandName="Find Actions" description="Quickly access UI elements" category="_rjtdfXHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtadXHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.showInformation" commandName="Show Tooltip Description" description="Displays information for the current caret location in a focused hover" category="_rjtddHHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtadnHlEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.tasks.ui.command.attachment.open" commandName="Open Attachment" category="_rjtdkXHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtad3HlEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.dsf.debug.ui.disassembly.commands.gotoPC" commandName="Go to Program Counter" description="Navigate to current program counter" category="_rjtdgnHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtaeHHlEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.commands.toggleMemoryMonitorsPane" commandName="Toggle Memory Monitors Pane" description="Toggle visibility of the Memory Monitors Pane" category="_rjtdgnHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtaeXHlEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.team.Discard" commandName="Replace with File in Index" category="_rjtdonHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtaenHlEfCAZJ6UDgwwcw" elementId="org.eclipse.tracecompass.tmf.ui.command.left" commandName="Left" category="_rjtdh3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtae3HlEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.RepositoriesViewCreateBranch" commandName="Create Branch..." category="_rjtdonHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtafHHlEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.RepositoriesViewCopyPath" commandName="Copy Path to Clipboard" category="_rjtdonHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtafXHlEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.builds.ui.command.NewTaskFromTest" commandName="New Task From Test" category="_rjtdc3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtafnHlEfCAZJ6UDgwwcw" elementId="org.eclipse.oomph.setup.editor.perform.startup" commandName="Perform Setup Tasks (Startup)" category="_rjtdk3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtaf3HlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.showRulerAnnotationInformation" commandName="Show Ruler Annotation Tooltip" description="Displays annotation information for the caret line in a focused hover" category="_rjtddHHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtagHHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.browser.openBrowser" commandName="Open Browser" description="Opens the default web browser." category="_rjtdfXHlEfCAZJ6UDgwwcw">
    <parameters xmi:id="_rjtagXHlEfCAZJ6UDgwwcw" elementId="url" name="URL"/>
    <parameters xmi:id="_rjtagnHlEfCAZJ6UDgwwcw" elementId="browserId" name="Browser Id"/>
    <parameters xmi:id="_rjtag3HlEfCAZJ6UDgwwcw" elementId="name" name="Browser Name"/>
    <parameters xmi:id="_rjtahHHlEfCAZJ6UDgwwcw" elementId="tooltip" name="Browser Tooltip"/>
  </commands>
  <commands xmi:id="_rjtahXHlEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.history.DeleteBranch" commandName="Delete Branch" category="_rjtdi3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtahnHlEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.navigate.opentype" commandName="Open Element" description="Open an element in an Editor" category="_rjtdkHHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtah3HlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.contentAssist.contextInformation" commandName="Context Information" description="Show Context Information" category="_rjtdcXHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtaiHHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.file.saveAs" commandName="Save As" description="Save the current contents to another location" category="_rjtde3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtaiXHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.window.previousPerspective" commandName="Previous Perspective" description="Switch to the previous perspective" category="_rjtdfXHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtainHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.window.splitEditor" commandName="Toggle Split Editor" description="Split or join the currently active editor." category="_rjtdfXHlEfCAZJ6UDgwwcw">
    <parameters xmi:id="_rjtai3HlEfCAZJ6UDgwwcw" elementId="Splitter.isHorizontal" name="Orientation" optional="false"/>
  </commands>
  <commands xmi:id="_rjtajHHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.navigate.collapseAll" commandName="Collapse All" description="Collapse the current tree" category="_rjtdlnHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtajXHlEfCAZJ6UDgwwcw" elementId="org.eclipse.compare.copyAllRightToLeft" commandName="Copy All from Right to Left" description="Copy All Changes from Right to Left" category="_rjtdmHHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtajnHlEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.tasks.ui.command.deactivateSelectedTask" commandName="Deactivate Selected Task" category="_rjtdlnHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtaj3HlEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.menu.updateWithModifiedFiles" commandName="Update Index with Modified Files" category="_rjtdm3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtakHHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.window.lockToolBar" commandName="Toggle Lock Toolbars" description="Toggle the Lock on the Toolbars" category="_rjtdfXHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtakXHlEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.tmf.ui.exportToText" commandName="Export to Text..." description="Export trace to text..." category="_rjtdh3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtaknHlEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.commands.Disconnect" commandName="Disconnect" description="Disconnect" category="_rjtdgnHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtak3HlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.navigate.removeFromWorkingSet" commandName="Remove From Working Set" description="Removes the selected object from a working set." category="_rjtdcXHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtalHHlEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.tasks.ui.command.openSelectedTask" commandName="Open Selected Task" category="_rjtdeHHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtalXHlEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.RepositoriesLinkWithSelection" commandName="Toggle &quot;Link with Editor and Selection&quot; (Git Repositories View)" category="_rjtdonHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtalnHlEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.context.ui.commands.toggle.focus.active.view" commandName="Focus on Active Task" description="Toggle the focus on active task for the active view" category="_rjtddnHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtal3HlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.ide.deleteCompleted" commandName="Delete Completed Tasks" description="Delete the tasks marked as completed" category="_rjtdi3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtamHHlEfCAZJ6UDgwwcw" elementId="org.eclipse.tracecompass.tmf.ui.command.analysis_add" commandName="Add External Analysis" description="Add External Analysis" category="_rjtdh3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtamXHlEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.tasks.ui.command.goToNextUnread" commandName="Go To Next Unread Task" category="_rjtdlnHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtamnHlEfCAZJ6UDgwwcw" elementId="org.eclipse.wst.validation.ValidationCommand" commandName="Validate" description="Invoke registered Validators" category="_rjtdi3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtam3HlEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.internal.lttng2.ui.commands.control.enableEventOnSession" commandName="Enable Event (default channel)..." description="Enable Event on Default Channel" category="_rjtdgXHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtanHHlEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.RebaseInteractiveCurrent" commandName="Interactive Rebase" category="_rjtdi3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtanXHlEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.tmf.ui.views.uml2sd.handlers.ShowNodeEnd" commandName="Show node end" description="Show the node end" category="_rjtdo3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtannHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.window.openEditorDropDown" commandName="Quick Switch Editor" description="Open the editor drop down list" category="_rjtdfXHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtan3HlEfCAZJ6UDgwwcw" elementId="AnsiConsole.command.copy_with_escapes" commandName="Copy Text With ANSI Escapes" description="Copy the console content to clipboard, including the escape sequences" category="_rjtdeXHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtaoHHlEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.tmf.ui.command.importtracepkg" commandName="Import Trace Package..." category="_rjtdh3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtaoXHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.deleteNextWord" commandName="Delete Next Word" description="Delete the next word" category="_rjtddHHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtaonHlEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.dsf.debug.ui.refreshAll" commandName="Refresh Debug Views" description="Refresh all data in debug views" category="_rjtdgnHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtao3HlEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.SkipRebase" commandName="Skip commit and continue" category="_rjtdonHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtapHHlEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.history.SetQuickdiffBaseline" commandName="Set quickdiff baseline" category="_rjtdi3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtapXHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.undo" commandName="Undo" description="Undo the last operation" category="_rjtdcXHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtapnHlEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.internal.lttng2.ui.commands.control.disconnect" commandName="Disconnect" description="Disconnect to Target Node" category="_rjtdgXHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtap3HlEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.tmf.ui.command.managecustomparsers" commandName="Manage Custom Parsers..." description="Manage Custom Parsers" category="_rjtdjHHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtaqHHlEfCAZJ6UDgwwcw" elementId="org.riscvstudio.ide.commands.Nuclei_SDK_Documentation" commandName="Nuclei SDK Documentation"/>
  <commands xmi:id="_rjtaqXHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.file.newQuickMenu" commandName="New menu" description="Open the New menu" category="_rjtde3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtaqnHlEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.actions.WatchCommand" commandName="Watch" description="Create a watch expression from the current selection and add it to the Expressions view" category="_rjtdi3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtaq3HlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.file.openWorkspace" commandName="Switch Workspace" description="Open the workspace selection dialog" category="_rjtde3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtarHHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.file.closeAll" commandName="Close All" description="Close all editors" category="_rjtde3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtarXHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.cut" commandName="Cut" description="Cut the selection to the clipboard" category="_rjtdcXHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtarnHlEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.internal.lttng2.ui.commands.control.addContextOnEvent" commandName="Add Context..." description="Add Context to Channel(s) and/or Event(s)" category="_rjtdgXHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtar3HlEfCAZJ6UDgwwcw" elementId="org.eclipse.tracecompass.tmf.ui.command.zoomout" commandName="Zoom out (mouse position)" category="_rjtdh3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtasHHlEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.team.Merge" commandName="Merge" category="_rjtdonHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtasXHlEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.team.ReplaceWithRef" commandName="Replace with branch, tag, or reference" category="_rjtdonHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtasnHlEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.tasks.ui.command.submitTask" commandName="Submit Task" description="Submits the currently open task" category="_rjtdkXHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtas3HlEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.edit.text.c.toggleMarkOccurrences" commandName="Toggle Mark Occurrences" description="Toggles mark occurrences in C/C++ editors" category="_rjtdkHHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtatHHlEfCAZJ6UDgwwcw" elementId="org.eclipse.wst.xml.ui.reload.dependencies" commandName="Reload Dependencies" description="Reload Dependencies" category="_rjtdi3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtatXHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.toggleShowSelectedElementOnly" commandName="Show Selected Element Only" description="Show Selected Element Only" category="_rjtdfXHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtatnHlEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.edit.text.c.uncomment" commandName="Uncomment" description="Uncomments the selected // style comment lines" category="_rjtdkHHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtat3HlEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.internal.merge.ToggleCurrentChangesCommand" commandName="Ignore Changes from Ancestor to Current Version" description="Toggle ignoring changes only between the ancestor and the current version in a three-way merge comparison" category="_rjtdonHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtauHHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.file.export" commandName="Export" description="Export" category="_rjtde3HlEfCAZJ6UDgwwcw">
    <parameters xmi:id="_rjtauXHlEfCAZJ6UDgwwcw" elementId="exportWizardId" name="Export Wizard"/>
  </commands>
  <commands xmi:id="_rjtaunHlEfCAZJ6UDgwwcw" elementId="org.eclipse.tracecompass.tmf.ui.command.report_delete" commandName="Delete Report" description="Delete this report from the project" category="_rjtdh3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtau3HlEfCAZJ6UDgwwcw" elementId="org.eclipse.embedcdt.debug.gdbjtag.restart.ui.commands.Restart" commandName="Restart" description="Restart a process or debug target without terminating and re-launching" category="_rjtdlHHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtavHHlEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.team.ApplyPatch" commandName="Apply Patch" category="_rjtdonHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtavXHlEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.make.ui.targetBuildCommand" commandName="Build Target Build" description="Invoke a make target build for the selected container." category="_rjtdm3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtavnHlEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.edit.text.c.organize.includes" commandName="Organize Includes" description="Evaluates all required includes and replaces the current includes" category="_rjtdkHHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtav3HlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.ide.copyConfigCommand" commandName="Copy Configuration Data To Clipboard" description="Copies the configuration data (system properties, installed bundles, etc) to the clipboard." category="_rjtdcXHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtawHHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.navigate.forwardHistory" commandName="Forward History" description="Move forward in the editor navigation history" category="_rjtdlnHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtawXHlEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.tmf.ui.views.uml2sd.handlers.MoveSDUp" commandName="Scroll up" description="Scroll up the sequence diagram" category="_rjtdo3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtawnHlEfCAZJ6UDgwwcw" elementId="org.eclipse.search.ui.performTextSearchProject" commandName="Find Text in Project" description="Searches the files in the project for specific text." category="_rjtdoHHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtaw3HlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.project.rebuildAll" commandName="Rebuild All" description="Rebuild all projects" category="_rjtdm3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtaxHHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.openLocalFile" commandName="Open File..." description="Open a file" category="_rjtde3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtaxXHlEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.team.Disconnect" commandName="Disconnect" category="_rjtdonHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtaxnHlEfCAZJ6UDgwwcw" elementId="org.eclipse.launchbar.ui.command.stop" commandName="Stop" category="_rjtdoXHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtax3HlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.file.refresh" commandName="Refresh" description="Refresh the selected items" category="_rjtde3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtayHHlEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.team.ReplaceWithOurs" commandName="Replace Conflicting Files with Our Revision" category="_rjtdonHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtayXHlEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.RepositoriesViewChangeCredentials" commandName="Change Credentials" category="_rjtdonHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtaynHlEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.edit.text.c.goto.matching.bracket" commandName="Go to Matching Bracket" description="Moves the cursor to the matching bracket" category="_rjtdkHHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtay3HlEfCAZJ6UDgwwcw" elementId="org.eclipse.oomph.p2.ui.SearchRepositories" commandName="Search Repositories" category="_rjtdhXHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtazHHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.window.closeAllPerspectives" commandName="Close All Perspectives" description="Close all open perspectives" category="_rjtdfXHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtazXHlEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.make.ui.targetBuildLastCommand" commandName="Rebuild Last Target" description="Rebuild the last make target for the selected container or project." category="_rjtdm3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtaznHlEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.dsf.gdb.ui.command.osview.connect" commandName="Connect" description="Connect to selected processes" category="_rjtdi3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtaz3HlEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.commands.TerminateAll" commandName="Terminate/Disconnect All" description="Terminate/Disconnect All" category="_rjtdgnHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjta0HHlEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.managedbuilder.ui.convertTarget" commandName="Convert To" category="_rjtdm3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjta0XHlEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.internal.lttng2.ui.commands.control.enableChannelOnDomain" commandName="Enable Channel..." description="Enable a Trace Channel" category="_rjtdgXHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjta0nHlEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.debug.ui.command.loadSymbols" commandName="Load Symbols" category="_rjtdi3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjta03HlEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.debug.ui.command.groupDebugContexts" commandName="Group" description="Groups the selected debug contexts" category="_rjtdnXHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjta1HHlEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.tasks.ui.command.disconnected" commandName="Disconnected" category="_rjtdi3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjta1XHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.select.lineStart" commandName="Select Line Start" description="Select to the beginning of the line of text" category="_rjtddHHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjta1nHlEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.changelog.core.preparechangelog" commandName="Prepare Changelog" description="Prepares Changelog" category="_rjtdd3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjta13HlEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.commit.Reword" commandName="Reword Commit" category="_rjtdi3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjta2HHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.editors.lineNumberToggle" commandName="Show Line Numbers" description="Toggle display of line numbers" category="_rjtddHHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjta2XHlEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.github.ui.command.createGist" commandName="Create Gist" description="Create Gist based on selection" category="_rjtdi3HlEfCAZJ6UDgwwcw">
    <parameters xmi:id="_rjta2nHlEfCAZJ6UDgwwcw" elementId="publicGist" name="Public Gist"/>
  </commands>
  <commands xmi:id="_rjta23HlEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.edit.text.c.select.enclosing" commandName="Select Enclosing C/C++ Element" description="Expand the selection to enclosing C/C++ element" category="_rjtdcXHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjta3HHlEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.tasks.index.ui.command.ResetIndex" commandName="Refresh Search Index" category="_rjtdeHHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjta3XHlEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.github.ui.command.rebasePullRequest" commandName="Rebase pull request" description="Rebase onto destination branch" category="_rjtdi3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjta3nHlEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.RepositoriesToggleBranchCommit" commandName="Toggle Latest Branch Commit" category="_rjtdonHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjta33HlEfCAZJ6UDgwwcw" elementId="org.eclipse.tm.terminal.paste" commandName="Paste" category="_rjtdnnHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjta4HHlEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.history.PushCommit" commandName="Push Commit..." category="_rjtdi3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjta4XHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.genericeditor.gotoMatchingBracket" commandName="Go to Matching Bracket" description="Moves the cursor to the matching bracket" category="_rjtdi3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjta4nHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.ide.showInSystemExplorer" commandName="Show In (System Explorer)" description="Show in system's explorer (file manager)" category="_rjtdlnHlEfCAZJ6UDgwwcw">
    <parameters xmi:id="_rjta43HlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.ide.showInSystemExplorer.path" name="Resource System Path Parameter"/>
  </commands>
  <commands xmi:id="_rjta5HHlEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.tasks.ui.command.new.local.task" commandName="New Local Task" category="_rjtdeHHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjta5XHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.select.lineDown" commandName="Select Line Down" description="Extend the selection to the next line of text" category="_rjtddHHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjta5nHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.lowerCase" commandName="To Lower Case" description="Changes the selection to lower case" category="_rjtddHHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjta53HlEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.team.CompareWithIndex" commandName="Compare with Index" category="_rjtdonHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjta6HHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.zoomOut" commandName="Zoom Out" description="Zoom out text, decrease default font size for text editors" category="_rjtddHHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjta6XHlEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.tasks.ui.command.markTaskIncomplete" commandName="Mark Task Incomplete" category="_rjtdeHHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjta6nHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.file.save" commandName="Save" description="Save the current contents" category="_rjtde3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjta63HlEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.tasks.ui.command.markTaskReadGoToNextUnread" commandName="Mark Task Read and Go To Next Unread Task" category="_rjtdeHHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjta7HHlEfCAZJ6UDgwwcw" elementId="org.eclipse.team.ui.applyPatch" commandName="Apply Patch..." description="Apply a patch to one or more workspace projects." category="_rjtdjnHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjta7XHlEfCAZJ6UDgwwcw" elementId="org.eclipse.tracecompass.tmf.ui.command.trim_trace" commandName="Export Time Selection as New Trace..." description="Create a new trace containing only the events in the currently selected time range. Only available if the trace type supports it, and if a time range is selected." category="_rjtdi3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjta7nHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.select.lineEnd" commandName="Select Line End" description="Select to the end of the line of text" category="_rjtddHHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjta73HlEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.tasks.ui.command.markTaskRead" commandName="Mark Task Read" category="_rjtdeHHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjta8HHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.select.wordPrevious" commandName="Select Previous Word" description="Select the previous word" category="_rjtddHHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjta8XHlEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.commands.StepOver" commandName="Step Over" description="Step over" category="_rjtdgnHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjta8nHlEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.debug.ui.command.reverseStepInto" commandName="Reverse Step Into" description="Perform Reverse Step Into" category="_rjtdpnHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjta83HlEfCAZJ6UDgwwcw" elementId="org.eclipse.compare.selectPreviousChange" commandName="Select Previous Change" description="Select Previous Change" category="_rjtdmHHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjta9HHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.file.exit" commandName="Exit" description="Exit the application" category="_rjtde3HlEfCAZJ6UDgwwcw">
    <parameters xmi:id="_rjta9XHlEfCAZJ6UDgwwcw" elementId="mayPrompt" name="may prompt"/>
  </commands>
  <commands xmi:id="_rjta9nHlEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.team.CompareWithHead" commandName="Compare with HEAD Revision" category="_rjtdonHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjta93HlEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.team.CompareWithCommit" commandName="Compare with Commit..." category="_rjtdonHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjta-HHlEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.RepositoriesViewOpen" commandName="Open" category="_rjtdonHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjta-XHlEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.tasks.ui.command.deactivateAllTasks" commandName="Deactivate Task" category="_rjtdlnHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjta-nHlEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.team.ShowHistory" commandName="Show in History" category="_rjtdonHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjta-3HlEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.internal.lttng2.ui.commands.control.assign.event" commandName="Enable Event..." description="Assign Event to Session and Channel and Enable Event" category="_rjtdgXHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjta_HHlEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.team.SimplePush" commandName="Push to Upstream" category="_rjtdonHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjta_XHlEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.commands.addMemoryMonitor" commandName="Add Memory Block" description="Add memory block" category="_rjtdgnHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjta_nHlEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.PushHeadToGerrit" commandName="Push Current Head to Gerrit" category="_rjtdonHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjta_3HlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.selectAll" commandName="Select All" description="Select all" category="_rjtdcXHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtbAHHlEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.tmf.ui.command.delete_suppl_files" commandName="Delete Supplementary Files..." category="_rjtdh3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtbAXHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.project.rebuildProject" commandName="Rebuild Project" description="Rebuild the selected projects" category="_rjtdm3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtbAnHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.copyLineDown" commandName="Copy Lines" description="Duplicates the selected lines and moves the selection to the copy" category="_rjtddHHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtbA3HlEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.menu.manage.configs.command" commandName="Manage Build Configurations" category="_rjtdm3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtbBHHlEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.refactor.extract.local.variable" commandName="Extract Local Variable - Refactoring " description="Extracts a local variable for the selected expression" category="_rjtdfnHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtbBXHlEfCAZJ6UDgwwcw" elementId="org.eclipse.tracecompass.tmf.ui.command.zoom.selection" commandName="Zoom to selection" category="_rjtdh3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtbBnHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.select.selectMultiSelectionUp" commandName="Multi selection up relative to anchor selection" description="Search next matching region above and add it to the current selection, or remove last element from current multi-selection " category="_rjtddHHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtbB3HlEfCAZJ6UDgwwcw" elementId="org.eclipse.oomph.setup.editor.perform" commandName="Perform Setup Tasks" category="_rjtdk3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtbCHHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.help.installationDialog" commandName="Installation Information" description="Open the installation dialog" category="_rjtdmnHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtbCXHlEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.commands.ToggleStepFilters" commandName="Use Step Filters" description="Toggles enablement of debug step filters" category="_rjtdgnHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtbCnHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.goto.lineUp" commandName="Line Up" description="Go up one line of text" category="_rjtddHHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtbC3HlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.goto.windowStart" commandName="Window Start" description="Go to the start of the window" category="_rjtddHHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtbDHHlEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.commons.ui.command.AddRepository" commandName="Add Repository" category="_rjtdiHHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtbDXHlEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.commit.DiffEditorQuickOutlineCommand" commandName="Quick Outline" description="Show the quick outline for a unified diff" category="_rjtdonHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtbDnHlEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.team.AssumeUnchanged" commandName="Assume Unchanged" category="_rjtdonHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtbD3HlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.window.closePerspective" commandName="Close Perspective" description="Close the current perspective" category="_rjtdfXHlEfCAZJ6UDgwwcw">
    <parameters xmi:id="_rjtbEHHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.window.closePerspective.perspectiveId" name="Perspective Id"/>
  </commands>
  <commands xmi:id="_rjtbEXHlEfCAZJ6UDgwwcw" elementId="org.eclipse.wst.xml.ui.nextSibling" commandName="Next Sibling" description="Go to Next Sibling" category="_rjtdcXHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtbEnHlEfCAZJ6UDgwwcw" elementId="org.eclipse.tracecompass.tmf.ui.command.zoomin" commandName="Zoom in (mouse position)" category="_rjtdh3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtbE3HlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.cheatsheets.openCheatSheetURL" commandName="Open Cheat Sheet from URL" description="Open a Cheat Sheet from file at a specified URL." category="_rjtdmnHlEfCAZJ6UDgwwcw">
    <parameters xmi:id="_rjtbFHHlEfCAZJ6UDgwwcw" elementId="cheatSheetId" name="Identifier" optional="false"/>
    <parameters xmi:id="_rjtbFXHlEfCAZJ6UDgwwcw" elementId="name" name="Name" optional="false"/>
    <parameters xmi:id="_rjtbFnHlEfCAZJ6UDgwwcw" elementId="url" name="URL" optional="false"/>
  </commands>
  <commands xmi:id="_rjtbF3HlEfCAZJ6UDgwwcw" elementId="org.eclipse.tracecompass.tmf.ui.command.right" commandName="Right" category="_rjtdh3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtbGHHlEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.team.PushBranch" commandName="Push Branch..." category="_rjtdonHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtbGXHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.file.closeAllSaved" commandName="Close All Saved" description="Close all saved editors" category="_rjtde3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtbGnHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.redo" commandName="Redo" description="Redo the last operation" category="_rjtdcXHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtbG3HlEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.refactoring.command.ExtractLocalVariable" commandName="Extract Local Variable..." category="_rjtdfnHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtbHHHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.navigate.selectWorkingSets" commandName="Select Working Sets" description="Select the working sets that are applicable for this window." category="_rjtdfXHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtbHXHlEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.codan.commands.runCodanCommand" commandName="Run Code Analysis" category="_rjtdnHHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtbHnHlEfCAZJ6UDgwwcw" elementId="org.eclipse.tm.terminal.view.ui.command.newview" commandName="New Terminal View" category="_rjtdl3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtbH3HlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.file.close" commandName="Close" description="Close the active editor" category="_rjtde3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtbIHHlEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.excludeCommand" commandName="Exclude from Build" category="_rjtdi3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtbIXHlEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.history.Reset" commandName="Reset..." category="_rjtdi3HlEfCAZJ6UDgwwcw">
    <parameters xmi:id="_rjtbInHlEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.history.ResetMode" name="Reset mode" optional="false"/>
  </commands>
  <commands xmi:id="_rjtbI3HlEfCAZJ6UDgwwcw" elementId="org.riscvstudio.ide.commands.Nuclei_System_Technology_Homepage" commandName="Nuclei System Technology Homepage"/>
  <commands xmi:id="_rjtbJHHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.navigate.previousSubTab" commandName="Previous Sub-Tab" description="Switch to the previous sub-tab" category="_rjtdlnHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtbJXHlEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.internal.lttng2.ui.commands.control.newConnection" commandName="New Connection..." description="New Connection to Target Node" category="_rjtdgXHlEfCAZJ6UDgwwcw">
    <parameters xmi:id="_rjtbJnHlEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.lttng2.control.ui.remoteServicesIdParameter" name="Remote Services ID"/>
    <parameters xmi:id="_rjtbJ3HlEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.lttng2.control.ui.connectionNameParameter" name="Connection Name"/>
  </commands>
  <commands xmi:id="_rjtbKHHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.navigate.showIn" commandName="Show In" category="_rjtdlnHlEfCAZJ6UDgwwcw">
    <parameters xmi:id="_rjtbKXHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.navigate.showIn.targetId" name="Show In Target Id" optional="false"/>
  </commands>
  <commands xmi:id="_rjtbKnHlEfCAZJ6UDgwwcw" elementId="sed.tabletree.collapseAll" commandName="Collapse All" category="_rjtdi3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtbK3HlEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.tmf.ui.command.new_folder" commandName="New Folder..." description="Create a new trace folder" category="_rjtdh3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtbLHHlEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.RepositoriesViewRemoveRemote" commandName="Delete Remote" category="_rjtdonHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtbLXHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.dialogs.openInputDialog" commandName="Open Input Dialog" description="Open an Input Dialog" category="_rjtdhHHlEfCAZJ6UDgwwcw">
    <parameters xmi:id="_rjtbLnHlEfCAZJ6UDgwwcw" elementId="title" name="Title"/>
    <parameters xmi:id="_rjtbL3HlEfCAZJ6UDgwwcw" elementId="message" name="Message"/>
    <parameters xmi:id="_rjtbMHHlEfCAZJ6UDgwwcw" elementId="initialValue" name="Initial Value"/>
    <parameters xmi:id="_rjtbMXHlEfCAZJ6UDgwwcw" elementId="cancelReturns" name="Return Value on Cancel"/>
  </commands>
  <commands xmi:id="_rjtbMnHlEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.internal.lttng2.ui.commands.control.load" commandName="Load..." description="Load session(s)" category="_rjtdgXHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtbM3HlEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.RebaseCurrent" commandName="Rebase" category="_rjtdi3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtbNHHlEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.debug.ui.command.startTracing" commandName="Start Tracing " description="Start Tracing Experiment" category="_rjtdp3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtbNXHlEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.tasks.ui.command.markTaskComplete" commandName="Mark Task Complete" category="_rjtdeHHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtbNnHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.select.windowStart" commandName="Select Window Start" description="Select to the start of the window" category="_rjtddHHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtbN3HlEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.tmf.ui.views.uml2sd.handlers.MoveSDLeft" commandName="Scroll left" description="Scroll left the sequence diagram" category="_rjtdo3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtbOHHlEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.RepositoriesViewImportProjects" commandName="Import Projects..." description="Import or create in local Git repository" category="_rjtdonHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtbOXHlEfCAZJ6UDgwwcw" elementId="org.eclipse.compare.copyAllLeftToRight" commandName="Copy All from Left to Right" description="Copy All Changes from Left to Right" category="_rjtdmHHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtbOnHlEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.dsf.gdb.ui.command.selectPreviousTraceRecord" commandName="Previous Trace Record" description="Select Previous Trace Record" category="_rjtdi3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtbO3HlEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.make.ui.targetCreateCommand" commandName="Create Build Target" description="Create a new make build target for the selected container." category="_rjtdm3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtbPHHlEfCAZJ6UDgwwcw" elementId="org.eclipse.search.ui.openFileSearchPage" commandName="File Search" description="Open the Search dialog's file search page" category="_rjtdoHHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtbPXHlEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.RepositoriesViewAddRepository" commandName="Add a Git Repository..." description="Adds an existing Git repository to the Git Repositories view" category="_rjtdonHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtbPnHlEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.tasks.ui.command.task.clearActiveTime" commandName="Clear Active Time" category="_rjtdeHHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtbP3HlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.ide.copyBuildIdCommand" commandName="Copy Build Id Information To Clipboard" description="Copies the build identification information to the clipboard." category="_rjtdcXHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtbQHHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.select.textEnd" commandName="Select Text End" description="Select to the end of the text" category="_rjtddHHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtbQXHlEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.context.ui.commands.task.attachContext" commandName="Attach Context" category="_rjtddnHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtbQnHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.goto.wordPrevious" commandName="Previous Word" description="Go to the previous word" category="_rjtddHHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtbQ3HlEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.debug.ui.command.connect" commandName="Connect" description="Connect to a process" category="_rjtdnXHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtbRHHlEfCAZJ6UDgwwcw" elementId="org.eclipse.tm.terminal.connector.local.command.launch" commandName="Open Local Terminal on Selection" category="_rjtdl3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtbRXHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.window.preferences" commandName="Preferences" description="Open the preferences dialog" category="_rjtdfXHlEfCAZJ6UDgwwcw">
    <parameters xmi:id="_rjtbRnHlEfCAZJ6UDgwwcw" elementId="preferencePageId" name="Preference Page"/>
  </commands>
  <commands xmi:id="_rjtbR3HlEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.commit.Squash" commandName="Squash Commits" category="_rjtdi3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtbSHHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.copy" commandName="Copy" description="Copy the selection to the clipboard" category="_rjtdcXHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtbSXHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.window.nextView" commandName="Next View" description="Switch to the next view" category="_rjtdfXHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtbSnHlEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.specific_content_assist.command" commandName="C/C++ Content Assist" description="A parameterizable command that invokes content assist with a single completion proposal category" category="_rjtdcXHlEfCAZJ6UDgwwcw">
    <parameters xmi:id="_rjtbS3HlEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.specific_content_assist.category_id" name="type" optional="false"/>
  </commands>
  <commands xmi:id="_rjtbTHHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.revertToSaved" commandName="Revert to Saved" description="Revert to the last saved state" category="_rjtdcXHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtbTXHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.views.properties.NewPropertySheetCommand" commandName="Properties" category="_rjtdi3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtbTnHlEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.commands.RunToLine" commandName="Run to Line" description="Resume and break when execution reaches the current line" category="_rjtdgnHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtbT3HlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.showChangeRulerInformation" commandName="Show Quick Diff Ruler Tooltip" description="Displays quick diff or revision information for the caret line in a focused hover" category="_rjtddHHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtbUHHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.upperCase" commandName="To Upper Case" description="Changes the selection to upper case" category="_rjtddHHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtbUXHlEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.team.ConfigureFetch" commandName="Configure Upstream Fetch" category="_rjtdonHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtbUnHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.navigate.goInto" commandName="Go Into" description="Navigate into the selected item" category="_rjtdlnHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtbU3HlEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.commands.OpenRunConfigurations" commandName="Run..." description="Open run launch configuration dialog" category="_rjtdgnHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtbVHHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.select.windowEnd" commandName="Select Window End" description="Select to the end of the window" category="_rjtddHHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtbVXHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.window.minimizePart" commandName="Minimize Active View or Editor" description="Minimizes the active view or editor" category="_rjtdfXHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtbVnHlEfCAZJ6UDgwwcw" elementId="org.eclipse.remote.ui.command.openConnection" commandName="Open Connection" category="_rjtdi3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtbV3HlEfCAZJ6UDgwwcw" elementId="org.eclipse.tm.terminal.command1" commandName="Terminal view insert" category="_rjtdnnHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtbWHHlEfCAZJ6UDgwwcw" elementId="org.riscvstudio.ide.newProject" commandName="newProject" category="_rjtdn3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtbWXHlEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.edit.open.outline" commandName="Show outline" description="Shows outline" category="_rjtdkHHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtbWnHlEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.team.Untrack" commandName="Untrack" category="_rjtdonHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtbW3HlEfCAZJ6UDgwwcw" elementId="org.eclipse.wst.sse.ui.add.block.comment" commandName="Add Block Comment" description="Add Block Comment" category="_rjtdcXHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtbXHHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.window.showSystemMenu" commandName="Show System Menu" description="Show the system menu" category="_rjtdfXHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtbXXHlEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.menu.rebuildIndex" commandName="Rebuild Index" category="_rjtdm3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtbXnHlEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.history.OpenInCommitViewerCommand" commandName="Open in Commit Viewer" description="Opens selected commit(s) in Commit Viewer(s)" category="_rjtdi3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtbX3HlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.window.savePerspective" commandName="Save Perspective As" description="Save the current perspective" category="_rjtdfXHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtbYHHlEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.debug.ui.command.stopTracing" commandName="Stop Tracing " description="Stop Tracing Experiment" category="_rjtdp3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtbYXHlEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.tmf.ui.command.clear_offset" commandName="Clear Time Offset" description="Clear time offset" category="_rjtdh3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtbYnHlEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.command.configureTrace" commandName="Configure Git Debug Trace" category="_rjtdonHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtbY3HlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.navigate.linkWithEditor" commandName="Toggle Link with Editor" description="Toggles linking of a view's selection with the active editor's selection" category="_rjtdlnHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtbZHHlEfCAZJ6UDgwwcw" elementId="org.eclipse.wst.sse.ui.open.file.from.source" commandName="Open Selection" description="Open an editor on the selected link" category="_rjtdcXHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtbZXHlEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.builds.ui.command.ShowTestResults" commandName="Show Test Results" category="_rjtdc3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtbZnHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.toggleBlockSelectionMode" commandName="Toggle Block Selection" description="Toggle block / column selection in the current text editor" category="_rjtdcXHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtbZ3HlEfCAZJ6UDgwwcw" elementId="org.eclipse.wst.sse.ui.goto.matching.bracket" commandName="Matching Character" description="Go to Matching Character" category="_rjtdlnHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtbaHHlEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.FetchGiteaPullRequest" commandName="Fetch Gitea Pull Request" category="_rjtdonHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtbaXHlEfCAZJ6UDgwwcw" elementId="org.eclipse.tracecompass.tmf.ui.timegraph.bookmark" commandName="Toggle Bookmark..." category="_rjtdh3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtbanHlEfCAZJ6UDgwwcw" elementId="org.eclipse.remote.ui.command.openTerminal" commandName="Open Command Shell" category="_rjtdi3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtba3HlEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.team.CompareIndexWithHead" commandName="Compare File in Index with HEAD Revision" category="_rjtdonHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtbbHHlEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.wikitext.ui.convertToDocbookCommand" commandName="Generate Docbook" category="_rjtdi3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtbbXHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.part.nextPage" commandName="Next Page" description="Switch to the next page" category="_rjtdlnHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtbbnHlEfCAZJ6UDgwwcw" elementId="org.riscvstudio.ide.commands.Nuclei_Documentation" commandName="NMSIS Documentation"/>
  <commands xmi:id="_rjtbb3HlEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.edit.text.c.remove.block.comment" commandName="Remove Block Comment" description="Removes the block comment enclosing the selection" category="_rjtdkHHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtbcHHlEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.refactor.extract.constant" commandName="Extract Constant - Refactoring " description="Extracts a constant for the selected expression" category="_rjtdfnHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtbcXHlEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.RepositoriesViewClone" commandName="Clone a Git Repository..." description="Clones a Git repository and adds the clone to the Git Repositories view" category="_rjtdonHlEfCAZJ6UDgwwcw">
    <parameters xmi:id="_rjtbcnHlEfCAZJ6UDgwwcw" elementId="repositoryUri" name="Repository URI"/>
  </commands>
  <commands xmi:id="_rjtbc3HlEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.edit.open.include.browser" commandName="Open Include Browser" description="Open an include browser on the selected element" category="_rjtdlnHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtbdHHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.clear.mark" commandName="Clear Mark" description="Clear the mark" category="_rjtddHHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtbdXHlEfCAZJ6UDgwwcw" elementId="org.eclipse.wst.sse.ui.quick_outline" commandName="Quick Outline" description="Show the quick outline for the editor input" category="_rjtdlnHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtbdnHlEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.debug.application.command.debugCore" commandName="Debug Core File" description="Debug a corefile" category="_rjtdnXHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtbd3HlEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.RepositoriesViewRemove" commandName="Remove Repository" category="_rjtdonHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtbeHHlEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.github.ui.command.mergePullRequest" commandName="Merge pull request" description="Merge into destination branch" category="_rjtdi3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtbeXHlEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.RepositoriesViewCreateRepository" commandName="Create a Git Repository..." description="Creates a new Git repository and adds it to the Git Repositories view" category="_rjtdonHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtbenHlEfCAZJ6UDgwwcw" elementId="org.eclipse.text.quicksearch.commands.quicksearchCommand" commandName="Quick Search" category="_rjtdfHHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtbe3HlEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.debug.ui.command.editRegisterGroup" commandName="Edit Register Group" description="Edits a Register Group" category="_rjtddXHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtbfHHlEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.team.PushTags" commandName="Push Tags..." category="_rjtdonHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtbfXHlEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.changelog.core.formatChangeLog" commandName="Format ChangeLog" description="Formats ChangeLog" category="_rjtdd3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtbfnHlEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.ContinueRebase" commandName="Continue Rebase" category="_rjtdonHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtbf3HlEfCAZJ6UDgwwcw" elementId="org.eclipse.help.ui.closeTray" commandName="Close User Assistance Tray" description="Close the user assistance tray containing context help information and cheat sheets." category="_rjtdmnHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtbgHHlEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.history.CreatePatch" commandName="Create Patch..." category="_rjtdi3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtbgXHlEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.managedbuilder.ui.rebuildConfigurations" commandName="Build Selected Configurations" category="_rjtdjXHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtbgnHlEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.commands.Resume" commandName="Resume" description="Resume" category="_rjtdgnHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtbg3HlEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.context.ui.commands.open.context.dialog" commandName="Show Context Quick View" description="Show Context Quick View" category="_rjtddnHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtbhHHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.file.restartWorkbench" commandName="Restart" description="Restart the workbench" category="_rjtde3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtbhXHlEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.team.CompareWithRef" commandName="Compare with Branch, Tag or Reference..." category="_rjtdonHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtbhnHlEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.context.ui.commands.attachment.retrieveContext" commandName="Retrieve Context Attachment" category="_rjtddnHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtbh3HlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.toggleOverwrite" commandName="Toggle Overwrite" description="Toggle overwrite mode" category="_rjtddHHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtbiHHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.folding.collapse_all" commandName="Collapse All" description="Collapses all folded regions" category="_rjtddHHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtbiXHlEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.tasks.ui.command.RefreshRepositoryTasks" commandName="Synchronize Changed" category="_rjtdi3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtbinHlEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.commands.ToggleWatchpoint" commandName="Toggle Watchpoint" description="Creates or removes a watchpoint" category="_rjtdgnHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtbi3HlEfCAZJ6UDgwwcw" elementId="org.eclipse.equinox.p2.ui.discovery.commands.ShowRepositoryCatalog" commandName="Show Repository Catalog" category="_rjtdi3HlEfCAZJ6UDgwwcw">
    <parameters xmi:id="_rjtbjHHlEfCAZJ6UDgwwcw" elementId="org.eclipse.equinox.p2.ui.discovery.commands.RepositoryParameter" name="P2 Repository URI"/>
  </commands>
  <commands xmi:id="_rjtbjXHlEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.commands.closeRendering" commandName="Close Rendering" description="Close the selected rendering." category="_rjtdgnHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtbjnHlEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.RepositoriesViewOpenInEditor" commandName="Open in Editor" category="_rjtdonHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtbj3HlEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.menu.wsselection.command" commandName="Manage Working Sets" category="_rjtdm3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtbkHHlEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.tmf.ui.views.uml2sd.handlers.MoveSDRight" commandName="Scroll right" description="Scroll right the sequence diagram" category="_rjtdo3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtbkXHlEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.team.submodule.update" commandName="Update Submodule" category="_rjtdonHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtbknHlEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.commands.ProfileLast" commandName="Profile" description="Launch in profile mode" category="_rjtdgnHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtbk3HlEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.team.Pull" commandName="Pull" category="_rjtdonHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtblHHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.swap.mark" commandName="Swap Mark" description="Swap the mark with the cursor position" category="_rjtddHHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtblXHlEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.context.ui.commands.interest.increment" commandName="Make Landmark" description="Make Landmark" category="_rjtddnHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtblnHlEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.edit.opencview" commandName="Show in C/C++ Project view" description="Shows the selected resource in the C/C++ Project view" category="_rjtdkHHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtbl3HlEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.tmf.ui.command.analysis_help" commandName="Help" description="Help" category="_rjtdh3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtbmHHlEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.RepositoriesViewConfigureFetch" commandName="Configure Fetch..." category="_rjtdonHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtbmXHlEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.RepositoriesViewShowInSystemExplorer" commandName="Show In System Explorer" category="_rjtdonHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtbmnHlEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.navigate.open.type.in.hierarchy" commandName="Open Type in Hierarchy" description="Open a type in the type hierarchy view" category="_rjtdlnHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtbm3HlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.shiftLeft" commandName="Shift Left" description="Shift a block of text to the left" category="_rjtdcXHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtbnHHlEfCAZJ6UDgwwcw" elementId="org.eclipse.wst.sse.ui.structure.select.next" commandName="Select Next Element" description="Expand selection to include next sibling" category="_rjtdcXHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtbnXHlEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.team.Push" commandName="Push..." category="_rjtdonHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtbnnHlEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.debug.command.breakpointProperties" commandName="C/C++ Breakpoint Properties" description="View and edit properties for a given C/C++ breakpoint" category="_rjtdgnHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtbn3HlEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.RepositoriesCreateGroup" commandName="Create a Repository Group" description="Create a repository group for structuring repositories in the Git Repositories view" category="_rjtdonHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtboHHlEfCAZJ6UDgwwcw" elementId="org.riscvstudio.ide.commands.Nuclei_Studio_User_Guide" commandName="Nuclei Studio User Guide"/>
  <commands xmi:id="_rjtboXHlEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.debug.ui.command.uncall" commandName="Uncall" description="Perform Uncall" category="_rjtdpnHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtbonHlEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.make.ui.edit.text.makefile.opendecl" commandName="Open declaration" description="Follow to the directive definition" category="_rjtdgHHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtbo3HlEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.team.stash.drop" commandName="Delete Stashed Commit..." category="_rjtdonHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtbpHHlEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.internal.lttng2.ui.commands.control.refresh" commandName="Refresh" description="Refresh Node Configuration" category="_rjtdgXHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtbpXHlEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.DebugPerspective" commandName="Debug" description="Open the debug perspective" category="_rjtdpHHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtbpnHlEfCAZJ6UDgwwcw" elementId="AnsiConsole.command.copy_without_escapes" commandName="Copy Text Without ANSI Escapes" description="Copy the console content to clipboard, removing the escape sequences" category="_rjtdeXHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtbp3HlEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.internal.lttng2.ui.commands.control.enableChannelOnSession" commandName="Enable Channel..." description="Enable a Trace Channel" category="_rjtdgXHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtbqHHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.navigate.expandAll" commandName="Expand All" description="Expand the current tree" category="_rjtdlnHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtbqXHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.file.saveAll" commandName="Save All" description="Save all current contents" category="_rjtde3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtbqnHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.file.closeOthers" commandName="Close Others" description="Close all editors except the one that is active" category="_rjtde3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtbq3HlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.editors.quickdiff.revertLine" commandName="Revert Line" description="Revert the current line" category="_rjtddHHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtbrHHlEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.commands.OpenDebugConfigurations" commandName="Debug..." description="Open debug launch configuration dialog" category="_rjtdgnHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtbrXHlEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.team.SimpleFetch" commandName="Fetch from Upstream" category="_rjtdonHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtbrnHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.window.previousEditor" commandName="Previous Editor" description="Switch to the previous editor" category="_rjtdfXHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtbr3HlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.zoomIn" commandName="Zoom In" description="Zoom in text, increase default font size for text editors" category="_rjtddHHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtbsHHlEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.tmf.ui.import" commandName="Import..." description="Import traces into project" category="_rjtdi3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtbsXHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.ide.OpenMarkersView" commandName="Open Another" description="Open another view" category="_rjtdi3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtbsnHlEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.commands.newRendering" commandName="New Rendering" description="Add a new rendering." category="_rjtdgnHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtbs3HlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.project.closeUnrelatedProjects" commandName="Close Unrelated Projects" description="Close unrelated projects" category="_rjtdm3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtbtHHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.ide.markers.copyMarkerResourceQualifiedName" commandName="Copy Resource Qualified Name To Clipboard" description="Copies markers resource qualified name to the clipboard" category="_rjtdcXHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtbtXHlEfCAZJ6UDgwwcw" elementId="org.eclipse.search.ui.performTextSearchWorkspace" commandName="Find Text in Workspace" description="Searches the files in the workspace for specific text." category="_rjtdoHHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtbtnHlEfCAZJ6UDgwwcw" elementId="org.eclipse.wst.sse.ui.remove.block.comment" commandName="Remove Block Comment" description="Remove Block Comment" category="_rjtdcXHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtbt3HlEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.tmf.ui.command.exporttracepkg" commandName="Export Trace Package..." category="_rjtdh3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtbuHHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.addBookmark" commandName="Add Bookmark" description="Add a bookmark" category="_rjtdcXHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtbuXHlEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.debug.ui.command.reverseToggle" commandName="Reverse Toggle" description="Toggle Reverse Debugging" category="_rjtdpnHlEfCAZJ6UDgwwcw">
    <parameters xmi:id="_rjtbunHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.commands.radioStateParameter" name="TraceMethod" optional="false"/>
  </commands>
  <commands xmi:id="_rjtbu3HlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.set.mark" commandName="Set Mark" description="Set the mark" category="_rjtddHHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtbvHHlEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.tasks.ui.command.goToPreviousUnread" commandName="Go To Previous Unread Task" category="_rjtdlnHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtbvXHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.views.showView" commandName="Show View" description="Shows a particular view" category="_rjtdj3HlEfCAZJ6UDgwwcw">
    <parameters xmi:id="_rjtbvnHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.views.showView.viewId" name="View"/>
    <parameters xmi:id="_rjtbv3HlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.views.showView.secondaryId" name="Secondary Id"/>
    <parameters xmi:id="_rjtbwHHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.views.showView.makeFast" name="As FastView"/>
  </commands>
  <commands xmi:id="_rjtbwXHlEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.refactor.hide.method" commandName="Hide Member Function..." category="_rjtdfnHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtbwnHlEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.make.ui.edit.text.makefile.toggle.comment" commandName="Toggle Comment" description="Comment/uncomment selected lines with # style comments" category="_rjtdgHHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtbw3HlEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.team.Ignore" commandName="Ignore" category="_rjtdonHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtbxHHlEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.commit.Edit" commandName="Edit Commit" category="_rjtdi3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtbxXHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.navigate.showResourceByPath" commandName="Show Resource in Navigator" description="Show a resource in the Navigator given its path" category="_rjtdlnHlEfCAZJ6UDgwwcw">
    <parameters xmi:id="_rjtbxnHlEfCAZJ6UDgwwcw" elementId="resourcePath" name="Resource Path" typeId="org.eclipse.ui.ide.resourcePath" optional="false"/>
  </commands>
  <commands xmi:id="_rjtbx3HlEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.commands.SkipAllBreakpoints" commandName="Skip All Breakpoints" description="Sets whether or not any breakpoint should suspend execution" category="_rjtdgnHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtbyHHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.delimiter.windows" commandName="Convert Line Delimiters to Windows (CRLF, \r\n, 0D0A, &#xa4;&#xb6;)" description="Converts the line delimiters to Windows (CRLF, \r\n, 0D0A, &#xa4;&#xb6;)" category="_rjtde3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtbyXHlEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.internal.lttng2.ui.commands.control.createSession" commandName="Create Session..." description="Create a Trace Session" category="_rjtdgXHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtbynHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.findNext" commandName="Find Next" description="Find next item" category="_rjtdcXHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtby3HlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.window.hidetrimbars" commandName="Toggle visibility of the window toolbars" description="Toggle the visibility of the toolbars of the current window" category="_rjtdfXHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtbzHHlEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.valgrind.launch.exportCommand" commandName="Export Valgrind Log Files" description="Exports Valgrind log output to a directory" category="_rjtdi3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtbzXHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.navigate.nextSubTab" commandName="Next Sub-Tab" description="Switch to the next sub-tab" category="_rjtdlnHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtbznHlEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.tasks.ui.command.addTaskRepository" commandName="Add Task Repository..." category="_rjtdeHHlEfCAZJ6UDgwwcw">
    <parameters xmi:id="_rjtbz3HlEfCAZJ6UDgwwcw" elementId="connectorKind" name="Repository Type"/>
  </commands>
  <commands xmi:id="_rjtb0HHlEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.edit.text.c.goto.next.member" commandName="Go to Next Member" description="Move the caret to the next member of the translation unit" category="_rjtdkHHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtb0XHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.addTask" commandName="Add Task..." description="Add a task" category="_rjtdcXHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtb0nHlEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.builds.ui.commands.OpenBuildElementWithBrowser" commandName="Open Build with Browser" category="_rjtdc3HlEfCAZJ6UDgwwcw">
    <parameters xmi:id="_rjtb03HlEfCAZJ6UDgwwcw" elementId="element" name="Element"/>
  </commands>
  <commands xmi:id="_rjtb1HHlEfCAZJ6UDgwwcw" elementId="org.eclipse.wst.sse.ui.structure.select.enclosing" commandName="Select Enclosing Element" description="Expand selection to include enclosing element" category="_rjtdcXHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtb1XHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.cheatsheets.openCheatSheet" commandName="Open Cheat Sheet" description="Open a Cheat Sheet." category="_rjtdmnHlEfCAZJ6UDgwwcw">
    <parameters xmi:id="_rjtb1nHlEfCAZJ6UDgwwcw" elementId="cheatSheetId" name="Identifier"/>
  </commands>
  <commands xmi:id="_rjtb13HlEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.tasks.ui.viewSource.command" commandName="View Unformatted Text" category="_rjtdeHHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtb2HHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.moveLineDown" commandName="Move Lines Down" description="Moves the selected lines down" category="_rjtddHHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtb2XHlEfCAZJ6UDgwwcw" elementId="org.eclipse.equinox.p2.ui.sdk.update" commandName="Check for Updates" category="_rjtdi3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtb2nHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.goto.textEnd" commandName="Text End" description="Go to the end of the text" category="_rjtddHHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtb23HlEfCAZJ6UDgwwcw" elementId="org.eclipse.launchbar.ui.command.launchActive" commandName="Launch Active Launch Configuration" category="_rjtdoXHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtb3HHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.part.previousPage" commandName="Previous Page" description="Switch to the previous page" category="_rjtdlnHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtb3XHlEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.edit.open.quick.type.hierarchy" commandName="Quick Type Hierarchy" description="Shows quick type hierarchy" category="_rjtdkHHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtb3nHlEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.hover.backwardMacroExpansion" commandName="Back" description="Steps backward in macro expansions" category="_rjtdkHHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtb33HlEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.tmf.ui.command.synchronize_traces" commandName="Synchronize Traces..." description="Synchronize 2 or more traces" category="_rjtdh3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtb4HHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.project.closeProject" commandName="Close Project" description="Close the selected project" category="_rjtdm3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtb4XHlEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.team.PullWithOptions" commandName="Pull..." category="_rjtdonHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtb4nHlEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.github.ui.command.checkoutPullRequest" commandName="Checkout Pull Request" description="Checkout pull request into topic branch" category="_rjtdi3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtb43HlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.select.addAllMatchesToMultiSelection" commandName="Add all matches to multi-selection" description="Looks for all regions matching the current selection or identifier and adds them to a multi-selection " category="_rjtddHHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtb5HHlEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.debug.ui.command.debugNewExecutable" commandName="Debug New Executable" description="Debug a new executable" category="_rjtdnXHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtb5XHlEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.tasks.ui.command.markTaskUnread" commandName="Mark Task Unread" category="_rjtdeHHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtb5nHlEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.dsf.gdb.ui.command.selectNextTraceRecord" commandName="Next Trace Record" description="Select Next Trace Record" category="_rjtdi3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtb53HlEfCAZJ6UDgwwcw" elementId="org.eclipse.search.ui.performTextSearchFile" commandName="Find Text in File" description="Searches the files in the file for specific text." category="_rjtdoHHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtb6HHlEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.internal.lttng2.ui.commands.control.enableLogger" commandName="Enable Logger" description="Enable Logger" category="_rjtdgXHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtb6XHlEfCAZJ6UDgwwcw" elementId="org.eclipse.wst.sse.ui.structure.select.last" commandName="Restore Last Selection" description="Restore last selection" category="_rjtdcXHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtb6nHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.cut.line" commandName="Cut Line" description="Cut a line of text, or multiple lines when invoked again without interruption" category="_rjtddHHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtb63HlEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.builds.ui.command.NewTaskFromBuild" commandName="New Task From Build" category="_rjtdc3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtb7HHlEfCAZJ6UDgwwcw" elementId="org.eclipse.tm.terminal.view.ui.command.launch" commandName="Open Terminal on Selection" category="_rjtdl3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtb7XHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.folding.expand_all" commandName="Expand All" description="Expands all folded regions" category="_rjtddHHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtb7nHlEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.changelog.core.preparechangelog2" commandName="Prepare Changelog In Editor" description="Prepares ChangeLog in an editor" category="_rjtdd3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtb73HlEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.tmf.ui.command.select_traces" commandName="Select Traces..." description="Select Traces" category="_rjtdh3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtb8HHlEfCAZJ6UDgwwcw" elementId="org.eclipse.oomph.setup.editor.openEditorDropdown" commandName="Open Setup Editor" category="_rjtdk3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtb8XHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.help.quickStartAction" commandName="Welcome" description="Show help for beginning users" category="_rjtdmnHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtb8nHlEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.hover.forwardMacroExpansion" commandName="Forward" description="Steps forward in macro expansions" category="_rjtdkHHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtb83HlEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.team.ReplaceWithPrevious" commandName="Replace with Previous Revision" category="_rjtdonHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtb9HHlEfCAZJ6UDgwwcw" elementId="org.eclipse.oomph.setup.ui.questionnaire" commandName="Configuration Questionnaire" description="Review the IDE's most fiercely contested preferences" category="_rjtdk3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtb9XHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.window.hideShowEditors" commandName="Toggle Shared Area Visibility" description="Toggles the visibility of the shared area" category="_rjtdfXHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtb9nHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.folding.restore" commandName="Reset Structure" description="Resets the folding structure" category="_rjtddHHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtb93HlEfCAZJ6UDgwwcw" elementId="org.eclipse.help.ui.indexcommand" commandName="Index" description="Show Keyword Index" category="_rjtdmnHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtb-HHlEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.internal.lttng2.ui.commands.control.destroySession" commandName="Destroy Session..." description="Destroy a Trace Session" category="_rjtdgXHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtb-XHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.navigate.forward" commandName="Forward" description="Navigate forward" category="_rjtdlnHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtb-nHlEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.team.CherryPick" commandName="Cherry Pick" category="_rjtdonHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtb-3HlEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.FetchGitHubPR" commandName="Fetch GitHub Pull Request" category="_rjtdonHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtb_HHlEfCAZJ6UDgwwcw" elementId="org.eclipse.launchbar.ui.command.configureActiveLaunch" commandName="Edit Active Launch Configuration" category="_rjtdoXHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtb_XHlEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.history.DeleteTag" commandName="&amp;Delete Tag" category="_rjtdi3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtb_nHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.window.pinEditor" commandName="Pin Editor" description="Pin the current editor" category="_rjtdfXHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtb_3HlEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.github.ui.command.fetchPullRequest" commandName="Fetch Pull Request Commits" description="Fetch commits from pull request" category="_rjtdi3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtcAHHlEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.refactor.override.methods" commandName="Override Methods..." description="Generates override methods for a selected class" category="_rjtdkHHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtcAXHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.goto.pageUp" commandName="Page Up" description="Go up one page" category="_rjtddHHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtcAnHlEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.team.submodule.sync" commandName="Sync Submodule" category="_rjtdonHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtcA3HlEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.team.DeleteBranch" commandName="Delete Branch" category="_rjtdonHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtcBHHlEfCAZJ6UDgwwcw" elementId="org.eclipse.tm.terminal.copy" commandName="Copy" category="_rjtdnnHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtcBXHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.goto.columnPrevious" commandName="Previous Column" description="Go to the previous column" category="_rjtddHHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtcBnHlEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.refactor.getters.and.setters" commandName="Generate Getters and Setters..." description="Generates getters and setters for a selected field" category="_rjtdkHHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtcB3HlEfCAZJ6UDgwwcw" elementId="org.riscvstudio.ide.project.convert.commands" commandName="RISC-V Project Convert Tool" category="_rjtdmXHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtcCHHlEfCAZJ6UDgwwcw" elementId="org.eclipse.compare.selectNextChange" commandName="Select Next Change" description="Select Next Change" category="_rjtdmHHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtcCXHlEfCAZJ6UDgwwcw" elementId="org.eclipse.wst.xml.ui.generate.xml" commandName="XML File..." description="Generate a XML file from the selected DTD or Schema" category="_rjtdi3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtcCnHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ltk.ui.refactoring.commands.renameResource" commandName="Rename Resource" description="Rename the selected resource and notify LTK participants." category="_rjtdpXHlEfCAZJ6UDgwwcw">
    <parameters xmi:id="_rjtcC3HlEfCAZJ6UDgwwcw" elementId="org.eclipse.ltk.ui.refactoring.commands.renameResource.newName.parameter.key" name="Selected resource's new name."/>
  </commands>
  <commands xmi:id="_rjtcDHHlEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.edit.open.quick.macro.explorer" commandName="Explore Macro Expansion" description="Opens a quick view for macro expansion exploration" category="_rjtdkHHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtcDXHlEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.team.Rebase" commandName="Rebase on" category="_rjtdonHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtcDnHlEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.tasks.ui.command.previousTask" commandName="Previous Task Command" category="_rjtdi3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtcD3HlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.file.properties" commandName="Properties" description="Display the properties of the selected item" category="_rjtde3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtcEHHlEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.internal.lttng2.ui.commands.control.addContextOnDomain" commandName="Add Context..." description="Add Context to Channel(s) and/or Event(s)" category="_rjtdgXHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtcEXHlEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.team.ReplaceWithHead" commandName="Replace with HEAD revision" category="_rjtdonHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtcEnHlEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.refactor.toggle.function" commandName="Toggle Function - Refactoring " description="Toggles the implementation between header and implementation file" category="_rjtdfnHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtcE3HlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.activeContextInfo" commandName="Show activeContext Info" category="_rjtdfXHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtcFHHlEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.tasks.ui.command.markTaskReadGoToPreviousUnread" commandName="Mark Task Read and Go To Previous Unread Task" category="_rjtdeHHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtcFXHlEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.cdt.ui.editor.folding.auto" commandName="Toggle Active Folding" description="Toggle Active Folding" category="_rjtdenHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtcFnHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.select.textStart" commandName="Select Text Start" description="Select to the beginning of the text" category="_rjtddHHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtcF3HlEfCAZJ6UDgwwcw" elementId="org.eclipse.wst.sse.ui.cleanup.document" commandName="Cleanup Document..." description="Cleanup document" category="_rjtdcXHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtcGHHlEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.builds.ui.command.ShowBuildOutput.url" commandName="Show Build Output" category="_rjtdi3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtcGXHlEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.team.ConfigurePush" commandName="Configure Upstream Push" category="_rjtdonHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtcGnHlEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.debug.ui.command.reverseResume" commandName="Reverse Resume" description="Perform Reverse Resume" category="_rjtdpnHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtcG3HlEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.menu.freshenAllFiles" commandName="Freshen All Files in Index" category="_rjtdm3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtcHHHlEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.command.nextpage" commandName="Next Page of Memory" description="Load next page of memory" category="_rjtdgnHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtcHXHlEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.command.gotoaddress" commandName="Go to Address" description="Go to Address" category="_rjtdgnHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtcHnHlEfCAZJ6UDgwwcw" elementId="org.eclipse.remote.ui.command.newConnection" commandName="New Connection" category="_rjtdi3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtcH3HlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.window.maximizePart" commandName="Maximize Active View or Editor" description="Toggles maximize/restore state of active view or editor" category="_rjtdfXHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtcIHHlEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.edit.text.c.find.word" commandName="Find Word" description="Selects a word and find the next occurrence" category="_rjtdkHHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtcIXHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.window.newEditor" commandName="Clone Editor" description="Open another editor on the active editor's input" category="_rjtdfXHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtcInHlEfCAZJ6UDgwwcw" elementId="org.eclipse.wst.sse.ui.format" commandName="Format" category="_rjtdi3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtcI3HlEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.tmf.ui.views.uml2sd.handlers.GoToMessage" commandName="Go to associated message" description="Go to the associated message" category="_rjtdo3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtcJHHlEfCAZJ6UDgwwcw" elementId="org.eclipse.search.ui.openSearchDialog" commandName="Open Search Dialog" description="Open the Search dialog" category="_rjtdoHHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtcJXHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.contentAssist.proposals" commandName="Content Assist" description="Content Assist" category="_rjtdcXHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtcJnHlEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.edit.text.c.select.previous" commandName="Select Previous C/C++ Element" description="Expand the selection to enclosing C/C++ element" category="_rjtdcXHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtcJ3HlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.navigate.up" commandName="Up" description="Navigate up one level" category="_rjtdlnHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtcKHHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.window.activateEditor" commandName="Activate Editor" description="Activate the editor" category="_rjtdfXHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtcKXHlEfCAZJ6UDgwwcw" elementId="sed.tabletree.expandAll" commandName="Expand All" category="_rjtdi3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtcKnHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.delete.line.to.end" commandName="Delete to End of Line" description="Delete to the end of a line of text" category="_rjtddHHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtcK3HlEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.commands.Terminate" commandName="Terminate" description="Terminate" category="_rjtdgnHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtcLHHlEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.team.ShowRepositoriesView" commandName="Show Git Repositories View" category="_rjtdonHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtcLXHlEfCAZJ6UDgwwcw" elementId="org.eclipse.help.ui.ignoreMissingPlaceholders" commandName="Do not warn of missing documentation" description="Sets the help preferences to no longer report a warning about the current set of missing documents." category="_rjtdmnHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtcLnHlEfCAZJ6UDgwwcw" elementId="org.eclipse.compare.compareWithOther" commandName="Compare With Other Resource" description="Compare resources, clipboard contents or editors" category="_rjtdmHHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtcL3HlEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.edit.text.rename.element" commandName="Rename - Refactoring " description="Renames the selected element" category="_rjtdfnHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtcMHHlEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.managedbuilder.ui.cleanFiles" commandName="Clean Selected File(s)" description="Deletes build output files for the selected source files" category="_rjtdjXHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtcMXHlEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.team.CreatePatch" commandName="Create Patch..." category="_rjtdonHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtcMnHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.editors.revisions.author.toggle" commandName="Toggle Revision Author Display" description="Toggles the display of the revision author" category="_rjtddHHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtcM3HlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.goto.windowEnd" commandName="Window End" description="Go to the end of the window" category="_rjtddHHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtcNHHlEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.edit.text.c.goto.prev.member" commandName="Go to Previous Member" description="Move the caret to the previous member of the translation unit" category="_rjtdkHHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtcNXHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.perspectives.showPerspective" commandName="Show Perspective" description="Show a particular perspective" category="_rjtdpHHlEfCAZJ6UDgwwcw">
    <parameters xmi:id="_rjtcNnHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.perspectives.showPerspective.perspectiveId" name="Parameter"/>
    <parameters xmi:id="_rjtcN3HlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.perspectives.showPerspective.newWindow" name="In New Window"/>
  </commands>
  <commands xmi:id="_rjtcOHHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.goto.line" commandName="Go to Line" description="Go to a specified line of text" category="_rjtdlnHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtcOXHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.editors.quickdiff.revert" commandName="Revert Lines" description="Revert the current selection, block or deleted lines" category="_rjtddHHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtcOnHlEfCAZJ6UDgwwcw" elementId="org.eclipse.wst.xml.ui.gotoMatchingTag" commandName="Matching Tag" description="Go to Matching Tag" category="_rjtdcXHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtcO3HlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.select.lineUp" commandName="Select Line Up" description="Extend the selection to the previous line of text" category="_rjtddHHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtcPHHlEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.team.submodule.add" commandName="Add Submodule" category="_rjtdonHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtcPXHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.cut.line.to.end" commandName="Cut to End of Line" description="Cut to the end of a line of text" category="_rjtddHHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtcPnHlEfCAZJ6UDgwwcw" elementId="org.eclipse.compare.copyLeftToRight" commandName="Copy from Left to Right" description="Copy Current Change from Left to Right" category="_rjtdmHHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtcP3HlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.project.openProject" commandName="Open Project" description="Open a project" category="_rjtdm3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtcQHHlEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.history.ShowBlame" commandName="Show Revision Information" category="_rjtdi3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtcQXHlEfCAZJ6UDgwwcw" elementId="org.riscvstudio.ide.commands.Nuclei_Studio_FAQ" commandName="Nuclei Studio FAQ"/>
  <commands xmi:id="_rjtcQnHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.project.properties" commandName="Properties" description="Display the properties of the selected item's project " category="_rjtdm3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtcQ3HlEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.edit.text.c.surround.with.quickMenu" commandName="Surround With Quick Menu" description="Shows the Surround With quick menu" category="_rjtdkHHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtcRHHlEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.history.CompareVersionsInTree" commandName="Compare in Tree" category="_rjtdi3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtcRXHlEfCAZJ6UDgwwcw" elementId="org.eclipse.epp.package.common.contribute" commandName="Contribute" description="Contribute to the development and success of the Eclipse IDE!" category="_rjtdi3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtcRnHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.select.columnPrevious" commandName="Select Previous Column" description="Select the previous column" category="_rjtddHHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtcR3HlEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.github.ui.command.cloneGist" commandName="Clone Gist" description="Clone Gist into Git repository" category="_rjtdi3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtcSHHlEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.internal.lttng2.ui.commands.control.enableEventOnDomain" commandName="Enable Event (default channel)..." description="Enable Event on Default Channel" category="_rjtdgXHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtcSXHlEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.RepositoriesToggleBranchHierarchy" commandName="Toggle Branch Representation" category="_rjtdonHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtcSnHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ltk.ui.refactoring.commands.deleteResources" commandName="Delete Resources" description="Delete the selected resources and notify LTK participants." category="_rjtdpXHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtcS3HlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.file.print" commandName="Print" description="Print" category="_rjtde3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtcTHHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.select.selectMultiSelectionDown" commandName="Multi selection down relative to anchor selection  " description="Search next matching region and add it to the current selection, or remove first element from current multi-selection " category="_rjtddHHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtcTXHlEfCAZJ6UDgwwcw" elementId="org.eclipse.wst.sse.ui.format.active.elements" commandName="Format Active Elements" description="Format active elements" category="_rjtdcXHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtcTnHlEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.deleteConfigsCommand" commandName="Reset to Default" category="_rjtdi3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtcT3HlEfCAZJ6UDgwwcw" elementId="org.riscvstudio.ide.import.package" commandName="Export to Package Management" category="_rjtdg3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtcUHHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.showRulerContextMenu" commandName="Show Ruler Context Menu" description="Show the context menu for the ruler" category="_rjtdfXHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtcUXHlEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.tasks.ui.command.task.clearOutgoing" commandName="Clear Outgoing Changes" category="_rjtdeHHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtcUnHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.folding.collapse" commandName="Collapse" description="Collapses the folded region at the current selection" category="_rjtddHHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtcU3HlEfCAZJ6UDgwwcw" elementId="org.eclipse.epp.mpc.ui.command.showFavorites" commandName="Eclipse Marketplace Favorites" description="Open Marketplace Favorites" category="_rjtdi3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtcVHHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.smartEnterInverse" commandName="Insert Line Above Current Line" description="Adds a new line above the current line" category="_rjtddHHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtcVXHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.window.spy" commandName="Show Contributing Plug-in" description="Shows contribution information for the currently selected element" category="_rjtdfXHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtcVnHlEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.edit.text.c.align.const" commandName="Align const qualifiers" description="Moves const qualifiers to the left or right of the type depending on the workspace preferences" category="_rjtdkHHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtcV3HlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.help.helpSearch" commandName="Help Search" description="Open the help search" category="_rjtdmnHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtcWHHlEfCAZJ6UDgwwcw" elementId="org.eclipse.launchbar.ui.command.openLaunchSelector" commandName="Open Launch Bar Config Selector" category="_rjtdoXHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtcWXHlEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.builds.ui.command.ShowTestResults.url" commandName="Show Test Results" category="_rjtdi3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtcWnHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.goto.lineDown" commandName="Line Down" description="Go down one line of text" category="_rjtddHHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtcW3HlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.gotoLastEditPosition" commandName="Previous Edit Location" description="Previous edit location" category="_rjtdlnHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtcXHHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.open.hyperlink" commandName="Open Hyperlink" description="Opens the hyperlink at the caret location or opens a chooser if more than one hyperlink is available" category="_rjtddHHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtcXXHlEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.wikitext.ui.convertToEclipseHelpCommand" commandName="Generate Eclipse Help (*.html and *-toc.xml)" category="_rjtdi3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtcXnHlEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.command.prevpage" commandName="Previous Page of Memory" description="Load previous page of memory" category="_rjtdgnHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtcX3HlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.ide.configureFilters" commandName="Filters..." description="Configure the filters to apply to the markers view" category="_rjtdi3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtcYHHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.navigate.previousTab" commandName="Previous Tab" description="Switch to the previous tab" category="_rjtdlnHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtcYXHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.dialogs.openMessageDialog" commandName="Open Message Dialog" description="Open a Message Dialog" category="_rjtdhHHlEfCAZJ6UDgwwcw">
    <parameters xmi:id="_rjtcYnHlEfCAZJ6UDgwwcw" elementId="title" name="Title"/>
    <parameters xmi:id="_rjtcY3HlEfCAZJ6UDgwwcw" elementId="message" name="Message"/>
    <parameters xmi:id="_rjtcZHHlEfCAZJ6UDgwwcw" elementId="imageType" name="Image Type Constant" typeId="org.eclipse.ui.dialogs.Integer"/>
    <parameters xmi:id="_rjtcZXHlEfCAZJ6UDgwwcw" elementId="defaultIndex" name="Default Button Index" typeId="org.eclipse.ui.dialogs.Integer"/>
    <parameters xmi:id="_rjtcZnHlEfCAZJ6UDgwwcw" elementId="buttonLabel0" name="First Button Label"/>
    <parameters xmi:id="_rjtcZ3HlEfCAZJ6UDgwwcw" elementId="buttonLabel1" name="Second Button Label"/>
    <parameters xmi:id="_rjtcaHHlEfCAZJ6UDgwwcw" elementId="buttonLabel2" name="Third Button Label"/>
    <parameters xmi:id="_rjtcaXHlEfCAZJ6UDgwwcw" elementId="buttonLabel3" name="Fourth Button Label"/>
    <parameters xmi:id="_rjtcanHlEfCAZJ6UDgwwcw" elementId="cancelReturns" name="Return Value on Cancel"/>
  </commands>
  <commands xmi:id="_rjtca3HlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.genericeditor.findReferences" commandName="Find References" description="Find other code items referencing the current selected item." category="_rjtdi3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtcbHHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.goto.lineEnd" commandName="Line End" description="Go to the end of the line of text" category="_rjtddHHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtcbXHlEfCAZJ6UDgwwcw" elementId="org.eclipse.embedcdt.packs.ui.commands.showPerspectiveCommand" commandName="Switch to CMSIS Packs Perspective" category="_rjtdf3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtcbnHlEfCAZJ6UDgwwcw" elementId="org.eclipse.wst.xml.ui.referencedFileErrors" commandName="Show Details..." description="Show Details..." category="_rjtdi3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtcb3HlEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.commands.RunLast" commandName="Run" description="Launch in run mode" category="_rjtdgnHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtccHHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.externalTools.commands.OpenExternalToolsConfigurations" commandName="External Tools..." description="Open external tools launch configuration dialog" category="_rjtdgnHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtccXHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.deletePrevious" commandName="Delete Previous" description="Delete the previous character" category="_rjtddHHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtccnHlEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.managedbuilder.ui.buildFiles" commandName="Build Selected File(s)" description="Rebuilds the selected source files" category="_rjtdjXHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtcc3HlEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.internal.lttng2.ui.commands.control.connect" commandName="Connect" description="Connect to Target Node" category="_rjtdgXHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtcdHHlEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.team.CompareWithPrevious" commandName="Compare with Previous Revision" category="_rjtdonHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtcdXHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.select.columnNext" commandName="Select Next Column" description="Select the next column" category="_rjtddHHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtcdnHlEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.tmf.ui.command.new_experiment" commandName="New..." description="Create Tracing Experiment" category="_rjtdh3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtcd3HlEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.commands.TerminateAndRelaunch" commandName="Terminate and Relaunch" description="Terminate and Relaunch" category="_rjtdgnHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtceHHlEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.team.Tag" commandName="Create Tag..." category="_rjtdonHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtceXHlEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.team.NoAssumeUnchanged" commandName="No Assume Unchanged" category="_rjtdonHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtcenHlEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.debug.ui.commands.viewMemory" commandName="View Memory" description="View variable in memory view" category="_rjtdi3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtce3HlEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.edit.text.c.select.next" commandName="Select Next C/C++ Element" description="Expand the selection to next C/C++ element" category="_rjtdcXHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtcfHHlEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.commands.RemoveAllBreakpoints" commandName="Remove All Breakpoints" description="Removes all breakpoints" category="_rjtdgnHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtcfXHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.navigator.resources.nested.changeProjectPresentation" commandName="P&amp;rojects Presentation" category="_rjtdi3HlEfCAZJ6UDgwwcw">
    <parameters xmi:id="_rjtcfnHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.navigator.resources.nested.enabled" name="&amp;Hierarchical"/>
    <parameters xmi:id="_rjtcf3HlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.commands.radioStateParameter" name="Nested Project view - Radio State" optional="false"/>
  </commands>
  <commands xmi:id="_rjtcgHHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.window.showKeyAssist" commandName="Show Key Assist" description="Show the key assist dialog" category="_rjtdfXHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtcgXHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.genericeditor.togglehighlight" commandName="Toggle Highlight" category="_rjtdi3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtcgnHlEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.managedbuilder.ui.cleanAllConfigurations" commandName="Clean All Configurations" category="_rjtdjXHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtcg3HlEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.team.AddToIndex" commandName="Add to Index" category="_rjtdonHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtchHHlEfCAZJ6UDgwwcw" elementId="org.eclipse.wst.sse.ui.toggle.comment" commandName="Toggle Comment" description="Toggle Comment" category="_rjtdcXHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtchXHlEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.debug.ui.command.saveTraceData" commandName="Save Trace Data " description="Save Trace Data to File" category="_rjtdp3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtchnHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.help.tipsAndTricksAction" commandName="Tips and Tricks" description="Open the tips and tricks help page" category="_rjtdmnHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtch3HlEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.internal.lttng2.ui.commands.control.stop" commandName="Stop" description="Stop Trace Session" category="_rjtdgXHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtciHHlEfCAZJ6UDgwwcw" elementId="org.riscvstudio.ide.commands.sampleCommand" commandName="SDK Configuration Tools" category="_rjtdn3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtciXHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.smartEnter" commandName="Insert Line Below Current Line" description="Adds a new line below the current line" category="_rjtddHHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtcinHlEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.tmf.ui.command.select_trace_type" commandName="Select Trace Type..." description="Select a trace type" category="_rjtdh3HlEfCAZJ6UDgwwcw">
    <parameters xmi:id="_rjtci3HlEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.tmf.ui.commandparameter.select_trace_type.type" name="Trace Type" optional="false"/>
  </commands>
  <commands xmi:id="_rjtcjHHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.goto.lineStart" commandName="Line Start" description="Go to the start of the line of text" category="_rjtddHHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtcjXHlEfCAZJ6UDgwwcw" elementId="org.eclipse.oomph.setup.editor.synchronizePreferences" commandName="Synchronize Preferences" category="_rjtdk3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtcjnHlEfCAZJ6UDgwwcw" elementId="org.eclipse.wst.xml.ui.cmnd.contentmodel.sych" commandName="Synch" category="_rjtdi3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtcj3HlEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.RepositoriesViewConfigureBranch" commandName="Configure Branch" category="_rjtdonHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtckHHlEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.commands.Suspend" commandName="Suspend" description="Suspend" category="_rjtdgnHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtckXHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.hippieCompletion" commandName="Word Completion" description="Context insensitive completion" category="_rjtdcXHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtcknHlEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.builds.ui.command.AbortBuild" commandName="Abort Build" category="_rjtdc3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtck3HlEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.debug.ui.command.StepIntoSelection" commandName="Step Into Selection" description="Step into the current selected statement" category="_rjtdgnHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtclHHlEfCAZJ6UDgwwcw" elementId="org.eclipse.remote.ui.command.deleteConnection" commandName="Delete Connection" category="_rjtdi3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtclXHlEfCAZJ6UDgwwcw" elementId="org.eclipse.team.ui.synchronizeLast" commandName="Repeat last synchronization" description="Repeat the last synchronization" category="_rjtdjnHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtclnHlEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.RepositoriesViewConfigureGerritRemote" commandName="Gerrit Configuration..." category="_rjtdonHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtcl3HlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.ide.configureColumns" commandName="Configure Columns..." description="Configure the columns in the markers view" category="_rjtdi3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtcmHHlEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.team.ReplaceWithCommit" commandName="Replace with commit" category="_rjtdonHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtcmXHlEfCAZJ6UDgwwcw" elementId="org.eclipse.tm.terminal.quickaccess" commandName="Quick Access" category="_rjtdnnHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtcmnHlEfCAZJ6UDgwwcw" elementId="org.eclipse.tracecompass.tmf.ui.command.convert_project" commandName="Configure or convert to Tracing Project" description="Configure or convert project to tracing project" category="_rjtdh3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtcm3HlEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.commands.DebugLast" commandName="Debug" description="Launch in debug mode" category="_rjtdgnHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtcnHHlEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.wikitext.ui.convertToHtmlCommand" commandName="Generate HTML" category="_rjtdi3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtcnXHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.window.previousView" commandName="Previous View" description="Switch to the previous view" category="_rjtdfXHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtcnnHlEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.internal.lttng2.ui.commands.control.import" commandName="Import..." description="Import Traces to LTTng Project" category="_rjtdgXHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtcn3HlEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.edit.open.type.hierarchy" commandName="Open Type Hierarchy" description="Open a type hierarchy on the selected element" category="_rjtdlnHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtcoHHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.ide.markers.copyDescription" commandName="Copy Description To Clipboard" description="Copies markers description field to the clipboard" category="_rjtdcXHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtcoXHlEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.debug.ui.command.removeRegisterGroups" commandName="Remove Register Groups" description="Removes one or more Register Groups" category="_rjtddXHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtconHlEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.refactor.implement.method" commandName="Implement Method - Source Generation " description="Implements a method for a selected method declaration" category="_rjtdkHHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtco3HlEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.commands.DropToFrame" commandName="Drop to Frame" description="Drop to Frame" category="_rjtdgnHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtcpHHlEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.edit.text.c.add.block.comment" commandName="Add Block Comment" description="Encloses the selection with a block comment" category="_rjtdkHHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtcpXHlEfCAZJ6UDgwwcw" elementId="org.eclipse.tm.terminal.view.ui.command.disconnect" commandName="Disconnect Terminal" category="_rjtdl3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtcpnHlEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.team.ShowBlame" commandName="Show Revision Information" category="_rjtdonHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtcp3HlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.folding.expand" commandName="Expand" description="Expands the folded region at the current selection" category="_rjtddHHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtcqHHlEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.debug.ui.command.castToType" commandName="Cast To Type..." category="_rjtdiXHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtcqXHlEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.tasks.ui.command.openRemoteTask" commandName="Open Remote Task" category="_rjtdlnHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtcqnHlEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.commands.nextMemoryBlock" commandName="Next Memory Monitor" description="Show renderings from next memory monitor." category="_rjtdgnHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtcq3HlEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.CompareWithEachOther" commandName="Compare with Each Other" description="Compare two files selected in the Compare Editor with each other." category="_rjtdmHHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtcrHHlEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.context.ui.commands.task.retrieveContext" commandName="Retrieve Context" category="_rjtddnHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtcrXHlEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.team.Fetch" commandName="Fetch" category="_rjtdonHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtcrnHlEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.tmf.ui.command.offset_traces" commandName="Apply Time Offset..." description="Shift traces by a constant time offset" category="_rjtdh3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtcr3HlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.window.togglestatusbar" commandName="Toggle Statusbar" description="Toggle the visibility of the bottom status bar" category="_rjtdfXHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtcsHHlEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.task.ui.editor.QuickOutline" commandName="Quick Outline" description="Show the quick outline for the editor input" category="_rjtdeHHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtcsXHlEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.edit.text.c.comment" commandName="Comment" description="Turns the selected lines into // style comments" category="_rjtdkHHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtcsnHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.select.multiCaretUp" commandName="Multi caret up" description="Add a new caret/multi selection above the current line, or remove the last caret/multi selection " category="_rjtddHHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtcs3HlEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.commands.eof" commandName="EOF" description="Send end of file" category="_rjtdgnHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtctHHlEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.AbortRebase" commandName="Abort Rebase" category="_rjtdonHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtctXHlEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.managedbuilder.ui.buildAllConfigurations" commandName="Build All Configurations" category="_rjtdjXHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtctnHlEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.tmf.ui.views.uml2sd.handlers.ShowNodeStart" commandName="Show node start " description="Show the node start" category="_rjtdo3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtct3HlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.navigate.showInQuickMenu" commandName="Show In..." description="Open the Show In menu" category="_rjtdlnHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtcuHHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.copyLineUp" commandName="Duplicate Lines" description="Duplicates the selected lines and leaves the selection unchanged" category="_rjtddHHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtcuXHlEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.commands.ToggleMethodBreakpoint" commandName="Toggle Method Breakpoint" description="Creates or removes a method breakpoint" category="_rjtdgnHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtcunHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.navigate.next" commandName="Next" description="Navigate to the next item" category="_rjtdlnHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtcu3HlEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.tasks.bugs.commands.newTaskFromMarker" commandName="New Task from Marker..." description="Report as Bug from Marker" category="_rjtdi3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtcvHHlEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.context.ui.commands.focus.view" commandName="Focus View" category="_rjtdi3HlEfCAZJ6UDgwwcw">
    <parameters xmi:id="_rjtcvXHlEfCAZJ6UDgwwcw" elementId="viewId" name="View ID to Focus" optional="false"/>
  </commands>
  <commands xmi:id="_rjtcvnHlEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.internal.lttng2.ui.commands.control.snapshot" commandName="Record Snapshot" description="Record a snapshot" category="_rjtdgXHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtcv3HlEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.commands.StepReturn" commandName="Step Return" description="Step return" category="_rjtdgnHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtcwHHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.browser.openBundleResource" commandName="Open Resource in Browser" description="Opens a bundle resource in the default web browser." category="_rjtdfXHlEfCAZJ6UDgwwcw">
    <parameters xmi:id="_rjtcwXHlEfCAZJ6UDgwwcw" elementId="plugin" name="Plugin"/>
    <parameters xmi:id="_rjtcwnHlEfCAZJ6UDgwwcw" elementId="path" name="Path"/>
  </commands>
  <commands xmi:id="_rjtcw3HlEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.search.finddecl" commandName="Declaration" description="Searches for declarations of the selected element in the workspace" category="_rjtdkHHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtcxHHlEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.internal.lttng2.ui.commands.control.enableEvent" commandName="Enable Event" description="Enable Event" category="_rjtdgXHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtcxXHlEfCAZJ6UDgwwcw" elementId="org.riscvstudio.ide.commands.sdkManageCommand" commandName="NPK Package Management" category="_rjtdhnHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtcxnHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.help.aboutAction" commandName="About" description="Open the about dialog" category="_rjtdmnHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtcx3HlEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.team.stash.create" commandName="Stash Changes..." category="_rjtdonHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtcyHHlEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.debug.ui.command.restoreDefaultType" commandName="Restore Original Type" description="View and edit properties for a given C/C++ breakpoint" category="_rjtdiXHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtcyXHlEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.tasks.ui.command.activateSelectedTask" commandName="Activate Selected Task" category="_rjtdlnHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtcynHlEfCAZJ6UDgwwcw" elementId="org.eclipse.wst.sse.ui.format.document" commandName="Format" description="Format selection" category="_rjtdcXHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtcy3HlEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.dsf.debug.ui.disassembly.commands.gotoAddress" commandName="Go to Address..." description="Navigate to address" category="_rjtdgnHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtczHHlEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.edit.text.c.sort.lines" commandName="Sort Lines" description="Sort selected lines alphabetically" category="_rjtdkHHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtczXHlEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.team.RemoveFromIndex" commandName="Remove from Index" category="_rjtdonHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtcznHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.newWizard" commandName="New" description="Open the New item wizard" category="_rjtde3HlEfCAZJ6UDgwwcw">
    <parameters xmi:id="_rjtcz3HlEfCAZJ6UDgwwcw" elementId="newWizardId" name="New Wizard"/>
  </commands>
  <commands xmi:id="_rjtc0HHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.window.newWindow" commandName="New Window" description="Open another window" category="_rjtdfXHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtc0XHlEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.builds.ui.commands.CopyDetails" commandName="Copy Details" category="_rjtdc3HlEfCAZJ6UDgwwcw">
    <parameters xmi:id="_rjtc0nHlEfCAZJ6UDgwwcw" elementId="kind" name="Kind"/>
    <parameters xmi:id="_rjtc03HlEfCAZJ6UDgwwcw" elementId="element" name="Element"/>
  </commands>
  <commands xmi:id="_rjtc1HHlEfCAZJ6UDgwwcw" elementId="org.eclipse.e4.ui.importer.configureProject" commandName="Configure and Detect Nested Projects..." category="_rjtdi3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtc1XHlEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.builds.ui.command.RunBuild" commandName="Run Build" category="_rjtdc3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtc1nHlEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.search.finddecl.workingset" commandName="Declaration in Working Set" description="Searches for declarations of the selected element in a working set" category="_rjtdkHHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtc13HlEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.history.CompareVersions" commandName="Compare with Each Other" category="_rjtdi3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtc2HHlEfCAZJ6UDgwwcw" elementId="org.eclipse.equinox.p2.ui.sdk.install" commandName="Install New Software..." category="_rjtdi3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtc2XHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.window.customizePerspective" commandName="Customize Perspective" description="Customize the current perspective" category="_rjtdfXHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtc2nHlEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.context.ui.commands.interest.decrement" commandName="Make Less Interesting" description="Make Less Interesting" category="_rjtddnHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtc23HlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.externaltools.ExternalToolMenuDelegateToolbar" commandName="Run Last Launched External Tool" description="Runs the last launched external Tool" category="_rjtdgnHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtc3HHlEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.CheckoutCommand" commandName="Check Out" category="_rjtdonHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtc3XHlEfCAZJ6UDgwwcw" elementId="org.eclipse.remote.ui.command.closeConnection" commandName="Close Connection" category="_rjtdi3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtc3nHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.moveLineUp" commandName="Move Lines Up" description="Moves the selected lines up" category="_rjtddHHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtc33HlEfCAZJ6UDgwwcw" elementId="org.eclipse.equinox.p2.ui.discovery.commands.ShowBundleCatalog" commandName="Show Bundle Catalog" category="_rjtdi3HlEfCAZJ6UDgwwcw">
    <parameters xmi:id="_rjtc4HHlEfCAZJ6UDgwwcw" elementId="org.eclipse.equinox.p2.ui.discovery.commands.DirectoryParameter" name="Directory URL"/>
    <parameters xmi:id="_rjtc4XHlEfCAZJ6UDgwwcw" elementId="org.eclipse.equinox.p2.ui.discovery.commands.TagsParameter" name="Tags"/>
  </commands>
  <commands xmi:id="_rjtc4nHlEfCAZJ6UDgwwcw" elementId="org.eclipse.userstorage.ui.showPullDown" commandName="Show Pull Down Menu" category="_rjtdmnHlEfCAZJ6UDgwwcw">
    <parameters xmi:id="_rjtc43HlEfCAZJ6UDgwwcw" elementId="intoolbar" name="In Tool Bar" optional="false"/>
  </commands>
  <commands xmi:id="_rjtc5HHlEfCAZJ6UDgwwcw" elementId="org.eclipse.tracecompass.tmf.ui.command.analysis_remove" commandName="Remove External Analysis" description="Remove External Analysis" category="_rjtdh3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtc5XHlEfCAZJ6UDgwwcw" elementId="org.eclipse.compare.copyRightToLeft" commandName="Copy from Right to Left" description="Copy Current Change from Right to Left" category="_rjtdmHHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtc5nHlEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.commands.OpenProfileConfigurations" commandName="Profile..." description="Open profile launch configuration dialog" category="_rjtdgnHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtc53HlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.ide.markCompleted" commandName="Mark Completed" description="Mark the selected tasks as completed" category="_rjtdi3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtc6HHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.recenter" commandName="Recenter" description="Scroll cursor line to center, top and bottom" category="_rjtddHHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtc6XHlEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.builds.ui.commands.OpenBuildElementWithBrowser.url" commandName="Open Build with Browser" category="_rjtdi3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtc6nHlEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.edit.text.c.toggle.source.header" commandName="Toggle Source/Header" description="Toggles between corresponding source and header files" category="_rjtdkHHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtc63HlEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.edit.text.c.format" commandName="Format" description="Formats Source Code" category="_rjtdkHHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtc7HHlEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.edit.text.c.select.last" commandName="Restore Last C/C++ Selection" description="Restore last selection in C/C++ editor" category="_rjtdcXHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtc7XHlEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.dsf.debug.ui.disassembly.commands.rulerToggleBreakpoint" commandName="Toggle Breakpoint" description="Toggle breakpoint in disassembly ruler" category="_rjtdgnHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtc7nHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.scroll.lineDown" commandName="Scroll Line Down" description="Scroll down one line of text" category="_rjtddHHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtc73HlEfCAZJ6UDgwwcw" elementId="org.eclipse.wst.sse.ui.search.find.occurrences" commandName="Occurrences in File" description="Find occurrences of the selection in the file" category="_rjtdcXHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtc8HHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.ToggleCoolbarAction" commandName="Toggle Main Toolbar Visibility" description="Toggles the visibility of the window toolbar" category="_rjtdfXHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtc8XHlEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.edit.text.c.copy.qualified.name" commandName="Copy Qualified Name" description="Copy a fully qualified name to the system clipboard" category="_rjtdkHHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtc8nHlEfCAZJ6UDgwwcw" elementId="org.eclipse.wst.sse.ui.outline.customFilter" commandName="&amp;Filters" category="_rjtdi3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtc83HlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.navigate.openResource" commandName="Open Resource" description="Open an editor on a particular resource" category="_rjtdlnHlEfCAZJ6UDgwwcw">
    <parameters xmi:id="_rjtc9HHlEfCAZJ6UDgwwcw" elementId="filePath" name="File Path" typeId="org.eclipse.ui.ide.resourcePath"/>
  </commands>
  <commands xmi:id="_rjtc9XHlEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.internal.lttng2.ui.commands.control.enableChannel" commandName="Enable Channel" description="Enable a Trace Channel" category="_rjtdgXHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtc9nHlEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.edit.opendecl" commandName="Open Declaration" description="Opens an editor on the selected element's declaration(s)" category="_rjtdkHHlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtc93HlEfCAZJ6UDgwwcw" elementId="org.riscvstudio.ide.project.convertall.commands" commandName="org.riscvstudio.ide.project.convertall.commands"/>
  <commands xmi:id="_rjtc-HHlEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.debugActionSet/org.eclipse.cdt.debug.ui.actions.ResumeAtLine" commandName="Resume at Line (C/C++)" category="_rjtdi3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtc-XHlEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.debugActionSet/org.eclipse.cdt.debug.ui.actions.MoveToLine" commandName="Move to Line (C/C++)" category="_rjtdi3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtc-nHlEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.debugActionSet/org.eclipse.cdt.debug.ui.actions.ToggleInstructionStepMode" commandName="Instruction Stepping Mode" category="_rjtdi3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtc-3HlEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.cdt.make.ui.updateActionSet/org.eclipse.cdt.make.ui.UpdateMakeAction" commandName="Update Old Make Project..." description="Update Old Make Project" category="_rjtdi3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtc_HHlEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.cdt.make.ui.makeTargetActionSet/org.eclipse.cdt.make.ui.actions.buildLastTargetAction" commandName="Rebuild Last Target" category="_rjtdi3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtc_XHlEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.cdt.make.ui.makeTargetActionSet/org.eclipse.cdt.make.ui.makeTargetAction" commandName="Build..." category="_rjtdi3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtc_nHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ltk.ui.refactor.show.refactoring.history" commandName="History..." category="_rjtdi3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtc_3HlEfCAZJ6UDgwwcw" elementId="org.eclipse.ltk.ui.refactor.create.refactoring.script" commandName="Create Script..." category="_rjtdi3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtdAHHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ltk.ui.refactor.apply.refactoring.script" commandName="Apply Script..." category="_rjtdi3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtdAXHlEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.cdt.ui.SearchActionSet/org.eclipse.cdt.ui.actions.OpenCSearchPage" commandName="C/C++..." category="_rjtdi3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtdAnHlEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.cdt.ui.buildConfigActionSet/org.eclipse.cdt.ui.buildActiveConfigToolbarAction" commandName="Build Active Configuration" description="Build the active configurations of selected projects" category="_rjtdi3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtdA3HlEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.cdt.ui.buildConfigActionSet/org.eclipse.cdt.ui.buildConfigToolbarAction" commandName="Active Build Configuration" description="Manage configurations for the current project" category="_rjtdi3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtdBHHlEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.cdt.ui.CElementCreationActionSet/org.eclipse.cdt.ui.actions.NewTypeDropDown" commandName="Class..." description="New C++ Class" category="_rjtdi3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtdBXHlEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.cdt.ui.CElementCreationActionSet/org.eclipse.cdt.ui.actions.NewFileDropDown" commandName="Source File..." description="New C/C++ Source File" category="_rjtdi3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtdBnHlEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.cdt.ui.CElementCreationActionSet/org.eclipse.cdt.ui.actions.NewFolderDropDown" commandName="Source Folder..." description="New C/C++ Source Folder" category="_rjtdi3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtdB3HlEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.cdt.ui.CElementCreationActionSet/org.eclipse.cdt.ui.actions.NewProjectDropDown" commandName="Project..." description="New C/C++ Project" category="_rjtdi3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtdCHHlEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.debug.ui.launchActionSet/org.eclipse.debug.internal.ui.actions.RunWithConfigurationAction" commandName="Run As" category="_rjtdi3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtdCXHlEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.debug.ui.launchActionSet/org.eclipse.debug.internal.ui.actions.RunHistoryMenuAction" commandName="Run History" category="_rjtdi3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtdCnHlEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.debug.ui.launchActionSet/org.eclipse.debug.internal.ui.actions.RunDropDownAction" commandName="Run" category="_rjtdi3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtdC3HlEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.debug.ui.launchActionSet/org.eclipse.debug.internal.ui.actions.DebugWithConfigurationAction" commandName="Debug As" category="_rjtdi3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtdDHHlEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.debug.ui.launchActionSet/org.eclipse.debug.internal.ui.actions.DebugHistoryMenuAction" commandName="Debug History" category="_rjtdi3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtdDXHlEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.debug.ui.launchActionSet/org.eclipse.debug.internal.ui.actions.DebugDropDownAction" commandName="Debug" category="_rjtdi3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtdDnHlEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.debug.ui.profileActionSet/org.eclipse.debug.internal.ui.actions.ProfileDropDownAction" commandName="Profile" category="_rjtdi3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtdD3HlEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.debug.ui.profileActionSet/org.eclipse.debug.internal.ui.actions.ProfileWithConfigurationAction" commandName="Profile As" category="_rjtdi3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtdEHHlEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.debug.ui.profileActionSet/org.eclipse.debug.internal.ui.actions.ProfileHistoryMenuAction" commandName="Profile History" category="_rjtdi3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtdEXHlEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.egit.ui.SearchActionSet/org.eclipse.egit.ui.actions.OpenCommitSearchPage" commandName="Git..." category="_rjtdi3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtdEnHlEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.mylyn.doc.actionSet/org.eclipse.mylyn.tasks.ui.bug.report" commandName="Report Bug or Enhancement..." description="Report Bug or Enhancement" category="_rjtdi3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtdE3HlEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.mylyn.tasks.ui.navigation.additions/org.eclipse.mylyn.tasks.ui.navigate.task.history" commandName="Activate Previous Task" description="Activate Previous Task" category="_rjtdi3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtdFHHlEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.ui.cheatsheets.actionSet/org.eclipse.ui.cheatsheets.actions.CheatSheetHelpMenuAction" commandName="Cheat Sheets..." category="_rjtdi3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtdFXHlEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.search.searchActionSet/org.eclipse.search.OpenSearchDialogPage" commandName="Search..." description="Search" category="_rjtdi3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtdFnHlEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.team.ui.actionSet/org.eclipse.team.ui.synchronizeAll" commandName="Synchronize..." description="Synchronize..." category="_rjtdi3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtdF3HlEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.team.ui.actionSet/org.eclipse.team.ui.ConfigureProject" commandName="Share Project..." description="Share the project with others using a version and configuration management system." category="_rjtdi3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtdGHHlEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.ui.externaltools.ExternalToolsSet/org.eclipse.ui.externaltools.ExternalToolMenuDelegateMenu" commandName="External Tools" category="_rjtdi3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtdGXHlEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.CEditor.BreakpointRulerActions/org.eclipse.cdt.debug.ui.CEditor.RulerTobbleBreakpointAction" commandName="%Dummy.label" category="_rjtdi3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtdGnHlEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.cdt.ui.editor.asm.AsmEditor.BreakpointRulerActions/org.eclipse.cdt.debug.ui.CEditor.RulerTobbleBreakpointAction" commandName="%Dummy.label" category="_rjtdi3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtdG3HlEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.ui.texteditor.ruler.actions/org.eclipse.ui.texteditor.BookmarkRulerAction" commandName="dummy" category="_rjtdi3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtdHHHlEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.ui.texteditor.ruler.actions/org.eclipse.cdt.internal.ui.text.correction.CSelectRulerAction" commandName="dummy" category="_rjtdi3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtdHXHlEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.ui.texteditor.ruler.actions/org.eclipse.ui.texteditor.SelectRulerAction" commandName="Text Editor Ruler Single-Click" category="_rjtdi3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtdHnHlEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.core.runtime.xml.source.ruler.actions/org.eclipse.ui.texteditor.BookmarkRulerAction" commandName="Add Bookmark..." category="_rjtdi3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtdH3HlEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.core.runtime.xml.source.ruler.actions/org.eclipse.ui.texteditor.SelectRulerAction" commandName="Select Ruler" category="_rjtdi3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtdIHHlEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.debugview.toolbar/org.eclipse.cdt.debug.internal.ui.actions.ToggleInstructionStepModeActionDelegate" commandName="Instruction Stepping Mode" description="Instruction Stepping Mode" category="_rjtdi3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtdIXHlEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.debugView.menu/org.eclipse.cdt.debug.internal.ui.actions.ShowFullPathsAction" commandName="Show Full Paths" description="Show Full Paths" category="_rjtdi3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtdInHlEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.breakpointView.menu/org.eclipse.cdt.debug.internal.ui.actions.ShowFullPathsAction" commandName="Show Full Paths" description="Show Full Paths" category="_rjtdi3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtdI3HlEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.breakpointView.menu/org.eclipse.cdt.debug.ui.addWatchpoint" commandName="Add Watchpoint (C/C++)..." description="Add Watchpoint (C/C++)" category="_rjtdi3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtdJHHlEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.breakpointView.menu/org.eclipse.cdt.debug.internal.ui.actions.AddEventBreakpointActionDelegate" commandName="Add Event Breakpoint (C/C++)..." description="Add Event Breakpoint (C/C++)" category="_rjtdi3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtdJXHlEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.breakpointView.menu/org.eclipse.cdt.debug.ui.addFunctionBreakpoint" commandName="Add Function Breakpoint (C/C++)..." description="Add Function Breakpoint (C/C++)" category="_rjtdi3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtdJnHlEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.breakpointView.menu/org.eclipse.cdt.debug.ui.addLineBreakpoint" commandName="Add Line Breakpoint (C/C++)..." description="Add Line Breakpoint (C/C++)" category="_rjtdi3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtdJ3HlEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.expression.toolbar/org.eclipse.pinclone.expression.pinDebugContext" commandName="Pin to Debug Context" category="_rjtdi3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtdKHHlEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.expression.toolbar/org.eclipse.pinclone.expression.clone" commandName="Open New View" category="_rjtdi3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtdKXHlEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.variable.toolbar/org.eclipse.pinclone.variable.pinDebugContext" commandName="Pin to Debug Context" category="_rjtdi3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtdKnHlEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.variable.toolbar/org.eclipse.pinclone.variable.clone" commandName="Open New View" category="_rjtdi3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtdK3HlEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.register.toolbar/org.eclipse.pinclone.register.pinDebugContext" commandName="Pin to Debug Context" category="_rjtdi3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtdLHHlEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.register.toolbar/org.eclipse.pinclone.register.clone" commandName="Open New View" category="_rjtdi3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtdLXHlEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.cdt.debug.ui.memory.floatingpoint.preferenceaction" commandName="Floating Point Rendering Preferences ..." description="Floating Point Rendering Preferences ..." category="_rjtdi3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtdLnHlEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.memoryBrowser.toolbar/org.eclipse.pinclone.memoryBrowser.pinDebugContext" commandName="Pin to Debug Context" category="_rjtdi3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtdL3HlEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.memoryBrowser.toolbar/org.eclipse.pinclone.memoryBrowser.clone" commandName="Open New View" category="_rjtdi3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtdMHHlEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.memory.memorybrowser.MemoryBrowser.clearExpressionList/org.eclipse.cdt.debug.ui.memory.memorybrowser.ClearExpressionListActionID" commandName="Clear Expressions" category="_rjtdi3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtdMXHlEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.debug.ui.MemoryView.findNext/org.eclipse.cdt.debug.ui.memory.search.FindNextAction" commandName="Find Next" category="_rjtdi3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtdMnHlEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.debug.ui.MemoryView.findReplace/org.eclipse.cdt.debug.ui.memory.search.FindAction" commandName="Find/Replace..." category="_rjtdi3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtdM3HlEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.memory.memorybrowser.MemoryBrowser.findNext/org.eclipse.cdt.debug.ui.memory.search.FindNextAction" commandName="Find Next" category="_rjtdi3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtdNHHlEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.cdt.debug.ui.memory.traditional.preferenceaction" commandName="Traditional Rendering Preferences..." description="Traditional Rendering Preferences..." category="_rjtdi3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtdNXHlEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.cdt.debug.ui.memory.transport.actions.ExportMemoryAction" commandName="Export" description="Export" category="_rjtdi3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtdNnHlEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.cdt.debug.ui.memory.transport.actions.ImportMemoryAction" commandName="Import" description="Import" category="_rjtdi3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtdN3HlEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.memory.memorybrowser.MemoryBrowser.toolbar/org.eclipse.cdt.debug.ui.memory.transport.actions.ExportMemoryAction" commandName="Export" description="Export" category="_rjtdi3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtdOHHlEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.memory.memorybrowser.MemoryBrowser.toolbar/org.eclipse.cdt.debug.ui.memory.transport.actions.ImportMemoryAction2" commandName="Import" description="Import" category="_rjtdi3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtdOXHlEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.cdt.dsf.gdb.ui.debugsources.view.refresh/org.eclipse.cdt.dsf.gdb.ui.debugsources.view.refresh" commandName="Refresh" category="_rjtdi3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtdOnHlEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.cdt.dsf.debug.ui.viewmodel.breakpoints.update.Refresh/org.eclipse.cdt.dsf.debug.ui.breakpoints.viewmodel.update.actions.refresh" commandName="Refresh" category="_rjtdi3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtdO3HlEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.cdt.dsf.debug.ui.viewmodel.variables.update.Refresh/org.eclipse.cdt.dsf.debug.ui.variables.viewmodel.update.actions.refresh" commandName="Refresh" category="_rjtdi3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtdPHHlEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.cdt.dsf.debug.ui.viewmodel.registers.update.Refresh/org.eclipse.cdt.dsf.debug.ui.registers.viewmodel.update.actions.refresh" commandName="Refresh" category="_rjtdi3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtdPXHlEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.cdt.dsf.debug.ui.viewmodel.expressions.update.Refresh/org.eclipse.cdt.dsf.debug.ui.expressions.viewmodel.update.actions.refresh" commandName="Refresh" category="_rjtdi3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtdPnHlEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.cdt.dsf.debug.ui.viewmodel.debugview.update.Refresh/org.eclipse.cdt.dsf.debug.ui.debugview.viewmodel.update.actions.refresh" commandName="Refresh" category="_rjtdi3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtdP3HlEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.disassembly.toolbar/org.eclipse.pinclone.disassembly.pinDebugContext" commandName="Pin to Debug Context" category="_rjtdi3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtdQHHlEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.disassembly.toolbar/org.eclipse.pinclone.disassembly.clone" commandName="Open New View" category="_rjtdi3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtdQXHlEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.mylyn.cdt.ui.cview.contribution/org.eclipse.mylyn.cdt.ui.cview.focusActiveTask.action" commandName="Focus on Active Task" description="Focus only on elements in active Mylyn task" category="_rjtdi3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtdQnHlEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.debug.ui.PulldownActions/org.eclipse.debug.ui.debugview.pulldown.ViewManagementAction" commandName="View Management..." category="_rjtdi3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtdQ3HlEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.debug.ui.debugview.toolbar/org.eclipse.debug.ui.debugview.toolbar.removeAllTerminated" commandName="Remove All Terminated" description="Remove All Terminated Launches" category="_rjtdi3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtdRHHlEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.debug.ui.debugview.toolbar/org.eclipse.debug.ui.debugview.toolbar.collapseAll" commandName="Collapse All" description="Collapse All" category="_rjtdi3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtdRXHlEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.debug.ui.breakpointsview.toolbar/org.eclipse.debug.ui.breakpointsView.toolbar.removeAll" commandName="Remove All" description="Remove All Breakpoints" category="_rjtdi3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtdRnHlEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.debug.ui.breakpointsview.toolbar/org.eclipse.debug.ui.breakpointsView.toolbar.linkWithDebugView" commandName="Link with Debug View" description="Link with Debug View" category="_rjtdi3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtdR3HlEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.debug.ui.breakpointsview.toolbar/org.eclipse.debug.ui.breakpointsView.toolbar.workingSets" commandName="Working Sets..." description="Manage Working Sets" category="_rjtdi3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtdSHHlEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.debug.ui.breakpointsview.toolbar/org.eclipse.debug.ui.breakpointsView.toolbar.clearDefaultBreakpointGroup" commandName="Deselect Default Working Set" description="Deselect Default Working Set" category="_rjtdi3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtdSXHlEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.debug.ui.breakpointsview.toolbar/org.eclipse.debug.ui.breakpointsView.toolbar.setDefaultBreakpointGroup" commandName="Select Default Working Set..." description="Select Default Working Set" category="_rjtdi3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtdSnHlEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.debug.ui.breakpointsview.toolbar/org.eclipse.debug.ui.breakpointsView.toolbar.sortByAction" commandName="Sort By" description="Sort By" category="_rjtdi3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtdS3HlEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.debug.ui.breakpointsview.toolbar/org.eclipse.debug.ui.breakpointsView.toolbar.groupByAction" commandName="Group By" description="Show" category="_rjtdi3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtdTHHlEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.debug.ui.expressionsView.toolbar/org.eclipse.debug.ui.expresssionsView.toolbar.removeAll" commandName="Remove All" description="Remove All Expressions" category="_rjtdi3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtdTXHlEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.debug.ui.expressionsView.toolbar/org.eclipse.debug.ui.expresssionsView.toolbar.AddWatchExpression" commandName="Add Watch Expression..." description="Create a new watch expression" category="_rjtdi3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtdTnHlEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.PinMemoryBlockAction" commandName="Pin Memory Monitor" description="Pin Memory Monitor" category="_rjtdi3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtdT3HlEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.NewMemoryViewAction" commandName="New Memory View" description="New Memory View" category="_rjtdi3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtdUHHlEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.togglemonitors" commandName="Toggle Memory Monitors Pane" description="Toggle Memory Monitors Pane" category="_rjtdi3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtdUXHlEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.linkrenderingpanes" commandName="Link Memory Rendering Panes" description="Link Memory Rendering Panes" category="_rjtdi3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtdUnHlEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.tablerendering.preferencesaction" commandName="Table Renderings Preferences..." description="&amp;Table Renderings Preferences..." category="_rjtdi3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtdU3HlEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.togglesplitpane" commandName="Toggle Split Pane" description="Toggle Split Pane" category="_rjtdi3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtdVHHlEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.switchMemoryBlock" commandName="Switch Memory Monitor" description="Switch Memory Monitor" category="_rjtdi3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtdVXHlEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.memoryViewPreferencesAction" commandName="Preferences..." description="&amp;Preferences..." category="_rjtdi3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtdVnHlEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.mylyn.context.ui.outline.contribution/org.eclipse.mylyn.context.ui.contentOutline.focus" commandName="Focus on Active Task" description="Focus on Active Task (Alt+click to reveal filtered elements)" category="_rjtdi3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtdV3HlEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.mylyn.ui.projectexplorer.filter/org.eclipse.mylyn.ide.ui.actions.focus.projectExplorer" commandName="Focus on Active Task" description="Focus on Active Task (Alt+click to reveal filtered elements)" category="_rjtdi3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtdWHHlEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.mylyn.ui.search.contribution/org.eclipse.mylyn.ide.ui.actions.focus.search.results" commandName="Focus on Active Task" description="Focus on Active Task (Alt+click to reveal filtered elements)" category="_rjtdi3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtdWXHlEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.mylyn.ui.resource.navigator.filter/org.eclipse.mylyn.ide.ui.actions.focus.resourceNavigator" commandName="Focus on Active Task" description="Focus on Active Task (Alt+click to reveal filtered elements)" category="_rjtdi3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtdWnHlEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.mylyn.problems.contribution/org.eclipse.mylyn.ide.ui.actions.focus.markers.problems" commandName="Focus on Active Task" description="Focus on Active Task" category="_rjtdi3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtdW3HlEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.mylyn.markers.all.contribution/org.eclipse.mylyn.ide.ui.actions.focus.markers.all" commandName="Focus on Active Task" description="Focus on Active Task" category="_rjtdi3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtdXHHlEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.mylyn.markers.tasks.contribution/org.eclipse.mylyn.ide.ui.actions.focus.markers.tasks" commandName="Focus on Active Task" description="Focus on Active Task" category="_rjtdi3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtdXXHlEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.mylyn.markers.bookmarks.contribution/org.eclipse.mylyn.ide.ui.actions.focus.markers.bookmarks" commandName="Focus on Active Task" description="Focus on Active Task" category="_rjtdi3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtdXnHlEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.mylyn.tasks.ui.actions.view/org.eclipse.mylyn.tasks.ui.search.open" commandName="Search Repository..." category="_rjtdi3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtdX3HlEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.mylyn.tasks.ui.actions.view/org.eclipse.mylyn.tasks.ui.synchronize.changed" commandName="Synchronize Changed" description="Synchronize Changed" category="_rjtdi3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtdYHHlEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.mylyn.tasks.ui.actions.view/org.eclipse.mylyn.tasks.ui.tasks.restore" commandName="Restore Tasks from History..." category="_rjtdi3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtdYXHlEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.mylyn.tasks.ui.actions.view/org.eclipse.mylyn.tasks.ui.open.repositories.view" commandName="Show Task Repositories View" description="Show Task Repositories View" category="_rjtdi3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtdYnHlEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.mylyn.tasks.ui.actions.view/org.eclipse.mylyn.doc.legend.show.action" commandName="Show UI Legend" description="Show Tasks UI Legend" category="_rjtdi3HlEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_rjtdY3HlEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.mylyn.tasks.ui.actions.view/org.eclipse.mylyn.context.ui.actions.tasklist.focus" commandName="Focus on Workweek" description="Focus on Workweek" category="_rjtdi3HlEfCAZJ6UDgwwcw"/>
  <addons xmi:id="_rjtdZHHlEfCAZJ6UDgwwcw" elementId="org.eclipse.e4.core.commands.service" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.e4.core.commands/org.eclipse.e4.core.commands.CommandServiceAddon"/>
  <addons xmi:id="_rjtdZXHlEfCAZJ6UDgwwcw" elementId="org.eclipse.e4.ui.contexts.service" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.e4.ui.services/org.eclipse.e4.ui.services.ContextServiceAddon"/>
  <addons xmi:id="_rjtdZnHlEfCAZJ6UDgwwcw" elementId="org.eclipse.e4.ui.bindings.service" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.e4.ui.bindings/org.eclipse.e4.ui.bindings.BindingServiceAddon"/>
  <addons xmi:id="_rjtdZ3HlEfCAZJ6UDgwwcw" elementId="org.eclipse.e4.ui.workbench.commands.model" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.e4.ui.workbench/org.eclipse.e4.ui.internal.workbench.addons.CommandProcessingAddon"/>
  <addons xmi:id="_rjtdaHHlEfCAZJ6UDgwwcw" elementId="org.eclipse.e4.ui.workbench.contexts.model" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.e4.ui.workbench/org.eclipse.e4.ui.internal.workbench.addons.ContextProcessingAddon"/>
  <addons xmi:id="_rjtdaXHlEfCAZJ6UDgwwcw" elementId="org.eclipse.e4.ui.workbench.bindings.model" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.swt/org.eclipse.e4.ui.workbench.swt.util.BindingProcessingAddon"/>
  <addons xmi:id="_rjtdanHlEfCAZJ6UDgwwcw" elementId="Cleanup Addon" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.addons.swt/org.eclipse.e4.ui.workbench.addons.cleanupaddon.CleanupAddon"/>
  <addons xmi:id="_rjtda3HlEfCAZJ6UDgwwcw" elementId="DnD Addon" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.addons.swt/org.eclipse.e4.ui.workbench.addons.dndaddon.DnDAddon"/>
  <addons xmi:id="_rjtdbHHlEfCAZJ6UDgwwcw" elementId="MinMax Addon" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.addons.swt/org.eclipse.e4.ui.workbench.addons.minmax.MinMaxAddon"/>
  <addons xmi:id="_rjtdbXHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.workbench.addon.0" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.e4.ui.workbench/org.eclipse.e4.ui.internal.workbench.addons.HandlerProcessingAddon"/>
  <addons xmi:id="_rjtdbnHlEfCAZJ6UDgwwcw" elementId="SplitterAddon" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.addons.swt/org.eclipse.e4.ui.workbench.addons.splitteraddon.SplitterAddon"/>
  <addons xmi:id="_rjtdb3HlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.ide.addon.0" contributionURI="bundleclass://org.eclipse.ui.ide/org.eclipse.ui.internal.ide.addons.SaveAllDirtyPartsAddon"/>
  <addons xmi:id="_rjtdcHHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.ide.application.addon.0" contributorURI="platform:/plugin/org.eclipse.ui.ide.application" contributionURI="bundleclass://org.eclipse.ui.ide.application/org.eclipse.ui.internal.ide.application.addons.ModelCleanupAddon"/>
  <categories xmi:id="_rjtdcXHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.category.edit" name="Edit"/>
  <categories xmi:id="_rjtdcnHlEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.wikitext.ui.editor.category" name="WikiText Markup Editing Commands" description="commands for editing lightweight markup"/>
  <categories xmi:id="_rjtdc3HlEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.builds.ui.category.Commands" name="Builds"/>
  <categories xmi:id="_rjtddHHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.category.textEditor" name="Text Editing" description="Text Editing Commands"/>
  <categories xmi:id="_rjtddXHlEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.debug.ui.category.registerGrouping" name="Register Grouping commands" description="Set of commands for Register Grouping"/>
  <categories xmi:id="_rjtddnHlEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.context.ui.commands" name="Focused UI" description="Task-Focused Interface"/>
  <categories xmi:id="_rjtdd3HlEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.changelog" name="Changelog" description="Changelog key bindings"/>
  <categories xmi:id="_rjtdeHHlEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.tasks.ui.commands" name="Task Repositories"/>
  <categories xmi:id="_rjtdeXHlEfCAZJ6UDgwwcw" elementId="AnsiConsole.command.categoryid" name="ANSI Support Commands"/>
  <categories xmi:id="_rjtdenHlEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.cdt.ui.commands" name="CDT Context" description="CDT Task-Focused Interface Commands"/>
  <categories xmi:id="_rjtde3HlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.category.file" name="File"/>
  <categories xmi:id="_rjtdfHHlEfCAZJ6UDgwwcw" elementId="org.eclipse.text.quicksearch.commands.category" name="Quick Search"/>
  <categories xmi:id="_rjtdfXHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.category.window" name="Window"/>
  <categories xmi:id="_rjtdfnHlEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.category.refactoring" name="Refactor - C++" description="C/C++ Refactorings"/>
  <categories xmi:id="_rjtdf3HlEfCAZJ6UDgwwcw" elementId="org.eclipse.embedcdt.packs.ui.commands.category" name="C/C++ Packages Category"/>
  <categories xmi:id="_rjtdgHHlEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.make.ui.category.source" name="Makefile Source" description="Makefile Source Actions"/>
  <categories xmi:id="_rjtdgXHlEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.internal.lttng2.ui.commands.control.category" name="LTTng Trace Control Commands" description="LTTng Trace Control Commands"/>
  <categories xmi:id="_rjtdgnHlEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.category.run" name="Run/Debug" description="Run/Debug command category"/>
  <categories xmi:id="_rjtdg3HlEfCAZJ6UDgwwcw" elementId="org.riscvstudio.ide.import.package" name="Export to Package Management"/>
  <categories xmi:id="_rjtdhHHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.category.dialogs" name="Dialogs" description="Commands for opening dialogs"/>
  <categories xmi:id="_rjtdhXHlEfCAZJ6UDgwwcw" elementId="org.eclipse.oomph" name="Oomph"/>
  <categories xmi:id="_rjtdhnHlEfCAZJ6UDgwwcw" elementId="org.riscvstudio.ide.commands.sdkManage" name="NPK Package Management"/>
  <categories xmi:id="_rjtdh3HlEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.tmf.ui.commands.category" name="Tracing" description="Tracing Commands"/>
  <categories xmi:id="_rjtdiHHlEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.commons.repositories.ui.category.Team" name="Team"/>
  <categories xmi:id="_rjtdiXHlEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.debug.ui.category.casting" name="Cast to Type or Array" description="Set of commands for displaying variables and expressions as other types or arrays."/>
  <categories xmi:id="_rjtdinHlEfCAZJ6UDgwwcw" elementId="org.eclipse.oomph.commands" name="Oomph"/>
  <categories xmi:id="_rjtdi3HlEfCAZJ6UDgwwcw" elementId="org.eclipse.core.commands.categories.autogenerated" name="Uncategorized" description="Commands that were either auto-generated or have no category"/>
  <categories xmi:id="_rjtdjHHlEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.tmf.ui.commands.parser.category" name="Parser Commands" description="Parser Commands"/>
  <categories xmi:id="_rjtdjXHlEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.managedbuilder.ui.category.build" name="C/C++ Build" description="C/C++ Build Actions"/>
  <categories xmi:id="_rjtdjnHlEfCAZJ6UDgwwcw" elementId="org.eclipse.team.ui.category.team" name="Version control (Team)" description="Actions that apply when working with a version control system"/>
  <categories xmi:id="_rjtdj3HlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.category.views" name="Views" description="Commands for opening views"/>
  <categories xmi:id="_rjtdkHHlEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.category.source" name="C/C++ Source" description="C/C++ Source Actions"/>
  <categories xmi:id="_rjtdkXHlEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.tasks.ui.category.editor" name="Task Editor"/>
  <categories xmi:id="_rjtdknHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.ide.markerContents" name="Contents" description="The category for menu contents"/>
  <categories xmi:id="_rjtdk3HlEfCAZJ6UDgwwcw" elementId="org.eclipse.oomph.setup.category" name="Oomph Setup"/>
  <categories xmi:id="_rjtdlHHlEfCAZJ6UDgwwcw" elementId="org.eclipse.embedcdt.debug.gdbjtag.restart.ui.category" name="Run/Debug" description="Run/Debug command category"/>
  <categories xmi:id="_rjtdlXHlEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.debug.ui.category.runControl" name="Run Control Commands" description="Set of commands for Run Control"/>
  <categories xmi:id="_rjtdlnHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.category.navigate" name="Navigate"/>
  <categories xmi:id="_rjtdl3HlEfCAZJ6UDgwwcw" elementId="org.eclipse.tm.terminal.view.ui.commands.category" name="Terminal Commands"/>
  <categories xmi:id="_rjtdmHHlEfCAZJ6UDgwwcw" elementId="org.eclipse.compare.ui.category.compare" name="Compare" description="Compare command category"/>
  <categories xmi:id="_rjtdmXHlEfCAZJ6UDgwwcw" elementId="org.riscvstudio.ide.project.convert.commands.category" name="RISC-V Project Convert Tool"/>
  <categories xmi:id="_rjtdmnHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.category.help" name="Help"/>
  <categories xmi:id="_rjtdm3HlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.category.project" name="Project"/>
  <categories xmi:id="_rjtdnHHlEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.codan.ui.commands.category" name="Code Analysis"/>
  <categories xmi:id="_rjtdnXHlEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.debug.ui.category.debugViewLayout" name="Debug View Layout Commands" description="Set of commands for controlling the Debug View Layout"/>
  <categories xmi:id="_rjtdnnHlEfCAZJ6UDgwwcw" elementId="org.eclipse.tm.terminal.category1" name="Terminal view commands" description="Terminal view commands"/>
  <categories xmi:id="_rjtdn3HlEfCAZJ6UDgwwcw" elementId="org.riscvstudio.ide.commands.category" name="SDK Configuration Tools"/>
  <categories xmi:id="_rjtdoHHlEfCAZJ6UDgwwcw" elementId="org.eclipse.search.ui.category.search" name="Search" description="Search command category"/>
  <categories xmi:id="_rjtdoXHlEfCAZJ6UDgwwcw" elementId="org.eclipse.launchbar.ui.category.launchBar" name="Launch Bar"/>
  <categories xmi:id="_rjtdonHlEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.commandCategory" name="Git"/>
  <categories xmi:id="_rjtdo3HlEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.tmf.ui.views.uml2sd.category" name="UML2 Sequence Diagram Viewer Commands" description="UML2 Sequence Diagram Viewer Commands"/>
  <categories xmi:id="_rjtdpHHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.category.perspectives" name="Perspectives" description="Commands for opening perspectives"/>
  <categories xmi:id="_rjtdpXHlEfCAZJ6UDgwwcw" elementId="org.eclipse.ltk.ui.category.refactoring" name="Refactoring"/>
  <categories xmi:id="_rjtdpnHlEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.debug.ui.category.reverseDebugging" name="Reverse Debugging Commands" description="Set of commands for Reverse Debugging"/>
  <categories xmi:id="_rjtdp3HlEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.debug.ui.category.tracing" name="Tracing Commands" description="Category for Tracing Commands"/>
</application:Application>
