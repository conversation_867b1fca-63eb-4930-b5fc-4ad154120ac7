<?xml version="1.0" encoding="ASCII"?>
<application:Application xmi:version="2.0" xmlns:xmi="http://www.omg.org/XMI" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:advanced="http://www.eclipse.org/ui/2010/UIModel/application/ui/advanced" xmlns:application="http://www.eclipse.org/ui/2010/UIModel/application" xmlns:basic="http://www.eclipse.org/ui/2010/UIModel/application/ui/basic" xmlns:menu="http://www.eclipse.org/ui/2010/UIModel/application/ui/menu" xmi:id="_YRYX4HHmEfCAZJ6UDgwwcw" elementId="org.eclipse.e4.legacy.ide.application" contributorURI="platform:/plugin/org.eclipse.platform" selectedElement="_YRYX4XHmEfCAZJ6UDgwwcw" bindingContexts="_YRZnPnHmEfCAZJ6UDgwwcw">
  <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;workbench>&#xD;&#xA;&lt;mruList>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;speaker_inference.c&quot; tooltip=&quot;qemu/galaxy_sdk/speaker_inference.c&quot;>&#xD;&#xA;&lt;persistable path=&quot;/qemu/galaxy_sdk/speaker_inference.c&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;nnom.c&quot; tooltip=&quot;qemu/galaxy_sdk/modules/external/nnom/src/core/nnom.c&quot;>&#xD;&#xA;&lt;persistable path=&quot;/qemu/galaxy_sdk/modules/external/nnom/src/core/nnom.c&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;nnom_output.c&quot; tooltip=&quot;qemu/galaxy_sdk/modules/external/nnom/src/layers/nnom_output.c&quot;>&#xD;&#xA;&lt;persistable path=&quot;/qemu/galaxy_sdk/modules/external/nnom/src/layers/nnom_output.c&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;nnom_tensor.c&quot; tooltip=&quot;qemu/galaxy_sdk/modules/external/nnom/src/core/nnom_tensor.c&quot;>&#xD;&#xA;&lt;persistable path=&quot;/qemu/galaxy_sdk/modules/external/nnom/src/core/nnom_tensor.c&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;nnom_local.c&quot; tooltip=&quot;qemu/galaxy_sdk/modules/external/nnom/src/backends/nnom_local.c&quot;>&#xD;&#xA;&lt;persistable path=&quot;/qemu/galaxy_sdk/modules/external/nnom/src/backends/nnom_local.c&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;nnom_softmax.c&quot; tooltip=&quot;qemu/galaxy_sdk/modules/external/nnom/src/layers/nnom_softmax.c&quot;>&#xD;&#xA;&lt;persistable path=&quot;/qemu/galaxy_sdk/modules/external/nnom/src/layers/nnom_softmax.c&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;nnom_dense.c&quot; tooltip=&quot;qemu/galaxy_sdk/modules/external/nnom/src/layers/nnom_dense.c&quot;>&#xD;&#xA;&lt;persistable path=&quot;/qemu/galaxy_sdk/modules/external/nnom/src/layers/nnom_dense.c&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;nnom_flatten.c&quot; tooltip=&quot;qemu/galaxy_sdk/modules/external/nnom/src/layers/nnom_flatten.c&quot;>&#xD;&#xA;&lt;persistable path=&quot;/qemu/galaxy_sdk/modules/external/nnom/src/layers/nnom_flatten.c&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;nnom_avgpool.c&quot; tooltip=&quot;qemu/galaxy_sdk/modules/external/nnom/src/layers/nnom_avgpool.c&quot;>&#xD;&#xA;&lt;persistable path=&quot;/qemu/galaxy_sdk/modules/external/nnom/src/layers/nnom_avgpool.c&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;nnom_activation.c&quot; tooltip=&quot;qemu/galaxy_sdk/modules/external/nnom/src/layers/nnom_activation.c&quot;>&#xD;&#xA;&lt;persistable path=&quot;/qemu/galaxy_sdk/modules/external/nnom/src/layers/nnom_activation.c&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;nnom_conv2d.c&quot; tooltip=&quot;qemu/galaxy_sdk/modules/external/nnom/src/layers/nnom_conv2d.c&quot;>&#xD;&#xA;&lt;persistable path=&quot;/qemu/galaxy_sdk/modules/external/nnom/src/layers/nnom_conv2d.c&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;nnom_maxpool.c&quot; tooltip=&quot;qemu/galaxy_sdk/modules/external/nnom/src/layers/nnom_maxpool.c&quot;>&#xD;&#xA;&lt;persistable path=&quot;/qemu/galaxy_sdk/modules/external/nnom/src/layers/nnom_maxpool.c&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;nnom_input.c&quot; tooltip=&quot;qemu/galaxy_sdk/modules/external/nnom/src/layers/nnom_input.c&quot;>&#xD;&#xA;&lt;persistable path=&quot;/qemu/galaxy_sdk/modules/external/nnom/src/layers/nnom_input.c&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;main.c&quot; tooltip=&quot;qemu/galaxy_sdk/main.c&quot;>&#xD;&#xA;&lt;persistable path=&quot;/qemu/galaxy_sdk/main.c&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;nnom.h&quot; tooltip=&quot;qemu/galaxy_sdk/modules/external/nnom/inc/nnom.h&quot;>&#xD;&#xA;&lt;persistable path=&quot;/qemu/galaxy_sdk/modules/external/nnom/inc/nnom.h&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;pdm_audio_02_data.h&quot; tooltip=&quot;qemu/galaxy_sdk/pdm_audio_02_data.h&quot;>&#xD;&#xA;&lt;persistable path=&quot;/qemu/galaxy_sdk/pdm_audio_02_data.h&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;pdm_audio_02_data.c&quot; tooltip=&quot;qemu/galaxy_sdk/pdm_audio_02_data.c&quot;>&#xD;&#xA;&lt;persistable path=&quot;/qemu/galaxy_sdk/pdm_audio_02_data.c&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;speaker_weights.h&quot; tooltip=&quot;qemu/galaxy_sdk/speaker_weights.h&quot;>&#xD;&#xA;&lt;persistable path=&quot;/qemu/galaxy_sdk/speaker_weights.h&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;real_mfcc_data.h&quot; tooltip=&quot;qemu/galaxy_sdk/real_mfcc_data.h&quot;>&#xD;&#xA;&lt;persistable path=&quot;/qemu/galaxy_sdk/real_mfcc_data.h&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;speaker_inference.h&quot; tooltip=&quot;qemu/galaxy_sdk/speaker_inference.h&quot;>&#xD;&#xA;&lt;persistable path=&quot;/qemu/galaxy_sdk/speaker_inference.h&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;speaker_weights1.h&quot; tooltip=&quot;qemu/galaxy_sdk/speaker_weights1.h&quot;>&#xD;&#xA;&lt;persistable path=&quot;/qemu/galaxy_sdk/speaker_weights1.h&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;nnom_port.h&quot; tooltip=&quot;qemu/galaxy_sdk/modules/external/nnom/port/nnom_port.h&quot;>&#xD;&#xA;&lt;persistable path=&quot;/qemu/galaxy_sdk/modules/external/nnom/port/nnom_port.h&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;nnom_conv2d.h&quot; tooltip=&quot;qemu/galaxy_sdk/modules/external/nnom/inc/layers/nnom_conv2d.h&quot;>&#xD;&#xA;&lt;persistable path=&quot;/qemu/galaxy_sdk/modules/external/nnom/inc/layers/nnom_conv2d.h&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.cdt.ui.ExternalEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;_stdint.h&quot; tooltip=&quot;D:\NucleiStudio\NucleiStudio\toolchain\gcc\riscv64-unknown-elf\include\sys\_stdint.h&quot;>&#xD;&#xA;&lt;persistable path=&quot;D:\NucleiStudio\NucleiStudio\toolchain\gcc\riscv64-unknown-elf\include\sys\_stdint.h&quot; project=&quot;qemu&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.cdt.ui.ExternalEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;_default_types.h&quot; tooltip=&quot;D:\NucleiStudio\NucleiStudio\toolchain\gcc\riscv64-unknown-elf\include\machine\_default_types.h&quot;>&#xD;&#xA;&lt;persistable path=&quot;D:\NucleiStudio\NucleiStudio\toolchain\gcc\riscv64-unknown-elf\include\machine\_default_types.h&quot; project=&quot;qemu&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.cdt.ui.ExternalEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;stddef.h&quot; tooltip=&quot;D:\NucleiStudio\NucleiStudio\toolchain\gcc\lib\gcc\riscv64-unknown-elf\13.1.1\include\stddef.h&quot;>&#xD;&#xA;&lt;persistable path=&quot;D:\NucleiStudio\NucleiStudio\toolchain\gcc\lib\gcc\riscv64-unknown-elf\13.1.1\include\stddef.h&quot; project=&quot;qemu&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;nnom_local_q15.c&quot; tooltip=&quot;qemu/galaxy_sdk/modules/external/nnom/src/backends/nnom_local_q15.c&quot;>&#xD;&#xA;&lt;persistable path=&quot;/qemu/galaxy_sdk/modules/external/nnom/src/backends/nnom_local_q15.c&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;nnom_utils.c&quot; tooltip=&quot;qemu/galaxy_sdk/modules/external/nnom/src/core/nnom_utils.c&quot;>&#xD;&#xA;&lt;persistable path=&quot;/qemu/galaxy_sdk/modules/external/nnom/src/core/nnom_utils.c&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;/mruList>&#xD;&#xA;&lt;/workbench>"/>
  <tags>activeSchemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
  <children xsi:type="basic:TrimmedWindow" xmi:id="_YRYX4XHmEfCAZJ6UDgwwcw" elementId="IDEWindow" contributorURI="platform:/plugin/org.eclipse.platform" selectedElement="_YRYX4nHmEfCAZJ6UDgwwcw" label="%trimmedwindow.label.eclipseSDK" x="2246" y="94" width="1024" height="768">
    <persistedState key="coolBarVisible" value="true"/>
    <persistedState key="perspectiveBarVisible" value="true"/>
    <persistedState key="isRestored" value="true"/>
    <persistedState key="show_in_time" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;show_in_time/>"/>
    <persistedState key="workingSets" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;workingSets/>"/>
    <persistedState key="aggregateWorkingSetId" value="Aggregate for window 1754150091289"/>
    <tags>topLevel</tags>
    <tags>shellMaximized</tags>
    <children xsi:type="basic:PartSashContainer" xmi:id="_YRYX4nHmEfCAZJ6UDgwwcw" selectedElement="_YRYX43HmEfCAZJ6UDgwwcw" horizontal="true">
      <children xsi:type="advanced:PerspectiveStack" xmi:id="_YRYX43HmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.ide.perspectivestack" containerData="7500" selectedElement="_YRYX-XHmEfCAZJ6UDgwwcw">
        <children xsi:type="advanced:Perspective" xmi:id="_YRYX5HHmEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.CPerspective" selectedElement="_YRYX5XHmEfCAZJ6UDgwwcw" label="C/C++" iconURI="platform:/plugin/org.eclipse.cdt.ui/icons/view16/c_pers.gif">
          <persistedState key="persp.hiddenItems" value="persp.hideToolbarSC:print,persp.hideToolbarSC:org.eclipse.ui.edit.undo,persp.hideToolbarSC:org.eclipse.ui.edit.redo,persp.hideToolbarSC:org.eclipse.ui.edit.text.toggleShowSelectedElementOnly,persp.hideToolbarSC:org.eclipse.debug.ui.commands.RunToLine,"/>
          <tags>persp.actionSet:org.eclipse.embedcdt.debug.gdbjtag.restart.ui.actionset</tags>
          <tags>persp.actionSet:org.eclipse.mylyn.doc.actionSet</tags>
          <tags>persp.actionSet:org.eclipse.mylyn.tasks.ui.navigation</tags>
          <tags>persp.actionSet:org.eclipse.ui.cheatsheets.actionSet</tags>
          <tags>persp.actionSet:org.eclipse.search.searchActionSet</tags>
          <tags>persp.actionSet:org.eclipse.text.quicksearch.actionSet</tags>
          <tags>persp.actionSet:org.eclipse.ui.edit.text.actionSet.annotationNavigation</tags>
          <tags>persp.actionSet:org.eclipse.ui.edit.text.actionSet.navigation</tags>
          <tags>persp.actionSet:org.eclipse.ui.edit.text.actionSet.convertLineDelimitersTo</tags>
          <tags>persp.actionSet:org.eclipse.ui.externaltools.ExternalToolsSet</tags>
          <tags>persp.actionSet:org.eclipse.ui.actionSet.keyBindings</tags>
          <tags>persp.actionSet:org.eclipse.ui.actionSet.openFiles</tags>
          <tags>persp.actionSet:org.eclipse.cdt.ui.SearchActionSet</tags>
          <tags>persp.actionSet:org.eclipse.cdt.ui.CElementCreationActionSet</tags>
          <tags>persp.actionSet:org.eclipse.ui.NavigateActionSet</tags>
          <tags>persp.viewSC:org.eclipse.ui.console.ConsoleView</tags>
          <tags>persp.viewSC:org.eclipse.search.ui.views.SearchView</tags>
          <tags>persp.viewSC:org.eclipse.ui.views.ContentOutline</tags>
          <tags>persp.viewSC:org.eclipse.ui.views.ProblemView</tags>
          <tags>persp.viewSC:org.eclipse.cdt.ui.CView</tags>
          <tags>persp.viewSC:org.eclipse.ui.views.PropertySheet</tags>
          <tags>persp.viewSC:org.eclipse.ui.views.TaskList</tags>
          <tags>persp.newWizSC:org.eclipse.cdt.ui.wizards.ConvertToMakeWizard</tags>
          <tags>persp.newWizSC:org.eclipse.cdt.ui.wizards.NewMakeFromExisting</tags>
          <tags>persp.newWizSC:org.eclipse.cdt.ui.wizard.project</tags>
          <tags>persp.newWizSC:myplugintest.wizards.SampleNewWizard</tags>
          <tags>persp.newWizSC:myplugintest.wizards.PackageWizard</tags>
          <tags>persp.newWizSC:org.eclipse.cdt.ui.wizards.NewSourceFolderCreationWizard</tags>
          <tags>persp.newWizSC:org.eclipse.cdt.ui.wizards.NewFolderCreationWizard</tags>
          <tags>persp.newWizSC:org.eclipse.cdt.ui.wizards.NewSourceFileCreationWizard</tags>
          <tags>persp.newWizSC:org.eclipse.cdt.ui.wizards.NewHeaderFileCreationWizard</tags>
          <tags>persp.newWizSC:org.eclipse.cdt.ui.wizards.NewFileCreationWizard</tags>
          <tags>persp.newWizSC:org.eclipse.cdt.ui.wizards.NewClassCreationWizard</tags>
          <tags>persp.actionSet:org.eclipse.debug.ui.profileActionSet</tags>
          <tags>persp.perspSC:org.eclipse.debug.ui.DebugPerspective</tags>
          <tags>persp.perspSC:org.eclipse.team.ui.TeamSynchronizingPerspective</tags>
          <tags>persp.actionSet:org.eclipse.debug.ui.launchActionSet</tags>
          <tags>persp.actionSet:org.eclipse.cdt.ui.buildConfigActionSet</tags>
          <tags>persp.actionSet:org.eclipse.cdt.ui.NavigationActionSet</tags>
          <tags>persp.actionSet:org.eclipse.cdt.ui.OpenActionSet</tags>
          <tags>persp.actionSet:org.eclipse.cdt.ui.CodingActionSet</tags>
          <tags>persp.actionSet:org.eclipse.ui.edit.text.actionSet.presentation</tags>
          <tags>persp.showIn:org.eclipse.cdt.ui.includeBrowser</tags>
          <tags>persp.showIn:org.eclipse.cdt.ui.CView</tags>
          <tags>persp.showIn:org.eclipse.ui.navigator.ProjectExplorer</tags>
          <tags>persp.viewSC:org.eclipse.ui.navigator.ProjectExplorer</tags>
          <tags>persp.viewSC:org.eclipse.cdt.ui.includeBrowser</tags>
          <tags>persp.viewSC:org.eclipse.mylyn.tasks.ui.views.tasks</tags>
          <tags>persp.newWizSC:org.eclipse.mylyn.tasks.ui.wizards.new.repository.task</tags>
          <tags>persp.showIn:org.eclipse.cdt.codan.internal.ui.views.ProblemDetails</tags>
          <tags>persp.viewSC:org.eclipse.cdt.codan.internal.ui.views.ProblemDetails</tags>
          <tags>persp.actionSet:org.eclipse.debug.ui.breakpointActionSet</tags>
          <tags>persp.viewSC:org.eclipse.cdt.make.ui.views.MakeView</tags>
          <tags>persp.actionSet:org.eclipse.cdt.make.ui.makeTargetActionSet</tags>
          <tags>persp.viewSC:org.eclipse.tm.terminal.view.ui.TerminalsView</tags>
          <tags>persp.showIn:org.eclipse.tm.terminal.view.ui.TerminalsView</tags>
          <tags>persp.viewSC:org.eclipse.embedcdt.internal.managedbuild.packs.ui.views.DocsView</tags>
          <children xsi:type="basic:PartSashContainer" xmi:id="_YRYX5XHmEfCAZJ6UDgwwcw" selectedElement="_YRYX6nHmEfCAZJ6UDgwwcw" horizontal="true">
            <children xsi:type="basic:PartStack" xmi:id="_YRYX5nHmEfCAZJ6UDgwwcw" elementId="topLeft" containerData="1417" selectedElement="_YRYX53HmEfCAZJ6UDgwwcw">
              <children xsi:type="advanced:Placeholder" xmi:id="_YRYX53HmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.navigator.ProjectExplorer" ref="_YRZAHnHmEfCAZJ6UDgwwcw" closeable="true">
                <tags>View</tags>
                <tags>categoryTag:General</tags>
              </children>
              <children xsi:type="advanced:Placeholder" xmi:id="_YRYX6HHmEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.CView" toBeRendered="false" ref="_YRZAOnHmEfCAZJ6UDgwwcw" closeable="true">
                <tags>View</tags>
                <tags>categoryTag:C/C++</tags>
              </children>
              <children xsi:type="advanced:Placeholder" xmi:id="_YRYX6XHmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.views.BookmarkView" toBeRendered="false" ref="_YRZAO3HmEfCAZJ6UDgwwcw" closeable="true">
                <tags>View</tags>
                <tags>categoryTag:General</tags>
              </children>
            </children>
            <children xsi:type="basic:PartSashContainer" xmi:id="_YRYX6nHmEfCAZJ6UDgwwcw" containerData="8583" selectedElement="_YRYX83HmEfCAZJ6UDgwwcw">
              <children xsi:type="basic:PartSashContainer" xmi:id="_YRYX63HmEfCAZJ6UDgwwcw" containerData="6809" selectedElement="_YRYX7HHmEfCAZJ6UDgwwcw" horizontal="true">
                <children xsi:type="advanced:Placeholder" xmi:id="_YRYX7HHmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.editorss" containerData="7500" ref="_YRY_5HHmEfCAZJ6UDgwwcw"/>
                <children xsi:type="basic:PartStack" xmi:id="_YRYX7XHmEfCAZJ6UDgwwcw" elementId="topRight" containerData="2500" selectedElement="_YRYX7nHmEfCAZJ6UDgwwcw">
                  <children xsi:type="advanced:Placeholder" xmi:id="_YRYX7nHmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.views.ContentOutline" ref="_YRZAcHHmEfCAZJ6UDgwwcw" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:General</tags>
                  </children>
                  <children xsi:type="advanced:Placeholder" xmi:id="_YRYX73HmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.views.minimap.MinimapView" toBeRendered="false" ref="_YRZAc3HmEfCAZJ6UDgwwcw" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:General</tags>
                  </children>
                  <children xsi:type="advanced:Placeholder" xmi:id="_YRYX8HHmEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.tasks.ui.views.tasks" ref="_YRZAdHHmEfCAZJ6UDgwwcw" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:Mylyn</tags>
                  </children>
                  <children xsi:type="advanced:Placeholder" xmi:id="_YRYX8XHmEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.make.ui.views.MakeView" ref="_YRZAdXHmEfCAZJ6UDgwwcw" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:Make</tags>
                  </children>
                  <children xsi:type="advanced:Placeholder" xmi:id="_YRYX8nHmEfCAZJ6UDgwwcw" elementId="org.eclipse.embedcdt.internal.managedbuild.packs.ui.views.DocsView" ref="_YRZAd3HmEfCAZJ6UDgwwcw" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:CMSIS Packs</tags>
                  </children>
                </children>
              </children>
              <children xsi:type="basic:PartStack" xmi:id="_YRYX83HmEfCAZJ6UDgwwcw" elementId="bottom" containerData="3191" selectedElement="_YRYX9nHmEfCAZJ6UDgwwcw">
                <children xsi:type="advanced:Placeholder" xmi:id="_YRYX9HHmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.views.ProblemView" ref="_YRZAPHHmEfCAZJ6UDgwwcw" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:General</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_YRYX9XHmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.views.TaskList" ref="_YRZARHHmEfCAZJ6UDgwwcw" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:General</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_YRYX9nHmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.console.ConsoleView" ref="_YRZARXHmEfCAZJ6UDgwwcw" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:General</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_YRYX93HmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.views.PropertySheet" ref="_YRZAb3HmEfCAZJ6UDgwwcw" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:General</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_YRYX-HHmEfCAZJ6UDgwwcw" elementId="org.eclipse.tm.terminal.view.ui.TerminalsView" toBeRendered="false" ref="_YRZAdnHmEfCAZJ6UDgwwcw" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:Terminal</tags>
                </children>
              </children>
            </children>
          </children>
        </children>
        <children xsi:type="advanced:Perspective" xmi:id="_YRYX-XHmEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.DebugPerspective" selectedElement="_YRYX-nHmEfCAZJ6UDgwwcw" label="Debug" iconURI="platform:/plugin/org.eclipse.debug.ui/$nl$/icons/full/eview16/debug_persp.png">
          <persistedState key="persp.hiddenItems" value="persp.hideToolbarSC:print,persp.hideToolbarSC:org.eclipse.ui.edit.undo,persp.hideToolbarSC:org.eclipse.ui.edit.redo,persp.hideToolbarSC:org.eclipse.ui.edit.text.toggleShowSelectedElementOnly,persp.hideToolbarSC:org.eclipse.debug.ui.commands.RunToLine,"/>
          <tags>persp.actionSet:org.eclipse.embedcdt.debug.gdbjtag.restart.ui.actionset</tags>
          <tags>persp.actionSet:org.eclipse.mylyn.doc.actionSet</tags>
          <tags>persp.actionSet:org.eclipse.mylyn.tasks.ui.navigation</tags>
          <tags>persp.actionSet:org.eclipse.ui.cheatsheets.actionSet</tags>
          <tags>persp.actionSet:org.eclipse.search.searchActionSet</tags>
          <tags>persp.actionSet:org.eclipse.text.quicksearch.actionSet</tags>
          <tags>persp.actionSet:org.eclipse.ui.edit.text.actionSet.annotationNavigation</tags>
          <tags>persp.actionSet:org.eclipse.ui.edit.text.actionSet.navigation</tags>
          <tags>persp.actionSet:org.eclipse.ui.edit.text.actionSet.convertLineDelimitersTo</tags>
          <tags>persp.actionSet:org.eclipse.ui.externaltools.ExternalToolsSet</tags>
          <tags>persp.actionSet:org.eclipse.ui.actionSet.keyBindings</tags>
          <tags>persp.actionSet:org.eclipse.ui.actionSet.openFiles</tags>
          <tags>persp.viewSC:org.eclipse.ui.views.ProgressView</tags>
          <tags>persp.viewSC:org.eclipse.ui.texteditor.TemplatesView</tags>
          <tags>persp.actionSet:org.eclipse.debug.ui.launchActionSet</tags>
          <tags>persp.actionSet:org.eclipse.debug.ui.debugActionSet</tags>
          <tags>persp.actionSet:org.eclipse.ui.NavigateActionSet</tags>
          <tags>persp.viewSC:org.eclipse.debug.ui.DebugView</tags>
          <tags>persp.viewSC:org.eclipse.debug.ui.VariableView</tags>
          <tags>persp.viewSC:org.eclipse.debug.ui.BreakpointView</tags>
          <tags>persp.viewSC:org.eclipse.debug.ui.ExpressionView</tags>
          <tags>persp.viewSC:org.eclipse.ui.views.ContentOutline</tags>
          <tags>persp.viewSC:org.eclipse.ui.console.ConsoleView</tags>
          <tags>persp.viewSC:org.eclipse.ui.views.ProblemView</tags>
          <tags>persp.viewSC:org.eclipse.ui.navigator.ProjectExplorer</tags>
          <tags>persp.viewSC:org.eclipse.cdt.visualizer.view</tags>
          <tags>persp.viewSC:org.eclipse.pde.runtime.LogView</tags>
          <tags>persp.actionSet:org.eclipse.debug.ui.breakpointActionSet</tags>
          <tags>persp.showIn:org.eclipse.ui.navigator.ProjectExplorer</tags>
          <tags>persp.perspSC:org.eclipse.cdt.ui.CPerspective</tags>
          <tags>persp.perspSC:org.eclipse.wst.xml.ui.perspective</tags>
          <tags>persp.showIn:org.eclipse.egit.ui.RepositoriesView</tags>
          <tags>persp.viewSC:org.eclipse.cdt.debug.ui.SignalsView</tags>
          <tags>persp.viewSC:org.eclipse.debug.ui.RegisterView</tags>
          <tags>persp.viewSC:org.eclipse.debug.ui.ModuleView</tags>
          <tags>persp.viewSC:org.eclipse.debug.ui.MemoryView</tags>
          <tags>persp.viewSC:org.eclipse.cdt.debug.ui.executablesView</tags>
          <tags>persp.actionSet:org.eclipse.cdt.debug.ui.debugActionSet</tags>
          <tags>persp.viewSC:org.eclipse.cdt.dsf.debug.ui.disassembly.view</tags>
          <tags>persp.viewSC:org.eclipse.cdt.debug.ui.memory.memorybrowser.MemoryBrowser</tags>
          <tags>persp.viewSC:org.eclipse.tm.terminal.view.ui.TerminalsView</tags>
          <tags>persp.showIn:org.eclipse.tm.terminal.view.ui.TerminalsView</tags>
          <tags>persp.viewSC:org.eclipse.cdt.dsf.gdb.ui.tracecontrol.view</tags>
          <tags>persp.viewSC:org.eclipse.cdt.debug.ui.debuggerConsoleView</tags>
          <tags>persp.viewSC:org.eclipse.cdt.dsf.gdb.ui.debugsources.view</tags>
          <tags>persp.viewSC:org.eclipse.embedcdt.internal.debug.gdbjtag.ui.views.PeripheralsView</tags>
          <tags>persp.editorOnboardingImageUri:platform:/plugin/org.eclipse.debug.ui/icons/full/onboarding_debug_persp.png</tags>
          <tags>persp.editorOnboardingText:Go hunt your bugs here.</tags>
          <tags>persp.editorOnboardingCommand:Find Actions$$$Ctrl+3</tags>
          <tags>persp.editorOnboardingCommand:Step Into$$$F5</tags>
          <tags>persp.editorOnboardingCommand:Step Over$$$F6</tags>
          <tags>persp.editorOnboardingCommand:Step Return$$$F7</tags>
          <tags>persp.editorOnboardingCommand:Resume$$$F8</tags>
          <children xsi:type="basic:PartSashContainer" xmi:id="_YRYX-nHmEfCAZJ6UDgwwcw" selectedElement="_YRYYAXHmEfCAZJ6UDgwwcw" horizontal="true">
            <children xsi:type="basic:PartSashContainer" xmi:id="_YRYX-3HmEfCAZJ6UDgwwcw" containerData="1453" selectedElement="_YRYX_HHmEfCAZJ6UDgwwcw" horizontal="true">
              <children xsi:type="basic:PartStack" xmi:id="_YRYX_HHmEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.internal.ui.NavigatorFolderView" containerData="5000" selectedElement="_YRYX_nHmEfCAZJ6UDgwwcw">
                <tags>org.eclipse.e4.primaryNavigationStack</tags>
                <children xsi:type="advanced:Placeholder" xmi:id="_YRYX_XHmEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.DebugView" ref="_YRZAeHHmEfCAZJ6UDgwwcw" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:Debug</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_YRYX_nHmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.navigator.ProjectExplorer" ref="_YRZAHnHmEfCAZJ6UDgwwcw" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:General</tags>
                </children>
              </children>
              <children xsi:type="basic:PartStack" xmi:id="_YRYX_3HmEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.dsf.gdb.ui.tracecontrol.viewMStack" toBeRendered="false" containerData="5000">
                <children xsi:type="advanced:Placeholder" xmi:id="_YRYYAHHmEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.dsf.gdb.ui.tracecontrol.view" toBeRendered="false" ref="_YRZBAXHmEfCAZJ6UDgwwcw" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:Debug</tags>
                </children>
              </children>
            </children>
            <children xsi:type="basic:PartSashContainer" xmi:id="_YRYYAXHmEfCAZJ6UDgwwcw" containerData="8547" selectedElement="_YRYYAnHmEfCAZJ6UDgwwcw">
              <children xsi:type="basic:PartSashContainer" xmi:id="_YRYYAnHmEfCAZJ6UDgwwcw" containerData="6383" selectedElement="_YRYYA3HmEfCAZJ6UDgwwcw" horizontal="true">
                <children xsi:type="advanced:Placeholder" xmi:id="_YRYYA3HmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.editorss" containerData="6500" ref="_YRY_5HHmEfCAZJ6UDgwwcw"/>
                <children xsi:type="basic:PartStack" xmi:id="_YRYYBHHmEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.internal.ui.OutlineFolderView" containerData="3500" selectedElement="_YRYYBXHmEfCAZJ6UDgwwcw">
                  <tags>org.eclipse.e4.secondaryNavigationStack</tags>
                  <tags>noFocus</tags>
                  <children xsi:type="advanced:Placeholder" xmi:id="_YRYYBXHmEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.VariableView" ref="_YRZAoHHmEfCAZJ6UDgwwcw" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:Debug</tags>
                  </children>
                  <children xsi:type="advanced:Placeholder" xmi:id="_YRYYBnHmEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.BreakpointView" ref="_YRZAwHHmEfCAZJ6UDgwwcw" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:Debug</tags>
                  </children>
                  <children xsi:type="advanced:Placeholder" xmi:id="_YRYYB3HmEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.ExpressionView" ref="_YRZA2XHmEfCAZJ6UDgwwcw" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:Debug</tags>
                  </children>
                  <children xsi:type="advanced:Placeholder" xmi:id="_YRYYCHHmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.views.ContentOutline" toBeRendered="false" ref="_YRZAcHHmEfCAZJ6UDgwwcw" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:General</tags>
                  </children>
                  <children xsi:type="advanced:Placeholder" xmi:id="_YRYYCXHmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.views.PropertySheet" toBeRendered="false" ref="_YRZAb3HmEfCAZJ6UDgwwcw" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:General</tags>
                  </children>
                  <children xsi:type="advanced:Placeholder" xmi:id="_YRYYCnHmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.views.minimap.MinimapView" toBeRendered="false" ref="_YRZAc3HmEfCAZJ6UDgwwcw" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:General</tags>
                  </children>
                  <children xsi:type="advanced:Placeholder" xmi:id="_YRYYC3HmEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.visualizer.view" ref="_YRZA9HHmEfCAZJ6UDgwwcw" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:Debug</tags>
                  </children>
                  <children xsi:type="advanced:Placeholder" xmi:id="_YRYYDHHmEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.debug.ui.SignalsView" toBeRendered="false" ref="_YRZA9nHmEfCAZJ6UDgwwcw" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:Debug</tags>
                  </children>
                  <children xsi:type="advanced:Placeholder" xmi:id="_YRYYDXHmEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.ModuleView" toBeRendered="false" ref="_YRZA93HmEfCAZJ6UDgwwcw" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:Debug</tags>
                  </children>
                  <children xsi:type="advanced:Placeholder" xmi:id="_YRYYDnHmEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.dsf.debug.ui.disassembly.view" ref="_YRZA-3HmEfCAZJ6UDgwwcw" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:Debug</tags>
                  </children>
                  <children xsi:type="advanced:Placeholder" xmi:id="_YRYYD3HmEfCAZJ6UDgwwcw" elementId="org.eclipse.embedcdt.internal.debug.gdbjtag.ui.views.PeripheralsView" ref="_YRZBDHHmEfCAZJ6UDgwwcw" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:Debug</tags>
                  </children>
                </children>
              </children>
              <children xsi:type="basic:PartStack" xmi:id="_YRYYEHHmEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.internal.ui.ToolsFolderView" containerData="3617" selectedElement="_YRYYEXHmEfCAZJ6UDgwwcw">
                <tags>Debug</tags>
                <tags>General</tags>
                <tags>noFocus</tags>
                <children xsi:type="advanced:Placeholder" xmi:id="_YRYYEXHmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.console.ConsoleView" ref="_YRZARXHmEfCAZJ6UDgwwcw" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:General</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_YRYYEnHmEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.RegisterView" toBeRendered="false" ref="_YRZAnHHmEfCAZJ6UDgwwcw" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:Debug</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_YRYYE3HmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.views.BookmarkView" toBeRendered="false" ref="_YRZAO3HmEfCAZJ6UDgwwcw" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:General</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_YRYYFHHmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.views.ProgressView" toBeRendered="false" ref="_YRZAn3HmEfCAZJ6UDgwwcw" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:General</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_YRYYFXHmEfCAZJ6UDgwwcw" elementId="org.eclipse.pde.runtime.LogView" toBeRendered="false" ref="_YRZA9XHmEfCAZJ6UDgwwcw" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:General</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_YRYYFnHmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.views.ProblemView" ref="_YRZAPHHmEfCAZJ6UDgwwcw" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:General</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_YRYYF3HmEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.debug.ui.executablesView" ref="_YRZA-HHmEfCAZJ6UDgwwcw" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:Debug</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_YRYYGHHmEfCAZJ6UDgwwcw" elementId="org.eclipse.tm.terminal.view.ui.TerminalsView" toBeRendered="false" ref="_YRZAdnHmEfCAZJ6UDgwwcw" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:Terminal</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_YRYYGXHmEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.debug.ui.debuggerConsoleView" ref="_YRZBAnHmEfCAZJ6UDgwwcw" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:Debug</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_YRYYGnHmEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.dsf.gdb.ui.debugsources.view" toBeRendered="false" ref="_YRZBC3HmEfCAZJ6UDgwwcw" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:Debug</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_YRYYG3HmEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.MemoryView" ref="_YRZBHXHmEfCAZJ6UDgwwcw" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:Debug</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_YRYYHHHmEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.VariableView:PIN_CLONE_VIEW_1" toBeRendered="false" ref="_YRZBMnHmEfCAZJ6UDgwwcw" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:Debug</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_YRYYHXHmEfCAZJ6UDgwwcw" elementId="org.eclipse.search.ui.views.SearchView" ref="_YRZBNXHmEfCAZJ6UDgwwcw" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:General</tags>
                </children>
              </children>
            </children>
          </children>
          <windows xsi:type="basic:TrimmedWindow" xmi:id="_YRYYHnHmEfCAZJ6UDgwwcw" toBeRendered="false" x="1805" y="140" width="1648" height="361">
            <tags>shellMaximized</tags>
            <children xsi:type="basic:PartStack" xmi:id="_YRYYH3HmEfCAZJ6UDgwwcw" elementId="PartStack@523685f6" toBeRendered="false">
              <children xsi:type="advanced:Placeholder" xmi:id="_YRYYIHHmEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.debug.ui.memory.memorybrowser.MemoryBrowser" toBeRendered="false" ref="_YRZA_nHmEfCAZJ6UDgwwcw" closeable="true">
                <tags>View</tags>
                <tags>categoryTag:Debug</tags>
              </children>
            </children>
          </windows>
        </children>
      </children>
      <children xsi:type="basic:PartStack" xmi:id="_YRYYIXHmEfCAZJ6UDgwwcw" elementId="stickyFolderRight" toBeRendered="false" containerData="2500">
        <children xsi:type="advanced:Placeholder" xmi:id="_YRYYInHmEfCAZJ6UDgwwcw" elementId="org.eclipse.help.ui.HelpView" toBeRendered="false" ref="_YRY_33HmEfCAZJ6UDgwwcw" closeable="true">
          <tags>View</tags>
          <tags>categoryTag:Help</tags>
        </children>
        <children xsi:type="advanced:Placeholder" xmi:id="_YRYYI3HmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.internal.introview" toBeRendered="false" ref="_YRY_4HHmEfCAZJ6UDgwwcw" closeable="true">
          <tags>View</tags>
          <tags>categoryTag:General</tags>
        </children>
        <children xsi:type="advanced:Placeholder" xmi:id="_YRYYJHHmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.cheatsheets.views.CheatSheetView" toBeRendered="false" ref="_YRY_43HmEfCAZJ6UDgwwcw" closeable="true">
          <tags>View</tags>
          <tags>categoryTag:Help</tags>
        </children>
      </children>
    </children>
    <sharedElements xsi:type="basic:Part" xmi:id="_YRY_33HmEfCAZJ6UDgwwcw" elementId="org.eclipse.help.ui.HelpView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Help" iconURI="platform:/plugin/org.eclipse.help.ui/icons/view16/help_view.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.help.ui.internal.views.HelpView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.help.ui"/>
      <tags>View</tags>
      <tags>categoryTag:Help</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_YRY_4HHmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.internal.introview" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Welcome" iconURI="platform:/plugin/org.eclipse.ui/icons/full/eview16/defaultview_misc.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.ViewIntroAdapterPart"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view>&#xD;&#xA;&lt;presentation currentPage=&quot;qroot&quot; restore=&quot;true&quot;/>&#xD;&#xA;&lt;standbyPart/>&#xD;&#xA;&lt;/view>"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
      <menus xmi:id="_YRY_4XHmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.internal.introview">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_YRY_4nHmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.internal.introview" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_YRY_43HmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.cheatsheets.views.CheatSheetView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Cheat Sheets" iconURI="platform:/plugin/org.eclipse.ui.cheatsheets/icons/view16/cheatsheet_view.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.cheatsheets.views.CheatSheetView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.cheatsheets"/>
      <tags>View</tags>
      <tags>categoryTag:Help</tags>
    </sharedElements>
    <sharedElements xsi:type="advanced:Area" xmi:id="_YRY_5HHmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.editorss" selectedElement="_YRY_5XHmEfCAZJ6UDgwwcw">
      <children xsi:type="basic:PartStack" xmi:id="_YRY_5XHmEfCAZJ6UDgwwcw" elementId="org.eclipse.e4.primaryDataStack" selectedElement="_YRY_6nHmEfCAZJ6UDgwwcw">
        <tags>org.eclipse.e4.primaryDataStack</tags>
        <tags>EditorStack</tags>
        <tags>active</tags>
        <tags>noFocus</tags>
        <children xsi:type="basic:Part" xmi:id="_YRY_5nHmEfCAZJ6UDgwwcw" elementId="org.eclipse.e4.ui.compatibility.editor" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor" label="main.c" iconURI="platform:/plugin/org.eclipse.cdt.ui/icons/obj16/c_file_obj.gif" closeable="true">
          <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;editor id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;main.c&quot; partName=&quot;main.c&quot; title=&quot;main.c&quot; tooltip=&quot;qemu/galaxy_sdk/main.c&quot;>&#xD;&#xA;&lt;input factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; path=&quot;/qemu/galaxy_sdk/main.c&quot;/>&#xD;&#xA;&lt;editorState selectionHorizontalPixel=&quot;0&quot; selectionLength=&quot;0&quot; selectionOffset=&quot;3300&quot; selectionTopPixel=&quot;2090&quot;/>&#xD;&#xA;&lt;/editor>"/>
          <tags>Editor</tags>
          <tags>removeOnHide</tags>
          <tags>org.eclipse.cdt.ui.editor.CEditor</tags>
        </children>
        <children xsi:type="basic:Part" xmi:id="_YRY_6nHmEfCAZJ6UDgwwcw" elementId="org.eclipse.e4.ui.compatibility.editor" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor" label="speaker_inference.c" iconURI="platform:/plugin/org.eclipse.cdt.ui/icons/obj16/c_file_obj.gif" closeable="true">
          <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;editor id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;speaker_inference.c&quot; partName=&quot;speaker_inference.c&quot; title=&quot;speaker_inference.c&quot; tooltip=&quot;qemu/galaxy_sdk/speaker_inference.c&quot;>&#xD;&#xA;&lt;input factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; path=&quot;/qemu/galaxy_sdk/speaker_inference.c&quot;/>&#xD;&#xA;&lt;editorState selectionHorizontalPixel=&quot;0&quot; selectionLength=&quot;0&quot; selectionOffset=&quot;4305&quot; selectionTopPixel=&quot;0&quot;/>&#xD;&#xA;&lt;/editor>"/>
          <tags>Editor</tags>
          <tags>removeOnHide</tags>
          <tags>org.eclipse.cdt.ui.editor.CEditor</tags>
          <tags>active</tags>
        </children>
        <children xsi:type="basic:Part" xmi:id="_YRY_7nHmEfCAZJ6UDgwwcw" elementId="org.eclipse.e4.ui.compatibility.editor" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor" label="nnom.c" iconURI="platform:/plugin/org.eclipse.cdt.ui/icons/obj16/c_file_obj.gif" closeable="true">
          <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;editor id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;nnom.c&quot; partName=&quot;nnom.c&quot; title=&quot;nnom.c&quot; tooltip=&quot;qemu/galaxy_sdk/modules/external/nnom/src/core/nnom.c&quot;>&#xD;&#xA;&lt;input factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; path=&quot;/qemu/galaxy_sdk/modules/external/nnom/src/core/nnom.c&quot;/>&#xD;&#xA;&lt;editorState selectionHorizontalPixel=&quot;0&quot; selectionLength=&quot;0&quot; selectionOffset=&quot;28295&quot; selectionTopPixel=&quot;22836&quot;/>&#xD;&#xA;&lt;/editor>"/>
          <tags>Editor</tags>
          <tags>removeOnHide</tags>
          <tags>org.eclipse.cdt.ui.editor.CEditor</tags>
        </children>
        <children xsi:type="basic:Part" xmi:id="_YRY_8nHmEfCAZJ6UDgwwcw" elementId="org.eclipse.e4.ui.compatibility.editor" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor" label="nnom_input.c" iconURI="platform:/plugin/org.eclipse.cdt.ui/icons/obj16/c_file_obj.gif" closeable="true">
          <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;editor id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;nnom_input.c&quot; partName=&quot;nnom_input.c&quot; title=&quot;nnom_input.c&quot; tooltip=&quot;qemu/galaxy_sdk/modules/external/nnom/src/layers/nnom_input.c&quot;>&#xD;&#xA;&lt;input factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; path=&quot;/qemu/galaxy_sdk/modules/external/nnom/src/layers/nnom_input.c&quot;/>&#xD;&#xA;&lt;editorState selectionHorizontalPixel=&quot;0&quot; selectionLength=&quot;0&quot; selectionOffset=&quot;4507&quot; selectionTopPixel=&quot;2662&quot;/>&#xD;&#xA;&lt;/editor>"/>
          <tags>Editor</tags>
          <tags>removeOnHide</tags>
          <tags>org.eclipse.cdt.ui.editor.CEditor</tags>
        </children>
        <children xsi:type="basic:Part" xmi:id="_YRY_9nHmEfCAZJ6UDgwwcw" elementId="org.eclipse.e4.ui.compatibility.editor" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor" label="nnom_tensor.c" iconURI="platform:/plugin/org.eclipse.cdt.ui/icons/obj16/c_file_obj.gif" closeable="true">
          <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;editor id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;nnom_tensor.c&quot; partName=&quot;nnom_tensor.c&quot; title=&quot;nnom_tensor.c&quot; tooltip=&quot;qemu/galaxy_sdk/modules/external/nnom/src/core/nnom_tensor.c&quot;>&#xD;&#xA;&lt;input factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; path=&quot;/qemu/galaxy_sdk/modules/external/nnom/src/core/nnom_tensor.c&quot;/>&#xD;&#xA;&lt;editorState selectionHorizontalPixel=&quot;0&quot; selectionLength=&quot;0&quot; selectionOffset=&quot;573&quot; selectionTopPixel=&quot;330&quot;/>&#xD;&#xA;&lt;/editor>"/>
          <tags>Editor</tags>
          <tags>removeOnHide</tags>
          <tags>org.eclipse.cdt.ui.editor.CEditor</tags>
        </children>
        <children xsi:type="basic:Part" xmi:id="_YRY_-nHmEfCAZJ6UDgwwcw" elementId="org.eclipse.e4.ui.compatibility.editor" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor" label="nnom_conv2d.c" iconURI="platform:/plugin/org.eclipse.cdt.ui/icons/obj16/c_file_obj.gif" closeable="true">
          <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;editor id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;nnom_conv2d.c&quot; partName=&quot;nnom_conv2d.c&quot; title=&quot;nnom_conv2d.c&quot; tooltip=&quot;qemu/galaxy_sdk/modules/external/nnom/src/layers/nnom_conv2d.c&quot;>&#xD;&#xA;&lt;input factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; path=&quot;/qemu/galaxy_sdk/modules/external/nnom/src/layers/nnom_conv2d.c&quot;/>&#xD;&#xA;&lt;editorState selectionHorizontalPixel=&quot;0&quot; selectionLength=&quot;0&quot; selectionOffset=&quot;18880&quot; selectionTopPixel=&quot;9020&quot;/>&#xD;&#xA;&lt;/editor>"/>
          <tags>Editor</tags>
          <tags>removeOnHide</tags>
          <tags>org.eclipse.cdt.ui.editor.CEditor</tags>
        </children>
        <children xsi:type="basic:Part" xmi:id="_YRY__nHmEfCAZJ6UDgwwcw" elementId="org.eclipse.e4.ui.compatibility.editor" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor" label="nnom_local.c" iconURI="platform:/plugin/org.eclipse.cdt.ui/icons/obj16/c_file_obj.gif" closeable="true">
          <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;editor id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;nnom_local.c&quot; partName=&quot;nnom_local.c&quot; title=&quot;nnom_local.c&quot; tooltip=&quot;qemu/galaxy_sdk/modules/external/nnom/src/backends/nnom_local.c&quot;>&#xD;&#xA;&lt;input factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; path=&quot;/qemu/galaxy_sdk/modules/external/nnom/src/backends/nnom_local.c&quot;/>&#xD;&#xA;&lt;editorState selectionHorizontalPixel=&quot;0&quot; selectionLength=&quot;0&quot; selectionOffset=&quot;51325&quot; selectionTopPixel=&quot;29458&quot;/>&#xD;&#xA;&lt;/editor>"/>
          <tags>Editor</tags>
          <tags>removeOnHide</tags>
          <tags>org.eclipse.cdt.ui.editor.CEditor</tags>
        </children>
        <children xsi:type="basic:Part" xmi:id="_YRZAAnHmEfCAZJ6UDgwwcw" elementId="org.eclipse.e4.ui.compatibility.editor" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor" label="nnom_activation.c" iconURI="platform:/plugin/org.eclipse.cdt.ui/icons/obj16/c_file_obj.gif" closeable="true">
          <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;editor id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;nnom_activation.c&quot; partName=&quot;nnom_activation.c&quot; title=&quot;nnom_activation.c&quot; tooltip=&quot;qemu/galaxy_sdk/modules/external/nnom/src/layers/nnom_activation.c&quot;>&#xD;&#xA;&lt;input factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; path=&quot;/qemu/galaxy_sdk/modules/external/nnom/src/layers/nnom_activation.c&quot;/>&#xD;&#xA;&lt;editorState selectionHorizontalPixel=&quot;0&quot; selectionLength=&quot;0&quot; selectionOffset=&quot;3598&quot; selectionTopPixel=&quot;3058&quot;/>&#xD;&#xA;&lt;/editor>"/>
          <tags>Editor</tags>
          <tags>removeOnHide</tags>
          <tags>org.eclipse.cdt.ui.editor.CEditor</tags>
        </children>
        <children xsi:type="basic:Part" xmi:id="_YRZABnHmEfCAZJ6UDgwwcw" elementId="org.eclipse.e4.ui.compatibility.editor" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor" label="nnom_maxpool.c" iconURI="platform:/plugin/org.eclipse.cdt.ui/icons/obj16/c_file_obj.gif" closeable="true">
          <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;editor id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;nnom_maxpool.c&quot; partName=&quot;nnom_maxpool.c&quot; title=&quot;nnom_maxpool.c&quot; tooltip=&quot;qemu/galaxy_sdk/modules/external/nnom/src/layers/nnom_maxpool.c&quot;>&#xD;&#xA;&lt;input factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; path=&quot;/qemu/galaxy_sdk/modules/external/nnom/src/layers/nnom_maxpool.c&quot;/>&#xD;&#xA;&lt;editorState selectionHorizontalPixel=&quot;0&quot; selectionLength=&quot;0&quot; selectionOffset=&quot;5121&quot; selectionTopPixel=&quot;3674&quot;/>&#xD;&#xA;&lt;/editor>"/>
          <tags>Editor</tags>
          <tags>removeOnHide</tags>
          <tags>org.eclipse.cdt.ui.editor.CEditor</tags>
        </children>
        <children xsi:type="basic:Part" xmi:id="_YRZACnHmEfCAZJ6UDgwwcw" elementId="org.eclipse.e4.ui.compatibility.editor" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor" label="nnom_avgpool.c" iconURI="platform:/plugin/org.eclipse.cdt.ui/icons/obj16/c_file_obj.gif" closeable="true">
          <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;editor id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;nnom_avgpool.c&quot; partName=&quot;nnom_avgpool.c&quot; title=&quot;nnom_avgpool.c&quot; tooltip=&quot;qemu/galaxy_sdk/modules/external/nnom/src/layers/nnom_avgpool.c&quot;>&#xD;&#xA;&lt;input factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; path=&quot;/qemu/galaxy_sdk/modules/external/nnom/src/layers/nnom_avgpool.c&quot;/>&#xD;&#xA;&lt;editorState selectionHorizontalPixel=&quot;0&quot; selectionLength=&quot;0&quot; selectionOffset=&quot;4218&quot; selectionTopPixel=&quot;3121&quot;/>&#xD;&#xA;&lt;/editor>"/>
          <tags>Editor</tags>
          <tags>removeOnHide</tags>
          <tags>org.eclipse.cdt.ui.editor.CEditor</tags>
        </children>
        <children xsi:type="basic:Part" xmi:id="_YRZADnHmEfCAZJ6UDgwwcw" elementId="org.eclipse.e4.ui.compatibility.editor" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor" label="nnom_flatten.c" iconURI="platform:/plugin/org.eclipse.cdt.ui/icons/obj16/c_file_obj.gif" closeable="true">
          <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;editor id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;nnom_flatten.c&quot; partName=&quot;nnom_flatten.c&quot; title=&quot;nnom_flatten.c&quot; tooltip=&quot;qemu/galaxy_sdk/modules/external/nnom/src/layers/nnom_flatten.c&quot;>&#xD;&#xA;&lt;input factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; path=&quot;/qemu/galaxy_sdk/modules/external/nnom/src/layers/nnom_flatten.c&quot;/>&#xD;&#xA;&lt;editorState selectionHorizontalPixel=&quot;0&quot; selectionLength=&quot;0&quot; selectionOffset=&quot;2137&quot; selectionTopPixel=&quot;1317&quot;/>&#xD;&#xA;&lt;/editor>"/>
          <tags>Editor</tags>
          <tags>removeOnHide</tags>
          <tags>org.eclipse.cdt.ui.editor.CEditor</tags>
        </children>
        <children xsi:type="basic:Part" xmi:id="_YRZAEnHmEfCAZJ6UDgwwcw" elementId="org.eclipse.e4.ui.compatibility.editor" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor" label="nnom_dense.c" iconURI="platform:/plugin/org.eclipse.cdt.ui/icons/obj16/c_file_obj.gif" closeable="true">
          <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;editor id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;nnom_dense.c&quot; partName=&quot;nnom_dense.c&quot; title=&quot;nnom_dense.c&quot; tooltip=&quot;qemu/galaxy_sdk/modules/external/nnom/src/layers/nnom_dense.c&quot;>&#xD;&#xA;&lt;input factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; path=&quot;/qemu/galaxy_sdk/modules/external/nnom/src/layers/nnom_dense.c&quot;/>&#xD;&#xA;&lt;editorState selectionHorizontalPixel=&quot;0&quot; selectionLength=&quot;0&quot; selectionOffset=&quot;7044&quot; selectionTopPixel=&quot;4287&quot;/>&#xD;&#xA;&lt;/editor>"/>
          <tags>Editor</tags>
          <tags>removeOnHide</tags>
          <tags>org.eclipse.cdt.ui.editor.CEditor</tags>
        </children>
        <children xsi:type="basic:Part" xmi:id="_YRZAFnHmEfCAZJ6UDgwwcw" elementId="org.eclipse.e4.ui.compatibility.editor" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor" label="nnom_softmax.c" iconURI="platform:/plugin/org.eclipse.cdt.ui/icons/obj16/c_file_obj.gif" closeable="true">
          <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;editor id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;nnom_softmax.c&quot; partName=&quot;nnom_softmax.c&quot; title=&quot;nnom_softmax.c&quot; tooltip=&quot;qemu/galaxy_sdk/modules/external/nnom/src/layers/nnom_softmax.c&quot;>&#xD;&#xA;&lt;input factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; path=&quot;/qemu/galaxy_sdk/modules/external/nnom/src/layers/nnom_softmax.c&quot;/>&#xD;&#xA;&lt;editorState selectionHorizontalPixel=&quot;0&quot; selectionLength=&quot;0&quot; selectionOffset=&quot;2129&quot; selectionTopPixel=&quot;1364&quot;/>&#xD;&#xA;&lt;/editor>"/>
          <tags>Editor</tags>
          <tags>removeOnHide</tags>
          <tags>org.eclipse.cdt.ui.editor.CEditor</tags>
        </children>
        <children xsi:type="basic:Part" xmi:id="_YRZAGnHmEfCAZJ6UDgwwcw" elementId="org.eclipse.e4.ui.compatibility.editor" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor" label="nnom_output.c" iconURI="platform:/plugin/org.eclipse.cdt.ui/icons/obj16/c_file_obj.gif" closeable="true">
          <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;editor id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;nnom_output.c&quot; partName=&quot;nnom_output.c&quot; title=&quot;nnom_output.c&quot; tooltip=&quot;qemu/galaxy_sdk/modules/external/nnom/src/layers/nnom_output.c&quot;>&#xD;&#xA;&lt;input factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; path=&quot;/qemu/galaxy_sdk/modules/external/nnom/src/layers/nnom_output.c&quot;/>&#xD;&#xA;&lt;editorState selectionHorizontalPixel=&quot;0&quot; selectionLength=&quot;0&quot; selectionOffset=&quot;1263&quot; selectionTopPixel=&quot;704&quot;/>&#xD;&#xA;&lt;/editor>"/>
          <tags>Editor</tags>
          <tags>removeOnHide</tags>
          <tags>org.eclipse.cdt.ui.editor.CEditor</tags>
        </children>
      </children>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_YRZAHnHmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.navigator.ProjectExplorer" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Project Explorer" iconURI="platform:/plugin/org.eclipse.ui.navigator.resources/icons/full/eview16/resource_persp.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.navigator.resources.ProjectExplorer"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.navigator.resources"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view CommonNavigator.LINKING_ENABLED=&quot;0&quot; currentWorkingSetName=&quot;Aggregate for window 1754150091289&quot; org.eclipse.cdt.ui.cview.groupincludes=&quot;false&quot; org.eclipse.cdt.ui.cview.groupmacros=&quot;false&quot; org.eclipse.cdt.ui.editor.CUChildren=&quot;true&quot; org.eclipse.ui.navigator.resources.workingSets.showTopLevelWorkingSets=&quot;0&quot;>&#xD;&#xA;&lt;lastRecentlyUsedFilters/>&#xD;&#xA;&lt;/view>"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
      <menus xmi:id="_YRZAH3HmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.navigator.ProjectExplorer">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_YRZAM3HmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.navigator.ProjectExplorer"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_YRZAOnHmEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.CView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="C/C++ Projects" iconURI="platform:/plugin/org.eclipse.cdt.ui/icons/view16/cview.gif" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.internal.ui.cview.CView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.ui"/>
      <tags>View</tags>
      <tags>categoryTag:C/C++</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_YRZAO3HmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.views.BookmarkView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Bookmarks" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/bkmrk_nav.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.markers.BookmarksView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_YRZAPHHmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.views.ProblemView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Problems" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/problems_view.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.markers.ProblemsView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view PRIMARY_SORT_FIELD=&quot;org.eclipse.ui.ide.severityAndDescriptionField&quot; categoryGroup=&quot;org.eclipse.ui.ide.severity&quot; markerContentGenerator=&quot;org.eclipse.ui.ide.problemsGenerator&quot;>&#xD;&#xA;&lt;columnWidths org.eclipse.ui.ide.locationField=&quot;90&quot; org.eclipse.ui.ide.markerType=&quot;90&quot; org.eclipse.ui.ide.pathField=&quot;120&quot; org.eclipse.ui.ide.resourceField=&quot;90&quot; org.eclipse.ui.ide.severityAndDescriptionField=&quot;300&quot;/>&#xD;&#xA;&lt;visible IMemento.internal.id=&quot;org.eclipse.ui.ide.severityAndDescriptionField&quot;/>&#xD;&#xA;&lt;visible IMemento.internal.id=&quot;org.eclipse.ui.ide.resourceField&quot;/>&#xD;&#xA;&lt;visible IMemento.internal.id=&quot;org.eclipse.ui.ide.pathField&quot;/>&#xD;&#xA;&lt;visible IMemento.internal.id=&quot;org.eclipse.ui.ide.locationField&quot;/>&#xD;&#xA;&lt;visible IMemento.internal.id=&quot;org.eclipse.ui.ide.markerType&quot;/>&#xD;&#xA;&lt;/view>"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
      <menus xmi:id="_YRZAPXHmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.views.ProblemView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_YRZAP3HmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.views.ProblemView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_YRZARHHmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.views.TaskList" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Tasks" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/tasks_tsk.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.markers.TasksView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_YRZARXHmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.console.ConsoleView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Console" iconURI="platform:/plugin/org.eclipse.ui.console/icons/full/cview16/console_view.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.console.ConsoleView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.console"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
      <menus xmi:id="_YRZARnHmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.console.ConsoleView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_YRZASnHmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.console.ConsoleView"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_YRZAb3HmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.views.PropertySheet" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Properties" iconURI="platform:/plugin/org.eclipse.ui.views/icons/full/eview16/prop_ps.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.views.properties.PropertySheet"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.views"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_YRZAcHHmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.views.ContentOutline" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Outline" iconURI="platform:/plugin/org.eclipse.ui.views/icons/full/eview16/outline_co.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.views.contentoutline.ContentOutline"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.views"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
      <menus xmi:id="_YRZAcXHmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.views.ContentOutline">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_YRZAcnHmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.views.ContentOutline" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_YRZAc3HmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.views.minimap.MinimapView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Minimap" iconURI="platform:/plugin/org.eclipse.ui.workbench.texteditor/icons/full/eview16/minimap.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.minimap.MinimapView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.workbench.texteditor"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_YRZAdHHmEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.tasks.ui.views.tasks" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Task List" iconURI="platform:/plugin/org.eclipse.mylyn.tasks.ui/icons/eview16/task-list.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.mylyn.internal.tasks.ui.views.TaskListView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.mylyn.tasks.ui"/>
      <tags>View</tags>
      <tags>categoryTag:Mylyn</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_YRZAdXHmEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.make.ui.views.MakeView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Build Targets" iconURI="platform:/plugin/org.eclipse.cdt.make.ui/icons/view16/make_target.gif" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.make.ui.views.MakeView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.make.ui"/>
      <tags>View</tags>
      <tags>categoryTag:Make</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_YRZAdnHmEfCAZJ6UDgwwcw" elementId="org.eclipse.tm.terminal.view.ui.TerminalsView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Terminal" iconURI="platform:/plugin/org.eclipse.tm.terminal.view.ui/icons/eview16/terminal_view.gif" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tm.terminal.view.ui.view.TerminalsView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tm.terminal.view.ui"/>
      <tags>View</tags>
      <tags>categoryTag:Terminal</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_YRZAd3HmEfCAZJ6UDgwwcw" elementId="org.eclipse.embedcdt.internal.managedbuild.packs.ui.views.DocsView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Documents" iconURI="platform:/plugin/org.eclipse.embedcdt.managedbuild.packs.ui/icons/pdficon_small.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.embedcdt.internal.managedbuild.packs.ui.views.DocsView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.embedcdt.managedbuild.packs.ui"/>
      <tags>View</tags>
      <tags>categoryTag:CMSIS Packs</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_YRZAeHHmEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.DebugView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Debug" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/debug_view.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.launch.LaunchView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:Debug</tags>
      <menus xmi:id="_YRZAeXHmEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.DebugView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_YRZAhHHmEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.DebugView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_YRZAnHHmEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.RegisterView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Registers" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/register_view.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.registers.RegistersView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:Debug</tags>
      <menus xmi:id="_YRZAnXHmEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.RegisterView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_YRZAnnHmEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.RegisterView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_YRZAn3HmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.views.ProgressView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Progress" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/pview.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.progress.ProgressView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_YRZAoHHmEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.VariableView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Variables" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/variable_view.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.variables.VariablesView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:Debug</tags>
      <menus xmi:id="_YRZAoXHmEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.VariableView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_YRZAtXHmEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.VariableView"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_YRZAwHHmEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.BreakpointView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Breakpoints" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/breakpoint_view.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.breakpoints.BreakpointsView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:Debug</tags>
      <menus xmi:id="_YRZAwXHmEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.BreakpointView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_YRZAy3HmEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.BreakpointView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_YRZA2XHmEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.ExpressionView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Expressions" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/watchlist_view.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.expression.ExpressionView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:Debug</tags>
      <menus xmi:id="_YRZA2nHmEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.ExpressionView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_YRZA5HHmEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.ExpressionView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_YRZA9HHmEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.visualizer.view" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Visualizer" iconURI="platform:/plugin/org.eclipse.cdt.visualizer.ui/icons/full/view16/visualizer_view.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.visualizer.ui.VisualizerView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.visualizer.ui"/>
      <tags>View</tags>
      <tags>categoryTag:Debug</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_YRZA9XHmEfCAZJ6UDgwwcw" elementId="org.eclipse.pde.runtime.LogView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Error Log" iconURI="platform:/plugin/org.eclipse.ui.views.log/icons/eview16/error_log.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.log.LogView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.views.log"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_YRZA9nHmEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.debug.ui.SignalsView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Signals" iconURI="platform:/plugin/org.eclipse.cdt.debug.ui/icons/view16/signals_view.gif" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.debug.internal.ui.views.signals.FlexibleSignalsView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.debug.ui"/>
      <tags>View</tags>
      <tags>categoryTag:Debug</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_YRZA93HmEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.ModuleView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Modules" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/module_view.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.modules.ModulesView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
      <tags>View</tags>
      <tags>categoryTag:Debug</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_YRZA-HHmEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.debug.ui.executablesView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Executables" iconURI="platform:/plugin/org.eclipse.cdt.debug.ui/icons/obj16/exec_view_obj.gif" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.debug.internal.ui.views.executables.ExecutablesView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.debug.ui"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:Debug</tags>
      <menus xmi:id="_YRZA-XHmEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.debug.ui.executablesView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_YRZA-nHmEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.debug.ui.executablesView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_YRZA-3HmEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.dsf.debug.ui.disassembly.view" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Disassembly" iconURI="platform:/plugin/org.eclipse.cdt.dsf.ui/icons/disassembly.gif" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.dsf.debug.internal.ui.disassembly.DisassemblyView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.dsf.ui"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view disassembly.syncActiveContext=&quot;true&quot; disassembly.trackExpression=&quot;false&quot;/>"/>
      <tags>View</tags>
      <tags>categoryTag:Debug</tags>
      <menus xmi:id="_YRZA_HHmEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.dsf.debug.ui.disassembly.view">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_YRZA_XHmEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.dsf.debug.ui.disassembly.view" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_YRZA_nHmEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.debug.ui.memory.memorybrowser.MemoryBrowser" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Memory Browser" iconURI="platform:/plugin/org.eclipse.cdt.debug.ui.memory.memorybrowser/icons/memorybrowser_view.gif" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.debug.ui.memory.memorybrowser.MemoryBrowser"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.debug.ui.memory.memorybrowser"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:Debug</tags>
      <menus xmi:id="_YRZA_3HmEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.debug.ui.memory.memorybrowser.MemoryBrowser">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_YRZBAHHmEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.debug.ui.memory.memorybrowser.MemoryBrowser" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_YRZBAXHmEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.dsf.gdb.ui.tracecontrol.view" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Trace Control" iconURI="platform:/plugin/org.eclipse.cdt.dsf.gdb.ui/icons/full/view16/tracecontrol_view.gif" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.dsf.gdb.internal.ui.tracepoints.TraceControlView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.dsf.gdb.ui"/>
      <tags>View</tags>
      <tags>categoryTag:Debug</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_YRZBAnHmEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.debug.ui.debuggerConsoleView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Debugger Console" iconURI="platform:/plugin/org.eclipse.cdt.debug.ui/icons/view16/debugger_console_view.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.debug.internal.ui.views.debuggerconsole.DebuggerConsoleView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.debug.ui"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:Debug</tags>
      <menus xmi:id="_YRZBA3HmEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.debug.ui.debuggerConsoleView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_YRZBBXHmEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.debug.ui.debuggerConsoleView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_YRZBC3HmEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.dsf.gdb.ui.debugsources.view" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Debug Sources" iconURI="platform:/plugin/org.eclipse.cdt.dsf.gdb.ui/icons/full/view16/debugsources_view.gif" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.dsf.gdb.internal.ui.debugsources.DebugSourcesView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.dsf.gdb.ui"/>
      <tags>View</tags>
      <tags>categoryTag:Debug</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_YRZBDHHmEfCAZJ6UDgwwcw" elementId="org.eclipse.embedcdt.internal.debug.gdbjtag.ui.views.PeripheralsView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Peripherals" iconURI="platform:/plugin/org.eclipse.embedcdt.debug.gdbjtag.ui/icons/peripheral.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.embedcdt.internal.debug.gdbjtag.ui.render.peripherals.PeripheralsView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.embedcdt.debug.gdbjtag.ui"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:Debug</tags>
      <menus xmi:id="_YRZBDXHmEfCAZJ6UDgwwcw" elementId="org.eclipse.embedcdt.internal.debug.gdbjtag.ui.views.PeripheralsView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_YRZBF3HmEfCAZJ6UDgwwcw" elementId="org.eclipse.embedcdt.internal.debug.gdbjtag.ui.views.PeripheralsView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_YRZBHXHmEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.MemoryView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Memory" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/memory_view.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.memory.MemoryView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:Debug</tags>
      <menus xmi:id="_YRZBHnHmEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.MemoryView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_YRZBJXHmEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.MemoryView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_YRZBMnHmEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.VariableView:PIN_CLONE_VIEW_1" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Variables &lt;1>" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/variable_view.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.variables.VariablesView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:Debug</tags>
      <menus xmi:id="_YRZBM3HmEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.VariableView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_YRZBNHHmEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.VariableView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_YRZBNXHmEfCAZJ6UDgwwcw" elementId="org.eclipse.search.ui.views.SearchView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Search" iconURI="platform:/plugin/org.eclipse.search/icons/full/eview16/searchres.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.search2.internal.ui.SearchView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.search"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view isPinned=&quot;false&quot;>&#xD;&#xA;&lt;view IMemento.internal.id=&quot;&quot; org.eclipse.search.lastActivation=&quot;0&quot;/>&#xD;&#xA;&lt;view IMemento.internal.id=&quot;org.eclipse.search.text.FileSearchResultPage&quot; org.eclipse.search.lastActivation=&quot;1&quot; org.eclipse.search.resultpage.layout=&quot;2&quot; org.eclipse.search.resultpage.limit=&quot;1000&quot; org.eclipse.search.resultpage.sorting=&quot;2&quot;/>&#xD;&#xA;&lt;/view>"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
      <menus xmi:id="_YRZBNnHmEfCAZJ6UDgwwcw" elementId="org.eclipse.search.ui.views.SearchView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_YRZBR3HmEfCAZJ6UDgwwcw" elementId="org.eclipse.search.ui.views.SearchView" visible="false"/>
    </sharedElements>
    <trimBars xmi:id="_YRZBbnHmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.main.toolbar" contributorURI="platform:/plugin/org.eclipse.platform">
      <children xsi:type="menu:ToolControl" xmi:id="_YRZBb3HmEfCAZJ6UDgwwcw" elementId="org.eclipse.launchbar" contributionURI="bundleclass://org.eclipse.launchbar.ui.controls/org.eclipse.launchbar.ui.controls.internal.LaunchBarControl"/>
      <children xsi:type="menu:ToolBar" xmi:id="_YRZBcHHmEfCAZJ6UDgwwcw" elementId="group.file" toBeRendered="false">
        <tags>toolbarSeparator</tags>
        <children xsi:type="menu:ToolBarSeparator" xmi:id="_YRZBcXHmEfCAZJ6UDgwwcw" elementId="group.file" toBeRendered="false"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_YRZBcnHmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.workbench.file">
        <tags>Draggable</tags>
        <children xsi:type="menu:HandledToolItem" xmi:id="_YRZBenHmEfCAZJ6UDgwwcw" elementId="print" visible="false" iconURI="platform:/plugin/org.eclipse.ui/icons/full/etool16/print_edit.png" tooltip="Print" command="_YRa3_nHmEfCAZJ6UDgwwcw"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_YRZBgHHmEfCAZJ6UDgwwcw" elementId="group.edit" toBeRendered="false">
        <tags>toolbarSeparator</tags>
        <children xsi:type="menu:ToolBarSeparator" xmi:id="_YRZBgXHmEfCAZJ6UDgwwcw" elementId="group.edit" toBeRendered="false"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_YRZBgnHmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.workbench.edit">
        <tags>Draggable</tags>
        <children xsi:type="menu:HandledToolItem" xmi:id="_YRZBhHHmEfCAZJ6UDgwwcw" elementId="undo" iconURI="platform:/plugin/org.eclipse.ui/icons/full/etool16/undo_edit.png" tooltip="Undo" enabled="false" command="_YRa2WHHmEfCAZJ6UDgwwcw"/>
        <children xsi:type="menu:HandledToolItem" xmi:id="_YRZBhXHmEfCAZJ6UDgwwcw" elementId="redo" iconURI="platform:/plugin/org.eclipse.ui/icons/full/etool16/redo_edit.png" tooltip="Redo" enabled="false" command="_YRa2zXHmEfCAZJ6UDgwwcw"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_YRZBhnHmEfCAZJ6UDgwwcw" elementId="additions" toBeRendered="false">
        <tags>toolbarSeparator</tags>
        <children xsi:type="menu:ToolBarSeparator" xmi:id="_YRZBh3HmEfCAZJ6UDgwwcw" elementId="additions" toBeRendered="false"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_YRZBynHmEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.CElementCreationActionSet" visible="false">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_YRZBy3HmEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.launchActionSet">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_YRZB0HHmEfCAZJ6UDgwwcw" elementId="org.eclipse.search.searchActionSet">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_YRZB1XHmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.actionSet.presentation">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_YRZB23HmEfCAZJ6UDgwwcw" elementId="group.nav" toBeRendered="false">
        <tags>toolbarSeparator</tags>
        <children xsi:type="menu:ToolBarSeparator" xmi:id="_YRZB3HHmEfCAZJ6UDgwwcw" elementId="group.nav" toBeRendered="false"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_YRZB3XHmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.workbench.navigate">
        <tags>Draggable</tags>
        <children xsi:type="menu:HandledToolItem" xmi:id="_YRZB43HmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.window.pinEditor" iconURI="platform:/plugin/org.eclipse.ui/icons/full/etool16/pin_editor.png" tooltip="Pin Editor" type="Check" command="_YRa3sXHmEfCAZJ6UDgwwcw"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_YRZB6HHmEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.editor.CEditor" visible="false">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_YRZB6XHmEfCAZJ6UDgwwcw" elementId="group.editor" toBeRendered="false">
        <tags>toolbarSeparator</tags>
        <children xsi:type="menu:ToolBarSeparator" xmi:id="_YRZB6nHmEfCAZJ6UDgwwcw" elementId="group.editor" toBeRendered="false"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_YRZB63HmEfCAZJ6UDgwwcw" elementId="group.help" toBeRendered="false">
        <tags>toolbarSeparator</tags>
        <children xsi:type="menu:ToolBarSeparator" xmi:id="_YRZB7HHmEfCAZJ6UDgwwcw" elementId="group.help" toBeRendered="false"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_YRZB7XHmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.workbench.help" visible="false">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolControl" xmi:id="_YRZB8HHmEfCAZJ6UDgwwcw" elementId="PerspectiveSpacer" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.renderers.swt/org.eclipse.e4.ui.workbench.renderers.swt.LayoutModifierToolControl">
        <tags>stretch</tags>
        <tags>SHOW_RESTORE_MENU</tags>
      </children>
      <children xsi:type="menu:ToolControl" xmi:id="_YRZB9HHmEfCAZJ6UDgwwcw" elementId="PerspectiveSwitcher" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.e4.ui.workbench.addons.perspectiveswitcher.PerspectiveSwitcher">
        <tags>Draggable</tags>
        <tags>HIDEABLE</tags>
        <tags>SHOW_RESTORE_MENU</tags>
      </children>
    </trimBars>
    <trimBars xmi:id="_YRZB-3HmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.trim.status" contributorURI="platform:/plugin/org.eclipse.platform" side="Bottom">
      <children xsi:type="menu:ToolControl" xmi:id="_YRZB_HHmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.StatusLine" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.StandardTrim">
        <tags>stretch</tags>
      </children>
      <children xsi:type="menu:ToolControl" xmi:id="_YRZB_XHmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.HeapStatus" contributorURI="platform:/plugin/org.eclipse.platform" toBeRendered="false" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.StandardTrim">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolControl" xmi:id="_YRZB_nHmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.ProgressBar" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.StandardTrim">
        <tags>Draggable</tags>
      </children>
    </trimBars>
    <trimBars xmi:id="_YRZCBnHmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.trim.vertical1" contributorURI="platform:/plugin/org.eclipse.platform" toBeRendered="false" side="Left">
      <children xsi:type="menu:ToolControl" xmi:id="_YRZCB3HmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.ide.perspectivestack(minimized)" toBeRendered="false" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.addons.swt/org.eclipse.e4.ui.workbench.addons.minmax.TrimStack">
        <tags>TrimStack</tags>
        <tags>Draggable</tags>
      </children>
    </trimBars>
    <trimBars xmi:id="_YRZCCHHmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.trim.vertical2" contributorURI="platform:/plugin/org.eclipse.platform" side="Right"/>
  </children>
  <bindingTables xmi:id="_YRZCCXHmEfCAZJ6UDgwwcw" contributorURI="platform:/plugin/org.eclipse.platform" bindingContext="_YRZnPnHmEfCAZJ6UDgwwcw">
    <bindings xmi:id="_YRZCCnHmEfCAZJ6UDgwwcw" keySequence="CTRL+1" command="_YRa2HnHmEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_YRZCC3HmEfCAZJ6UDgwwcw" keySequence="CTRL+SHIFT+L" command="_YRa4M3HmEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_YRZCDHHmEfCAZJ6UDgwwcw" keySequence="CTRL+V" command="_YRa1inHmEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_YRZCDXHmEfCAZJ6UDgwwcw" keySequence="CTRL+A" command="_YRa2snHmEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_YRZCDnHmEfCAZJ6UDgwwcw" keySequence="CTRL+C" command="_YRa2-3HmEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_YRZCD3HmEfCAZJ6UDgwwcw" keySequence="CTRL+X" command="_YRa2YHHmEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_YRZCEHHmEfCAZJ6UDgwwcw" keySequence="CTRL+Y" command="_YRa2zXHmEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_YRZCEXHmEfCAZJ6UDgwwcw" keySequence="CTRL+Z" command="_YRa2WHHmEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_YRZCEnHmEfCAZJ6UDgwwcw" keySequence="ALT+PAGE_UP" command="_YRa213HmEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_YRZCE3HmEfCAZJ6UDgwwcw" keySequence="ALT+PAGE_DOWN" command="_YRa3gHHmEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_YRZCFHHmEfCAZJ6UDgwwcw" keySequence="SHIFT+INSERT" command="_YRa1inHmEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_YRZCFXHmEfCAZJ6UDgwwcw" keySequence="ALT+F11" command="_YRa1xnHmEfCAZJ6UDgwwcw">
      <tags>platform:win32</tags>
    </bindings>
    <bindings xmi:id="_YRZCFnHmEfCAZJ6UDgwwcw" keySequence="CTRL+F10" command="_YRa1q3HmEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_YRZCF3HmEfCAZJ6UDgwwcw" keySequence="CTRL+INSERT" command="_YRa2-3HmEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_YRZCGHHmEfCAZJ6UDgwwcw" keySequence="CTRL+PAGE_UP" command="_YRa4E3HmEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_YRZCGXHmEfCAZJ6UDgwwcw" keySequence="CTRL+PAGE_DOWN" command="_YRa2JnHmEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_YRZCGnHmEfCAZJ6UDgwwcw" keySequence="ALT+SHIFT+F3" command="_YRa4CHHmEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_YRZCG3HmEfCAZJ6UDgwwcw" keySequence="SHIFT+DEL" command="_YRa2YHHmEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_YRZCHHHmEfCAZJ6UDgwwcw" keySequence="ALT+/" command="_YRa32HHmEfCAZJ6UDgwwcw">
      <tags>locale:zh</tags>
    </bindings>
  </bindingTables>
  <bindingTables xmi:id="_YRZCHXHmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.textEditorScope" bindingContext="_YRZnQ3HmEfCAZJ6UDgwwcw">
    <bindings xmi:id="_YRZCHnHmEfCAZJ6UDgwwcw" keySequence="CTRL+SHIFT+CR" command="_YRa4B3HmEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_YRZCH3HmEfCAZJ6UDgwwcw" keySequence="CTRL+BS" command="_YRa1Y3HmEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_YRZCIHHmEfCAZJ6UDgwwcw" keySequence="CTRL+SHIFT+Q" command="_YRa2C3HmEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_YRZCIXHmEfCAZJ6UDgwwcw" keySequence="ALT+CTRL+C" command="_YRa1n3HmEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_YRZCInHmEfCAZJ6UDgwwcw" keySequence="CTRL+SHIFT+J" command="_YRa1_HHmEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_YRZCI3HmEfCAZJ6UDgwwcw" keySequence="CTRL++" command="_YRa3YnHmEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_YRZCJHHmEfCAZJ6UDgwwcw" keySequence="CTRL+-" command="_YRa2m3HmEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_YRZCJXHmEfCAZJ6UDgwwcw" keySequence="ALT+CTRL+P" command="_YRa3oXHmEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_YRZCJnHmEfCAZJ6UDgwwcw" keySequence="ALT+CTRL+V" command="_YRa1tnHmEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_YRZCJ3HmEfCAZJ6UDgwwcw" keySequence="ALT+CTRL+J" command="_YRa2F3HmEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_YRZCKHHmEfCAZJ6UDgwwcw" keySequence="ALT+SHIFT+A" command="_YRa3GXHmEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_YRZCKXHmEfCAZJ6UDgwwcw" keySequence="CTRL+J" command="_YRa1sXHmEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_YRZCKnHmEfCAZJ6UDgwwcw" keySequence="CTRL+L" command="_YRa363HmEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_YRZCK3HmEfCAZJ6UDgwwcw" keySequence="CTRL+D" command="_YRa1unHmEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_YRZCLHHmEfCAZJ6UDgwwcw" keySequence="CTRL+=" command="_YRa3YnHmEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_YRZCLXHmEfCAZJ6UDgwwcw" keySequence="ALT+CTRL+/" command="_YRa4RHHmEfCAZJ6UDgwwcw">
      <tags>locale:zh</tags>
    </bindings>
    <bindings xmi:id="_YRZCLnHmEfCAZJ6UDgwwcw" keySequence="ALT+SHIFT+Y" command="_YRa1WnHmEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_YRZCL3HmEfCAZJ6UDgwwcw" keySequence="CTRL+SHIFT+DEL" command="_YRa33XHmEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_YRZCMHHmEfCAZJ6UDgwwcw" keySequence="CTRL+SHIFT+X" command="_YRa3A3HmEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_YRZCMXHmEfCAZJ6UDgwwcw" keySequence="CTRL+SHIFT+Y" command="_YRa2mXHmEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_YRZCMnHmEfCAZJ6UDgwwcw" keySequence="CTRL+DEL" command="_YRa2VHHmEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_YRZCM3HmEfCAZJ6UDgwwcw" keySequence="ALT+ARROW_UP" command="_YRa4kXHmEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_YRZCNHHmEfCAZJ6UDgwwcw" keySequence="ALT+ARROW_DOWN" command="_YRa3i3HmEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_YRZCNXHmEfCAZJ6UDgwwcw" keySequence="SHIFT+END" command="_YRa2oXHmEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_YRZCNnHmEfCAZJ6UDgwwcw" keySequence="SHIFT+HOME" command="_YRa2iHHmEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_YRZCN3HmEfCAZJ6UDgwwcw" keySequence="END" command="_YRa4H3HmEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_YRZCOHHmEfCAZJ6UDgwwcw" keySequence="INSERT" command="_YRa3OnHmEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_YRZCOXHmEfCAZJ6UDgwwcw" keySequence="F2" command="_YRa2KHHmEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_YRZCOnHmEfCAZJ6UDgwwcw" keySequence="HOME" command="_YRa4P3HmEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_YRZCO3HmEfCAZJ6UDgwwcw" keySequence="ALT+CTRL+ARROW_UP" command="_YRa4a3HmEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_YRZCPHHmEfCAZJ6UDgwwcw" keySequence="ALT+CTRL+ARROW_DOWN" command="_YRa2tXHmEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_YRZCPXHmEfCAZJ6UDgwwcw" keySequence="CTRL+SHIFT+INSERT" command="_YRa153HmEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_YRZCPnHmEfCAZJ6UDgwwcw" keySequence="CTRL+SHIFT+ARROW_LEFT" command="_YRa2o3HmEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_YRZCP3HmEfCAZJ6UDgwwcw" keySequence="CTRL+SHIFT+ARROW_RIGHT" command="_YRa17nHmEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_YRZCQHHmEfCAZJ6UDgwwcw" keySequence="CTRL+F10" command="_YRa4A3HmEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_YRZCQXHmEfCAZJ6UDgwwcw" keySequence="CTRL+END" command="_YRa3jXHmEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_YRZCQnHmEfCAZJ6UDgwwcw" keySequence="CTRL+ARROW_UP" command="_YRa11nHmEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_YRZCQ3HmEfCAZJ6UDgwwcw" keySequence="CTRL+ARROW_DOWN" command="_YRa4oXHmEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_YRZCRHHmEfCAZJ6UDgwwcw" keySequence="CTRL+ARROW_LEFT" command="_YRa29XHmEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_YRZCRXHmEfCAZJ6UDgwwcw" keySequence="CTRL+ARROW_RIGHT" command="_YRa2CHHmEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_YRZCRnHmEfCAZJ6UDgwwcw" keySequence="CTRL+HOME" command="_YRa1iXHmEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_YRZCR3HmEfCAZJ6UDgwwcw" keySequence="CTRL+NUMPAD_MULTIPLY" command="_YRa3oHHmEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_YRZCSHHmEfCAZJ6UDgwwcw" keySequence="CTRL+NUMPAD_ADD" command="_YRa4WnHmEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_YRZCSXHmEfCAZJ6UDgwwcw" keySequence="CTRL+NUMPAD_SUBTRACT" command="_YRa4BXHmEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_YRZCSnHmEfCAZJ6UDgwwcw" keySequence="CTRL+NUMPAD_DIVIDE" command="_YRa12HHmEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_YRZCS3HmEfCAZJ6UDgwwcw" keySequence="CTRL+SHIFT+NUMPAD_MULTIPLY" command="_YRa3qXHmEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_YRZCTHHmEfCAZJ6UDgwwcw" keySequence="CTRL+SHIFT+NUMPAD_DIVIDE" command="_YRa3O3HmEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_YRZCTXHmEfCAZJ6UDgwwcw" keySequence="SHIFT+CR" command="_YRa4PHHmEfCAZJ6UDgwwcw"/>
  </bindingTables>
  <bindingTables xmi:id="_YRZCTnHmEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.cEditorScope" bindingContext="_YRZnWHHmEfCAZJ6UDgwwcw">
    <bindings xmi:id="_YRZCT3HmEfCAZJ6UDgwwcw" keySequence="ALT+CTRL+SHIFT+C" command="_YRa4pHHmEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_YRZCUHHmEfCAZJ6UDgwwcw" keySequence="CTRL+TAB" command="_YRa4nXHmEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_YRZCUXHmEfCAZJ6UDgwwcw" keySequence="CTRL+SHIFT+P" command="_YRa2fXHmEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_YRZCUnHmEfCAZJ6UDgwwcw" keySequence="CTRL+SHIFT+T" command="_YRa2OXHmEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_YRZCU3HmEfCAZJ6UDgwwcw" keySequence="CTRL+7" command="_YRa1jXHmEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_YRZCVHHmEfCAZJ6UDgwwcw" keySequence="CTRL+SHIFT+H" command="_YRa3TXHmEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_YRZCVXHmEfCAZJ6UDgwwcw" keySequence="CTRL+SHIFT+N" command="_YRa2B3HmEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_YRZCVnHmEfCAZJ6UDgwwcw" keySequence="CTRL+/" command="_YRa1jXHmEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_YRZCV3HmEfCAZJ6UDgwwcw" keySequence="CTRL+SHIFT+O" command="_YRa2cXHmEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_YRZCWHHmEfCAZJ6UDgwwcw" keySequence="CTRL+SHIFT+A" command="_YRa4CXHmEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_YRZCWXHmEfCAZJ6UDgwwcw" keySequence="ALT+CTRL+S" command="_YRa4f3HmEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_YRZCWnHmEfCAZJ6UDgwwcw" keySequence="CTRL+#" command="_YRa3v3HmEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_YRZCW3HmEfCAZJ6UDgwwcw" keySequence="CTRL+SHIFT+C" command="_YRa1jXHmEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_YRZCXHHmEfCAZJ6UDgwwcw" keySequence="CTRL+SHIFT+F" command="_YRa4nnHmEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_YRZCXXHmEfCAZJ6UDgwwcw" keySequence="CTRL+SHIFT+G" command="_YRa1XnHmEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_YRZmAHHmEfCAZJ6UDgwwcw" keySequence="ALT+CTRL+H" command="_YRa2IXHmEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_YRZmAXHmEfCAZJ6UDgwwcw" keySequence="ALT+CTRL+I" command="_YRa3JnHmEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_YRZmAnHmEfCAZJ6UDgwwcw" keySequence="CTRL+T" command="_YRa3kHHmEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_YRZmA3HmEfCAZJ6UDgwwcw" keySequence="CTRL+I" command="_YRa1-XHmEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_YRZmBHHmEfCAZJ6UDgwwcw" keySequence="CTRL+O" command="_YRa3DHHmEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_YRZmBXHmEfCAZJ6UDgwwcw" keySequence="CTRL+SHIFT+/" command="_YRa4V3HmEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_YRZmBnHmEfCAZJ6UDgwwcw" keySequence="ALT+SHIFT+R" command="_YRa34nHmEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_YRZmB3HmEfCAZJ6UDgwwcw" keySequence="ALT+SHIFT+S" command="_YRa1rXHmEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_YRZmCHHmEfCAZJ6UDgwwcw" keySequence="ALT+SHIFT+T" command="_YRa3xXHmEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_YRZmCXHmEfCAZJ6UDgwwcw" keySequence="CTRL+G" command="_YRa4dnHmEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_YRZmCnHmEfCAZJ6UDgwwcw" keySequence="ALT+SHIFT+L" command="_YRa2t3HmEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_YRZmC3HmEfCAZJ6UDgwwcw" keySequence="ALT+SHIFT+M" command="_YRa1gHHmEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_YRZmDHHmEfCAZJ6UDgwwcw" keySequence="CTRL+=" command="_YRa3v3HmEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_YRZmDXHmEfCAZJ6UDgwwcw" keySequence="ALT+SHIFT+O" command="_YRa2ZnHmEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_YRZmDnHmEfCAZJ6UDgwwcw" keySequence="ALT+SHIFT+Z" command="_YRa39nHmEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_YRZmD3HmEfCAZJ6UDgwwcw" keySequence="CTRL+SHIFT+\" command="_YRa3InHmEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_YRZmEHHmEfCAZJ6UDgwwcw" keySequence="F3" command="_YRa4qXHmEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_YRZmEXHmEfCAZJ6UDgwwcw" keySequence="F4" command="_YRa4UnHmEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_YRZmEnHmEfCAZJ6UDgwwcw" keySequence="CTRL+SHIFT+ARROW_UP" command="_YRa353HmEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_YRZmE3HmEfCAZJ6UDgwwcw" keySequence="CTRL+SHIFT+ARROW_DOWN" command="_YRa3g3HmEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_YRZmFHHmEfCAZJ6UDgwwcw" keySequence="ALT+SHIFT+ARROW_UP" command="_YRa2jnHmEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_YRZmFXHmEfCAZJ6UDgwwcw" keySequence="ALT+SHIFT+ARROW_DOWN" command="_YRa4n3HmEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_YRZmFnHmEfCAZJ6UDgwwcw" keySequence="ALT+SHIFT+ARROW_LEFT" command="_YRa32XHmEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_YRZmF3HmEfCAZJ6UDgwwcw" keySequence="ALT+SHIFT+ARROW_RIGHT" command="_YRa4LnHmEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_YRZmGHHmEfCAZJ6UDgwwcw" keySequence="ALT+C" command="_YRa3I3HmEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_YRZmGXHmEfCAZJ6UDgwwcw" keySequence="SHIFT+TAB" command="_YRa3TnHmEfCAZJ6UDgwwcw"/>
  </bindingTables>
  <bindingTables xmi:id="_YRZmGnHmEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.tasks.ui.views.tasks" bindingContext="_YRZnZHHmEfCAZJ6UDgwwcw">
    <bindings xmi:id="_YRZmG3HmEfCAZJ6UDgwwcw" keySequence="CTRL+CR" command="_YRa2R3HmEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_YRZmHHHmEfCAZJ6UDgwwcw" keySequence="ALT+SHIFT+C" command="_YRa26HHmEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_YRZmHXHmEfCAZJ6UDgwwcw" keySequence="ALT+SHIFT+R" command="_YRa2onHmEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_YRZmHnHmEfCAZJ6UDgwwcw" keySequence="ALT+SHIFT+U" command="_YRa3mHHmEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_YRZmH3HmEfCAZJ6UDgwwcw" keySequence="ALT+SHIFT+I" command="_YRa2nHHmEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_YRZmIHHmEfCAZJ6UDgwwcw" keySequence="ALT+ARROW_UP" command="_YRa3b3HmEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_YRZmIXHmEfCAZJ6UDgwwcw" keySequence="ALT+ARROW_DOWN" command="_YRa2THHmEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_YRZmInHmEfCAZJ6UDgwwcw" keySequence="SHIFT+INSERT" command="_YRa1y3HmEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_YRZmI3HmEfCAZJ6UDgwwcw" keySequence="INSERT" command="_YRa2l3HmEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_YRZmJHHmEfCAZJ6UDgwwcw" keySequence="F4" command="_YRa1p3HmEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_YRZmJXHmEfCAZJ6UDgwwcw" keySequence="ALT+SHIFT+ARROW_UP" command="_YRa3x3HmEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_YRZmJnHmEfCAZJ6UDgwwcw" keySequence="ALT+SHIFT+ARROW_DOWN" command="_YRa2nnHmEfCAZJ6UDgwwcw"/>
  </bindingTables>
  <bindingTables xmi:id="_YRZmJ3HmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.contexts.window" bindingContext="_YRZnP3HmEfCAZJ6UDgwwcw">
    <bindings xmi:id="_YRZmKHHmEfCAZJ6UDgwwcw" keySequence="ALT+CTRL+SHIFT+T" command="_YRa1qnHmEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_YRZmKXHmEfCAZJ6UDgwwcw" keySequence="ALT+CTRL+SHIFT+L" command="_YRa3LXHmEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_YRZmKnHmEfCAZJ6UDgwwcw" keySequence="ALT+SHIFT+Q O" command="_YRa3cHHmEfCAZJ6UDgwwcw">
      <parameters xmi:id="_YRZmK3HmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.ui.views.ContentOutline"/>
    </bindings>
    <bindings xmi:id="_YRZmLHHmEfCAZJ6UDgwwcw" keySequence="ALT+CTRL+B" command="_YRa3enHmEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_YRZmLXHmEfCAZJ6UDgwwcw" keySequence="CTRL+SHIFT+R" command="_YRa4pnHmEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_YRZmLnHmEfCAZJ6UDgwwcw" keySequence="ALT+SHIFT+Q Q" command="_YRa3cHHmEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_YRZmL3HmEfCAZJ6UDgwwcw" keySequence="CTRL+SHIFT+S" command="_YRa3XHHmEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_YRZmMHHmEfCAZJ6UDgwwcw" keySequence="CTRL+3" command="_YRa2J3HmEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_YRZmMXHmEfCAZJ6UDgwwcw" keySequence="ALT+SHIFT+Q S" command="_YRa3cHHmEfCAZJ6UDgwwcw">
      <parameters xmi:id="_YRZmMnHmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.search.ui.views.SearchView"/>
    </bindings>
    <bindings xmi:id="_YRZmM3HmEfCAZJ6UDgwwcw" keySequence="CTRL+6" command="_YRa4O3HmEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_YRZmNHHmEfCAZJ6UDgwwcw" keySequence="ALT+SHIFT+Q V" command="_YRa3cHHmEfCAZJ6UDgwwcw">
      <parameters xmi:id="_YRZmNXHmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.debug.ui.VariableView"/>
    </bindings>
    <bindings xmi:id="_YRZmNnHmEfCAZJ6UDgwwcw" keySequence="ALT+CTRL+G" command="_YRa3aHHmEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_YRZmN3HmEfCAZJ6UDgwwcw" keySequence="CTRL+SHIFT+W" command="_YRa2X3HmEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_YRZmOHHmEfCAZJ6UDgwwcw" keySequence="ALT+SHIFT+Q H" command="_YRa3cHHmEfCAZJ6UDgwwcw">
      <parameters xmi:id="_YRZmOXHmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.ui.cheatsheets.views.CheatSheetView"/>
    </bindings>
    <bindings xmi:id="_YRZmOnHmEfCAZJ6UDgwwcw" keySequence="CTRL+SHIFT+K" command="_YRa10nHmEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_YRZmO3HmEfCAZJ6UDgwwcw" keySequence="ALT+SHIFT+Q K" command="_YRa3cHHmEfCAZJ6UDgwwcw">
      <parameters xmi:id="_YRZmPHHmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.mylyn.tasks.ui.views.tasks"/>
    </bindings>
    <bindings xmi:id="_YRZmPXHmEfCAZJ6UDgwwcw" keySequence="CTRL+," command="_YRa1jnHmEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_YRZmPnHmEfCAZJ6UDgwwcw" keySequence="ALT+SHIFT+Q L" command="_YRa3cHHmEfCAZJ6UDgwwcw">
      <parameters xmi:id="_YRZmP3HmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.pde.runtime.LogView"/>
    </bindings>
    <bindings xmi:id="_YRZmQHHmEfCAZJ6UDgwwcw" keySequence="CTRL+." command="_YRa4bXHmEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_YRZmQXHmEfCAZJ6UDgwwcw" keySequence="ALT+CTRL+P" command="_YRa2iXHmEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_YRZmQnHmEfCAZJ6UDgwwcw" keySequence="CTRL+SHIFT+B" command="_YRa103HmEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_YRZmQ3HmEfCAZJ6UDgwwcw" keySequence="CTRL+#" command="_YRa1rHHmEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_YRZmRHHmEfCAZJ6UDgwwcw" keySequence="ALT+CTRL+T" command="_YRa293HmEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_YRZmRXHmEfCAZJ6UDgwwcw" keySequence="CTRL+SHIFT+E" command="_YRa143HmEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_YRZmRnHmEfCAZJ6UDgwwcw" keySequence="ALT+SHIFT+Q X" command="_YRa3cHHmEfCAZJ6UDgwwcw">
      <parameters xmi:id="_YRZmR3HmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.ui.views.ProblemView"/>
    </bindings>
    <bindings xmi:id="_YRZmSHHmEfCAZJ6UDgwwcw" keySequence="ALT+SHIFT+Q Y" command="_YRa3cHHmEfCAZJ6UDgwwcw">
      <parameters xmi:id="_YRZmSXHmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.team.sync.views.SynchronizeView"/>
    </bindings>
    <bindings xmi:id="_YRZmSnHmEfCAZJ6UDgwwcw" keySequence="ALT+SHIFT+Q Z" command="_YRa3cHHmEfCAZJ6UDgwwcw">
      <parameters xmi:id="_YRZmS3HmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.team.ui.GenericHistoryView"/>
    </bindings>
    <bindings xmi:id="_YRZmTHHmEfCAZJ6UDgwwcw" keySequence="CTRL+P" command="_YRa3_nHmEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_YRZmTXHmEfCAZJ6UDgwwcw" keySequence="CTRL+Q" command="_YRa4DnHmEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_YRZmTnHmEfCAZJ6UDgwwcw" keySequence="CTRL+S" command="_YRa2nXHmEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_YRZmT3HmEfCAZJ6UDgwwcw" keySequence="CTRL+W" command="_YRa20nHmEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_YRZmUHHmEfCAZJ6UDgwwcw" keySequence="CTRL+H" command="_YRa313HmEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_YRZmUXHmEfCAZJ6UDgwwcw" keySequence="CTRL+K" command="_YRa3fXHmEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_YRZmUnHmEfCAZJ6UDgwwcw" keySequence="CTRL+M" command="_YRa30nHmEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_YRZmU3HmEfCAZJ6UDgwwcw" keySequence="CTRL+N" command="_YRa4gXHmEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_YRZmVHHmEfCAZJ6UDgwwcw" keySequence="ALT+SHIFT+?" command="_YRa2OnHmEfCAZJ6UDgwwcw">
      <tags>locale:zh</tags>
    </bindings>
    <bindings xmi:id="_YRZmVXHmEfCAZJ6UDgwwcw" keySequence="CTRL+B" command="_YRa1k3HmEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_YRZmVnHmEfCAZJ6UDgwwcw" keySequence="ALT+SHIFT+Q B" command="_YRa3cHHmEfCAZJ6UDgwwcw">
      <parameters xmi:id="_YRZmV3HmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.debug.ui.BreakpointView"/>
    </bindings>
    <bindings xmi:id="_YRZmWHHmEfCAZJ6UDgwwcw" keySequence="ALT+SHIFT+Q C" command="_YRa3cHHmEfCAZJ6UDgwwcw">
      <parameters xmi:id="_YRZmWXHmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.ui.console.ConsoleView"/>
    </bindings>
    <bindings xmi:id="_YRZmWnHmEfCAZJ6UDgwwcw" keySequence="CTRL+E" command="_YRa2UXHmEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_YRZmW3HmEfCAZJ6UDgwwcw" keySequence="CTRL+F" command="_YRa1wXHmEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_YRZmXHHmEfCAZJ6UDgwwcw" keySequence="ALT+SHIFT+W" command="_YRa4anHmEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_YRZmXXHmEfCAZJ6UDgwwcw" keySequence="ALT+SHIFT+H" command="_YRa2SXHmEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_YRZmXnHmEfCAZJ6UDgwwcw" keySequence="ALT+SHIFT+N" command="_YRa2XHHmEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_YRZmX3HmEfCAZJ6UDgwwcw" keySequence="CTRL+_" command="_YRa2PXHmEfCAZJ6UDgwwcw">
      <parameters xmi:id="_YRZmYHHmEfCAZJ6UDgwwcw" elementId="Splitter.isHorizontal" name="Splitter.isHorizontal" value="true"/>
    </bindings>
    <bindings xmi:id="_YRZmYXHmEfCAZJ6UDgwwcw" keySequence="CTRL+{" command="_YRa2PXHmEfCAZJ6UDgwwcw">
      <parameters xmi:id="_YRZmYnHmEfCAZJ6UDgwwcw" elementId="Splitter.isHorizontal" name="Splitter.isHorizontal" value="false"/>
    </bindings>
    <bindings xmi:id="_YRZmY3HmEfCAZJ6UDgwwcw" keySequence="SHIFT+F9" command="_YRa2cHHmEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_YRZmZHHmEfCAZJ6UDgwwcw" keySequence="ALT+ARROW_LEFT" command="_YRa1r3HmEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_YRZmZXHmEfCAZJ6UDgwwcw" keySequence="ALT+ARROW_RIGHT" command="_YRa2c3HmEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_YRZmZnHmEfCAZJ6UDgwwcw" keySequence="SHIFT+F5" command="_YRa2vHHmEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_YRZmZ3HmEfCAZJ6UDgwwcw" keySequence="ALT+F7" command="_YRa3IHHmEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_YRZmaHHmEfCAZJ6UDgwwcw" keySequence="F9" command="_YRa2gHHmEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_YRZmaXHmEfCAZJ6UDgwwcw" keySequence="F11" command="_YRa4TnHmEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_YRZmanHmEfCAZJ6UDgwwcw" keySequence="F12" command="_YRa323HmEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_YRZma3HmEfCAZJ6UDgwwcw" keySequence="F2" command="_YRa1knHmEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_YRZmbHHmEfCAZJ6UDgwwcw" keySequence="F5" command="_YRa2enHmEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_YRZmbXHmEfCAZJ6UDgwwcw" keySequence="CTRL+SHIFT+F7" command="_YRa4UHHmEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_YRZmbnHmEfCAZJ6UDgwwcw" keySequence="CTRL+SHIFT+F8" command="_YRa2PHHmEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_YRZmb3HmEfCAZJ6UDgwwcw" keySequence="CTRL+SHIFT+F9" command="_YRa2rHHmEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_YRZmcHHmEfCAZJ6UDgwwcw" keySequence="ALT+CTRL+ARROW_LEFT" command="_YRa4DnHmEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_YRZmcXHmEfCAZJ6UDgwwcw" keySequence="ALT+CTRL+ARROW_RIGHT" command="_YRa123HmEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_YRZmcnHmEfCAZJ6UDgwwcw" keySequence="CTRL+SHIFT+F12" command="_YRa1c3HmEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_YRZmc3HmEfCAZJ6UDgwwcw" keySequence="CTRL+SHIFT+F4" command="_YRa2X3HmEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_YRZmdHHmEfCAZJ6UDgwwcw" keySequence="CTRL+SHIFT+F6" command="_YRa3YXHmEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_YRZmdXHmEfCAZJ6UDgwwcw" keySequence="CTRL+F7" command="_YRa2_HHmEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_YRZmdnHmEfCAZJ6UDgwwcw" keySequence="CTRL+F8" command="_YRa2H3HmEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_YRZmd3HmEfCAZJ6UDgwwcw" keySequence="CTRL+F9" command="_YRa15nHmEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_YRZmeHHmEfCAZJ6UDgwwcw" keySequence="CTRL+F11" command="_YRa4InHmEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_YRZmeXHmEfCAZJ6UDgwwcw" keySequence="CTRL+F12" command="_YRa11XHmEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_YRZmenHmEfCAZJ6UDgwwcw" keySequence="CTRL+F4" command="_YRa20nHmEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_YRZme3HmEfCAZJ6UDgwwcw" keySequence="CTRL+F6" command="_YRa1yXHmEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_YRZmfHHmEfCAZJ6UDgwwcw" keySequence="ALT+SHIFT+F7" command="_YRa3j3HmEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_YRZmfXHmEfCAZJ6UDgwwcw" keySequence="ALT+CTRL+SHIFT+ARROW_UP" command="_YRa3SHHmEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_YRZmfnHmEfCAZJ6UDgwwcw" keySequence="ALT+CTRL+SHIFT+ARROW_DOWN" command="_YRa4jXHmEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_YRZmf3HmEfCAZJ6UDgwwcw" keySequence="ALT+CTRL+SHIFT+ARROW_RIGHT" command="_YRa3NnHmEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_YRZmgHHmEfCAZJ6UDgwwcw" keySequence="CTRL+SHIFT+NUMPAD_MULTIPLY" command="_YRa3W3HmEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_YRZmgXHmEfCAZJ6UDgwwcw" keySequence="CTRL+SHIFT+NUMPAD_DIVIDE" command="_YRa2P3HmEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_YRZmgnHmEfCAZJ6UDgwwcw" keySequence="ALT+CTRL+SHIFT+F12" command="_YRa4XHHmEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_YRZmg3HmEfCAZJ6UDgwwcw" keySequence="DEL" command="_YRa1znHmEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_YRZmhHHmEfCAZJ6UDgwwcw" keySequence="ALT+?" command="_YRa2OnHmEfCAZJ6UDgwwcw">
      <tags>locale:zh</tags>
    </bindings>
    <bindings xmi:id="_YRZmhXHmEfCAZJ6UDgwwcw" keySequence="ALT+-" command="_YRa3D3HmEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_YRZmhnHmEfCAZJ6UDgwwcw" keySequence="ALT+CR" command="_YRa3wnHmEfCAZJ6UDgwwcw"/>
  </bindingTables>
  <bindingTables xmi:id="_YRZmh3HmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.genericeditor.genericEditorContext" bindingContext="_YRZnRnHmEfCAZJ6UDgwwcw">
    <bindings xmi:id="_YRZmiHHmEfCAZJ6UDgwwcw" keySequence="CTRL+SHIFT+P" command="_YRa2lHHmEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_YRZmiXHmEfCAZJ6UDgwwcw" keySequence="CTRL+SHIFT+G" command="_YRa4HnHmEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_YRZminHmEfCAZJ6UDgwwcw" keySequence="F3" command="_YRa4D3HmEfCAZJ6UDgwwcw"/>
  </bindingTables>
  <bindingTables xmi:id="_YRZmi3HmEfCAZJ6UDgwwcw" elementId="org.eclipse.wst.sse.ui.structuredTextEditorScope" bindingContext="_YRZnTHHmEfCAZJ6UDgwwcw">
    <bindings xmi:id="_YRZmjHHmEfCAZJ6UDgwwcw" keySequence="CTRL+SHIFT+P" command="_YRa3GnHmEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_YRZmjXHmEfCAZJ6UDgwwcw" keySequence="CTRL+SHIFT+A" command="_YRa4onHmEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_YRZmjnHmEfCAZJ6UDgwwcw" keySequence="CTRL+SHIFT+C" command="_YRa4N3HmEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_YRZmj3HmEfCAZJ6UDgwwcw" keySequence="CTRL+SHIFT+F" command="_YRa4fXHmEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_YRZmkHHmEfCAZJ6UDgwwcw" keySequence="CTRL+SHIFT+>" command="_YRa37XHmEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_YRZmkXHmEfCAZJ6UDgwwcw" keySequence="CTRL+I" command="_YRa4AHHmEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_YRZmknHmEfCAZJ6UDgwwcw" keySequence="CTRL+O" command="_YRa3KHHmEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_YRZmk3HmEfCAZJ6UDgwwcw" keySequence="CTRL+SHIFT+/" command="_YRa3DnHmEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_YRZmlHHmEfCAZJ6UDgwwcw" keySequence="CTRL+SHIFT+\" command="_YRa3aXHmEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_YRZmlXHmEfCAZJ6UDgwwcw" keySequence="F3" command="_YRa3F3HmEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_YRZmlnHmEfCAZJ6UDgwwcw" keySequence="CTRL+SHIFT+ARROW_UP" command="_YRa1-HHmEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_YRZml3HmEfCAZJ6UDgwwcw" keySequence="CTRL+SHIFT+ARROW_DOWN" command="_YRa2xHHmEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_YRZmmHHmEfCAZJ6UDgwwcw" keySequence="ALT+SHIFT+ARROW_UP" command="_YRa3h3HmEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_YRZmmXHmEfCAZJ6UDgwwcw" keySequence="ALT+SHIFT+ARROW_DOWN" command="_YRa3nHHmEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_YRZmmnHmEfCAZJ6UDgwwcw" keySequence="ALT+SHIFT+ARROW_LEFT" command="_YRa1oXHmEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_YRZmm3HmEfCAZJ6UDgwwcw" keySequence="ALT+SHIFT+ARROW_RIGHT" command="_YRa3T3HmEfCAZJ6UDgwwcw"/>
  </bindingTables>
  <bindingTables xmi:id="_YRZmnHHmEfCAZJ6UDgwwcw" elementId="org.eclipse.compare.compareEditorScope" bindingContext="_YRZnW3HmEfCAZJ6UDgwwcw">
    <bindings xmi:id="_YRZmnXHmEfCAZJ6UDgwwcw" keySequence="ALT+CTRL+C" command="_YRa1n3HmEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_YRZmnnHmEfCAZJ6UDgwwcw" keySequence="ALT+CTRL+P" command="_YRa3oXHmEfCAZJ6UDgwwcw"/>
  </bindingTables>
  <bindingTables xmi:id="_YRZmn3HmEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.cViewScope" bindingContext="_YRZnZXHmEfCAZJ6UDgwwcw">
    <bindings xmi:id="_YRZmoHHmEfCAZJ6UDgwwcw" keySequence="CTRL+SHIFT+T" command="_YRa2OXHmEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_YRZmoXHmEfCAZJ6UDgwwcw" keySequence="CTRL+SHIFT+H" command="_YRa3TXHmEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_YRZmonHmEfCAZJ6UDgwwcw" keySequence="CTRL+SHIFT+G" command="_YRa1XnHmEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_YRZmo3HmEfCAZJ6UDgwwcw" keySequence="ALT+CTRL+H" command="_YRa2IXHmEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_YRZmpHHmEfCAZJ6UDgwwcw" keySequence="ALT+CTRL+I" command="_YRa3JnHmEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_YRZmpXHmEfCAZJ6UDgwwcw" keySequence="ALT+SHIFT+R" command="_YRa34nHmEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_YRZmpnHmEfCAZJ6UDgwwcw" keySequence="CTRL+G" command="_YRa4dnHmEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_YRZmp3HmEfCAZJ6UDgwwcw" keySequence="F3" command="_YRa4qXHmEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_YRZmqHHmEfCAZJ6UDgwwcw" keySequence="F4" command="_YRa4UnHmEfCAZJ6UDgwwcw"/>
  </bindingTables>
  <bindingTables xmi:id="_YRZmqXHmEfCAZJ6UDgwwcw" elementId="org.eclipse.tm.terminal.EditContext" bindingContext="_YRZnQXHmEfCAZJ6UDgwwcw">
    <bindings xmi:id="_YRZmqnHmEfCAZJ6UDgwwcw" keySequence="CTRL+SHIFT+V" command="_YRa2knHmEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_YRZmq3HmEfCAZJ6UDgwwcw" keySequence="CTRL+SHIFT+C" command="_YRa3t3HmEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_YRZmrHHmEfCAZJ6UDgwwcw" keySequence="ALT+ARROW_UP" command="_YRa1YXHmEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_YRZmrXHmEfCAZJ6UDgwwcw" keySequence="ALT+ARROW_RIGHT" command="_YRa4THHmEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_YRZmrnHmEfCAZJ6UDgwwcw" keySequence="SHIFT+INSERT" command="_YRa2knHmEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_YRZmr3HmEfCAZJ6UDgwwcw" keySequence="CTRL+INSERT" command="_YRa3t3HmEfCAZJ6UDgwwcw"/>
  </bindingTables>
  <bindingTables xmi:id="_YRZmsHHmEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.tasks.ui.editors.task" bindingContext="_YRZnR3HmEfCAZJ6UDgwwcw">
    <bindings xmi:id="_YRZmsXHmEfCAZJ6UDgwwcw" keySequence="CTRL+SHIFT+M" command="_YRa1fXHmEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_YRZmsnHmEfCAZJ6UDgwwcw" keySequence="ALT+SHIFT+C" command="_YRa26HHmEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_YRZms3HmEfCAZJ6UDgwwcw" keySequence="CTRL+O" command="_YRa4Y3HmEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_YRZmtHHmEfCAZJ6UDgwwcw" keySequence="ALT+SHIFT+R" command="_YRa2onHmEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_YRZmtXHmEfCAZJ6UDgwwcw" keySequence="ALT+SHIFT+S" command="_YRa2ZXHmEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_YRZmtnHmEfCAZJ6UDgwwcw" keySequence="ALT+SHIFT+U" command="_YRa3mHHmEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_YRZmt3HmEfCAZJ6UDgwwcw" keySequence="ALT+SHIFT+I" command="_YRa2nHHmEfCAZJ6UDgwwcw"/>
  </bindingTables>
  <bindingTables xmi:id="_YRZmuHHmEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.make.ui.makefileEditorScope" bindingContext="_YRZnSHHmEfCAZJ6UDgwwcw">
    <bindings xmi:id="_YRZmuXHmEfCAZJ6UDgwwcw" keySequence="CTRL+/" command="_YRa3dXHmEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_YRZmunHmEfCAZJ6UDgwwcw" keySequence="F3" command="_YRa3VXHmEfCAZJ6UDgwwcw"/>
  </bindingTables>
  <bindingTables xmi:id="_YRZmu3HmEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.memoryview" bindingContext="_YRZnXXHmEfCAZJ6UDgwwcw">
    <bindings xmi:id="_YRZmvHHmEfCAZJ6UDgwwcw" keySequence="ALT+CTRL+M" command="_YRa2sHHmEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_YRZmvXHmEfCAZJ6UDgwwcw" keySequence="ALT+CTRL+N" command="_YRa4XXHmEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_YRZmvnHmEfCAZJ6UDgwwcw" keySequence="CTRL+T" command="_YRa2K3HmEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_YRZmv3HmEfCAZJ6UDgwwcw" keySequence="CTRL+W" command="_YRa3QHHmEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_YRZmwHHmEfCAZJ6UDgwwcw" keySequence="CTRL+N" command="_YRa3ZXHmEfCAZJ6UDgwwcw"/>
  </bindingTables>
  <bindingTables xmi:id="_YRZmwXHmEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.debugging" bindingContext="_YRZnXnHmEfCAZJ6UDgwwcw">
    <bindings xmi:id="_YRZmwnHmEfCAZJ6UDgwwcw" keySequence="CTRL+R" command="_YRa3AXHmEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_YRZmw3HmEfCAZJ6UDgwwcw" keySequence="F7" command="_YRa4cnHmEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_YRZmxHHmEfCAZJ6UDgwwcw" keySequence="F8" command="_YRa3NXHmEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_YRZmxXHmEfCAZJ6UDgwwcw" keySequence="F5" command="_YRa1nnHmEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_YRZmxnHmEfCAZJ6UDgwwcw" keySequence="F6" command="_YRa2pHHmEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_YRZmx3HmEfCAZJ6UDgwwcw" keySequence="CTRL+F2" command="_YRa33nHmEfCAZJ6UDgwwcw"/>
  </bindingTables>
  <bindingTables xmi:id="_YRZmyHHmEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.memory.abstractasynctablerendering" bindingContext="_YRZnX3HmEfCAZJ6UDgwwcw">
    <bindings xmi:id="_YRZmyXHmEfCAZJ6UDgwwcw" keySequence="CTRL+SHIFT+," command="_YRa4EXHmEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_YRZmynHmEfCAZJ6UDgwwcw" keySequence="CTRL+SHIFT+." command="_YRa3z3HmEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_YRZmy3HmEfCAZJ6UDgwwcw" keySequence="CTRL+G" command="_YRa30HHmEfCAZJ6UDgwwcw"/>
  </bindingTables>
  <bindingTables xmi:id="_YRZmzHHmEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.DiffViewer" bindingContext="_YRZnRHHmEfCAZJ6UDgwwcw">
    <bindings xmi:id="_YRZmzXHmEfCAZJ6UDgwwcw" keySequence="CTRL+O" command="_YRa2wHHmEfCAZJ6UDgwwcw"/>
  </bindingTables>
  <bindingTables xmi:id="_YRZmznHmEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.wikitext.ui.editor.markupSourceContext" bindingContext="_YRZnSnHmEfCAZJ6UDgwwcw">
    <bindings xmi:id="_YRZmz3HmEfCAZJ6UDgwwcw" keySequence="CTRL+O" command="_YRa1hHHmEfCAZJ6UDgwwcw"/>
  </bindingTables>
  <bindingTables xmi:id="_YRZm0HHmEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.RepositoriesView" bindingContext="_YRZnZnHmEfCAZJ6UDgwwcw">
    <bindings xmi:id="_YRZm0XHmEfCAZJ6UDgwwcw" keySequence="CTRL+C" command="_YRa2L3HmEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_YRZm0nHmEfCAZJ6UDgwwcw" keySequence="CTRL+ARROW_LEFT" command="_YRa1tXHmEfCAZJ6UDgwwcw"/>
  </bindingTables>
  <bindingTables xmi:id="_YRZm03HmEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.ReflogView" bindingContext="_YRZnYnHmEfCAZJ6UDgwwcw">
    <bindings xmi:id="_YRZm1HHmEfCAZJ6UDgwwcw" keySequence="CTRL+C" command="_YRa1ynHmEfCAZJ6UDgwwcw"/>
  </bindingTables>
  <bindingTables xmi:id="_YRZm1XHmEfCAZJ6UDgwwcw" elementId="org.eclipse.tracecompass.tmf.ui.view.timegraph.context" bindingContext="_YRZnbnHmEfCAZJ6UDgwwcw">
    <bindings xmi:id="_YRZm1nHmEfCAZJ6UDgwwcw" keySequence="CTRL+D" command="_YRa3HHHmEfCAZJ6UDgwwcw"/>
  </bindingTables>
  <bindingTables xmi:id="_YRZm13HmEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.dsf.debug.ui.disassembly.context" bindingContext="_YRZnYHHmEfCAZJ6UDgwwcw">
    <bindings xmi:id="_YRZm2HHmEfCAZJ6UDgwwcw" keySequence="CTRL+G" command="_YRa4fnHmEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_YRZm2XHmEfCAZJ6UDgwwcw" keySequence="HOME" command="_YRa2KnHmEfCAZJ6UDgwwcw"/>
  </bindingTables>
  <bindingTables xmi:id="_YRZm2nHmEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.console" bindingContext="_YRZnWXHmEfCAZJ6UDgwwcw">
    <bindings xmi:id="_YRZm23HmEfCAZJ6UDgwwcw" keySequence="CTRL+Z" command="_YRa4ZnHmEfCAZJ6UDgwwcw">
      <tags>platform:win32</tags>
    </bindings>
  </bindingTables>
  <bindingTables xmi:id="_YRZm3HHmEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.debug.ui.debugging" bindingContext="_YRZnYXHmEfCAZJ6UDgwwcw">
    <bindings xmi:id="_YRZm3XHmEfCAZJ6UDgwwcw" keySequence="SHIFT+F7" command="_YRa3VHHmEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_YRZm3nHmEfCAZJ6UDgwwcw" keySequence="SHIFT+F8" command="_YRa3zXHmEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_YRZm33HmEfCAZJ6UDgwwcw" keySequence="SHIFT+F5" command="_YRa2pXHmEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_YRZm4HHmEfCAZJ6UDgwwcw" keySequence="SHIFT+F6" command="_YRa1lnHmEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_YRZm4XHmEfCAZJ6UDgwwcw" keySequence="CTRL+F5" command="_YRa4RnHmEfCAZJ6UDgwwcw"/>
  </bindingTables>
  <bindingTables xmi:id="_YRZm4nHmEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.macroExpansionHoverScope" bindingContext="_YRZnaXHmEfCAZJ6UDgwwcw">
    <bindings xmi:id="_YRZm43HmEfCAZJ6UDgwwcw" keySequence="ALT+ARROW_LEFT" command="_YRa3kXHmEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_YRZm5HHmEfCAZJ6UDgwwcw" keySequence="ALT+ARROW_RIGHT" command="_YRa3pXHmEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_YRZm5XHmEfCAZJ6UDgwwcw" keySequence="F3" command="_YRa4qXHmEfCAZJ6UDgwwcw"/>
  </bindingTables>
  <bindingTables xmi:id="_YRZm5nHmEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.internal.wikitext.ui.editor.basicMarkupSourceContext" bindingContext="_YRZnSXHmEfCAZJ6UDgwwcw">
    <bindings xmi:id="_YRZm53HmEfCAZJ6UDgwwcw" keySequence="F1" command="_YRa1a3HmEfCAZJ6UDgwwcw"/>
  </bindingTables>
  <bindingTables xmi:id="_YRZm6HHmEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.RepositoriesView.SingleRepository" bindingContext="_YRZnZ3HmEfCAZJ6UDgwwcw">
    <bindings xmi:id="_YRZm6XHmEfCAZJ6UDgwwcw" keySequence="F2" command="_YRa10HHmEfCAZJ6UDgwwcw"/>
  </bindingTables>
  <bindingTables xmi:id="_YRZm6nHmEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.asmEditorScope" bindingContext="_YRZnRXHmEfCAZJ6UDgwwcw">
    <bindings xmi:id="_YRZm63HmEfCAZJ6UDgwwcw" keySequence="F3" command="_YRa4qXHmEfCAZJ6UDgwwcw"/>
  </bindingTables>
  <bindingTables xmi:id="_YRZm7HHmEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.tmf.ui.view.uml2sd.context" bindingContext="_YRZna3HmEfCAZJ6UDgwwcw">
    <bindings xmi:id="_YRZm7XHmEfCAZJ6UDgwwcw" keySequence="ALT+SHIFT+ARROW_UP" command="_YRa2dHHmEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_YRZm7nHmEfCAZJ6UDgwwcw" keySequence="ALT+SHIFT+ARROW_DOWN" command="_YRa1e3HmEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_YRZm73HmEfCAZJ6UDgwwcw" keySequence="ALT+SHIFT+ARROW_LEFT" command="_YRa26nHmEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_YRZm8HHmEfCAZJ6UDgwwcw" keySequence="ALT+SHIFT+ARROW_RIGHT" command="_YRa3Q3HmEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_YRZm8XHmEfCAZJ6UDgwwcw" keySequence="ALT+SHIFT+HOME" command="_YRa4aXHmEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_YRZm8nHmEfCAZJ6UDgwwcw" keySequence="ALT+SHIFT+END" command="_YRa2UHHmEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_YRZm83HmEfCAZJ6UDgwwcw" keySequence="ALT+R" command="_YRa31nHmEfCAZJ6UDgwwcw"/>
  </bindingTables>
  <bindingTables xmi:id="_YRZm9HHmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.console.ConsoleView" bindingContext="_YRZnXHHmEfCAZJ6UDgwwcw">
    <bindings xmi:id="_YRZm9XHmEfCAZJ6UDgwwcw" keySequence="CTRL+INSERT" command="_YRa3WXHmEfCAZJ6UDgwwcw"/>
  </bindingTables>
  <bindingTables xmi:id="_YRZm9nHmEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.changelog.core.changelogEditorScope" bindingContext="_YRZnS3HmEfCAZJ6UDgwwcw">
    <bindings xmi:id="_YRZm93HmEfCAZJ6UDgwwcw" keySequence="ESC CTRL+F" command="_YRa3MHHmEfCAZJ6UDgwwcw"/>
  </bindingTables>
  <bindingTables xmi:id="_YRZm-HHmEfCAZJ6UDgwwcw" elementId="org.eclipse.tracecompass.tmf.ui.view.context" bindingContext="_YRZnbXHmEfCAZJ6UDgwwcw">
    <bindings xmi:id="_YRZm-XHmEfCAZJ6UDgwwcw" keySequence="Z" command="_YRa2uHHmEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_YRZm-nHmEfCAZJ6UDgwwcw" keySequence="+" command="_YRa1X3HmEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_YRZm-3HmEfCAZJ6UDgwwcw" keySequence="-" command="_YRa1o3HmEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_YRZm_HHmEfCAZJ6UDgwwcw" keySequence="/" command="_YRa1_3HmEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_YRZm_XHmEfCAZJ6UDgwwcw" keySequence="S" command="_YRa2YnHmEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_YRZm_nHmEfCAZJ6UDgwwcw" keySequence="W" command="_YRa2xXHmEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_YRZm_3HmEfCAZJ6UDgwwcw" keySequence="A" command="_YRa2LXHmEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_YRZnAHHmEfCAZJ6UDgwwcw" keySequence="D" command="_YRa2ynHmEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_YRZnAXHmEfCAZJ6UDgwwcw" keySequence="=" command="_YRa1X3HmEfCAZJ6UDgwwcw"/>
  </bindingTables>
  <bindingTables xmi:id="_YRZnAnHmEfCAZJ6UDgwwcw" elementId="org.eclipse.tm.terminal.TerminalContext" bindingContext="_YRZnY3HmEfCAZJ6UDgwwcw">
    <bindings xmi:id="_YRZnA3HmEfCAZJ6UDgwwcw" keySequence="ALT+Y" command="_YRa3CnHmEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_YRZnBHHmEfCAZJ6UDgwwcw" keySequence="ALT+A" command="_YRa3CnHmEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_YRZnBXHmEfCAZJ6UDgwwcw" keySequence="ALT+B" command="_YRa3CnHmEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_YRZnBnHmEfCAZJ6UDgwwcw" keySequence="ALT+C" command="_YRa3CnHmEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_YRZnB3HmEfCAZJ6UDgwwcw" keySequence="ALT+D" command="_YRa3CnHmEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_YRZnCHHmEfCAZJ6UDgwwcw" keySequence="ALT+E" command="_YRa3CnHmEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_YRZnCXHmEfCAZJ6UDgwwcw" keySequence="ALT+F" command="_YRa3CnHmEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_YRZnCnHmEfCAZJ6UDgwwcw" keySequence="ALT+G" command="_YRa3CnHmEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_YRZnC3HmEfCAZJ6UDgwwcw" keySequence="ALT+P" command="_YRa3CnHmEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_YRZnDHHmEfCAZJ6UDgwwcw" keySequence="ALT+R" command="_YRa3CnHmEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_YRZnDXHmEfCAZJ6UDgwwcw" keySequence="ALT+S" command="_YRa3CnHmEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_YRZnDnHmEfCAZJ6UDgwwcw" keySequence="ALT+T" command="_YRa3CnHmEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_YRZnD3HmEfCAZJ6UDgwwcw" keySequence="ALT+V" command="_YRa3CnHmEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_YRZnEHHmEfCAZJ6UDgwwcw" keySequence="ALT+W" command="_YRa3CnHmEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_YRZnEXHmEfCAZJ6UDgwwcw" keySequence="ALT+H" command="_YRa3CnHmEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_YRZnEnHmEfCAZJ6UDgwwcw" keySequence="ALT+L" command="_YRa3CnHmEfCAZJ6UDgwwcw"/>
    <bindings xmi:id="_YRZnE3HmEfCAZJ6UDgwwcw" keySequence="ALT+N" command="_YRa3CnHmEfCAZJ6UDgwwcw"/>
  </bindingTables>
  <bindingTables xmi:id="_YRZnFHHmEfCAZJ6UDgwwcw" bindingContext="_YRZnb3HmEfCAZJ6UDgwwcw"/>
  <bindingTables xmi:id="_YRZnFXHmEfCAZJ6UDgwwcw" bindingContext="_YRZncHHmEfCAZJ6UDgwwcw"/>
  <bindingTables xmi:id="_YRZnFnHmEfCAZJ6UDgwwcw" bindingContext="_YRZncXHmEfCAZJ6UDgwwcw"/>
  <bindingTables xmi:id="_YRZnF3HmEfCAZJ6UDgwwcw" bindingContext="_YRZncnHmEfCAZJ6UDgwwcw"/>
  <bindingTables xmi:id="_YRZnGHHmEfCAZJ6UDgwwcw" bindingContext="_YRZnc3HmEfCAZJ6UDgwwcw"/>
  <bindingTables xmi:id="_YRZnGXHmEfCAZJ6UDgwwcw" bindingContext="_YRZndHHmEfCAZJ6UDgwwcw"/>
  <bindingTables xmi:id="_YRZnGnHmEfCAZJ6UDgwwcw" bindingContext="_YRZndXHmEfCAZJ6UDgwwcw"/>
  <bindingTables xmi:id="_YRZnG3HmEfCAZJ6UDgwwcw" bindingContext="_YRZndnHmEfCAZJ6UDgwwcw"/>
  <bindingTables xmi:id="_YRZnHHHmEfCAZJ6UDgwwcw" bindingContext="_YRZnd3HmEfCAZJ6UDgwwcw"/>
  <bindingTables xmi:id="_YRZnHXHmEfCAZJ6UDgwwcw" bindingContext="_YRZneHHmEfCAZJ6UDgwwcw"/>
  <bindingTables xmi:id="_YRZnHnHmEfCAZJ6UDgwwcw" bindingContext="_YRZneXHmEfCAZJ6UDgwwcw"/>
  <bindingTables xmi:id="_YRZnH3HmEfCAZJ6UDgwwcw" bindingContext="_YRZnenHmEfCAZJ6UDgwwcw"/>
  <bindingTables xmi:id="_YRZnIHHmEfCAZJ6UDgwwcw" bindingContext="_YRZne3HmEfCAZJ6UDgwwcw"/>
  <bindingTables xmi:id="_YRZnIXHmEfCAZJ6UDgwwcw" bindingContext="_YRZnfHHmEfCAZJ6UDgwwcw"/>
  <bindingTables xmi:id="_YRZnInHmEfCAZJ6UDgwwcw" bindingContext="_YRZnfXHmEfCAZJ6UDgwwcw"/>
  <bindingTables xmi:id="_YRZnI3HmEfCAZJ6UDgwwcw" bindingContext="_YRZnfnHmEfCAZJ6UDgwwcw"/>
  <bindingTables xmi:id="_YRZnJHHmEfCAZJ6UDgwwcw" bindingContext="_YRZnf3HmEfCAZJ6UDgwwcw"/>
  <bindingTables xmi:id="_YRZnJXHmEfCAZJ6UDgwwcw" bindingContext="_YRZngHHmEfCAZJ6UDgwwcw"/>
  <bindingTables xmi:id="_YRZnJnHmEfCAZJ6UDgwwcw" bindingContext="_YRZngXHmEfCAZJ6UDgwwcw"/>
  <bindingTables xmi:id="_YRZnJ3HmEfCAZJ6UDgwwcw" bindingContext="_YRZngnHmEfCAZJ6UDgwwcw"/>
  <bindingTables xmi:id="_YRZnKHHmEfCAZJ6UDgwwcw" bindingContext="_YRZng3HmEfCAZJ6UDgwwcw"/>
  <bindingTables xmi:id="_YRZnKXHmEfCAZJ6UDgwwcw" bindingContext="_YRZnhHHmEfCAZJ6UDgwwcw"/>
  <bindingTables xmi:id="_YRZnKnHmEfCAZJ6UDgwwcw" bindingContext="_YRZnhXHmEfCAZJ6UDgwwcw"/>
  <bindingTables xmi:id="_YRZnK3HmEfCAZJ6UDgwwcw" bindingContext="_YRZnhnHmEfCAZJ6UDgwwcw"/>
  <bindingTables xmi:id="_YRZnLHHmEfCAZJ6UDgwwcw" bindingContext="_YRZnh3HmEfCAZJ6UDgwwcw"/>
  <bindingTables xmi:id="_YRZnLXHmEfCAZJ6UDgwwcw" bindingContext="_YRZniHHmEfCAZJ6UDgwwcw"/>
  <bindingTables xmi:id="_YRZnLnHmEfCAZJ6UDgwwcw" bindingContext="_YRZniXHmEfCAZJ6UDgwwcw"/>
  <bindingTables xmi:id="_YRZnL3HmEfCAZJ6UDgwwcw" bindingContext="_YRZninHmEfCAZJ6UDgwwcw"/>
  <bindingTables xmi:id="_YRZnMHHmEfCAZJ6UDgwwcw" bindingContext="_YRZni3HmEfCAZJ6UDgwwcw"/>
  <bindingTables xmi:id="_YRZnMXHmEfCAZJ6UDgwwcw" bindingContext="_YRZnjHHmEfCAZJ6UDgwwcw"/>
  <bindingTables xmi:id="_YRZnMnHmEfCAZJ6UDgwwcw" bindingContext="_YRZnjXHmEfCAZJ6UDgwwcw"/>
  <bindingTables xmi:id="_YRZnM3HmEfCAZJ6UDgwwcw" bindingContext="_YRZnjnHmEfCAZJ6UDgwwcw"/>
  <bindingTables xmi:id="_YRZnNHHmEfCAZJ6UDgwwcw" bindingContext="_YRZnj3HmEfCAZJ6UDgwwcw"/>
  <bindingTables xmi:id="_YRZnNXHmEfCAZJ6UDgwwcw" bindingContext="_YRZnkHHmEfCAZJ6UDgwwcw"/>
  <bindingTables xmi:id="_YRZnNnHmEfCAZJ6UDgwwcw" bindingContext="_YRZnkXHmEfCAZJ6UDgwwcw"/>
  <bindingTables xmi:id="_YRZnN3HmEfCAZJ6UDgwwcw" bindingContext="_YRZnknHmEfCAZJ6UDgwwcw"/>
  <bindingTables xmi:id="_YRZnOHHmEfCAZJ6UDgwwcw" bindingContext="_YRZnk3HmEfCAZJ6UDgwwcw"/>
  <bindingTables xmi:id="_YRZnOXHmEfCAZJ6UDgwwcw" bindingContext="_YRZnlHHmEfCAZJ6UDgwwcw"/>
  <bindingTables xmi:id="_YRZnOnHmEfCAZJ6UDgwwcw" bindingContext="_YRZnlXHmEfCAZJ6UDgwwcw"/>
  <bindingTables xmi:id="_YRZnO3HmEfCAZJ6UDgwwcw" bindingContext="_YRZnlnHmEfCAZJ6UDgwwcw"/>
  <bindingTables xmi:id="_YRZnPHHmEfCAZJ6UDgwwcw" bindingContext="_YRZnl3HmEfCAZJ6UDgwwcw"/>
  <bindingTables xmi:id="_YRZnPXHmEfCAZJ6UDgwwcw" bindingContext="_YRZnmHHmEfCAZJ6UDgwwcw"/>
  <rootContext xmi:id="_YRZnPnHmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.contexts.dialogAndWindow" contributorURI="platform:/plugin/org.eclipse.platform" name="In Dialogs and Windows" description="Either a dialog or a window is open">
    <children xmi:id="_YRZnP3HmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.contexts.window" contributorURI="platform:/plugin/org.eclipse.platform" name="In Windows" description="A window is open">
      <children xmi:id="_YRZnQHHmEfCAZJ6UDgwwcw" elementId="org.eclipse.e4.ui.contexts.views" contributorURI="platform:/plugin/org.eclipse.platform" name="%bindingcontext.name.bindingView"/>
      <children xmi:id="_YRZnQXHmEfCAZJ6UDgwwcw" elementId="org.eclipse.tm.terminal.EditContext" name="Terminal Control in Focus" description="Show modified keyboard shortcuts in context menu"/>
      <children xmi:id="_YRZnQnHmEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.BreakpointView" name="In Breakpoints View" description="The breakpoints view context"/>
      <children xmi:id="_YRZnQ3HmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.textEditorScope" name="Editing Text" description="Editing Text Context">
        <children xmi:id="_YRZnRHHmEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.DiffViewer" name="In Diff Viewer"/>
        <children xmi:id="_YRZnRXHmEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.asmEditorScope" name="Assembly Editor" description="Editor for Assembly Source Files"/>
        <children xmi:id="_YRZnRnHmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.genericeditor.genericEditorContext" name="in Generic Code Editor" description="When editing in the Generic Code Editor"/>
        <children xmi:id="_YRZnR3HmEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.tasks.ui.editors.task" name="In Tasks Editor"/>
        <children xmi:id="_YRZnSHHmEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.make.ui.makefileEditorScope" name="Makefile Editor" description="Editor for makefiles"/>
        <children xmi:id="_YRZnSXHmEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.internal.wikitext.ui.editor.basicMarkupSourceContext" name="WikiText Markup Source Context" description="WikiText markup editing context">
          <children xmi:id="_YRZnSnHmEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.wikitext.ui.editor.markupSourceContext" name="WikiText Markup Source Context" description="WikiText markup editing context"/>
        </children>
        <children xmi:id="_YRZnS3HmEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.changelog.core.changelogEditorScope" name="ChangeLog Editor"/>
        <children xmi:id="_YRZnTHHmEfCAZJ6UDgwwcw" elementId="org.eclipse.wst.sse.ui.structuredTextEditorScope" name="Editing in Structured Text Editors" description="Editing in Structured Text Editors">
          <children xmi:id="_YRZnTXHmEfCAZJ6UDgwwcw" elementId="org.eclipse.wst.xml.navigation" name="XML Source Navigation" description="XML Source Navigation"/>
          <children xmi:id="_YRZnTnHmEfCAZJ6UDgwwcw" elementId="org.eclipse.wst.xml.cleanup" name="XML Source Cleanup" description="XML Source Cleanup"/>
          <children xmi:id="_YRZnT3HmEfCAZJ6UDgwwcw" elementId="org.eclipse.wst.sse.comments" name="Source Comments in Structured Text Editors" description="Source Comments in Structured Text Editors"/>
          <children xmi:id="_YRZnUHHmEfCAZJ6UDgwwcw" elementId="org.eclipse.core.runtime.xml" name="Editing XML Source" description="Editing XML Source"/>
          <children xmi:id="_YRZnUXHmEfCAZJ6UDgwwcw" elementId="org.eclipse.wst.xml.occurrences" name="XML Source Occurrences" description="XML Source Occurrences"/>
          <children xmi:id="_YRZnUnHmEfCAZJ6UDgwwcw" elementId="org.eclipse.wst.xml.grammar" name="XML Source Grammar" description="XML Source Grammar"/>
          <children xmi:id="_YRZnU3HmEfCAZJ6UDgwwcw" elementId="org.eclipse.wst.xml.comments" name="XML Source Comments" description="XML Source Comments"/>
          <children xmi:id="_YRZnVHHmEfCAZJ6UDgwwcw" elementId="org.eclipse.wst.xml.expand" name="XML Source Expand/Collapse" description="XML Source Expand/Collapse"/>
          <children xmi:id="_YRZnVXHmEfCAZJ6UDgwwcw" elementId="org.eclipse.wst.sse.hideFormat" name="Editing in Structured Text Editors" description="Editing in Structured Text Editors"/>
          <children xmi:id="_YRZnVnHmEfCAZJ6UDgwwcw" elementId="org.eclipse.wst.xml.selection" name="XML Source Selection" description="XML Source Selection"/>
          <children xmi:id="_YRZnV3HmEfCAZJ6UDgwwcw" elementId="org.eclipse.wst.xml.dependencies" name="XML Source Dependencies" description="XML Source Dependencies"/>
        </children>
        <children xmi:id="_YRZnWHHmEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.cEditorScope" name="C/C++ Editor" description="Editor for C/C++ Source Files"/>
      </children>
      <children xmi:id="_YRZnWXHmEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.console" name="In I/O Console" description="In I/O console"/>
      <children xmi:id="_YRZnWnHmEfCAZJ6UDgwwcw" elementId="org.eclipse.tm.terminal.view.ui.TerminalsView" name="In Terminal View" description="Show modified keyboard shortcuts in context menu"/>
      <children xmi:id="_YRZnW3HmEfCAZJ6UDgwwcw" elementId="org.eclipse.compare.compareEditorScope" name="Comparing in an Editor" description="Comparing in an Editor"/>
      <children xmi:id="_YRZnXHHmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.console.ConsoleView" name="In Console View" description="In Console View"/>
      <children xmi:id="_YRZnXXHmEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.memoryview" name="In Memory View" description="In memory view"/>
      <children xmi:id="_YRZnXnHmEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.debugging" name="Debugging" description="Debugging programs">
        <children xmi:id="_YRZnX3HmEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.memory.abstractasynctablerendering" name="In Table Memory Rendering" description="In Table Memory Rendering"/>
        <children xmi:id="_YRZnYHHmEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.dsf.debug.ui.disassembly.context" name="In Disassembly" description="When debugging in assembly mode"/>
        <children xmi:id="_YRZnYXHmEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.debug.ui.debugging" name="Debugging C/C++" description="Debugging C/C++ Programs"/>
      </children>
      <children xmi:id="_YRZnYnHmEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.ReflogView" name="In Git Reflog View"/>
      <children xmi:id="_YRZnY3HmEfCAZJ6UDgwwcw" elementId="org.eclipse.tm.terminal.TerminalContext" name="Terminal Typing Connected" description="Override ALT+x menu access keys while typing into the Terminal"/>
      <children xmi:id="_YRZnZHHmEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.tasks.ui.views.tasks" name="In Tasks View"/>
      <children xmi:id="_YRZnZXHmEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.cViewScope" name="In C/C++ Views" description="In C/C++ Views"/>
      <children xmi:id="_YRZnZnHmEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.RepositoriesView" name="In Git Repositories View">
        <children xmi:id="_YRZnZ3HmEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.RepositoriesView.SingleRepository" name="In Git Repositories View"/>
      </children>
    </children>
    <children xmi:id="_YRZnaHHmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.contexts.dialog" contributorURI="platform:/plugin/org.eclipse.platform" name="In Dialogs" description="A dialog is open"/>
    <children xmi:id="_YRZnaXHmEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.macroExpansionHoverScope" name="In Macro Expansion Hover" description="In Macro Expansion Hover"/>
  </rootContext>
  <rootContext xmi:id="_YRZnanHmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.contexts.actionSet" name="Action Set" description="Parent context for action sets"/>
  <rootContext xmi:id="_YRZna3HmEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.tmf.ui.view.uml2sd.context" name="UML2 Sequence Diagram Viewer"/>
  <rootContext xmi:id="_YRZnbHHmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.contexts.workbenchMenu" name="Workbench Menu" description="When no Workbench windows are active"/>
  <rootContext xmi:id="_YRZnbXHmEfCAZJ6UDgwwcw" elementId="org.eclipse.tracecompass.tmf.ui.view.context" name="In Time-Based View"/>
  <rootContext xmi:id="_YRZnbnHmEfCAZJ6UDgwwcw" elementId="org.eclipse.tracecompass.tmf.ui.view.timegraph.context" name="In Time Graph"/>
  <rootContext xmi:id="_YRZnb3HmEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.debug.ui.debugActionSet" name="Auto::org.eclipse.cdt.debug.ui.debugActionSet"/>
  <rootContext xmi:id="_YRZncHHmEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.debug.ui.reverseDebuggingActionSet" name="Auto::org.eclipse.cdt.debug.ui.reverseDebuggingActionSet"/>
  <rootContext xmi:id="_YRZncXHmEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.debug.ui.tracepointActionSet" name="Auto::org.eclipse.cdt.debug.ui.tracepointActionSet"/>
  <rootContext xmi:id="_YRZncnHmEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.debug.ui.debugViewLayoutActionSet" name="Auto::org.eclipse.cdt.debug.ui.debugViewLayoutActionSet"/>
  <rootContext xmi:id="_YRZnc3HmEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.dsf.debug.ui.updateModes" name="Auto::org.eclipse.cdt.dsf.debug.ui.updateModes"/>
  <rootContext xmi:id="_YRZndHHmEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.make.ui.updateActionSet" name="Auto::org.eclipse.cdt.make.ui.updateActionSet"/>
  <rootContext xmi:id="_YRZndXHmEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.make.ui.makeTargetActionSet" name="Auto::org.eclipse.cdt.make.ui.makeTargetActionSet"/>
  <rootContext xmi:id="_YRZndnHmEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.cdt.ui.actionSet" name="Auto::org.eclipse.mylyn.cdt.ui.actionSet"/>
  <rootContext xmi:id="_YRZnd3HmEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.CodingActionSet" name="Auto::org.eclipse.cdt.ui.CodingActionSet"/>
  <rootContext xmi:id="_YRZneHHmEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.SearchActionSet" name="Auto::org.eclipse.cdt.ui.SearchActionSet"/>
  <rootContext xmi:id="_YRZneXHmEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.NavigationActionSet" name="Auto::org.eclipse.cdt.ui.NavigationActionSet"/>
  <rootContext xmi:id="_YRZnenHmEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.OpenActionSet" name="Auto::org.eclipse.cdt.ui.OpenActionSet"/>
  <rootContext xmi:id="_YRZne3HmEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.buildConfigActionSet" name="Auto::org.eclipse.cdt.ui.buildConfigActionSet"/>
  <rootContext xmi:id="_YRZnfHHmEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.CElementCreationActionSet" name="Auto::org.eclipse.cdt.ui.CElementCreationActionSet"/>
  <rootContext xmi:id="_YRZnfXHmEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.text.c.actionSet.presentation" name="Auto::org.eclipse.cdt.ui.text.c.actionSet.presentation"/>
  <rootContext xmi:id="_YRZnfnHmEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.breakpointActionSet" name="Auto::org.eclipse.debug.ui.breakpointActionSet"/>
  <rootContext xmi:id="_YRZnf3HmEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.debugActionSet" name="Auto::org.eclipse.debug.ui.debugActionSet"/>
  <rootContext xmi:id="_YRZngHHmEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.launchActionSet" name="Auto::org.eclipse.debug.ui.launchActionSet"/>
  <rootContext xmi:id="_YRZngXHmEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.profileActionSet" name="Auto::org.eclipse.debug.ui.profileActionSet"/>
  <rootContext xmi:id="_YRZngnHmEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.gitaction" name="Auto::org.eclipse.egit.ui.gitaction"/>
  <rootContext xmi:id="_YRZng3HmEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.navigation" name="Auto::org.eclipse.egit.ui.navigation"/>
  <rootContext xmi:id="_YRZnhHHmEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.SearchActionSet" name="Auto::org.eclipse.egit.ui.SearchActionSet"/>
  <rootContext xmi:id="_YRZnhXHmEfCAZJ6UDgwwcw" elementId="org.eclipse.embedcdt.debug.gdbjtag.restart.ui.actionset" name="Auto::org.eclipse.embedcdt.debug.gdbjtag.restart.ui.actionset"/>
  <rootContext xmi:id="_YRZnhnHmEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.context.ui.actionSet" name="Auto::org.eclipse.mylyn.context.ui.actionSet"/>
  <rootContext xmi:id="_YRZnh3HmEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.doc.actionSet" name="Auto::org.eclipse.mylyn.doc.actionSet"/>
  <rootContext xmi:id="_YRZniHHmEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.tasks.ui.navigation" name="Auto::org.eclipse.mylyn.tasks.ui.navigation"/>
  <rootContext xmi:id="_YRZniXHmEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.tasks.ui.navigation.additions" name="Auto::org.eclipse.mylyn.tasks.ui.navigation.additions"/>
  <rootContext xmi:id="_YRZninHmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.cheatsheets.actionSet" name="Auto::org.eclipse.ui.cheatsheets.actionSet"/>
  <rootContext xmi:id="_YRZni3HmEfCAZJ6UDgwwcw" elementId="org.eclipse.search.searchActionSet" name="Auto::org.eclipse.search.searchActionSet"/>
  <rootContext xmi:id="_YRZnjHHmEfCAZJ6UDgwwcw" elementId="org.eclipse.team.ui.actionSet" name="Auto::org.eclipse.team.ui.actionSet"/>
  <rootContext xmi:id="_YRZnjXHmEfCAZJ6UDgwwcw" elementId="org.eclipse.text.quicksearch.actionSet" name="Auto::org.eclipse.text.quicksearch.actionSet"/>
  <rootContext xmi:id="_YRZnjnHmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.actionSet.annotationNavigation" name="Auto::org.eclipse.ui.edit.text.actionSet.annotationNavigation"/>
  <rootContext xmi:id="_YRZnj3HmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.actionSet.navigation" name="Auto::org.eclipse.ui.edit.text.actionSet.navigation"/>
  <rootContext xmi:id="_YRZnkHHmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.actionSet.convertLineDelimitersTo" name="Auto::org.eclipse.ui.edit.text.actionSet.convertLineDelimitersTo"/>
  <rootContext xmi:id="_YRZnkXHmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.externaltools.ExternalToolsSet" name="Auto::org.eclipse.ui.externaltools.ExternalToolsSet"/>
  <rootContext xmi:id="_YRZnknHmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.NavigateActionSet" name="Auto::org.eclipse.ui.NavigateActionSet"/>
  <rootContext xmi:id="_YRZnk3HmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.actionSet.keyBindings" name="Auto::org.eclipse.ui.actionSet.keyBindings"/>
  <rootContext xmi:id="_YRZnlHHmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.WorkingSetModificationActionSet" name="Auto::org.eclipse.ui.WorkingSetModificationActionSet"/>
  <rootContext xmi:id="_YRZnlXHmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.WorkingSetActionSet" name="Auto::org.eclipse.ui.WorkingSetActionSet"/>
  <rootContext xmi:id="_YRZnlnHmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.actionSet.openFiles" name="Auto::org.eclipse.ui.actionSet.openFiles"/>
  <rootContext xmi:id="_YRZnl3HmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.actionSet.presentation" name="Auto::org.eclipse.ui.edit.text.actionSet.presentation"/>
  <rootContext xmi:id="_YRZnmHHmEfCAZJ6UDgwwcw" elementId="org.riscvstudio.debug.gdbjtag.qemu.riscv.launchConfigurationType.internal.org.eclipse.debug.ui.DebugPerspective" name="Auto::org.riscvstudio.debug.gdbjtag.qemu.riscv.launchConfigurationType.internal.org.eclipse.debug.ui.DebugPerspective"/>
  <descriptors xmi:id="_YRZnmXHmEfCAZJ6UDgwwcw" elementId="org.eclipse.e4.ui.compatibility.editor" allowMultiple="true" category="org.eclipse.e4.primaryDataStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor">
    <tags>Editor</tags>
    <tags>removeOnHide</tags>
  </descriptors>
  <descriptors xmi:id="_YRZnmnHmEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.codan.internal.ui.views.ProblemDetails" label="Problem Details" iconURI="platform:/plugin/org.eclipse.cdt.codan.ui/icons/edit_bug.gif" tooltip="" category="C/C++" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.codan.internal.ui.views.ProblemDetails"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.codan.ui"/>
    <tags>View</tags>
    <tags>categoryTag:C/C++</tags>
  </descriptors>
  <descriptors xmi:id="_YRZnm3HmEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.debug.ui.executablesView" label="Executables" iconURI="platform:/plugin/org.eclipse.cdt.debug.ui/icons/obj16/exec_view_obj.gif" tooltip="" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.debug.internal.ui.views.executables.ExecutablesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_YRZnnHHmEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.debug.ui.SignalsView" label="Signals" iconURI="platform:/plugin/org.eclipse.cdt.debug.ui/icons/view16/signals_view.gif" tooltip="" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.debug.internal.ui.views.signals.FlexibleSignalsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_YRZnnXHmEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.debug.ui.debuggerConsoleView" label="Debugger Console" iconURI="platform:/plugin/org.eclipse.cdt.debug.ui/icons/view16/debugger_console_view.png" tooltip="" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.debug.internal.ui.views.debuggerconsole.DebuggerConsoleView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_YRZnnnHmEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.debug.ui.memory.memorybrowser.MemoryBrowser" label="Memory Browser" iconURI="platform:/plugin/org.eclipse.cdt.debug.ui.memory.memorybrowser/icons/memorybrowser_view.gif" tooltip="" allowMultiple="true" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.debug.ui.memory.memorybrowser.MemoryBrowser"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.debug.ui.memory.memorybrowser"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_YRZnn3HmEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.dsf.gdb.ui.tracecontrol.view" label="Trace Control" iconURI="platform:/plugin/org.eclipse.cdt.dsf.gdb.ui/icons/full/view16/tracecontrol_view.gif" tooltip="" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.dsf.gdb.internal.ui.tracepoints.TraceControlView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.dsf.gdb.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_YRZnoHHmEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.dsf.gdb.ui.osresources.view" label="OS Resources" iconURI="platform:/plugin/org.eclipse.cdt.dsf.gdb.ui/icons/full/view16/osresources_view.gif" tooltip="" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.dsf.gdb.internal.ui.osview.OSResourcesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.dsf.gdb.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_YRZnoXHmEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.dsf.gdb.ui.debugsources.view" label="Debug Sources" iconURI="platform:/plugin/org.eclipse.cdt.dsf.gdb.ui/icons/full/view16/debugsources_view.gif" tooltip="" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.dsf.gdb.internal.ui.debugsources.DebugSourcesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.dsf.gdb.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_YRZnonHmEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.dsf.debug.ui.disassembly.view" label="Disassembly" iconURI="platform:/plugin/org.eclipse.cdt.dsf.ui/icons/disassembly.gif" tooltip="" allowMultiple="true" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.dsf.debug.internal.ui.disassembly.DisassemblyView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.dsf.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_YRZno3HmEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.make.ui.views.MakeView" label="Build Targets" iconURI="platform:/plugin/org.eclipse.cdt.make.ui/icons/view16/make_target.gif" tooltip="" category="Make" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.make.ui.views.MakeView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.make.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Make</tags>
  </descriptors>
  <descriptors xmi:id="_YRZnpHHmEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.testsrunner.resultsview" label="C/C++ Unit" iconURI="platform:/plugin/org.eclipse.cdt.testsrunner/icons/eview16/cppunit.gif" tooltip="" category="C/C++" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.testsrunner.internal.ui.view.ResultsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.testsrunner"/>
    <tags>View</tags>
    <tags>categoryTag:C/C++</tags>
  </descriptors>
  <descriptors xmi:id="_YRZnpXHmEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.CView" label="C/C++ Projects" iconURI="platform:/plugin/org.eclipse.cdt.ui/icons/view16/cview.gif" tooltip="" category="C/C++" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.internal.ui.cview.CView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.ui"/>
    <tags>View</tags>
    <tags>categoryTag:C/C++</tags>
  </descriptors>
  <descriptors xmi:id="_YRZnpnHmEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.IndexView" label="C/C++ Index" iconURI="platform:/plugin/org.eclipse.cdt.ui/icons/view16/types.gif" tooltip="" category="C/C++" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.internal.ui.indexview.IndexView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.ui"/>
    <tags>View</tags>
    <tags>categoryTag:C/C++</tags>
  </descriptors>
  <descriptors xmi:id="_YRZnp3HmEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.includeBrowser" label="Include Browser" iconURI="platform:/plugin/org.eclipse.cdt.ui/icons/view16/includeBrowser.gif" tooltip="" category="C/C++" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.internal.ui.includebrowser.IBViewPart"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.ui"/>
    <tags>View</tags>
    <tags>categoryTag:C/C++</tags>
  </descriptors>
  <descriptors xmi:id="_YRZnqHHmEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.callHierarchy" label="Call Hierarchy" iconURI="platform:/plugin/org.eclipse.cdt.ui/icons/view16/call_hierarchy.gif" tooltip="" allowMultiple="true" category="C/C++" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.internal.ui.callhierarchy.CHViewPart"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.ui"/>
    <tags>View</tags>
    <tags>categoryTag:C/C++</tags>
  </descriptors>
  <descriptors xmi:id="_YRZnqXHmEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.typeHierarchy" label="Type Hierarchy" iconURI="platform:/plugin/org.eclipse.cdt.ui/icons/view16/class_hi.gif" tooltip="" category="C/C++" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.internal.ui.typehierarchy.THViewPart"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.ui"/>
    <tags>View</tags>
    <tags>categoryTag:C/C++</tags>
  </descriptors>
  <descriptors xmi:id="_YRZnqnHmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.texteditor.TemplatesView" label="Templates" iconURI="platform:/plugin/org.eclipse.cdt.ui/icons/view16/templates.gif" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.texteditor.templates.TemplatesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.ui"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_YRZnq3HmEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.visualizer.view" label="Visualizer" iconURI="platform:/plugin/org.eclipse.cdt.visualizer.ui/icons/full/view16/visualizer_view.png" tooltip="" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.visualizer.ui.VisualizerView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.visualizer.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_YRZnrHHmEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.DebugView" label="Debug" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/debug_view.png" tooltip="" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.launch.LaunchView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_YRZnrXHmEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.BreakpointView" label="Breakpoints" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/breakpoint_view.png" tooltip="" allowMultiple="true" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.breakpoints.BreakpointsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_YRZnrnHmEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.VariableView" label="Variables" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/variable_view.png" tooltip="" allowMultiple="true" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.variables.VariablesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_YRZnr3HmEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.ExpressionView" label="Expressions" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/watchlist_view.png" tooltip="" allowMultiple="true" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.expression.ExpressionView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_YRZnsHHmEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.RegisterView" label="Registers" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/register_view.png" tooltip="" allowMultiple="true" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.registers.RegistersView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_YRZnsXHmEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.ModuleView" label="Modules" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/module_view.png" tooltip="" allowMultiple="true" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.modules.ModulesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_YRZnsnHmEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.MemoryView" label="Memory" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/memory_view.png" tooltip="" allowMultiple="true" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.memory.MemoryView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_YRZns3HmEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.launchView" label="Launch Configurations" iconURI="platform:/plugin/org.eclipse.debug.ui.launchview/icons/run_exc.png" tooltip="" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.debug.ui.launchview/org.eclipse.debug.ui.launchview.internal.view.LaunchViewImpl">
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_YRZntHHmEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.RepositoriesView" label="Git Repositories" iconURI="platform:/plugin/org.eclipse.egit.ui/icons/eview16/repo_rep.png" tooltip="" category="Git" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.egit.ui.internal.repository.RepositoriesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.egit.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Git</tags>
  </descriptors>
  <descriptors xmi:id="_YRZntXHmEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.StagingView" label="Git Staging" iconURI="platform:/plugin/org.eclipse.egit.ui/icons/eview16/staging.png" tooltip="" category="Git" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.egit.ui.internal.staging.StagingView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.egit.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Git</tags>
  </descriptors>
  <descriptors xmi:id="_YRZntnHmEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.InteractiveRebaseView" label="Git Interactive Rebase" iconURI="platform:/plugin/org.eclipse.egit.ui/icons/eview16/rebase_interactive.png" tooltip="" category="Git" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.egit.ui.internal.rebase.RebaseInteractiveView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.egit.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Git</tags>
  </descriptors>
  <descriptors xmi:id="_YRZnt3HmEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.CompareTreeView" label="Git Tree Compare" iconURI="platform:/plugin/org.eclipse.egit.ui/icons/obj16/gitrepository.png" tooltip="" category="Git" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.egit.ui.internal.dialogs.CompareTreeView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.egit.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Git</tags>
    <tags>NoRestore</tags>
  </descriptors>
  <descriptors xmi:id="_YRZnuHHmEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.ReflogView" label="Git Reflog" iconURI="platform:/plugin/org.eclipse.egit.ui/icons/eview16/reflog.png" tooltip="" category="Git" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.egit.ui.internal.reflog.ReflogView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.egit.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Git</tags>
  </descriptors>
  <descriptors xmi:id="_YRZnuXHmEfCAZJ6UDgwwcw" elementId="org.eclipse.embedcdt.internal.debug.gdbjtag.ui.views.PeripheralsView" label="Peripherals" iconURI="platform:/plugin/org.eclipse.embedcdt.debug.gdbjtag.ui/icons/peripheral.png" tooltip="" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.embedcdt.internal.debug.gdbjtag.ui.render.peripherals.PeripheralsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.embedcdt.debug.gdbjtag.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_YRZnunHmEfCAZJ6UDgwwcw" elementId="org.eclipse.embedcdt.internal.managedbuild.packs.ui.views.DocsView" label="Documents" iconURI="platform:/plugin/org.eclipse.embedcdt.managedbuild.packs.ui/icons/pdficon_small.png" tooltip="" category="CMSIS Packs" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.embedcdt.internal.managedbuild.packs.ui.views.DocsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.embedcdt.managedbuild.packs.ui"/>
    <tags>View</tags>
    <tags>categoryTag:CMSIS Packs</tags>
  </descriptors>
  <descriptors xmi:id="_YRZnu3HmEfCAZJ6UDgwwcw" elementId="org.eclipse.embedcdt.internal.packs.ui.views.DevicesView" label="Devices" iconURI="platform:/plugin/org.eclipse.embedcdt.packs.ui/icons/hardware_chip.png" tooltip="" category="CMSIS Packs" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.embedcdt.internal.packs.ui.views.DevicesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.embedcdt.packs.ui"/>
    <tags>View</tags>
    <tags>categoryTag:CMSIS Packs</tags>
  </descriptors>
  <descriptors xmi:id="_YRZnvHHmEfCAZJ6UDgwwcw" elementId="org.eclipse.embedcdt.internal.packs.ui.views.BoardsView" label="Boards" iconURI="platform:/plugin/org.eclipse.embedcdt.packs.ui/icons/board.png" tooltip="" category="CMSIS Packs" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.embedcdt.internal.packs.ui.views.BoardsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.embedcdt.packs.ui"/>
    <tags>View</tags>
    <tags>categoryTag:CMSIS Packs</tags>
  </descriptors>
  <descriptors xmi:id="_YRZnvXHmEfCAZJ6UDgwwcw" elementId="org.eclipse.embedcdt.internal.packs.ui.views.KeywordsView" label="Keywords" iconURI="platform:/plugin/org.eclipse.embedcdt.packs.ui/icons/info_obj.png" tooltip="" category="CMSIS Packs" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.embedcdt.internal.packs.ui.views.KeywordsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.embedcdt.packs.ui"/>
    <tags>View</tags>
    <tags>categoryTag:CMSIS Packs</tags>
  </descriptors>
  <descriptors xmi:id="_YRZnvnHmEfCAZJ6UDgwwcw" elementId="org.eclipse.embedcdt.internal.packs.ui.views.PacksView" label="CMSIS Packs" iconURI="platform:/plugin/org.eclipse.embedcdt.packs.ui/icons/packages.png" tooltip="" category="CMSIS Packs" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.embedcdt.internal.packs.ui.views.PacksView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.embedcdt.packs.ui"/>
    <tags>View</tags>
    <tags>categoryTag:CMSIS Packs</tags>
  </descriptors>
  <descriptors xmi:id="_YRZnv3HmEfCAZJ6UDgwwcw" elementId="org.eclipse.embedcdt.internal.packs.ui.views.OutlineView" label="Outline" iconURI="platform:/plugin/org.eclipse.embedcdt.packs.ui/icons/outline_co.png" tooltip="" category="CMSIS Packs" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.embedcdt.internal.packs.ui.views.OutlineView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.embedcdt.packs.ui"/>
    <tags>View</tags>
    <tags>categoryTag:CMSIS Packs</tags>
  </descriptors>
  <descriptors xmi:id="_YRZnwHHmEfCAZJ6UDgwwcw" elementId="org.eclipse.help.ui.HelpView" label="Help" iconURI="platform:/plugin/org.eclipse.help.ui/icons/view16/help_view.png" tooltip="" category="Help" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.help.ui.internal.views.HelpView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.help.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Help</tags>
  </descriptors>
  <descriptors xmi:id="_YRZnwXHmEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.dataviewers.charts.view" label="Chart View" iconURI="platform:/plugin/org.eclipse.linuxtools.dataviewers.charts/icons/chart_icon.png" tooltip="" allowMultiple="true" category="Charts" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.linuxtools.internal.dataviewers.charts.view.ChartView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.linuxtools.dataviewers.charts"/>
    <tags>View</tags>
    <tags>categoryTag:Charts</tags>
  </descriptors>
  <descriptors xmi:id="_YRZnwnHmEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.gcov.view" label="gcov" iconURI="platform:/plugin/org.eclipse.linuxtools.gcov.core/icons/toggle.gif" tooltip="" allowMultiple="true" category="Profiling" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.linuxtools.internal.gcov.view.CovView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.linuxtools.gcov.core"/>
    <tags>View</tags>
    <tags>categoryTag:Profiling</tags>
    <tags>NoRestore</tags>
  </descriptors>
  <descriptors xmi:id="_YRZnw3HmEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.gprof.view" label="gprof" iconURI="platform:/plugin/org.eclipse.linuxtools.gprof/icons/toggle.gif" tooltip="Gprof view displays the profiling information contained in a gmon.out file" allowMultiple="true" category="Profiling" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.linuxtools.internal.gprof.view.GmonView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.linuxtools.gprof"/>
    <tags>View</tags>
    <tags>categoryTag:Profiling</tags>
    <tags>NoRestore</tags>
  </descriptors>
  <descriptors xmi:id="_YRZnxHHmEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.valgrind.ui.valgrindview" label="Valgrind" iconURI="platform:/plugin/org.eclipse.linuxtools.valgrind.ui/icons/valgrind-icon.png" tooltip="" category="Profiling" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.linuxtools.internal.valgrind.ui.ValgrindViewPart"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.linuxtools.valgrind.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Profiling</tags>
  </descriptors>
  <descriptors xmi:id="_YRZnxXHmEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.builds.navigator.builds" label="Builds" iconURI="platform:/plugin/org.eclipse.mylyn.builds.ui/icons/eview16/build-view.png" tooltip="" category="Mylyn" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.mylyn.internal.builds.ui.view.BuildsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.mylyn.builds.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Mylyn</tags>
  </descriptors>
  <descriptors xmi:id="_YRZnxnHmEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.commons.identity.ui.navigator.People" label="People" iconURI="platform:/plugin/org.eclipse.mylyn.commons.identity.ui/icons/obj16/people.gif" tooltip="" category="Mylyn" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.mylyn.internal.commons.identity.ui.PeopleView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.mylyn.commons.identity.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Mylyn</tags>
  </descriptors>
  <descriptors xmi:id="_YRZnx3HmEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.commons.repositories.ui.navigator.Repositories" label="Team Repositories" iconURI="platform:/plugin/org.eclipse.mylyn.commons.repositories.ui/icons/eview16/repositories.gif" tooltip="" category="Mylyn" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.mylyn.internal.commons.repositories.ui.RepositoriesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.mylyn.commons.repositories.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Mylyn</tags>
  </descriptors>
  <descriptors xmi:id="_YRZnyHHmEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.reviews.Explorer" label="Review" iconURI="platform:/plugin/org.eclipse.mylyn.reviews.ui/icons/obj16/review.png" tooltip="View artifacts and comments associated with reviews." category="Mylyn" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.mylyn.internal.reviews.ui.views.ReviewExplorer"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.mylyn.reviews.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Mylyn</tags>
  </descriptors>
  <descriptors xmi:id="_YRZnyXHmEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.tasks.ui.views.tasks" label="Task List" iconURI="platform:/plugin/org.eclipse.mylyn.tasks.ui/icons/eview16/task-list.png" tooltip="" allowMultiple="true" category="Mylyn" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.mylyn.internal.tasks.ui.views.TaskListView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.mylyn.tasks.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Mylyn</tags>
  </descriptors>
  <descriptors xmi:id="_YRZnynHmEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.tasks.ui.views.repositories" label="Task Repositories" iconURI="platform:/plugin/org.eclipse.mylyn.tasks.ui/icons/eview16/repositories.png" tooltip="" category="Mylyn" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.mylyn.internal.tasks.ui.views.TaskRepositoriesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.mylyn.tasks.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Mylyn</tags>
  </descriptors>
  <descriptors xmi:id="_YRZny3HmEfCAZJ6UDgwwcw" elementId="org.eclipse.oomph.p2.ui.RepositoryExplorer" label="Repository Explorer" iconURI="platform:/plugin/org.eclipse.oomph.p2.ui/icons/obj16/repository.gif" tooltip="" category="Oomph" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.oomph.p2.internal.ui.RepositoryExplorer"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.oomph.p2.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Oomph</tags>
  </descriptors>
  <descriptors xmi:id="_YRZnzHHmEfCAZJ6UDgwwcw" elementId="org.eclipse.remote.ui.view.connections" label="Connections" iconURI="platform:/plugin/org.eclipse.remote.ui/icons/connection.gif" tooltip="" category="Connections" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.remote.internal.ui.views.RemoteConnectionsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.remote.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Connections</tags>
  </descriptors>
  <descriptors xmi:id="_YRZnzXHmEfCAZJ6UDgwwcw" elementId="org.eclipse.search.ui.views.SearchView" label="Search" iconURI="platform:/plugin/org.eclipse.search/icons/full/eview16/searchres.png" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.search2.internal.ui.SearchView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.search"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_YRZnznHmEfCAZJ6UDgwwcw" elementId="org.eclipse.team.sync.views.SynchronizeView" label="Synchronize" iconURI="platform:/plugin/org.eclipse.team.ui/icons/full/eview16/synch_synch.png" tooltip="" allowMultiple="true" category="Version Control (Team)" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.team.internal.ui.synchronize.SynchronizeView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.team.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Version Control (Team)</tags>
  </descriptors>
  <descriptors xmi:id="_YRZnz3HmEfCAZJ6UDgwwcw" elementId="org.eclipse.team.ui.GenericHistoryView" label="History" iconURI="platform:/plugin/org.eclipse.team.ui/icons/full/eview16/history_view.png" tooltip="" allowMultiple="true" category="Version Control (Team)" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.team.internal.ui.history.GenericHistoryView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.team.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Version Control (Team)</tags>
  </descriptors>
  <descriptors xmi:id="_YRZn0HHmEfCAZJ6UDgwwcw" elementId="org.eclipse.tm.terminal.view.ui.TerminalsView" label="Terminal" iconURI="platform:/plugin/org.eclipse.tm.terminal.view.ui/icons/eview16/terminal_view.gif" tooltip="" allowMultiple="true" category="Terminal" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tm.terminal.view.ui.view.TerminalsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tm.terminal.view.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Terminal</tags>
  </descriptors>
  <descriptors xmi:id="_YRZn0XHmEfCAZJ6UDgwwcw" elementId="org.eclipse.tcf.te.ui.terminals.TerminalsView" label="Terminals (Old)" iconURI="platform:/plugin/org.eclipse.tm.terminal.view.ui/icons/eview16/terminal_view.gif" tooltip="" allowMultiple="true" category="Other" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tm.terminal.view.ui.view.OldTerminalsViewHandler"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tm.terminal.view.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Other</tags>
  </descriptors>
  <descriptors xmi:id="_YRZn0nHmEfCAZJ6UDgwwcw" elementId="org.eclipse.tracecompass.analysis.counters.ui.views.countersview" label="Counters" iconURI="platform:/plugin/org.eclipse.tracecompass.analysis.counters.ui/icons/counter.png" tooltip="" category="LTTng" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.analysis.counters.ui.views.CounterView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.analysis.counters.ui"/>
    <tags>View</tags>
    <tags>categoryTag:LTTng</tags>
  </descriptors>
  <descriptors xmi:id="_YRZn03HmEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.tmf.analysis.graph.ui.criticalpath.view.criticalpathview" label="Critical Flow View" iconURI="platform:/plugin/org.eclipse.tracecompass.analysis.graph.ui/icons/eview16/critical-path.png" tooltip="" category="LTTng" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.internal.analysis.graph.ui.criticalpath.view.CriticalPathView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.analysis.graph.ui"/>
    <tags>View</tags>
    <tags>categoryTag:LTTng</tags>
  </descriptors>
  <descriptors xmi:id="_YRZn1HHmEfCAZJ6UDgwwcw" elementId="org.eclipse.tracecompass.analysis.lami.views.reportview" label="Analysis Report" iconURI="platform:/plugin/org.eclipse.ui/icons/full/eview16/defaultview_misc.png" tooltip="" allowMultiple="true" category="Tracing" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.internal.provisional.analysis.lami.ui.views.LamiReportView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.analysis.lami.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Tracing</tags>
    <tags>NoRestore</tags>
  </descriptors>
  <descriptors xmi:id="_YRZn1XHmEfCAZJ6UDgwwcw" elementId="org.eclipse.tracecompass.analysis.timing.ui.segstore.table:org.eclipse.tracecompass.analysis.os.linux.core.swslatency.sws" label="Sched_Wakeup/Switch Latencies" iconURI="platform:/plugin/org.eclipse.tracecompass.analysis.os.linux.ui/icons/obj16/latency.png" tooltip="" category="LTTng" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.analysis.timing.ui.views.segmentstore.table.SegmentStoreTableView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.analysis.os.linux.ui"/>
    <tags>View</tags>
    <tags>categoryTag:LTTng</tags>
  </descriptors>
  <descriptors xmi:id="_YRZn1nHmEfCAZJ6UDgwwcw" elementId="org.eclipse.tracecompass.internal.analysis.os.linux.ui.views.swslatency.scatter" label="Sched_Wakeup/Switch Latency vs Time" iconURI="platform:/plugin/org.eclipse.tracecompass.analysis.os.linux.ui/icons/obj16/scatter.png" tooltip="" category="LTTng" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.internal.analysis.os.linux.ui.views.swslatency.SWSLatencyScatterView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.analysis.os.linux.ui"/>
    <tags>View</tags>
    <tags>categoryTag:LTTng</tags>
  </descriptors>
  <descriptors xmi:id="_YRZn13HmEfCAZJ6UDgwwcw" elementId="org.eclipse.tracecompass.analysis.timing.ui.segstore.statistics:org.eclipse.tracecompass.analysis.os.linux.core.swslatency.sws" label="Sched_Wakeup/Switch Latency Statistics" iconURI="platform:/plugin/org.eclipse.tracecompass.analysis.os.linux.ui/icons/obj16/statistics_view.gif" tooltip="" category="LTTng" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.analysis.timing.ui.views.segmentstore.statistics.SegmentStoreStatisticsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.analysis.os.linux.ui"/>
    <tags>View</tags>
    <tags>categoryTag:LTTng</tags>
  </descriptors>
  <descriptors xmi:id="_YRZn2HHmEfCAZJ6UDgwwcw" elementId="org.eclipse.tracecompass.analysis.os.linux.ui.segmentstore.statistics.prioname" label="Priority/Thread name Statistics" iconURI="platform:/plugin/org.eclipse.tracecompass.analysis.os.linux.ui/icons/eview16/statistics_view.gif" tooltip="" category="Tracing" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.internal.analysis.os.linux.ui.segmentstore.statistics.PriorityThreadNameStatisticsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.analysis.os.linux.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Tracing</tags>
  </descriptors>
  <descriptors xmi:id="_YRZn2XHmEfCAZJ6UDgwwcw" elementId="org.eclipse.tracecompass.analysis.os.linux.ui.segmentstore.statistics.prioname:org.eclipse.tracecompass.analysis.os.linux.core.swslatency.sws" label="Sched_Wakeup/Switch Latency Priority/Thread name Statistics" iconURI="platform:/plugin/org.eclipse.tracecompass.analysis.os.linux.ui/icons/obj16/statistics_view.gif" tooltip="" category="LTTng" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.internal.analysis.os.linux.ui.segmentstore.statistics.PriorityThreadNameStatisticsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.analysis.os.linux.ui"/>
    <tags>View</tags>
    <tags>categoryTag:LTTng</tags>
  </descriptors>
  <descriptors xmi:id="_YRZn2nHmEfCAZJ6UDgwwcw" elementId="org.eclipse.tracecompass.analysis.os.linux.ui.segmentstore.statistics.priority" label="Priority Statistics" iconURI="platform:/plugin/org.eclipse.tracecompass.analysis.os.linux.ui/icons/eview16/statistics_view.gif" tooltip="" category="Tracing" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.internal.analysis.os.linux.ui.segmentstore.statistics.PriorityStatisticsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.analysis.os.linux.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Tracing</tags>
  </descriptors>
  <descriptors xmi:id="_YRZn23HmEfCAZJ6UDgwwcw" elementId="org.eclipse.tracecompass.analysis.os.linux.ui.segmentstore.statistics.priority:org.eclipse.tracecompass.analysis.os.linux.core.swslatency.sws" label="Sched_Wakeup/Switch Latency Priority Statistics" iconURI="platform:/plugin/org.eclipse.tracecompass.analysis.os.linux.ui/icons/obj16/statistics_view.gif" tooltip="" category="LTTng" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.internal.analysis.os.linux.ui.segmentstore.statistics.PriorityStatisticsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.analysis.os.linux.ui"/>
    <tags>View</tags>
    <tags>categoryTag:LTTng</tags>
  </descriptors>
  <descriptors xmi:id="_YRZn3HHmEfCAZJ6UDgwwcw" elementId="org.eclipse.tracecompass.internal.analysis.os.linux.ui.views.swslatency.density" label="Sched_Wakeup/Switch Density" iconURI="platform:/plugin/org.eclipse.tracecompass.analysis.os.linux.ui/icons/obj16/density.png" tooltip="" category="LTTng" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.internal.analysis.os.linux.ui.views.swslatency.SWSLatencyDensityView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.analysis.os.linux.ui"/>
    <tags>View</tags>
    <tags>categoryTag:LTTng</tags>
  </descriptors>
  <descriptors xmi:id="_YRZn3XHmEfCAZJ6UDgwwcw" elementId="org.eclipse.tracecompass.analysis.os.linux.views.controlflow" label="Control Flow" iconURI="platform:/plugin/org.eclipse.tracecompass.analysis.os.linux.ui/icons/eview16/control_flow_view.gif" tooltip="" category="LTTng" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.internal.analysis.os.linux.ui.views.controlflow.ControlFlowView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.analysis.os.linux.ui"/>
    <tags>View</tags>
    <tags>categoryTag:LTTng</tags>
  </descriptors>
  <descriptors xmi:id="_YRZn3nHmEfCAZJ6UDgwwcw" elementId="org.eclipse.tracecompass.analysis.os.linux.views.resources" label="Resources" iconURI="platform:/plugin/org.eclipse.tracecompass.analysis.os.linux.ui/icons/eview16/resources_view.gif" tooltip="" category="LTTng" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.internal.analysis.os.linux.ui.views.resources.ResourcesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.analysis.os.linux.ui"/>
    <tags>View</tags>
    <tags>categoryTag:LTTng</tags>
  </descriptors>
  <descriptors xmi:id="_YRZn33HmEfCAZJ6UDgwwcw" elementId="org.eclipse.tracecompass.analysis.os.linux.views.cpuusage" label="CPU Usage" iconURI="platform:/plugin/org.eclipse.tracecompass.analysis.os.linux.ui/icons/eview16/cpu-usage.png" tooltip="" category="LTTng" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.internal.analysis.os.linux.ui.views.cpuusage.CpuUsageView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.analysis.os.linux.ui"/>
    <tags>View</tags>
    <tags>categoryTag:LTTng</tags>
  </descriptors>
  <descriptors xmi:id="_YRZn4HHmEfCAZJ6UDgwwcw" elementId="org.eclipse.tracecompass.analysis.timing.ui.segstore.table:org.eclipse.tracecompass.analysis.os.linux.latency.syscall" label="System Call Latencies" iconURI="platform:/plugin/org.eclipse.tracecompass.analysis.os.linux.ui/icons/eview16/latency.png" tooltip="" category="LTTng" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.analysis.timing.ui.views.segmentstore.table.SegmentStoreTableView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.analysis.os.linux.ui"/>
    <tags>View</tags>
    <tags>categoryTag:LTTng</tags>
  </descriptors>
  <descriptors xmi:id="_YRZn4XHmEfCAZJ6UDgwwcw" elementId="org.eclipse.tracecompass.analysis.os.linux.views.latency.scatter" label="System Call Latency vs Time" iconURI="platform:/plugin/org.eclipse.tracecompass.analysis.os.linux.ui/icons/eview16/scatter.png" tooltip="" category="LTTng" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.internal.analysis.os.linux.ui.views.latency.SystemCallLatencyScatterView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.analysis.os.linux.ui"/>
    <tags>View</tags>
    <tags>categoryTag:LTTng</tags>
  </descriptors>
  <descriptors xmi:id="_YRZn4nHmEfCAZJ6UDgwwcw" elementId="org.eclipse.tracecompass.analysis.timing.ui.segstore.statistics:org.eclipse.tracecompass.analysis.os.linux.latency.syscall" label="System Call Latency Statistics" iconURI="platform:/plugin/org.eclipse.tracecompass.analysis.os.linux.ui/icons/eview16/statistics_view.gif" tooltip="" category="LTTng" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.analysis.timing.ui.views.segmentstore.statistics.SegmentStoreStatisticsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.analysis.os.linux.ui"/>
    <tags>View</tags>
    <tags>categoryTag:LTTng</tags>
  </descriptors>
  <descriptors xmi:id="_YRZn43HmEfCAZJ6UDgwwcw" elementId="org.eclipse.tracecompass.analysis.os.linux.views.latency.density" label="System Call Density" iconURI="platform:/plugin/org.eclipse.tracecompass.analysis.os.linux.ui/icons/eview16/density.png" tooltip="" category="LTTng" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.internal.analysis.os.linux.ui.views.latency.SystemCallLatencyDensityView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.analysis.os.linux.ui"/>
    <tags>View</tags>
    <tags>categoryTag:LTTng</tags>
  </descriptors>
  <descriptors xmi:id="_YRZn5HHmEfCAZJ6UDgwwcw" elementId="org.eclipse.tracecompass.analysis.os.linux.ui.kernelmemoryusageview" label="Kernel Memory Usage View" iconURI="platform:/plugin/org.eclipse.tracecompass.analysis.os.linux.ui/icons/eview16/memory-usage.png" tooltip="" category="LTTng" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.internal.analysis.os.linux.ui.views.kernelmemoryusage.KernelMemoryUsageView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.analysis.os.linux.ui"/>
    <tags>View</tags>
    <tags>categoryTag:LTTng</tags>
  </descriptors>
  <descriptors xmi:id="_YRZn5XHmEfCAZJ6UDgwwcw" elementId="org.eclipse.tracecompass.analysis.os.linux.views.diskioactivity" label="Disk I/O Activity" iconURI="platform:/plugin/org.eclipse.tracecompass.analysis.os.linux.ui/icons/eview16/resources_view.gif" tooltip="" category="LTTng" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.internal.analysis.os.linux.ui.views.io.diskioactivity.DiskIOActivityView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.analysis.os.linux.ui"/>
    <tags>View</tags>
    <tags>categoryTag:LTTng</tags>
  </descriptors>
  <descriptors xmi:id="_YRZn5nHmEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.tmf.ui.views.callstack" label="Flame Chart" iconURI="platform:/plugin/org.eclipse.tracecompass.analysis.profiling.ui/icons/eview16/callstack_view.gif" tooltip="" category="Tracing" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.analysis.profiling.ui.views.flamechart.FlameChartView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.analysis.profiling.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Tracing</tags>
  </descriptors>
  <descriptors xmi:id="_YRZn53HmEfCAZJ6UDgwwcw" elementId="org.eclipse.tracecompass.internal.analysis.timing.ui.callgraph.callgraphDensity" label="Function Durations Distribution" iconURI="platform:/plugin/org.eclipse.tracecompass.analysis.profiling.ui/icons/eview16/funcdensity.png" tooltip="" category="Tracing" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.internal.analysis.profiling.ui.callgraph.CallGraphDensityView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.analysis.profiling.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Tracing</tags>
  </descriptors>
  <descriptors xmi:id="_YRZn6HHmEfCAZJ6UDgwwcw" elementId="org.eclipse.tracecompass.internal.analysis.timing.ui.flamegraph.flamegraphView" label="Flame Graph" iconURI="platform:/plugin/org.eclipse.tracecompass.analysis.profiling.ui/icons/eview16/flame.png" tooltip="" category="Tracing" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.internal.analysis.profiling.ui.flamegraph.FlameGraphView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.analysis.profiling.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Tracing</tags>
  </descriptors>
  <descriptors xmi:id="_YRZn6XHmEfCAZJ6UDgwwcw" elementId="org.eclipse.tracecompass.internal.analysis.timing.ui.callgraph.statistics.callgraphstatistics" label="Function Duration Statistics" iconURI="platform:/plugin/org.eclipse.tracecompass.analysis.profiling.ui/icons/eview16/statistics_view.gif" tooltip="" category="Tracing" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.internal.analysis.profiling.ui.callgraph.statistics.CallGraphStatisticsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.analysis.profiling.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Tracing</tags>
  </descriptors>
  <descriptors xmi:id="_YRZn6nHmEfCAZJ6UDgwwcw" elementId="org.eclipse.tracecompass.analysis.timing.ui.segstore.table" label="Segment Store Table" iconURI="platform:/plugin/org.eclipse.tracecompass.analysis.timing.ui/icons/eview16/latency.png" tooltip="" category="Tracing" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.analysis.timing.ui.views.segmentstore.table.SegmentStoreTableView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.analysis.timing.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Tracing</tags>
  </descriptors>
  <descriptors xmi:id="_YRZn63HmEfCAZJ6UDgwwcw" elementId="org.eclipse.tracecompass.analysis.timing.ui.segstore.statistics" label="Descriptive Statistics" iconURI="platform:/plugin/org.eclipse.tracecompass.analysis.timing.ui/icons/eview16/statistics_view.gif" tooltip="" category="Tracing" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.analysis.timing.ui.views.segmentstore.statistics.SegmentStoreStatisticsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.analysis.timing.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Tracing</tags>
  </descriptors>
  <descriptors xmi:id="_YRZn7HHmEfCAZJ6UDgwwcw" elementId="org.eclipse.tracecompass.analysis.timing.ui.segstore.scatter2" label="Segments vs Time" iconURI="platform:/plugin/org.eclipse.tracecompass.analysis.timing.ui/icons/eview16/latency.png" tooltip="" category="Tracing" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.analysis.timing.ui.views.segmentstore.scatter.SegmentStoreScatterView2"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.analysis.timing.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Tracing</tags>
  </descriptors>
  <descriptors xmi:id="_YRZn7XHmEfCAZJ6UDgwwcw" elementId="org.eclipse.tracecompass.analysis.timing.ui.segstore.table:org.eclipse.tracecompass.internal.analysis.timing.core.event.matching" label="Event Match Latencies" iconURI="platform:/plugin/org.eclipse.tracecompass.analysis.timing.ui/icons/eview16/latency.png" tooltip="" category="LTTng" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.analysis.timing.ui.views.segmentstore.table.SegmentStoreTableView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.analysis.timing.ui"/>
    <tags>View</tags>
    <tags>categoryTag:LTTng</tags>
  </descriptors>
  <descriptors xmi:id="_YRZn7nHmEfCAZJ6UDgwwcw" elementId="org.eclipse.tracecompass.analysis.timing.ui.segstore.statistics:org.eclipse.tracecompass.internal.analysis.timing.core.event.matching" label="Event Match Latency Statistics" iconURI="platform:/plugin/org.eclipse.tracecompass.analysis.timing.ui/icons/eview16/statistics_view.gif" tooltip="" category="LTTng" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.analysis.timing.ui.views.segmentstore.statistics.SegmentStoreStatisticsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.analysis.timing.ui"/>
    <tags>View</tags>
    <tags>categoryTag:LTTng</tags>
  </descriptors>
  <descriptors xmi:id="_YRZn73HmEfCAZJ6UDgwwcw" elementId="org.eclipse.tracecompass.analysis.timing.ui.segstore.scatter2:org.eclipse.tracecompass.internal.analysis.timing.core.event.matching" label="Event Matches Scatter Graph" iconURI="platform:/plugin/org.eclipse.tracecompass.analysis.timing.ui/icons/eview16/statistics_view.gif" tooltip="" category="LTTng" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.analysis.timing.ui.views.segmentstore.scatter.SegmentStoreScatterView2"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.analysis.timing.ui"/>
    <tags>View</tags>
    <tags>categoryTag:LTTng</tags>
  </descriptors>
  <descriptors xmi:id="_YRZn8HHmEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.internal.lttng2.ui.views.control" label="Control" iconURI="platform:/plugin/org.eclipse.tracecompass.lttng2.control.ui/icons/eview16/control_view.gif" tooltip="" category="LTTng" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.internal.lttng2.control.ui.views.ControlView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.lttng2.control.ui"/>
    <tags>View</tags>
    <tags>categoryTag:LTTng</tags>
  </descriptors>
  <descriptors xmi:id="_YRZn8XHmEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.lttng2.ust.memoryusage" label="UST Memory Usage" iconURI="platform:/plugin/org.eclipse.tracecompass.lttng2.ust.ui/icons/eview16/memory-usage.png" tooltip="" category="LTTng" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.internal.lttng2.ust.ui.views.memusage.UstMemoryUsageView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.lttng2.ust.ui"/>
    <tags>View</tags>
    <tags>categoryTag:LTTng</tags>
  </descriptors>
  <descriptors xmi:id="_YRZn8nHmEfCAZJ6UDgwwcw" elementId="org.eclipse.tracecompass.analysis.timing.ui.segstore.table:org.eclipse.linuxtools.lttng2.ust.analysis.memory" label="Potential Leaks" iconURI="platform:/plugin/org.eclipse.tracecompass.lttng2.ust.ui/icons/eview16/memory-usage.png" tooltip="" category="LTTng" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.analysis.timing.ui.views.segmentstore.table.SegmentStoreTableView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.lttng2.ust.ui"/>
    <tags>View</tags>
    <tags>categoryTag:LTTng</tags>
  </descriptors>
  <descriptors xmi:id="_YRZn83HmEfCAZJ6UDgwwcw" elementId="org.eclipse.tracecompass.analysis.timing.ui.segstore.scatter2:org.eclipse.linuxtools.lttng2.ust.analysis.memory" label="Potential Leaks vs Time" iconURI="platform:/plugin/org.eclipse.tracecompass.lttng2.ust.ui/icons/eview16/memory-usage.png" tooltip="" category="LTTng" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.analysis.timing.ui.views.segmentstore.scatter.SegmentStoreScatterView2"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.lttng2.ust.ui"/>
    <tags>View</tags>
    <tags>categoryTag:LTTng</tags>
  </descriptors>
  <descriptors xmi:id="_YRZn9HHmEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.tmf.analysis.xml.ui.views.timegraph" label="XML Time Graph View" iconURI="platform:/plugin/org.eclipse.tracecompass.tmf.analysis.xml.ui/icons/eview16/ganttxml.png" tooltip="" allowMultiple="true" category="LTTng" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.internal.tmf.analysis.xml.ui.views.timegraph.XmlTimeGraphView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.tmf.analysis.xml.ui"/>
    <tags>View</tags>
    <tags>categoryTag:LTTng</tags>
  </descriptors>
  <descriptors xmi:id="_YRZn9XHmEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.internal.tmf.analysis.xml.ui.views.xyview" label="XML XY Chart View" iconURI="platform:/plugin/org.eclipse.tracecompass.tmf.analysis.xml.ui/icons/eview16/linechartxml.png" tooltip="" allowMultiple="true" category="LTTng" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.internal.tmf.analysis.xml.ui.views.xychart.XmlXYView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.tmf.analysis.xml.ui"/>
    <tags>View</tags>
    <tags>categoryTag:LTTng</tags>
  </descriptors>
  <descriptors xmi:id="_YRZn9nHmEfCAZJ6UDgwwcw" elementId="org.eclipse.tracecompass.internal.tmf.analysis.xml.ui.views.latencytable" label="Latency Table" iconURI="platform:/plugin/org.eclipse.tracecompass.tmf.analysis.xml.ui/icons/eview16/latency.png" tooltip="" category="Tracing" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.internal.tmf.analysis.xml.ui.views.latency.PatternLatencyTableView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.tmf.analysis.xml.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Tracing</tags>
  </descriptors>
  <descriptors xmi:id="_YRZn93HmEfCAZJ6UDgwwcw" elementId="org.eclipse.tracecompass.internal.tmf.analysis.xml.ui.views.scattergraph" label="Latency vs Time" iconURI="platform:/plugin/org.eclipse.tracecompass.tmf.analysis.xml.ui/icons/eview16/scatter.png" tooltip="" category="Tracing" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.internal.tmf.analysis.xml.ui.views.latency.PatternScatterGraphView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.tmf.analysis.xml.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Tracing</tags>
  </descriptors>
  <descriptors xmi:id="_YRZn-HHmEfCAZJ6UDgwwcw" elementId="org.eclipse.tracecompass.internal.tmf.analysis.xml.ui.views.density" label="Latency vs Count" iconURI="platform:/plugin/org.eclipse.tracecompass.tmf.analysis.xml.ui/icons/eview16/density.png" tooltip="" category="Tracing" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.internal.tmf.analysis.xml.ui.views.latency.PatternDensityView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.tmf.analysis.xml.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Tracing</tags>
  </descriptors>
  <descriptors xmi:id="_YRZn-XHmEfCAZJ6UDgwwcw" elementId="org.eclipse.tracecompass.internal.tmf.analysis.xml.ui.views.statistics" label="Latency Statistics" iconURI="platform:/plugin/org.eclipse.tracecompass.tmf.analysis.xml.ui/icons/eview16/statistics_view.gif" tooltip="" category="Tracing" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.internal.tmf.analysis.xml.ui.views.latency.PatternStatisticsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.tmf.analysis.xml.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Tracing</tags>
  </descriptors>
  <descriptors xmi:id="_YRZn-nHmEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.tmf.ui.views.timechart" label="Time Chart" iconURI="platform:/plugin/org.eclipse.tracecompass.tmf.ui/icons/eview16/timechart_view.gif" tooltip="" category="Tracing" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.tmf.ui.views.timechart.TimeChartView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.tmf.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Tracing</tags>
  </descriptors>
  <descriptors xmi:id="_YRZn-3HmEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.tmf.ui.views.ssvisualizer" label="State System Explorer" iconURI="platform:/plugin/org.eclipse.tracecompass.tmf.ui/icons/eview16/events_view.gif" tooltip="" category="Tracing" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.tmf.ui.views.statesystem.TmfStateSystemExplorer"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.tmf.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Tracing</tags>
  </descriptors>
  <descriptors xmi:id="_YRZn_HHmEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.tmf.ui.views.colors" label="Colors" iconURI="platform:/plugin/org.eclipse.tracecompass.tmf.ui/icons/eview16/colors_view.gif" tooltip="" category="Tracing" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.tmf.ui.views.colors.ColorsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.tmf.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Tracing</tags>
  </descriptors>
  <descriptors xmi:id="_YRZn_XHmEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.tmf.ui.views.filter" label="Filters" iconURI="platform:/plugin/org.eclipse.tracecompass.tmf.ui/icons/eview16/filters_view.gif" tooltip="" category="Tracing" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.tmf.ui.views.filter.FilterView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.tmf.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Tracing</tags>
  </descriptors>
  <descriptors xmi:id="_YRZn_nHmEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.tmf.ui.tmfUml2SDSyncView" label="Sequence Diagram" iconURI="platform:/plugin/org.eclipse.tracecompass.tmf.ui/icons/eview16/sequencediagram_view.gif" tooltip="" category="Tracing" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.tmf.ui.views.uml2sd.SDView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.tmf.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Tracing</tags>
  </descriptors>
  <descriptors xmi:id="_YRZn_3HmEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.tmf.ui.views.statistics" label="Statistics" iconURI="platform:/plugin/org.eclipse.tracecompass.tmf.ui/icons/eview16/statistics_view.gif" tooltip="" category="Tracing" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.internal.tmf.ui.views.statistics.TmfStatisticsViewImpl"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.tmf.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Tracing</tags>
  </descriptors>
  <descriptors xmi:id="_YRZoAHHmEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.tmf.ui.views.histogram" label="Histogram" iconURI="platform:/plugin/org.eclipse.tracecompass.tmf.ui/icons/eview16/histogram.gif" tooltip="" category="Tracing" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.tmf.ui.views.histogram.HistogramView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.tmf.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Tracing</tags>
  </descriptors>
  <descriptors xmi:id="_YRZoAXHmEfCAZJ6UDgwwcw" elementId="org.eclipse.tracecompass.tmf.ui.views.eventdensity" label="Event Density" iconURI="platform:/plugin/org.eclipse.tracecompass.tmf.ui/icons/eview16/histogram.gif" tooltip="" category="Tracing" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.internal.tmf.ui.views.eventdensity.EventDensityView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.tmf.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Tracing</tags>
  </descriptors>
  <descriptors xmi:id="_YRZoAnHmEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.tmf.ui.views.synchronization" label="Synchronization" iconURI="platform:/plugin/org.eclipse.tracecompass.tmf.ui/icons/eview16/synced.gif" tooltip="" category="Tracing" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.tmf.ui.views.synchronization.TmfSynchronizationView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.tmf.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Tracing</tags>
  </descriptors>
  <descriptors xmi:id="_YRZoA3HmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.internal.introview" label="Welcome" iconURI="platform:/plugin/org.eclipse.ui/icons/full/eview16/defaultview_misc.png" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.ViewIntroAdapterPart"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_YRZoBHHmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.browser.view" label="Internal Web Browser" iconURI="platform:/plugin/org.eclipse.ui.browser/icons/obj16/internal_browser.png" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.browser.WebBrowserView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.browser"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_YRZoBXHmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.cheatsheets.views.CheatSheetView" label="Cheat Sheets" iconURI="platform:/plugin/org.eclipse.ui.cheatsheets/icons/view16/cheatsheet_view.png" tooltip="" category="Help" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.cheatsheets.views.CheatSheetView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.cheatsheets"/>
    <tags>View</tags>
    <tags>categoryTag:Help</tags>
  </descriptors>
  <descriptors xmi:id="_YRZoBnHmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.console.ConsoleView" label="Console" iconURI="platform:/plugin/org.eclipse.ui.console/icons/full/cview16/console_view.png" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.console.ConsoleView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.console"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_YRZoB3HmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.views.ProgressView" label="Progress" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/pview.png" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.progress.ProgressView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_YRZoCHHmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.views.BookmarkView" label="Bookmarks" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/bkmrk_nav.png" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.markers.BookmarksView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_YRZoCXHmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.views.TaskList" label="Tasks" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/tasks_tsk.png" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.markers.TasksView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_YRZoCnHmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.views.ProblemView" label="Problems" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/problems_view.png" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.markers.ProblemsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_YRZoC3HmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.views.AllMarkersView" label="Markers" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/problems_view.png" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.markers.AllMarkersView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_YRZoDHHmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.navigator.ProjectExplorer" label="Project Explorer" iconURI="platform:/plugin/org.eclipse.ui.navigator.resources/icons/full/eview16/resource_persp.png" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.navigator.resources.ProjectExplorer"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.navigator.resources"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_YRZoDXHmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.views.PropertySheet" label="Properties" iconURI="platform:/plugin/org.eclipse.ui.views/icons/full/eview16/prop_ps.png" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.views.properties.PropertySheet"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.views"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_YRZoDnHmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.views.ContentOutline" label="Outline" iconURI="platform:/plugin/org.eclipse.ui.views/icons/full/eview16/outline_co.png" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.views.contentoutline.ContentOutline"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.views"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_YRZoD3HmEfCAZJ6UDgwwcw" elementId="org.eclipse.pde.runtime.LogView" label="Error Log" iconURI="platform:/plugin/org.eclipse.ui.views.log/icons/eview16/error_log.png" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.log.LogView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.views.log"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_YRZoEHHmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.views.minimap.MinimapView" label="Minimap" iconURI="platform:/plugin/org.eclipse.ui.workbench.texteditor/icons/full/eview16/minimap.png" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.minimap.MinimapView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.workbench.texteditor"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_YRZoEXHmEfCAZJ6UDgwwcw" elementId="org.eclipse.wst.xml.ui.views.annotations.XMLAnnotationsView" label="Documentation" iconURI="platform:/plugin/org.eclipse.wst.xml.ui/icons/full/obj16/comment_obj.gif" tooltip="" category="XML" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.wst.xml.ui.internal.views.annotations.XMLAnnotationsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.wst.xml.ui"/>
    <tags>View</tags>
    <tags>categoryTag:XML</tags>
  </descriptors>
  <descriptors xmi:id="_YRZoEnHmEfCAZJ6UDgwwcw" elementId="org.eclipse.wst.xml.ui.contentmodel.view" label="Content Model" iconURI="platform:/plugin/org.eclipse.wst.xml.ui/icons/full/view16/hierarchy.gif" tooltip="" category="XML" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.wst.xml.ui.internal.views.contentmodel.ContentModelView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.wst.xml.ui"/>
    <tags>View</tags>
    <tags>categoryTag:XML</tags>
  </descriptors>
  <descriptors xmi:id="_YRZoE3HmEfCAZJ6UDgwwcw" elementId="org.riscvstudio.ide.tools.trace.ui.TabTrace" label="Trace" iconURI="platform:/plugin/org.riscvstudio.ide.tools.trace/icons/trace/trace.png" tooltip="" category="RV Trace" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.riscvstudio.ide.tools.trace.ui.TabTrace"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.riscvstudio.ide.tools.trace"/>
    <tags>View</tags>
    <tags>categoryTag:RV Trace</tags>
  </descriptors>
  <trimContributions xmi:id="_YRa1RnHmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.ide.application.trimcontribution.QuickAccess" contributorURI="platform:/plugin/org.eclipse.ui.ide.application" toBeRendered="false" parentId="org.eclipse.ui.main.toolbar" positionInParent="last">
    <children xsi:type="menu:ToolControl" xmi:id="_YRa1R3HmEfCAZJ6UDgwwcw" elementId="Spacer Glue" contributorURI="platform:/plugin/org.eclipse.ui.ide.application" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.renderers.swt/org.eclipse.e4.ui.workbench.renderers.swt.LayoutModifierToolControl">
      <tags>glue</tags>
      <tags>move_after:PerspectiveSpacer</tags>
      <tags>SHOW_RESTORE_MENU</tags>
    </children>
    <children xsi:type="menu:ToolControl" xmi:id="_YRa1SHHmEfCAZJ6UDgwwcw" elementId="SearchField" contributorURI="platform:/plugin/org.eclipse.ui.ide.application" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.quickaccess.SearchField">
      <tags>move_after:Spacer Glue</tags>
      <tags>HIDEABLE</tags>
      <tags>SHOW_RESTORE_MENU</tags>
    </children>
    <children xsi:type="menu:ToolControl" xmi:id="_YRa1SXHmEfCAZJ6UDgwwcw" elementId="Search-PS Glue" contributorURI="platform:/plugin/org.eclipse.ui.ide.application" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.renderers.swt/org.eclipse.e4.ui.workbench.renderers.swt.LayoutModifierToolControl">
      <tags>glue</tags>
      <tags>move_after:SearchField</tags>
      <tags>SHOW_RESTORE_MENU</tags>
    </children>
  </trimContributions>
  <commands xmi:id="_YRa1WXHmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.select.pageUp" commandName="Select Page Up" description="Select to the top of the page" category="_YRa5J3HmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa1WnHmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.toggleWordWrap" commandName="Toggle Word Wrap" description="Toggle word wrap in the current text editor" category="_YRa5JHHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa1W3HmEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.history.ResetQuickdiffBaseline" commandName="Reset quickdiff baseline" category="_YRa5PnHmEfCAZJ6UDgwwcw">
    <parameters xmi:id="_YRa1XHHmEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.history.ResetQuickdiffBaselineTarget" name="Reset target (HEAD, HEAD^1)" optional="false"/>
  </commands>
  <commands xmi:id="_YRa1XXHmEfCAZJ6UDgwwcw" elementId="org.eclipse.oomph.p2.ui.SearchRequirements" commandName="Search Requirements" category="_YRa5OHHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa1XnHmEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.search.findrefs" commandName="References" description="Searches for references to the selected element in the workspace" category="_YRa5Q3HmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa1X3HmEfCAZJ6UDgwwcw" elementId="org.eclipse.tracecompass.tmf.ui.command.zoomin.selection" commandName="Zoom in (selection)" category="_YRa5OnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa1YHHmEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.FetchGitLabMergeRequest" commandName="Fetch GitLab Merge Request" category="_YRa5VXHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa1YXHmEfCAZJ6UDgwwcw" elementId="org.eclipse.tm.terminal.maximize" commandName="Maximize Active View or Editor" category="_YRa5UXHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa1YnHmEfCAZJ6UDgwwcw" elementId="org.eclipse.oomph.setup.editor.openDiscoveredType" commandName="Open Discovered Type" category="_YRa5RnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa1Y3HmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.deletePreviousWord" commandName="Delete Previous Word" description="Delete the previous word" category="_YRa5J3HmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa1ZHHmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.select.stopMultiSelection" commandName="End multi-selection" description="Unselects all multi-selections returning to a single cursor " category="_YRa5J3HmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa1ZXHmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.delimiter.unix" commandName="Convert Line Delimiters to Unix (LF, \n, 0A, &#xb6;)" description="Converts the line delimiters to Unix (LF, \n, 0A, &#xb6;)" category="_YRa5LnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa1ZnHmEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.history.Edit" commandName="Edit Commit" category="_YRa5PnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa1Z3HmEfCAZJ6UDgwwcw" elementId="org.eclipse.epp.mpc.ui.command.showMarketplaceWizard" commandName="Eclipse Marketplace" description="Show the Eclipse Marketplace wizard" category="_YRa5PnHmEfCAZJ6UDgwwcw">
    <parameters xmi:id="_YRa1aHHmEfCAZJ6UDgwwcw" elementId="trigger" name="trigger"/>
  </commands>
  <commands xmi:id="_YRa1aXHmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.navigate.addToWorkingSet" commandName="Add to Working Set" description="Adds the selected object to a working set." category="_YRa5JHHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa1anHmEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.team.Revert" commandName="Revert Commit" category="_YRa5VXHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa1a3HmEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.wikitext.ui.editor.showCheatSheetCommand" commandName="Show Markup Cheat Sheet" category="_YRa5PnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa1bHHmEfCAZJ6UDgwwcw" elementId="org.eclipse.team.ui.TeamSynchronizingPerspective" commandName="Team Synchronizing" description="Open the Team Synchronizing Perspective" category="_YRa5V3HmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa1bXHmEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.search.findrefs.workingset" commandName="References in Working Set" description="Searches for references to the selected element in a working set" category="_YRa5Q3HmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa1bnHmEfCAZJ6UDgwwcw" elementId="org.eclipse.epp.mpc.ui.command.showInstalled" commandName="Manage installed plug-ins" description="Update or uninstall plug-ins installed from the Marketplace" category="_YRa5PnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa1b3HmEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.debug.application.command.debugNewExecutable" commandName="Debug New Executable" description="Debug a new executable" category="_YRa5UHHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa1cHHmEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.debug.ui.command.ungroupDebugContexts" commandName="Ungroup" description="Ungroups the selected debug contexts" category="_YRa5UHHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa1cXHmEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.context.ui.commands.task.clearContext" commandName="Clear Context" category="_YRa5KXHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa1cnHmEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.commands.ToggleLineBreakpoint" commandName="Toggle Line Breakpoint" description="Creates or removes a line breakpoint" category="_YRa5NXHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa1c3HmEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.tasks.ui.command.searchForTask" commandName="Search Repository for Task" category="_YRa5SXHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa1dHHmEfCAZJ6UDgwwcw" elementId="org.eclipse.tracecompass.tmf.ui.copy_to_clipboard" commandName="Copy to Clipboard" description="Copy to Clipboard" category="_YRa5OnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa1dXHmEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.debug.application.command.debugRemoteExecutable" commandName="Debug Remote Executable" description="Debug a Remote executable" category="_YRa5UHHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa1dnHmEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.debug.ui.command.castToArray" commandName="Cast To Type..." category="_YRa5PHHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa1d3HmEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.commit.UnifiedDiffCommand" commandName="Show Unified Diff" category="_YRa5PnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa1eHHmEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.internal.lttng2.ui.commands.control.disableLogger" commandName="Disable Logger" description="Disable Logger" category="_YRa5NHHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa1eXHmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.move" commandName="Move..." description="Move the selected item" category="_YRa5LnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa1enHmEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.internal.lttng2.ui.commands.control.delete" commandName="Delete" description="Delete Target Node" category="_YRa5NHHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa1e3HmEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.tmf.ui.views.uml2sd.handlers.MoveSDDown" commandName="Scroll down" description="Scroll down the sequence diagram" category="_YRa5VnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa1fHHmEfCAZJ6UDgwwcw" elementId="org.eclipse.tracecompass.tmf.analysis.xml.ui.managexmlanalyses" commandName="Manage XML analyses..." description="Manage XML files containing analysis information" category="_YRa5OnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa1fXHmEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.tasks.ui.command.maximizePart" commandName="Maximize Part" description="Maximize Part" category="_YRa5RHHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa1fnHmEfCAZJ6UDgwwcw" elementId="org.eclipse.compare.ignoreWhiteSpace" commandName="Ignore White Space" description="Ignore white space where applicable" category="_YRa5S3HmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa1f3HmEfCAZJ6UDgwwcw" elementId="org.eclipse.oomph.setup.editor.importProjects" commandName="Import Projects" category="_YRa5RnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa1gHHmEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.refactor.extract.function" commandName="Extract Function - Refactoring " description="Extracts a function for the selected list of expressions or statements" category="_YRa5MXHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa1gXHmEfCAZJ6UDgwwcw" elementId="org.eclipse.wst.xml.ui.disable.grammar.constraints" commandName="Turn off Grammar Constraints" description="Turn off grammar Constraints" category="_YRa5PnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa1gnHmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.navigate.goToResource" commandName="Go to Resource" description="Go to a particular resource in the active view" category="_YRa5SXHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa1g3HmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.window.resetPerspective" commandName="Reset Perspective" description="Reset the current perspective to its default state" category="_YRa5MHHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa1hHHmEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.wikitext.ui.quickOutlineCommand" commandName="Quick Outline" description="Open a popup dialog with a quick outline of the current document" category="_YRa5SXHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa1hXHmEfCAZJ6UDgwwcw" elementId="AnsiConsole.command.enable_disable" commandName="Enable / Disable ANSI Support" description="Enable / disable ANSI Support" category="_YRa5LHHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa1hnHmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.project.buildLast" commandName="Repeat Working Set Build" description="Repeat the last working set build" category="_YRa5TnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa1h3HmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.project.buildProject" commandName="Build Project" description="Build the selected project" category="_YRa5TnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa1iHHmEfCAZJ6UDgwwcw" elementId="org.eclipse.compare.switchLeftAndRight" commandName="Swap Left and Right View" description="Switch the left and right sides in the compare editor" category="_YRa5S3HmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa1iXHmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.goto.textStart" commandName="Text Start" description="Go to the beginning of the text" category="_YRa5J3HmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa1inHmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.paste" commandName="Paste" description="Paste from the clipboard" category="_YRa5JHHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa1i3HmEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.menu.updateUnresolvedIncludes" commandName="Re-resolve Unresolved Includes" category="_YRa5TnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa1jHHmEfCAZJ6UDgwwcw" elementId="org.eclipse.oomph.setup.editor.refreshCache" commandName="Refresh Remote Cache" category="_YRa5RnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa1jXHmEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.edit.text.c.toggle.comment" commandName="Toggle Comment" description="Toggle comment the selected lines" category="_YRa5Q3HmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa1jnHmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.navigate.previous" commandName="Previous" description="Navigate to the previous item" category="_YRa5SXHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa1j3HmEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.team.clean" commandName="Clean..." category="_YRa5VXHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa1kHHmEfCAZJ6UDgwwcw" elementId="org.eclipse.tracecompass.tmf.ui.command.open_as_experiment" commandName="Open As Experiment..." description="Open selected traces as an experiment" category="_YRa5LnHmEfCAZJ6UDgwwcw">
    <parameters xmi:id="_YRa1kXHmEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.tmf.ui.commandparameter.select_trace_type.type" name="Trace Type" optional="false"/>
  </commands>
  <commands xmi:id="_YRa1knHmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.rename" commandName="Rename" description="Rename the selected item" category="_YRa5LnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa1k3HmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.project.buildAll" commandName="Build All" description="Build all projects" category="_YRa5TnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa1lHHmEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.history.OpenInTextEditorCommand" commandName="Open in Text Editor" category="_YRa5PnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa1lXHmEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.valgrind.launch.clearMarkersCommand" commandName="Remove Valgrind Markers" description="Removes all Valgrind markers" category="_YRa5PnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa1lnHmEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.debug.ui.command.reverseStepOver" commandName="Reverse Step Over" description="Perform Reverse Step Over" category="_YRa5WXHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa1l3HmEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.internal.lttng2.ui.commands.control.save" commandName="Save..." description="Save session(s)" category="_YRa5NHHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa1mHHmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.cut.line.to.beginning" commandName="Cut to Beginning of Line" description="Cut to the beginning of a line of text" category="_YRa5J3HmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa1mXHmEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.debug.application.command.debugAttachedExecutable" commandName="Debug Attached Executable" description="Debug an attached executable" category="_YRa5UHHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa1mnHmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.editors.revisions.rendering.cycle" commandName="Cycle Revision Coloring Mode" description="Cycles through the available coloring modes for revisions" category="_YRa5J3HmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa1m3HmEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.search.findrefs.project" commandName="References in Project" description="Searches for references to the selected element in the enclosing project" category="_YRa5Q3HmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa1nHHmEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.search.finddecl.project" commandName="Declaration in Project" description="Searches for declarations of the selected element in the enclosing project" category="_YRa5Q3HmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa1nXHmEfCAZJ6UDgwwcw" elementId="org.eclipse.ltk.ui.refactoring.commands.moveResources" commandName="Move Resources" description="Move the selected resources and notify LTK participants." category="_YRa5WHHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa1nnHmEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.commands.StepInto" commandName="Step Into" description="Step into" category="_YRa5NXHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa1n3HmEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.changelog.core.actions.KeyActionCommand" commandName="Insert ChangeLog entry" description="Insert a ChangeLog entry" category="_YRa5KnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa1oHHmEfCAZJ6UDgwwcw" elementId="org.eclipse.e4.ui.importer.openDirectory" commandName="Open Projects from File System..." category="_YRa5PnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa1oXHmEfCAZJ6UDgwwcw" elementId="org.eclipse.wst.sse.ui.structure.select.previous" commandName="Select Previous Element" description="Expand selection to include previous sibling" category="_YRa5JHHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa1onHmEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.RepositoriesViewClearCredentials" commandName="Clear Credentials" category="_YRa5VXHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa1o3HmEfCAZJ6UDgwwcw" elementId="org.eclipse.tracecompass.tmf.ui.command.zoomout.selection" commandName="Zoom out (selection)" category="_YRa5OnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa1pHHmEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.debug.ui.memory.memorybrowser.jumpToMemory" commandName="Jump to Memory" description="Open memory view and add memory monitor for address" category="_YRa5NXHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa1pXHmEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.wikitext.ui.convertToMarkupCommand" commandName="Generate Markup" category="_YRa5PnHmEfCAZJ6UDgwwcw">
    <parameters xmi:id="_YRa1pnHmEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.wikitext.ui.targetLanguage" name="TargetLanguage" optional="false"/>
  </commands>
  <commands xmi:id="_YRa1p3HmEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.tasks.ui.command.showToolTip" commandName="Show Tooltip Description" category="_YRa5K3HmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa1qHHmEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.internal.lttng2.ui.commands.control.addContextOnChannel" commandName="Add Context..." description="Add Context to Channel(s) and/or Event(s)" category="_YRa5NHHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa1qXHmEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.context.ui.commands.task.copyContext" commandName="Copy Context" category="_YRa5KXHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa1qnHmEfCAZJ6UDgwwcw" elementId="org.eclipse.tm.terminal.view.ui.command.launchToolbar" commandName="Open Local Terminal on Selection" category="_YRa5SnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa1q3HmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.window.showViewMenu" commandName="Show View Menu" description="Show the view menu" category="_YRa5MHHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa1rHHmEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.team.Commit" commandName="Commit..." category="_YRa5VXHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa1rXHmEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.edit.text.c.source.quickMenu" commandName="Show Source Quick Menu" description="Shows the source quick menu" category="_YRa5Q3HmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa1rnHmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.shiftRight" commandName="Shift Right" description="Shift a block of text to the right" category="_YRa5JHHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa1r3HmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.navigate.backwardHistory" commandName="Backward History" description="Move backward in the editor navigation history" category="_YRa5SXHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa1sHHmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.removeTrailingWhitespace" commandName="Remove Trailing Whitespace" description="Removes the trailing whitespace of each line" category="_YRa5LnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa1sXHmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.findIncremental" commandName="Incremental Find" description="Incremental find" category="_YRa5JHHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa1snHmEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.ImportChangedProjectsCommandId" commandName="Import Changed Projects" description="Import or create in local Git repository" category="_YRa5VXHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa1s3HmEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.navigate.open.element.in.call.hierarchy" commandName="Open Element in Call Hierarchy" description="Open an element in the call hierarchy view" category="_YRa5SXHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa1tHHmEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.builds.ui.command.ShowBuildOutput" commandName="Show Build Output" category="_YRa5JnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa1tXHmEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.RepositoriesViewCollapseWorkingTree" commandName="Collapse Working Tree" category="_YRa5VXHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa1tnHmEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.changelog.core.prepareCommit" commandName="Prepare Commit" description="Copies latest changelog entry to clipboard" category="_YRa5KnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa1t3HmEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.RepositoriesViewNewRemote" commandName="Create Remote..." category="_YRa5VXHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa1uHHmEfCAZJ6UDgwwcw" elementId="org.eclipse.embedcdt.packs.ui.commands.updateCommand" commandName="Refresh" category="_YRa5MnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa1uXHmEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.commands.Restart" commandName="Restart" description="Restart a process or debug target without terminating and re-launching" category="_YRa5NXHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa1unHmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.delete.line" commandName="Delete Line" description="Delete a line of text" category="_YRa5J3HmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa1u3HmEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.internal.lttng2.ui.commands.control.executeScript" commandName="Execute Command Script..." description="Execute Command Script" category="_YRa5NHHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa1vHHmEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.tmf.ui.command.remove" commandName="Remove" category="_YRa5PnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa1vXHmEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.internal.lttng2.ui.commands.control.enableEventOnChannel" commandName="Enable Event..." description="Enable Event" category="_YRa5NHHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa1vnHmEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.gdbtrace.ui.command.project.trace.selectexecutable" commandName="Select Trace Executable..." description="Select executable binary file for a GDB trace" category="_YRa5PnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa1v3HmEfCAZJ6UDgwwcw" elementId="org.eclipse.oomph.setup.editor.performDropdown" commandName="Perform Dropdown" category="_YRa5RnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa1wHHmEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.internal.lttng2.ui.commands.control.start" commandName="Start" description="Start Trace Session" category="_YRa5NHHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa1wXHmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.findReplace" commandName="Find and Replace" description="Find and replace text" category="_YRa5JHHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa1wnHmEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.tmf.ui.openFile" commandName="Open File" description="Opens a file" category="_YRa5LnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa1w3HmEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.team.MergeTool" commandName="Merge Tool" category="_YRa5VXHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa1xHHmEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.command.shareProject" commandName="Share with Git" description="Share the project using Git" category="_YRa5PnHmEfCAZJ6UDgwwcw">
    <parameters xmi:id="_YRa1xXHmEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.command.projectNameParameter" name="Project" optional="false"/>
  </commands>
  <commands xmi:id="_YRa1xnHmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.window.fullscreenmode" commandName="Toggle Full Screen" description="Toggles the window between full screen and normal" category="_YRa5MHHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa1x3HmEfCAZJ6UDgwwcw" elementId="org.eclipse.equinox.p2.ui.sdk.installationDetails" commandName="Installation Details" category="_YRa5PnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa1yHHmEfCAZJ6UDgwwcw" elementId="org.eclipse.team.ui.synchronizeAll" commandName="Synchronize..." description="Synchronize resources in the workspace with another location" category="_YRa5QXHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa1yXHmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.window.nextEditor" commandName="Next Editor" description="Switch to the next editor" category="_YRa5MHHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa1ynHmEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.internal.reflog.CopyCommand" commandName="Copy Commit Id" category="_YRa5PnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa1y3HmEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.tasks.ui.command.new.subtask" commandName="New Subtask" category="_YRa5K3HmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa1zHHmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.help.helpContents" commandName="Help Contents" description="Open the help contents" category="_YRa5TXHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa1zXHmEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.team.Reset" commandName="Reset..." category="_YRa5VXHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa1znHmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.delete" commandName="Delete" description="Delete the selection" category="_YRa5JHHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa1z3HmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.delete.line.to.beginning" commandName="Delete to Beginning of Line" description="Delete to the beginning of a line of text" category="_YRa5J3HmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa10HHmEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.team.RenameBranch" commandName="Rename Branch..." category="_YRa5VXHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa10XHmEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.team.Synchronize" commandName="Synchronize" category="_YRa5VXHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa10nHmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.findPrevious" commandName="Find Previous" description="Find previous item" category="_YRa5JHHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa103HmEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.commands.ToggleBreakpoint" commandName="Toggle Breakpoint" description="Creates or removes a breakpoint" category="_YRa5NXHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa11HHmEfCAZJ6UDgwwcw" elementId="org.eclipse.launchbar.ui.command.buildActive" commandName="Build Active Launch Configuration" category="_YRa5VHHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa11XHmEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.tasks.ui.command.openTask" commandName="Open Task" category="_YRa5SXHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa11nHmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.scroll.lineUp" commandName="Scroll Line Up" description="Scroll up one line of text" category="_YRa5J3HmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa113HmEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.menu.findUnresolvedIncludes" commandName="Search for Unresolved Includes" category="_YRa5TnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa12HHmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.folding.toggle" commandName="Toggle Folding" description="Toggles folding in the current editor" category="_YRa5J3HmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa12XHmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.toggleShowWhitespaceCharacters" commandName="Show Whitespace Characters" description="Shows whitespace characters in current text editor" category="_YRa5J3HmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa12nHmEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.internal.lttng2.ui.commands.control.disableChannel" commandName="Disable Channel" description="Disable a Trace Channel" category="_YRa5NHHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa123HmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.gotoNextEditPosition" commandName="Next Edit Location" description="Next edit location" category="_YRa5SXHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa13HHmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.file.revert" commandName="Revert" description="Revert to the last saved state" category="_YRa5LnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa13XHmEfCAZJ6UDgwwcw" elementId="org.eclipse.oomph.ui.ToggleOfflineMode" commandName="Toggle Offline Mode" category="_YRa5PXHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa13nHmEfCAZJ6UDgwwcw" elementId="org.eclipse.oomph.setup.editor.openLog" commandName="Open Setup Log" category="_YRa5RnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa133HmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.project.buildAutomatically" commandName="Build Automatically" description="Toggle the workspace build automatically function" category="_YRa5TnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa14HHmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.file.import" commandName="Import" description="Import" category="_YRa5LnHmEfCAZJ6UDgwwcw">
    <parameters xmi:id="_YRa14XHmEfCAZJ6UDgwwcw" elementId="importWizardId" name="Import Wizard"/>
  </commands>
  <commands xmi:id="_YRa14nHmEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.history.Merge" commandName="Merge" category="_YRa5PnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa143HmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.window.switchToEditor" commandName="Switch to Editor" description="Switch to an editor" category="_YRa5MHHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa15HHmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.help.dynamicHelp" commandName="Show Context Help" description="Open the contextual help" category="_YRa5TXHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa15XHmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.toMultiSelection" commandName="To multi-selection" description="Turn current selection into multiple text selections" category="_YRa5JHHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa15nHmEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.tasks.ui.command.activateTask" commandName="Activate Task" category="_YRa5SXHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa153HmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.toggleInsertMode" commandName="Toggle Insert Mode" description="Toggle insert mode" category="_YRa5JHHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa16HHmEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.RepositoriesViewDelete" commandName="Delete Repository" category="_YRa5VXHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa16XHmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.file.closePart" commandName="Close Part" description="Close the active workbench part" category="_YRa5MHHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa16nHmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.editors.revisions.id.toggle" commandName="Toggle Revision Id Display" description="Toggles the display of the revision id" category="_YRa5J3HmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa163HmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.project.cleanAction" commandName="Build Clean" description="Discard old built state" category="_YRa5TnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa17HHmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.select.multiCaretDown" commandName="Multi caret down" description="Add a new caret/multi selection below the current line, or remove the first caret/multi selection " category="_YRa5J3HmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa17XHmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.navigate.back" commandName="Back" description="Navigate back" category="_YRa5SXHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa17nHmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.select.wordNext" commandName="Select Next Word" description="Select the next word" category="_YRa5J3HmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa173HmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.goto.pageDown" commandName="Page Down" description="Go down one page" category="_YRa5J3HmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa18HHmEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.builds.ui.commands.OpenBuildElement" commandName="Open Build Element" category="_YRa5JnHmEfCAZJ6UDgwwcw">
    <parameters xmi:id="_YRa18XHmEfCAZJ6UDgwwcw" elementId="element" name="Element"/>
  </commands>
  <commands xmi:id="_YRa18nHmEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.refactoring.command.ExtractConstant" commandName="Extract Constant..." category="_YRa5MXHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa183HmEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.team.ui.commands.CopyCommitMessage" commandName="Copy Commit Message for Task" description="Copies a commit message for the currently selected task to the clipboard." category="_YRa5K3HmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa19HHmEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.team.GarbageCollect" commandName="Collect Garbage" category="_YRa5VXHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa19XHmEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.history.CompareWithWorkingTree" commandName="Compare with Working Tree" category="_YRa5PnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa19nHmEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.team.Branch" commandName="Branch" description="Check out, rename, create, or delete a branch in a git repository" category="_YRa5VXHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa193HmEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.debug.ui.command.loadAllSymbols" commandName="Load Symbols For All" category="_YRa5PnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa1-HHmEfCAZJ6UDgwwcw" elementId="org.eclipse.wst.xml.ui.previousSibling" commandName="Previous Sibling" description="Go to Previous Sibling" category="_YRa5JHHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa1-XHmEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.edit.text.c.indent" commandName="Indent Line" description="Indents the current line" category="_YRa5Q3HmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa1-nHmEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.RepositoriesViewConfigurePush" commandName="Configure Push..." category="_YRa5VXHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa1-3HmEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.menu.createParserLog" commandName="Create Parser Log File" category="_YRa5TnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa1_HHmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.findIncrementalReverse" commandName="Incremental Find Reverse" description="Incremental find reverse" category="_YRa5JHHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa1_XHmEfCAZJ6UDgwwcw" elementId="org.eclipse.epp.mpc.ui.command.importFavoritesWizard" commandName="Import Marketplace Favorites" description="Import another user's Marketplace Favorites List" category="_YRa5PnHmEfCAZJ6UDgwwcw">
    <parameters xmi:id="_YRa1_nHmEfCAZJ6UDgwwcw" elementId="favoritesUrl" name="favoritesUrl"/>
  </commands>
  <commands xmi:id="_YRa1_3HmEfCAZJ6UDgwwcw" elementId="org.eclipse.tracecompass.tmf.ui.filter" commandName="Filter Time Graph events" category="_YRa5OnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa2AHHmEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.team.stash.apply" commandName="Apply Stashed Changes" category="_YRa5VXHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa2AXHmEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.edit.text.c.goto.next.bookmark" commandName="Next Bookmark" description="Goes to the next bookmark of the selected file" category="_YRa5Q3HmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa2AnHmEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.debug.ui.command.addRegisterGroup" commandName="Add RegisterGroup" description="Adds a Register Group" category="_YRa5KHHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa2A3HmEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.debug.ui.command.resumeWithoutSignal" commandName="Resume Without Signal" description="Resume Without Signal" category="_YRa5SHHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa2BHHmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.goto.columnNext" commandName="Next Column" description="Go to the next column" category="_YRa5J3HmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa2BXHmEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.history.Squash" commandName="Squash Commits" category="_YRa5PnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa2BnHmEfCAZJ6UDgwwcw" elementId="org.eclipse.search.ui.performTextSearchWorkingSet" commandName="Find Text in Working Set" description="Searches the files in the working set for specific text." category="_YRa5U3HmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa2B3HmEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.edit.text.c.add.include" commandName="Add Include" description="Create include statement on selection" category="_YRa5Q3HmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa2CHHmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.goto.wordNext" commandName="Next Word" description="Go to the next word" category="_YRa5J3HmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa2CXHmEfCAZJ6UDgwwcw" elementId="org.eclipse.oomph.setup.donate" commandName="Sponsor" description="Sponsor to the development of the Eclipse IDE" category="_YRa5PnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa2CnHmEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.internal.lttng2.ui.commands.control.assign.logger" commandName="Enable Logger..." description="Assign Logger to Session and Enable Logger" category="_YRa5NHHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa2C3HmEfCAZJ6UDgwwcw" elementId="org.eclipse.quickdiff.toggle" commandName="Quick Diff Toggle" description="Toggles quick diff information display on the line number ruler" category="_YRa5JHHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa2DHHmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.deleteNext" commandName="Delete Next" description="Delete the next character" category="_YRa5J3HmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa2DXHmEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.FetchGerritChange" commandName="Fetch From Gerrit" category="_YRa5VXHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa2DnHmEfCAZJ6UDgwwcw" elementId="org.eclipse.tracecompass.tmf.remote.ui.command.fetchlog" commandName="Fetch Remote Traces..." category="_YRa5OnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa2D3HmEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.team.ReplaceWithTheirs" commandName="Replace Conflicting Files with Their Revision" category="_YRa5VXHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa2EHHmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.toggleShowKeys" commandName="Toggle Show Key Bindings" description="Shows key binding when command is invoked" category="_YRa5MHHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa2EXHmEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.tasks.ui.command.SynchronizeAll" commandName="Synchronize Changed" category="_YRa5K3HmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa2EnHmEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.history.Reword" commandName="Reword Commit" category="_YRa5PnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa2E3HmEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.team.OpenCommit" commandName="Open Git Commit" category="_YRa5VXHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa2FHHmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.help.displayHelp" commandName="Display Help" description="Display a Help topic" category="_YRa5TXHmEfCAZJ6UDgwwcw">
    <parameters xmi:id="_YRa2FXHmEfCAZJ6UDgwwcw" elementId="href" name="Help topic href"/>
  </commands>
  <commands xmi:id="_YRa2FnHmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.select.pageDown" commandName="Select Page Down" description="Select to the bottom of the page" category="_YRa5J3HmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa2F3HmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.join.lines" commandName="Join Lines" description="Join lines of text" category="_YRa5J3HmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa2GHHmEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.dsf.ui.addRegistersExpression" commandName="Add Expression Group > Registers" category="_YRa5NXHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa2GXHmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.window.showContextMenu" commandName="Show Context Menu" description="Show the context menu" category="_YRa5MHHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa2GnHmEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.debug.ui.command.restoreRegisterGroups" commandName="Restore Default Register Groups" description="Restores the Default Register Groups" category="_YRa5KHHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa2G3HmEfCAZJ6UDgwwcw" elementId="org.eclipse.oomph.p2.ui.ExploreRepository" commandName="Explore Repository" category="_YRa5OHHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa2HHHmEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.internal.lttng2.ui.commands.control.disableEvent" commandName="Disable Event" description="Disable Event" category="_YRa5NHHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa2HXHmEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.team.InstallLfsLocal" commandName="Enable LFS locally" category="_YRa5VXHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa2HnHmEfCAZJ6UDgwwcw" elementId="org.eclipse.jdt.ui.edit.text.java.correction.assist.proposals" commandName="Quick Fix" description="Suggest possible fixes for a problem" category="_YRa5JHHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa2H3HmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.window.nextPerspective" commandName="Next Perspective" description="Switch to the next perspective" category="_YRa5MHHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa2IHHmEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.tasks.ui.command.UpdateRepositoryConfiguration" commandName="Update Repository Configuration" category="_YRa5PnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa2IXHmEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.edit.open.call.hierarchy" commandName="Open Call Hierarchy" description="Opens the call hierarchy for the selected element" category="_YRa5SXHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa2InHmEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.commands.console.clear" commandName="Clear Console" description="Clear Console" category="_YRa5JHHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa2I3HmEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.dsf.ui.addLocalsExpression" commandName="Add Expression Group > Local Variables" category="_YRa5NXHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa2JHHmEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.history.ShowVersions" commandName="Open this Version" category="_YRa5PnHmEfCAZJ6UDgwwcw">
    <parameters xmi:id="_YRa2JXHmEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.history.CompareMode" name="Compare mode"/>
  </commands>
  <commands xmi:id="_YRa2JnHmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.navigate.nextTab" commandName="Next Tab" description="Switch to the next tab" category="_YRa5SXHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa2J3HmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.window.quickAccess" commandName="Find Actions" description="Quickly access UI elements" category="_YRa5MHHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa2KHHmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.showInformation" commandName="Show Tooltip Description" description="Displays information for the current caret location in a focused hover" category="_YRa5J3HmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa2KXHmEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.tasks.ui.command.attachment.open" commandName="Open Attachment" category="_YRa5RHHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa2KnHmEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.dsf.debug.ui.disassembly.commands.gotoPC" commandName="Go to Program Counter" description="Navigate to current program counter" category="_YRa5NXHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa2K3HmEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.commands.toggleMemoryMonitorsPane" commandName="Toggle Memory Monitors Pane" description="Toggle visibility of the Memory Monitors Pane" category="_YRa5NXHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa2LHHmEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.team.Discard" commandName="Replace with File in Index" category="_YRa5VXHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa2LXHmEfCAZJ6UDgwwcw" elementId="org.eclipse.tracecompass.tmf.ui.command.left" commandName="Left" category="_YRa5OnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa2LnHmEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.RepositoriesViewCreateBranch" commandName="Create Branch..." category="_YRa5VXHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa2L3HmEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.RepositoriesViewCopyPath" commandName="Copy Path to Clipboard" category="_YRa5VXHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa2MHHmEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.builds.ui.command.NewTaskFromTest" commandName="New Task From Test" category="_YRa5JnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa2MXHmEfCAZJ6UDgwwcw" elementId="org.eclipse.oomph.setup.editor.perform.startup" commandName="Perform Setup Tasks (Startup)" category="_YRa5RnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa2MnHmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.showRulerAnnotationInformation" commandName="Show Ruler Annotation Tooltip" description="Displays annotation information for the caret line in a focused hover" category="_YRa5J3HmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa2M3HmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.browser.openBrowser" commandName="Open Browser" description="Opens the default web browser." category="_YRa5MHHmEfCAZJ6UDgwwcw">
    <parameters xmi:id="_YRa2NHHmEfCAZJ6UDgwwcw" elementId="url" name="URL"/>
    <parameters xmi:id="_YRa2NXHmEfCAZJ6UDgwwcw" elementId="browserId" name="Browser Id"/>
    <parameters xmi:id="_YRa2NnHmEfCAZJ6UDgwwcw" elementId="name" name="Browser Name"/>
    <parameters xmi:id="_YRa2N3HmEfCAZJ6UDgwwcw" elementId="tooltip" name="Browser Tooltip"/>
  </commands>
  <commands xmi:id="_YRa2OHHmEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.history.DeleteBranch" commandName="Delete Branch" category="_YRa5PnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa2OXHmEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.navigate.opentype" commandName="Open Element" description="Open an element in an Editor" category="_YRa5Q3HmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa2OnHmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.contentAssist.contextInformation" commandName="Context Information" description="Show Context Information" category="_YRa5JHHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa2O3HmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.file.saveAs" commandName="Save As" description="Save the current contents to another location" category="_YRa5LnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa2PHHmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.window.previousPerspective" commandName="Previous Perspective" description="Switch to the previous perspective" category="_YRa5MHHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa2PXHmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.window.splitEditor" commandName="Toggle Split Editor" description="Split or join the currently active editor." category="_YRa5MHHmEfCAZJ6UDgwwcw">
    <parameters xmi:id="_YRa2PnHmEfCAZJ6UDgwwcw" elementId="Splitter.isHorizontal" name="Orientation" optional="false"/>
  </commands>
  <commands xmi:id="_YRa2P3HmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.navigate.collapseAll" commandName="Collapse All" description="Collapse the current tree" category="_YRa5SXHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa2QHHmEfCAZJ6UDgwwcw" elementId="org.eclipse.compare.copyAllRightToLeft" commandName="Copy All from Right to Left" description="Copy All Changes from Right to Left" category="_YRa5S3HmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa2QXHmEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.tasks.ui.command.deactivateSelectedTask" commandName="Deactivate Selected Task" category="_YRa5SXHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa2QnHmEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.menu.updateWithModifiedFiles" commandName="Update Index with Modified Files" category="_YRa5TnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa2Q3HmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.window.lockToolBar" commandName="Toggle Lock Toolbars" description="Toggle the Lock on the Toolbars" category="_YRa5MHHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa2RHHmEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.tmf.ui.exportToText" commandName="Export to Text..." description="Export trace to text..." category="_YRa5OnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa2RXHmEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.commands.Disconnect" commandName="Disconnect" description="Disconnect" category="_YRa5NXHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa2RnHmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.navigate.removeFromWorkingSet" commandName="Remove From Working Set" description="Removes the selected object from a working set." category="_YRa5JHHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa2R3HmEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.tasks.ui.command.openSelectedTask" commandName="Open Selected Task" category="_YRa5K3HmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa2SHHmEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.RepositoriesLinkWithSelection" commandName="Toggle &quot;Link with Editor and Selection&quot; (Git Repositories View)" category="_YRa5VXHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa2SXHmEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.context.ui.commands.toggle.focus.active.view" commandName="Focus on Active Task" description="Toggle the focus on active task for the active view" category="_YRa5KXHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa2SnHmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.ide.deleteCompleted" commandName="Delete Completed Tasks" description="Delete the tasks marked as completed" category="_YRa5PnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa2S3HmEfCAZJ6UDgwwcw" elementId="org.eclipse.tracecompass.tmf.ui.command.analysis_add" commandName="Add External Analysis" description="Add External Analysis" category="_YRa5OnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa2THHmEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.tasks.ui.command.goToNextUnread" commandName="Go To Next Unread Task" category="_YRa5SXHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa2TXHmEfCAZJ6UDgwwcw" elementId="org.eclipse.wst.validation.ValidationCommand" commandName="Validate" description="Invoke registered Validators" category="_YRa5PnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa2TnHmEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.internal.lttng2.ui.commands.control.enableEventOnSession" commandName="Enable Event (default channel)..." description="Enable Event on Default Channel" category="_YRa5NHHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa2T3HmEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.RebaseInteractiveCurrent" commandName="Interactive Rebase" category="_YRa5PnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa2UHHmEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.tmf.ui.views.uml2sd.handlers.ShowNodeEnd" commandName="Show node end" description="Show the node end" category="_YRa5VnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa2UXHmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.window.openEditorDropDown" commandName="Quick Switch Editor" description="Open the editor drop down list" category="_YRa5MHHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa2UnHmEfCAZJ6UDgwwcw" elementId="AnsiConsole.command.copy_with_escapes" commandName="Copy Text With ANSI Escapes" description="Copy the console content to clipboard, including the escape sequences" category="_YRa5LHHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa2U3HmEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.tmf.ui.command.importtracepkg" commandName="Import Trace Package..." category="_YRa5OnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa2VHHmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.deleteNextWord" commandName="Delete Next Word" description="Delete the next word" category="_YRa5J3HmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa2VXHmEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.dsf.debug.ui.refreshAll" commandName="Refresh Debug Views" description="Refresh all data in debug views" category="_YRa5NXHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa2VnHmEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.SkipRebase" commandName="Skip commit and continue" category="_YRa5VXHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa2V3HmEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.history.SetQuickdiffBaseline" commandName="Set quickdiff baseline" category="_YRa5PnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa2WHHmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.undo" commandName="Undo" description="Undo the last operation" category="_YRa5JHHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa2WXHmEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.internal.lttng2.ui.commands.control.disconnect" commandName="Disconnect" description="Disconnect to Target Node" category="_YRa5NHHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa2WnHmEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.tmf.ui.command.managecustomparsers" commandName="Manage Custom Parsers..." description="Manage Custom Parsers" category="_YRa5P3HmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa2W3HmEfCAZJ6UDgwwcw" elementId="org.riscvstudio.ide.commands.Nuclei_SDK_Documentation" commandName="Nuclei SDK Documentation"/>
  <commands xmi:id="_YRa2XHHmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.file.newQuickMenu" commandName="New menu" description="Open the New menu" category="_YRa5LnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa2XXHmEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.actions.WatchCommand" commandName="Watch" description="Create a watch expression from the current selection and add it to the Expressions view" category="_YRa5PnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa2XnHmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.file.openWorkspace" commandName="Switch Workspace" description="Open the workspace selection dialog" category="_YRa5LnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa2X3HmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.file.closeAll" commandName="Close All" description="Close all editors" category="_YRa5LnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa2YHHmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.cut" commandName="Cut" description="Cut the selection to the clipboard" category="_YRa5JHHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa2YXHmEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.internal.lttng2.ui.commands.control.addContextOnEvent" commandName="Add Context..." description="Add Context to Channel(s) and/or Event(s)" category="_YRa5NHHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa2YnHmEfCAZJ6UDgwwcw" elementId="org.eclipse.tracecompass.tmf.ui.command.zoomout" commandName="Zoom out (mouse position)" category="_YRa5OnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa2Y3HmEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.team.Merge" commandName="Merge" category="_YRa5VXHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa2ZHHmEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.team.ReplaceWithRef" commandName="Replace with branch, tag, or reference" category="_YRa5VXHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa2ZXHmEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.tasks.ui.command.submitTask" commandName="Submit Task" description="Submits the currently open task" category="_YRa5RHHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa2ZnHmEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.edit.text.c.toggleMarkOccurrences" commandName="Toggle Mark Occurrences" description="Toggles mark occurrences in C/C++ editors" category="_YRa5Q3HmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa2Z3HmEfCAZJ6UDgwwcw" elementId="org.eclipse.wst.xml.ui.reload.dependencies" commandName="Reload Dependencies" description="Reload Dependencies" category="_YRa5PnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa2aHHmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.toggleShowSelectedElementOnly" commandName="Show Selected Element Only" description="Show Selected Element Only" category="_YRa5MHHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa2aXHmEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.edit.text.c.uncomment" commandName="Uncomment" description="Uncomments the selected // style comment lines" category="_YRa5Q3HmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa2anHmEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.internal.merge.ToggleCurrentChangesCommand" commandName="Ignore Changes from Ancestor to Current Version" description="Toggle ignoring changes only between the ancestor and the current version in a three-way merge comparison" category="_YRa5VXHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa2a3HmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.file.export" commandName="Export" description="Export" category="_YRa5LnHmEfCAZJ6UDgwwcw">
    <parameters xmi:id="_YRa2bHHmEfCAZJ6UDgwwcw" elementId="exportWizardId" name="Export Wizard"/>
  </commands>
  <commands xmi:id="_YRa2bXHmEfCAZJ6UDgwwcw" elementId="org.eclipse.tracecompass.tmf.ui.command.report_delete" commandName="Delete Report" description="Delete this report from the project" category="_YRa5OnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa2bnHmEfCAZJ6UDgwwcw" elementId="org.eclipse.embedcdt.debug.gdbjtag.restart.ui.commands.Restart" commandName="Restart" description="Restart a process or debug target without terminating and re-launching" category="_YRa5R3HmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa2b3HmEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.team.ApplyPatch" commandName="Apply Patch" category="_YRa5VXHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa2cHHmEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.make.ui.targetBuildCommand" commandName="Build Target Build" description="Invoke a make target build for the selected container." category="_YRa5TnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa2cXHmEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.edit.text.c.organize.includes" commandName="Organize Includes" description="Evaluates all required includes and replaces the current includes" category="_YRa5Q3HmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa2cnHmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.ide.copyConfigCommand" commandName="Copy Configuration Data To Clipboard" description="Copies the configuration data (system properties, installed bundles, etc) to the clipboard." category="_YRa5JHHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa2c3HmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.navigate.forwardHistory" commandName="Forward History" description="Move forward in the editor navigation history" category="_YRa5SXHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa2dHHmEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.tmf.ui.views.uml2sd.handlers.MoveSDUp" commandName="Scroll up" description="Scroll up the sequence diagram" category="_YRa5VnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa2dXHmEfCAZJ6UDgwwcw" elementId="org.eclipse.search.ui.performTextSearchProject" commandName="Find Text in Project" description="Searches the files in the project for specific text." category="_YRa5U3HmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa2dnHmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.project.rebuildAll" commandName="Rebuild All" description="Rebuild all projects" category="_YRa5TnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa2d3HmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.openLocalFile" commandName="Open File..." description="Open a file" category="_YRa5LnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa2eHHmEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.team.Disconnect" commandName="Disconnect" category="_YRa5VXHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa2eXHmEfCAZJ6UDgwwcw" elementId="org.eclipse.launchbar.ui.command.stop" commandName="Stop" category="_YRa5VHHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa2enHmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.file.refresh" commandName="Refresh" description="Refresh the selected items" category="_YRa5LnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa2e3HmEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.team.ReplaceWithOurs" commandName="Replace Conflicting Files with Our Revision" category="_YRa5VXHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa2fHHmEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.RepositoriesViewChangeCredentials" commandName="Change Credentials" category="_YRa5VXHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa2fXHmEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.edit.text.c.goto.matching.bracket" commandName="Go to Matching Bracket" description="Moves the cursor to the matching bracket" category="_YRa5Q3HmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa2fnHmEfCAZJ6UDgwwcw" elementId="org.eclipse.oomph.p2.ui.SearchRepositories" commandName="Search Repositories" category="_YRa5OHHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa2f3HmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.window.closeAllPerspectives" commandName="Close All Perspectives" description="Close all open perspectives" category="_YRa5MHHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa2gHHmEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.make.ui.targetBuildLastCommand" commandName="Rebuild Last Target" description="Rebuild the last make target for the selected container or project." category="_YRa5TnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa2gXHmEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.dsf.gdb.ui.command.osview.connect" commandName="Connect" description="Connect to selected processes" category="_YRa5PnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa2gnHmEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.commands.TerminateAll" commandName="Terminate/Disconnect All" description="Terminate/Disconnect All" category="_YRa5NXHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa2g3HmEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.managedbuilder.ui.convertTarget" commandName="Convert To" category="_YRa5TnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa2hHHmEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.internal.lttng2.ui.commands.control.enableChannelOnDomain" commandName="Enable Channel..." description="Enable a Trace Channel" category="_YRa5NHHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa2hXHmEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.debug.ui.command.loadSymbols" commandName="Load Symbols" category="_YRa5PnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa2hnHmEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.debug.ui.command.groupDebugContexts" commandName="Group" description="Groups the selected debug contexts" category="_YRa5UHHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa2h3HmEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.tasks.ui.command.disconnected" commandName="Disconnected" category="_YRa5PnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa2iHHmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.select.lineStart" commandName="Select Line Start" description="Select to the beginning of the line of text" category="_YRa5J3HmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa2iXHmEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.changelog.core.preparechangelog" commandName="Prepare Changelog" description="Prepares Changelog" category="_YRa5KnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa2inHmEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.commit.Reword" commandName="Reword Commit" category="_YRa5PnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa2i3HmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.editors.lineNumberToggle" commandName="Show Line Numbers" description="Toggle display of line numbers" category="_YRa5J3HmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa2jHHmEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.github.ui.command.createGist" commandName="Create Gist" description="Create Gist based on selection" category="_YRa5PnHmEfCAZJ6UDgwwcw">
    <parameters xmi:id="_YRa2jXHmEfCAZJ6UDgwwcw" elementId="publicGist" name="Public Gist"/>
  </commands>
  <commands xmi:id="_YRa2jnHmEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.edit.text.c.select.enclosing" commandName="Select Enclosing C/C++ Element" description="Expand the selection to enclosing C/C++ element" category="_YRa5JHHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa2j3HmEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.tasks.index.ui.command.ResetIndex" commandName="Refresh Search Index" category="_YRa5K3HmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa2kHHmEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.github.ui.command.rebasePullRequest" commandName="Rebase pull request" description="Rebase onto destination branch" category="_YRa5PnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa2kXHmEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.RepositoriesToggleBranchCommit" commandName="Toggle Latest Branch Commit" category="_YRa5VXHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa2knHmEfCAZJ6UDgwwcw" elementId="org.eclipse.tm.terminal.paste" commandName="Paste" category="_YRa5UXHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa2k3HmEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.history.PushCommit" commandName="Push Commit..." category="_YRa5PnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa2lHHmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.genericeditor.gotoMatchingBracket" commandName="Go to Matching Bracket" description="Moves the cursor to the matching bracket" category="_YRa5PnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa2lXHmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.ide.showInSystemExplorer" commandName="Show In (System Explorer)" description="Show in system's explorer (file manager)" category="_YRa5SXHmEfCAZJ6UDgwwcw">
    <parameters xmi:id="_YRa2lnHmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.ide.showInSystemExplorer.path" name="Resource System Path Parameter"/>
  </commands>
  <commands xmi:id="_YRa2l3HmEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.tasks.ui.command.new.local.task" commandName="New Local Task" category="_YRa5K3HmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa2mHHmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.select.lineDown" commandName="Select Line Down" description="Extend the selection to the next line of text" category="_YRa5J3HmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa2mXHmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.lowerCase" commandName="To Lower Case" description="Changes the selection to lower case" category="_YRa5J3HmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa2mnHmEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.team.CompareWithIndex" commandName="Compare with Index" category="_YRa5VXHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa2m3HmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.zoomOut" commandName="Zoom Out" description="Zoom out text, decrease default font size for text editors" category="_YRa5J3HmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa2nHHmEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.tasks.ui.command.markTaskIncomplete" commandName="Mark Task Incomplete" category="_YRa5K3HmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa2nXHmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.file.save" commandName="Save" description="Save the current contents" category="_YRa5LnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa2nnHmEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.tasks.ui.command.markTaskReadGoToNextUnread" commandName="Mark Task Read and Go To Next Unread Task" category="_YRa5K3HmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa2n3HmEfCAZJ6UDgwwcw" elementId="org.eclipse.team.ui.applyPatch" commandName="Apply Patch..." description="Apply a patch to one or more workspace projects." category="_YRa5QXHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa2oHHmEfCAZJ6UDgwwcw" elementId="org.eclipse.tracecompass.tmf.ui.command.trim_trace" commandName="Export Time Selection as New Trace..." description="Create a new trace containing only the events in the currently selected time range. Only available if the trace type supports it, and if a time range is selected." category="_YRa5PnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa2oXHmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.select.lineEnd" commandName="Select Line End" description="Select to the end of the line of text" category="_YRa5J3HmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa2onHmEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.tasks.ui.command.markTaskRead" commandName="Mark Task Read" category="_YRa5K3HmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa2o3HmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.select.wordPrevious" commandName="Select Previous Word" description="Select the previous word" category="_YRa5J3HmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa2pHHmEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.commands.StepOver" commandName="Step Over" description="Step over" category="_YRa5NXHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa2pXHmEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.debug.ui.command.reverseStepInto" commandName="Reverse Step Into" description="Perform Reverse Step Into" category="_YRa5WXHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa2pnHmEfCAZJ6UDgwwcw" elementId="org.eclipse.compare.selectPreviousChange" commandName="Select Previous Change" description="Select Previous Change" category="_YRa5S3HmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa2p3HmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.file.exit" commandName="Exit" description="Exit the application" category="_YRa5LnHmEfCAZJ6UDgwwcw">
    <parameters xmi:id="_YRa2qHHmEfCAZJ6UDgwwcw" elementId="mayPrompt" name="may prompt"/>
  </commands>
  <commands xmi:id="_YRa2qXHmEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.team.CompareWithHead" commandName="Compare with HEAD Revision" category="_YRa5VXHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa2qnHmEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.team.CompareWithCommit" commandName="Compare with Commit..." category="_YRa5VXHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa2q3HmEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.RepositoriesViewOpen" commandName="Open" category="_YRa5VXHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa2rHHmEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.tasks.ui.command.deactivateAllTasks" commandName="Deactivate Task" category="_YRa5SXHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa2rXHmEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.team.ShowHistory" commandName="Show in History" category="_YRa5VXHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa2rnHmEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.internal.lttng2.ui.commands.control.assign.event" commandName="Enable Event..." description="Assign Event to Session and Channel and Enable Event" category="_YRa5NHHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa2r3HmEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.team.SimplePush" commandName="Push to Upstream" category="_YRa5VXHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa2sHHmEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.commands.addMemoryMonitor" commandName="Add Memory Block" description="Add memory block" category="_YRa5NXHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa2sXHmEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.PushHeadToGerrit" commandName="Push Current Head to Gerrit" category="_YRa5VXHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa2snHmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.selectAll" commandName="Select All" description="Select all" category="_YRa5JHHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa2s3HmEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.tmf.ui.command.delete_suppl_files" commandName="Delete Supplementary Files..." category="_YRa5OnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa2tHHmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.project.rebuildProject" commandName="Rebuild Project" description="Rebuild the selected projects" category="_YRa5TnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa2tXHmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.copyLineDown" commandName="Copy Lines" description="Duplicates the selected lines and moves the selection to the copy" category="_YRa5J3HmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa2tnHmEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.menu.manage.configs.command" commandName="Manage Build Configurations" category="_YRa5TnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa2t3HmEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.refactor.extract.local.variable" commandName="Extract Local Variable - Refactoring " description="Extracts a local variable for the selected expression" category="_YRa5MXHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa2uHHmEfCAZJ6UDgwwcw" elementId="org.eclipse.tracecompass.tmf.ui.command.zoom.selection" commandName="Zoom to selection" category="_YRa5OnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa2uXHmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.select.selectMultiSelectionUp" commandName="Multi selection up relative to anchor selection" description="Search next matching region above and add it to the current selection, or remove last element from current multi-selection " category="_YRa5J3HmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa2unHmEfCAZJ6UDgwwcw" elementId="org.eclipse.oomph.setup.editor.perform" commandName="Perform Setup Tasks" category="_YRa5RnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa2u3HmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.help.installationDialog" commandName="Installation Information" description="Open the installation dialog" category="_YRa5TXHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa2vHHmEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.commands.ToggleStepFilters" commandName="Use Step Filters" description="Toggles enablement of debug step filters" category="_YRa5NXHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa2vXHmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.goto.lineUp" commandName="Line Up" description="Go up one line of text" category="_YRa5J3HmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa2vnHmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.goto.windowStart" commandName="Window Start" description="Go to the start of the window" category="_YRa5J3HmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa2v3HmEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.commons.ui.command.AddRepository" commandName="Add Repository" category="_YRa5O3HmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa2wHHmEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.commit.DiffEditorQuickOutlineCommand" commandName="Quick Outline" description="Show the quick outline for a unified diff" category="_YRa5VXHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa2wXHmEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.team.AssumeUnchanged" commandName="Assume Unchanged" category="_YRa5VXHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa2wnHmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.window.closePerspective" commandName="Close Perspective" description="Close the current perspective" category="_YRa5MHHmEfCAZJ6UDgwwcw">
    <parameters xmi:id="_YRa2w3HmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.window.closePerspective.perspectiveId" name="Perspective Id"/>
  </commands>
  <commands xmi:id="_YRa2xHHmEfCAZJ6UDgwwcw" elementId="org.eclipse.wst.xml.ui.nextSibling" commandName="Next Sibling" description="Go to Next Sibling" category="_YRa5JHHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa2xXHmEfCAZJ6UDgwwcw" elementId="org.eclipse.tracecompass.tmf.ui.command.zoomin" commandName="Zoom in (mouse position)" category="_YRa5OnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa2xnHmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.cheatsheets.openCheatSheetURL" commandName="Open Cheat Sheet from URL" description="Open a Cheat Sheet from file at a specified URL." category="_YRa5TXHmEfCAZJ6UDgwwcw">
    <parameters xmi:id="_YRa2x3HmEfCAZJ6UDgwwcw" elementId="cheatSheetId" name="Identifier" optional="false"/>
    <parameters xmi:id="_YRa2yHHmEfCAZJ6UDgwwcw" elementId="name" name="Name" optional="false"/>
    <parameters xmi:id="_YRa2yXHmEfCAZJ6UDgwwcw" elementId="url" name="URL" optional="false"/>
  </commands>
  <commands xmi:id="_YRa2ynHmEfCAZJ6UDgwwcw" elementId="org.eclipse.tracecompass.tmf.ui.command.right" commandName="Right" category="_YRa5OnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa2y3HmEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.team.PushBranch" commandName="Push Branch..." category="_YRa5VXHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa2zHHmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.file.closeAllSaved" commandName="Close All Saved" description="Close all saved editors" category="_YRa5LnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa2zXHmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.redo" commandName="Redo" description="Redo the last operation" category="_YRa5JHHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa2znHmEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.refactoring.command.ExtractLocalVariable" commandName="Extract Local Variable..." category="_YRa5MXHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa2z3HmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.navigate.selectWorkingSets" commandName="Select Working Sets" description="Select the working sets that are applicable for this window." category="_YRa5MHHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa20HHmEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.codan.commands.runCodanCommand" commandName="Run Code Analysis" category="_YRa5T3HmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa20XHmEfCAZJ6UDgwwcw" elementId="org.eclipse.tm.terminal.view.ui.command.newview" commandName="New Terminal View" category="_YRa5SnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa20nHmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.file.close" commandName="Close" description="Close the active editor" category="_YRa5LnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa203HmEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.excludeCommand" commandName="Exclude from Build" category="_YRa5PnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa21HHmEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.history.Reset" commandName="Reset..." category="_YRa5PnHmEfCAZJ6UDgwwcw">
    <parameters xmi:id="_YRa21XHmEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.history.ResetMode" name="Reset mode" optional="false"/>
  </commands>
  <commands xmi:id="_YRa21nHmEfCAZJ6UDgwwcw" elementId="org.riscvstudio.ide.commands.Nuclei_System_Technology_Homepage" commandName="Nuclei System Technology Homepage"/>
  <commands xmi:id="_YRa213HmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.navigate.previousSubTab" commandName="Previous Sub-Tab" description="Switch to the previous sub-tab" category="_YRa5SXHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa22HHmEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.internal.lttng2.ui.commands.control.newConnection" commandName="New Connection..." description="New Connection to Target Node" category="_YRa5NHHmEfCAZJ6UDgwwcw">
    <parameters xmi:id="_YRa22XHmEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.lttng2.control.ui.remoteServicesIdParameter" name="Remote Services ID"/>
    <parameters xmi:id="_YRa22nHmEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.lttng2.control.ui.connectionNameParameter" name="Connection Name"/>
  </commands>
  <commands xmi:id="_YRa223HmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.navigate.showIn" commandName="Show In" category="_YRa5SXHmEfCAZJ6UDgwwcw">
    <parameters xmi:id="_YRa23HHmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.navigate.showIn.targetId" name="Show In Target Id" optional="false"/>
  </commands>
  <commands xmi:id="_YRa23XHmEfCAZJ6UDgwwcw" elementId="sed.tabletree.collapseAll" commandName="Collapse All" category="_YRa5PnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa23nHmEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.tmf.ui.command.new_folder" commandName="New Folder..." description="Create a new trace folder" category="_YRa5OnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa233HmEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.RepositoriesViewRemoveRemote" commandName="Delete Remote" category="_YRa5VXHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa24HHmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.dialogs.openInputDialog" commandName="Open Input Dialog" description="Open an Input Dialog" category="_YRa5N3HmEfCAZJ6UDgwwcw">
    <parameters xmi:id="_YRa24XHmEfCAZJ6UDgwwcw" elementId="title" name="Title"/>
    <parameters xmi:id="_YRa24nHmEfCAZJ6UDgwwcw" elementId="message" name="Message"/>
    <parameters xmi:id="_YRa243HmEfCAZJ6UDgwwcw" elementId="initialValue" name="Initial Value"/>
    <parameters xmi:id="_YRa25HHmEfCAZJ6UDgwwcw" elementId="cancelReturns" name="Return Value on Cancel"/>
  </commands>
  <commands xmi:id="_YRa25XHmEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.internal.lttng2.ui.commands.control.load" commandName="Load..." description="Load session(s)" category="_YRa5NHHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa25nHmEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.RebaseCurrent" commandName="Rebase" category="_YRa5PnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa253HmEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.debug.ui.command.startTracing" commandName="Start Tracing " description="Start Tracing Experiment" category="_YRa5WnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa26HHmEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.tasks.ui.command.markTaskComplete" commandName="Mark Task Complete" category="_YRa5K3HmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa26XHmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.select.windowStart" commandName="Select Window Start" description="Select to the start of the window" category="_YRa5J3HmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa26nHmEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.tmf.ui.views.uml2sd.handlers.MoveSDLeft" commandName="Scroll left" description="Scroll left the sequence diagram" category="_YRa5VnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa263HmEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.RepositoriesViewImportProjects" commandName="Import Projects..." description="Import or create in local Git repository" category="_YRa5VXHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa27HHmEfCAZJ6UDgwwcw" elementId="org.eclipse.compare.copyAllLeftToRight" commandName="Copy All from Left to Right" description="Copy All Changes from Left to Right" category="_YRa5S3HmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa27XHmEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.dsf.gdb.ui.command.selectPreviousTraceRecord" commandName="Previous Trace Record" description="Select Previous Trace Record" category="_YRa5PnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa27nHmEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.make.ui.targetCreateCommand" commandName="Create Build Target" description="Create a new make build target for the selected container." category="_YRa5TnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa273HmEfCAZJ6UDgwwcw" elementId="org.eclipse.search.ui.openFileSearchPage" commandName="File Search" description="Open the Search dialog's file search page" category="_YRa5U3HmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa28HHmEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.RepositoriesViewAddRepository" commandName="Add a Git Repository..." description="Adds an existing Git repository to the Git Repositories view" category="_YRa5VXHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa28XHmEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.tasks.ui.command.task.clearActiveTime" commandName="Clear Active Time" category="_YRa5K3HmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa28nHmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.ide.copyBuildIdCommand" commandName="Copy Build Id Information To Clipboard" description="Copies the build identification information to the clipboard." category="_YRa5JHHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa283HmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.select.textEnd" commandName="Select Text End" description="Select to the end of the text" category="_YRa5J3HmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa29HHmEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.context.ui.commands.task.attachContext" commandName="Attach Context" category="_YRa5KXHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa29XHmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.goto.wordPrevious" commandName="Previous Word" description="Go to the previous word" category="_YRa5J3HmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa29nHmEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.debug.ui.command.connect" commandName="Connect" description="Connect to a process" category="_YRa5UHHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa293HmEfCAZJ6UDgwwcw" elementId="org.eclipse.tm.terminal.connector.local.command.launch" commandName="Open Local Terminal on Selection" category="_YRa5SnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa2-HHmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.window.preferences" commandName="Preferences" description="Open the preferences dialog" category="_YRa5MHHmEfCAZJ6UDgwwcw">
    <parameters xmi:id="_YRa2-XHmEfCAZJ6UDgwwcw" elementId="preferencePageId" name="Preference Page"/>
  </commands>
  <commands xmi:id="_YRa2-nHmEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.commit.Squash" commandName="Squash Commits" category="_YRa5PnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa2-3HmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.copy" commandName="Copy" description="Copy the selection to the clipboard" category="_YRa5JHHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa2_HHmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.window.nextView" commandName="Next View" description="Switch to the next view" category="_YRa5MHHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa2_XHmEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.specific_content_assist.command" commandName="C/C++ Content Assist" description="A parameterizable command that invokes content assist with a single completion proposal category" category="_YRa5JHHmEfCAZJ6UDgwwcw">
    <parameters xmi:id="_YRa2_nHmEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.specific_content_assist.category_id" name="type" optional="false"/>
  </commands>
  <commands xmi:id="_YRa2_3HmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.revertToSaved" commandName="Revert to Saved" description="Revert to the last saved state" category="_YRa5JHHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa3AHHmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.views.properties.NewPropertySheetCommand" commandName="Properties" category="_YRa5PnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa3AXHmEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.commands.RunToLine" commandName="Run to Line" description="Resume and break when execution reaches the current line" category="_YRa5NXHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa3AnHmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.showChangeRulerInformation" commandName="Show Quick Diff Ruler Tooltip" description="Displays quick diff or revision information for the caret line in a focused hover" category="_YRa5J3HmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa3A3HmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.upperCase" commandName="To Upper Case" description="Changes the selection to upper case" category="_YRa5J3HmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa3BHHmEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.team.ConfigureFetch" commandName="Configure Upstream Fetch" category="_YRa5VXHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa3BXHmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.navigate.goInto" commandName="Go Into" description="Navigate into the selected item" category="_YRa5SXHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa3BnHmEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.commands.OpenRunConfigurations" commandName="Run..." description="Open run launch configuration dialog" category="_YRa5NXHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa3B3HmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.select.windowEnd" commandName="Select Window End" description="Select to the end of the window" category="_YRa5J3HmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa3CHHmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.window.minimizePart" commandName="Minimize Active View or Editor" description="Minimizes the active view or editor" category="_YRa5MHHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa3CXHmEfCAZJ6UDgwwcw" elementId="org.eclipse.remote.ui.command.openConnection" commandName="Open Connection" category="_YRa5PnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa3CnHmEfCAZJ6UDgwwcw" elementId="org.eclipse.tm.terminal.command1" commandName="Terminal view insert" category="_YRa5UXHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa3C3HmEfCAZJ6UDgwwcw" elementId="org.riscvstudio.ide.newProject" commandName="newProject" category="_YRa5UnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa3DHHmEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.edit.open.outline" commandName="Show outline" description="Shows outline" category="_YRa5Q3HmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa3DXHmEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.team.Untrack" commandName="Untrack" category="_YRa5VXHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa3DnHmEfCAZJ6UDgwwcw" elementId="org.eclipse.wst.sse.ui.add.block.comment" commandName="Add Block Comment" description="Add Block Comment" category="_YRa5JHHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa3D3HmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.window.showSystemMenu" commandName="Show System Menu" description="Show the system menu" category="_YRa5MHHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa3EHHmEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.menu.rebuildIndex" commandName="Rebuild Index" category="_YRa5TnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa3EXHmEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.history.OpenInCommitViewerCommand" commandName="Open in Commit Viewer" description="Opens selected commit(s) in Commit Viewer(s)" category="_YRa5PnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa3EnHmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.window.savePerspective" commandName="Save Perspective As" description="Save the current perspective" category="_YRa5MHHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa3E3HmEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.debug.ui.command.stopTracing" commandName="Stop Tracing " description="Stop Tracing Experiment" category="_YRa5WnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa3FHHmEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.tmf.ui.command.clear_offset" commandName="Clear Time Offset" description="Clear time offset" category="_YRa5OnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa3FXHmEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.command.configureTrace" commandName="Configure Git Debug Trace" category="_YRa5VXHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa3FnHmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.navigate.linkWithEditor" commandName="Toggle Link with Editor" description="Toggles linking of a view's selection with the active editor's selection" category="_YRa5SXHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa3F3HmEfCAZJ6UDgwwcw" elementId="org.eclipse.wst.sse.ui.open.file.from.source" commandName="Open Selection" description="Open an editor on the selected link" category="_YRa5JHHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa3GHHmEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.builds.ui.command.ShowTestResults" commandName="Show Test Results" category="_YRa5JnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa3GXHmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.toggleBlockSelectionMode" commandName="Toggle Block Selection" description="Toggle block / column selection in the current text editor" category="_YRa5JHHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa3GnHmEfCAZJ6UDgwwcw" elementId="org.eclipse.wst.sse.ui.goto.matching.bracket" commandName="Matching Character" description="Go to Matching Character" category="_YRa5SXHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa3G3HmEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.FetchGiteaPullRequest" commandName="Fetch Gitea Pull Request" category="_YRa5VXHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa3HHHmEfCAZJ6UDgwwcw" elementId="org.eclipse.tracecompass.tmf.ui.timegraph.bookmark" commandName="Toggle Bookmark..." category="_YRa5OnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa3HXHmEfCAZJ6UDgwwcw" elementId="org.eclipse.remote.ui.command.openTerminal" commandName="Open Command Shell" category="_YRa5PnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa3HnHmEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.team.CompareIndexWithHead" commandName="Compare File in Index with HEAD Revision" category="_YRa5VXHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa3H3HmEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.wikitext.ui.convertToDocbookCommand" commandName="Generate Docbook" category="_YRa5PnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa3IHHmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.part.nextPage" commandName="Next Page" description="Switch to the next page" category="_YRa5SXHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa3IXHmEfCAZJ6UDgwwcw" elementId="org.riscvstudio.ide.commands.Nuclei_Documentation" commandName="NMSIS Documentation"/>
  <commands xmi:id="_YRa3InHmEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.edit.text.c.remove.block.comment" commandName="Remove Block Comment" description="Removes the block comment enclosing the selection" category="_YRa5Q3HmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa3I3HmEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.refactor.extract.constant" commandName="Extract Constant - Refactoring " description="Extracts a constant for the selected expression" category="_YRa5MXHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa3JHHmEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.RepositoriesViewClone" commandName="Clone a Git Repository..." description="Clones a Git repository and adds the clone to the Git Repositories view" category="_YRa5VXHmEfCAZJ6UDgwwcw">
    <parameters xmi:id="_YRa3JXHmEfCAZJ6UDgwwcw" elementId="repositoryUri" name="Repository URI"/>
  </commands>
  <commands xmi:id="_YRa3JnHmEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.edit.open.include.browser" commandName="Open Include Browser" description="Open an include browser on the selected element" category="_YRa5SXHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa3J3HmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.clear.mark" commandName="Clear Mark" description="Clear the mark" category="_YRa5J3HmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa3KHHmEfCAZJ6UDgwwcw" elementId="org.eclipse.wst.sse.ui.quick_outline" commandName="Quick Outline" description="Show the quick outline for the editor input" category="_YRa5SXHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa3KXHmEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.debug.application.command.debugCore" commandName="Debug Core File" description="Debug a corefile" category="_YRa5UHHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa3KnHmEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.RepositoriesViewRemove" commandName="Remove Repository" category="_YRa5VXHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa3K3HmEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.github.ui.command.mergePullRequest" commandName="Merge pull request" description="Merge into destination branch" category="_YRa5PnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa3LHHmEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.RepositoriesViewCreateRepository" commandName="Create a Git Repository..." description="Creates a new Git repository and adds it to the Git Repositories view" category="_YRa5VXHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa3LXHmEfCAZJ6UDgwwcw" elementId="org.eclipse.text.quicksearch.commands.quicksearchCommand" commandName="Quick Search" category="_YRa5L3HmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa3LnHmEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.debug.ui.command.editRegisterGroup" commandName="Edit Register Group" description="Edits a Register Group" category="_YRa5KHHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa3L3HmEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.team.PushTags" commandName="Push Tags..." category="_YRa5VXHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa3MHHmEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.changelog.core.formatChangeLog" commandName="Format ChangeLog" description="Formats ChangeLog" category="_YRa5KnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa3MXHmEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.ContinueRebase" commandName="Continue Rebase" category="_YRa5VXHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa3MnHmEfCAZJ6UDgwwcw" elementId="org.eclipse.help.ui.closeTray" commandName="Close User Assistance Tray" description="Close the user assistance tray containing context help information and cheat sheets." category="_YRa5TXHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa3M3HmEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.history.CreatePatch" commandName="Create Patch..." category="_YRa5PnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa3NHHmEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.managedbuilder.ui.rebuildConfigurations" commandName="Build Selected Configurations" category="_YRa5QHHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa3NXHmEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.commands.Resume" commandName="Resume" description="Resume" category="_YRa5NXHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa3NnHmEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.context.ui.commands.open.context.dialog" commandName="Show Context Quick View" description="Show Context Quick View" category="_YRa5KXHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa3N3HmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.file.restartWorkbench" commandName="Restart" description="Restart the workbench" category="_YRa5LnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa3OHHmEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.team.CompareWithRef" commandName="Compare with Branch, Tag or Reference..." category="_YRa5VXHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa3OXHmEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.context.ui.commands.attachment.retrieveContext" commandName="Retrieve Context Attachment" category="_YRa5KXHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa3OnHmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.toggleOverwrite" commandName="Toggle Overwrite" description="Toggle overwrite mode" category="_YRa5J3HmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa3O3HmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.folding.collapse_all" commandName="Collapse All" description="Collapses all folded regions" category="_YRa5J3HmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa3PHHmEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.tasks.ui.command.RefreshRepositoryTasks" commandName="Synchronize Changed" category="_YRa5PnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa3PXHmEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.commands.ToggleWatchpoint" commandName="Toggle Watchpoint" description="Creates or removes a watchpoint" category="_YRa5NXHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa3PnHmEfCAZJ6UDgwwcw" elementId="org.eclipse.equinox.p2.ui.discovery.commands.ShowRepositoryCatalog" commandName="Show Repository Catalog" category="_YRa5PnHmEfCAZJ6UDgwwcw">
    <parameters xmi:id="_YRa3P3HmEfCAZJ6UDgwwcw" elementId="org.eclipse.equinox.p2.ui.discovery.commands.RepositoryParameter" name="P2 Repository URI"/>
  </commands>
  <commands xmi:id="_YRa3QHHmEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.commands.closeRendering" commandName="Close Rendering" description="Close the selected rendering." category="_YRa5NXHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa3QXHmEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.RepositoriesViewOpenInEditor" commandName="Open in Editor" category="_YRa5VXHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa3QnHmEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.menu.wsselection.command" commandName="Manage Working Sets" category="_YRa5TnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa3Q3HmEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.tmf.ui.views.uml2sd.handlers.MoveSDRight" commandName="Scroll right" description="Scroll right the sequence diagram" category="_YRa5VnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa3RHHmEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.team.submodule.update" commandName="Update Submodule" category="_YRa5VXHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa3RXHmEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.commands.ProfileLast" commandName="Profile" description="Launch in profile mode" category="_YRa5NXHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa3RnHmEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.team.Pull" commandName="Pull" category="_YRa5VXHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa3R3HmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.swap.mark" commandName="Swap Mark" description="Swap the mark with the cursor position" category="_YRa5J3HmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa3SHHmEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.context.ui.commands.interest.increment" commandName="Make Landmark" description="Make Landmark" category="_YRa5KXHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa3SXHmEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.edit.opencview" commandName="Show in C/C++ Project view" description="Shows the selected resource in the C/C++ Project view" category="_YRa5Q3HmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa3SnHmEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.tmf.ui.command.analysis_help" commandName="Help" description="Help" category="_YRa5OnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa3S3HmEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.RepositoriesViewConfigureFetch" commandName="Configure Fetch..." category="_YRa5VXHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa3THHmEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.RepositoriesViewShowInSystemExplorer" commandName="Show In System Explorer" category="_YRa5VXHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa3TXHmEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.navigate.open.type.in.hierarchy" commandName="Open Type in Hierarchy" description="Open a type in the type hierarchy view" category="_YRa5SXHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa3TnHmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.shiftLeft" commandName="Shift Left" description="Shift a block of text to the left" category="_YRa5JHHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa3T3HmEfCAZJ6UDgwwcw" elementId="org.eclipse.wst.sse.ui.structure.select.next" commandName="Select Next Element" description="Expand selection to include next sibling" category="_YRa5JHHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa3UHHmEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.team.Push" commandName="Push..." category="_YRa5VXHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa3UXHmEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.debug.command.breakpointProperties" commandName="C/C++ Breakpoint Properties" description="View and edit properties for a given C/C++ breakpoint" category="_YRa5NXHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa3UnHmEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.RepositoriesCreateGroup" commandName="Create a Repository Group" description="Create a repository group for structuring repositories in the Git Repositories view" category="_YRa5VXHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa3U3HmEfCAZJ6UDgwwcw" elementId="org.riscvstudio.ide.commands.Nuclei_Studio_User_Guide" commandName="Nuclei Studio User Guide"/>
  <commands xmi:id="_YRa3VHHmEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.debug.ui.command.uncall" commandName="Uncall" description="Perform Uncall" category="_YRa5WXHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa3VXHmEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.make.ui.edit.text.makefile.opendecl" commandName="Open declaration" description="Follow to the directive definition" category="_YRa5M3HmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa3VnHmEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.team.stash.drop" commandName="Delete Stashed Commit..." category="_YRa5VXHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa3V3HmEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.internal.lttng2.ui.commands.control.refresh" commandName="Refresh" description="Refresh Node Configuration" category="_YRa5NHHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa3WHHmEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.DebugPerspective" commandName="Debug" description="Open the debug perspective" category="_YRa5V3HmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa3WXHmEfCAZJ6UDgwwcw" elementId="AnsiConsole.command.copy_without_escapes" commandName="Copy Text Without ANSI Escapes" description="Copy the console content to clipboard, removing the escape sequences" category="_YRa5LHHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa3WnHmEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.internal.lttng2.ui.commands.control.enableChannelOnSession" commandName="Enable Channel..." description="Enable a Trace Channel" category="_YRa5NHHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa3W3HmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.navigate.expandAll" commandName="Expand All" description="Expand the current tree" category="_YRa5SXHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa3XHHmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.file.saveAll" commandName="Save All" description="Save all current contents" category="_YRa5LnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa3XXHmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.file.closeOthers" commandName="Close Others" description="Close all editors except the one that is active" category="_YRa5LnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa3XnHmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.editors.quickdiff.revertLine" commandName="Revert Line" description="Revert the current line" category="_YRa5J3HmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa3X3HmEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.commands.OpenDebugConfigurations" commandName="Debug..." description="Open debug launch configuration dialog" category="_YRa5NXHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa3YHHmEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.team.SimpleFetch" commandName="Fetch from Upstream" category="_YRa5VXHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa3YXHmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.window.previousEditor" commandName="Previous Editor" description="Switch to the previous editor" category="_YRa5MHHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa3YnHmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.zoomIn" commandName="Zoom In" description="Zoom in text, increase default font size for text editors" category="_YRa5J3HmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa3Y3HmEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.tmf.ui.import" commandName="Import..." description="Import traces into project" category="_YRa5PnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa3ZHHmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.ide.OpenMarkersView" commandName="Open Another" description="Open another view" category="_YRa5PnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa3ZXHmEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.commands.newRendering" commandName="New Rendering" description="Add a new rendering." category="_YRa5NXHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa3ZnHmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.project.closeUnrelatedProjects" commandName="Close Unrelated Projects" description="Close unrelated projects" category="_YRa5TnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa3Z3HmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.ide.markers.copyMarkerResourceQualifiedName" commandName="Copy Resource Qualified Name To Clipboard" description="Copies markers resource qualified name to the clipboard" category="_YRa5JHHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa3aHHmEfCAZJ6UDgwwcw" elementId="org.eclipse.search.ui.performTextSearchWorkspace" commandName="Find Text in Workspace" description="Searches the files in the workspace for specific text." category="_YRa5U3HmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa3aXHmEfCAZJ6UDgwwcw" elementId="org.eclipse.wst.sse.ui.remove.block.comment" commandName="Remove Block Comment" description="Remove Block Comment" category="_YRa5JHHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa3anHmEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.tmf.ui.command.exporttracepkg" commandName="Export Trace Package..." category="_YRa5OnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa3a3HmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.addBookmark" commandName="Add Bookmark" description="Add a bookmark" category="_YRa5JHHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa3bHHmEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.debug.ui.command.reverseToggle" commandName="Reverse Toggle" description="Toggle Reverse Debugging" category="_YRa5WXHmEfCAZJ6UDgwwcw">
    <parameters xmi:id="_YRa3bXHmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.commands.radioStateParameter" name="TraceMethod" optional="false"/>
  </commands>
  <commands xmi:id="_YRa3bnHmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.set.mark" commandName="Set Mark" description="Set the mark" category="_YRa5J3HmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa3b3HmEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.tasks.ui.command.goToPreviousUnread" commandName="Go To Previous Unread Task" category="_YRa5SXHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa3cHHmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.views.showView" commandName="Show View" description="Shows a particular view" category="_YRa5QnHmEfCAZJ6UDgwwcw">
    <parameters xmi:id="_YRa3cXHmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.views.showView.viewId" name="View"/>
    <parameters xmi:id="_YRa3cnHmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.views.showView.secondaryId" name="Secondary Id"/>
    <parameters xmi:id="_YRa3c3HmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.views.showView.makeFast" name="As FastView"/>
  </commands>
  <commands xmi:id="_YRa3dHHmEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.refactor.hide.method" commandName="Hide Member Function..." category="_YRa5MXHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa3dXHmEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.make.ui.edit.text.makefile.toggle.comment" commandName="Toggle Comment" description="Comment/uncomment selected lines with # style comments" category="_YRa5M3HmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa3dnHmEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.team.Ignore" commandName="Ignore" category="_YRa5VXHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa3d3HmEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.commit.Edit" commandName="Edit Commit" category="_YRa5PnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa3eHHmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.navigate.showResourceByPath" commandName="Show Resource in Navigator" description="Show a resource in the Navigator given its path" category="_YRa5SXHmEfCAZJ6UDgwwcw">
    <parameters xmi:id="_YRa3eXHmEfCAZJ6UDgwwcw" elementId="resourcePath" name="Resource Path" typeId="org.eclipse.ui.ide.resourcePath" optional="false"/>
  </commands>
  <commands xmi:id="_YRa3enHmEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.commands.SkipAllBreakpoints" commandName="Skip All Breakpoints" description="Sets whether or not any breakpoint should suspend execution" category="_YRa5NXHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa3e3HmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.delimiter.windows" commandName="Convert Line Delimiters to Windows (CRLF, \r\n, 0D0A, &#xa4;&#xb6;)" description="Converts the line delimiters to Windows (CRLF, \r\n, 0D0A, &#xa4;&#xb6;)" category="_YRa5LnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa3fHHmEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.internal.lttng2.ui.commands.control.createSession" commandName="Create Session..." description="Create a Trace Session" category="_YRa5NHHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa3fXHmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.findNext" commandName="Find Next" description="Find next item" category="_YRa5JHHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa3fnHmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.window.hidetrimbars" commandName="Toggle visibility of the window toolbars" description="Toggle the visibility of the toolbars of the current window" category="_YRa5MHHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa3f3HmEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.valgrind.launch.exportCommand" commandName="Export Valgrind Log Files" description="Exports Valgrind log output to a directory" category="_YRa5PnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa3gHHmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.navigate.nextSubTab" commandName="Next Sub-Tab" description="Switch to the next sub-tab" category="_YRa5SXHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa3gXHmEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.tasks.ui.command.addTaskRepository" commandName="Add Task Repository..." category="_YRa5K3HmEfCAZJ6UDgwwcw">
    <parameters xmi:id="_YRa3gnHmEfCAZJ6UDgwwcw" elementId="connectorKind" name="Repository Type"/>
  </commands>
  <commands xmi:id="_YRa3g3HmEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.edit.text.c.goto.next.member" commandName="Go to Next Member" description="Move the caret to the next member of the translation unit" category="_YRa5Q3HmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa3hHHmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.addTask" commandName="Add Task..." description="Add a task" category="_YRa5JHHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa3hXHmEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.builds.ui.commands.OpenBuildElementWithBrowser" commandName="Open Build with Browser" category="_YRa5JnHmEfCAZJ6UDgwwcw">
    <parameters xmi:id="_YRa3hnHmEfCAZJ6UDgwwcw" elementId="element" name="Element"/>
  </commands>
  <commands xmi:id="_YRa3h3HmEfCAZJ6UDgwwcw" elementId="org.eclipse.wst.sse.ui.structure.select.enclosing" commandName="Select Enclosing Element" description="Expand selection to include enclosing element" category="_YRa5JHHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa3iHHmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.cheatsheets.openCheatSheet" commandName="Open Cheat Sheet" description="Open a Cheat Sheet." category="_YRa5TXHmEfCAZJ6UDgwwcw">
    <parameters xmi:id="_YRa3iXHmEfCAZJ6UDgwwcw" elementId="cheatSheetId" name="Identifier"/>
  </commands>
  <commands xmi:id="_YRa3inHmEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.tasks.ui.viewSource.command" commandName="View Unformatted Text" category="_YRa5K3HmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa3i3HmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.moveLineDown" commandName="Move Lines Down" description="Moves the selected lines down" category="_YRa5J3HmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa3jHHmEfCAZJ6UDgwwcw" elementId="org.eclipse.equinox.p2.ui.sdk.update" commandName="Check for Updates" category="_YRa5PnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa3jXHmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.goto.textEnd" commandName="Text End" description="Go to the end of the text" category="_YRa5J3HmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa3jnHmEfCAZJ6UDgwwcw" elementId="org.eclipse.launchbar.ui.command.launchActive" commandName="Launch Active Launch Configuration" category="_YRa5VHHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa3j3HmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.part.previousPage" commandName="Previous Page" description="Switch to the previous page" category="_YRa5SXHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa3kHHmEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.edit.open.quick.type.hierarchy" commandName="Quick Type Hierarchy" description="Shows quick type hierarchy" category="_YRa5Q3HmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa3kXHmEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.hover.backwardMacroExpansion" commandName="Back" description="Steps backward in macro expansions" category="_YRa5Q3HmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa3knHmEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.tmf.ui.command.synchronize_traces" commandName="Synchronize Traces..." description="Synchronize 2 or more traces" category="_YRa5OnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa3k3HmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.project.closeProject" commandName="Close Project" description="Close the selected project" category="_YRa5TnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa3lHHmEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.team.PullWithOptions" commandName="Pull..." category="_YRa5VXHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa3lXHmEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.github.ui.command.checkoutPullRequest" commandName="Checkout Pull Request" description="Checkout pull request into topic branch" category="_YRa5PnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa3lnHmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.select.addAllMatchesToMultiSelection" commandName="Add all matches to multi-selection" description="Looks for all regions matching the current selection or identifier and adds them to a multi-selection " category="_YRa5J3HmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa3l3HmEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.debug.ui.command.debugNewExecutable" commandName="Debug New Executable" description="Debug a new executable" category="_YRa5UHHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa3mHHmEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.tasks.ui.command.markTaskUnread" commandName="Mark Task Unread" category="_YRa5K3HmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa3mXHmEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.dsf.gdb.ui.command.selectNextTraceRecord" commandName="Next Trace Record" description="Select Next Trace Record" category="_YRa5PnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa3mnHmEfCAZJ6UDgwwcw" elementId="org.eclipse.search.ui.performTextSearchFile" commandName="Find Text in File" description="Searches the files in the file for specific text." category="_YRa5U3HmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa3m3HmEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.internal.lttng2.ui.commands.control.enableLogger" commandName="Enable Logger" description="Enable Logger" category="_YRa5NHHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa3nHHmEfCAZJ6UDgwwcw" elementId="org.eclipse.wst.sse.ui.structure.select.last" commandName="Restore Last Selection" description="Restore last selection" category="_YRa5JHHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa3nXHmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.cut.line" commandName="Cut Line" description="Cut a line of text, or multiple lines when invoked again without interruption" category="_YRa5J3HmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa3nnHmEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.builds.ui.command.NewTaskFromBuild" commandName="New Task From Build" category="_YRa5JnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa3n3HmEfCAZJ6UDgwwcw" elementId="org.eclipse.tm.terminal.view.ui.command.launch" commandName="Open Terminal on Selection" category="_YRa5SnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa3oHHmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.folding.expand_all" commandName="Expand All" description="Expands all folded regions" category="_YRa5J3HmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa3oXHmEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.changelog.core.preparechangelog2" commandName="Prepare Changelog In Editor" description="Prepares ChangeLog in an editor" category="_YRa5KnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa3onHmEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.tmf.ui.command.select_traces" commandName="Select Traces..." description="Select Traces" category="_YRa5OnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa3o3HmEfCAZJ6UDgwwcw" elementId="org.eclipse.oomph.setup.editor.openEditorDropdown" commandName="Open Setup Editor" category="_YRa5RnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa3pHHmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.help.quickStartAction" commandName="Welcome" description="Show help for beginning users" category="_YRa5TXHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa3pXHmEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.hover.forwardMacroExpansion" commandName="Forward" description="Steps forward in macro expansions" category="_YRa5Q3HmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa3pnHmEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.team.ReplaceWithPrevious" commandName="Replace with Previous Revision" category="_YRa5VXHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa3p3HmEfCAZJ6UDgwwcw" elementId="org.eclipse.oomph.setup.ui.questionnaire" commandName="Configuration Questionnaire" description="Review the IDE's most fiercely contested preferences" category="_YRa5RnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa3qHHmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.window.hideShowEditors" commandName="Toggle Shared Area Visibility" description="Toggles the visibility of the shared area" category="_YRa5MHHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa3qXHmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.folding.restore" commandName="Reset Structure" description="Resets the folding structure" category="_YRa5J3HmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa3qnHmEfCAZJ6UDgwwcw" elementId="org.eclipse.help.ui.indexcommand" commandName="Index" description="Show Keyword Index" category="_YRa5TXHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa3q3HmEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.internal.lttng2.ui.commands.control.destroySession" commandName="Destroy Session..." description="Destroy a Trace Session" category="_YRa5NHHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa3rHHmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.navigate.forward" commandName="Forward" description="Navigate forward" category="_YRa5SXHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa3rXHmEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.team.CherryPick" commandName="Cherry Pick" category="_YRa5VXHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa3rnHmEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.FetchGitHubPR" commandName="Fetch GitHub Pull Request" category="_YRa5VXHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa3r3HmEfCAZJ6UDgwwcw" elementId="org.eclipse.launchbar.ui.command.configureActiveLaunch" commandName="Edit Active Launch Configuration" category="_YRa5VHHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa3sHHmEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.history.DeleteTag" commandName="&amp;Delete Tag" category="_YRa5PnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa3sXHmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.window.pinEditor" commandName="Pin Editor" description="Pin the current editor" category="_YRa5MHHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa3snHmEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.github.ui.command.fetchPullRequest" commandName="Fetch Pull Request Commits" description="Fetch commits from pull request" category="_YRa5PnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa3s3HmEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.refactor.override.methods" commandName="Override Methods..." description="Generates override methods for a selected class" category="_YRa5Q3HmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa3tHHmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.goto.pageUp" commandName="Page Up" description="Go up one page" category="_YRa5J3HmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa3tXHmEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.team.submodule.sync" commandName="Sync Submodule" category="_YRa5VXHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa3tnHmEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.team.DeleteBranch" commandName="Delete Branch" category="_YRa5VXHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa3t3HmEfCAZJ6UDgwwcw" elementId="org.eclipse.tm.terminal.copy" commandName="Copy" category="_YRa5UXHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa3uHHmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.goto.columnPrevious" commandName="Previous Column" description="Go to the previous column" category="_YRa5J3HmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa3uXHmEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.refactor.getters.and.setters" commandName="Generate Getters and Setters..." description="Generates getters and setters for a selected field" category="_YRa5Q3HmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa3unHmEfCAZJ6UDgwwcw" elementId="org.riscvstudio.ide.project.convert.commands" commandName="RISC-V Project Convert Tool" category="_YRa5THHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa3u3HmEfCAZJ6UDgwwcw" elementId="org.eclipse.compare.selectNextChange" commandName="Select Next Change" description="Select Next Change" category="_YRa5S3HmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa3vHHmEfCAZJ6UDgwwcw" elementId="org.eclipse.wst.xml.ui.generate.xml" commandName="XML File..." description="Generate a XML file from the selected DTD or Schema" category="_YRa5PnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa3vXHmEfCAZJ6UDgwwcw" elementId="org.eclipse.ltk.ui.refactoring.commands.renameResource" commandName="Rename Resource" description="Rename the selected resource and notify LTK participants." category="_YRa5WHHmEfCAZJ6UDgwwcw">
    <parameters xmi:id="_YRa3vnHmEfCAZJ6UDgwwcw" elementId="org.eclipse.ltk.ui.refactoring.commands.renameResource.newName.parameter.key" name="Selected resource's new name."/>
  </commands>
  <commands xmi:id="_YRa3v3HmEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.edit.open.quick.macro.explorer" commandName="Explore Macro Expansion" description="Opens a quick view for macro expansion exploration" category="_YRa5Q3HmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa3wHHmEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.team.Rebase" commandName="Rebase on" category="_YRa5VXHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa3wXHmEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.tasks.ui.command.previousTask" commandName="Previous Task Command" category="_YRa5PnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa3wnHmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.file.properties" commandName="Properties" description="Display the properties of the selected item" category="_YRa5LnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa3w3HmEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.internal.lttng2.ui.commands.control.addContextOnDomain" commandName="Add Context..." description="Add Context to Channel(s) and/or Event(s)" category="_YRa5NHHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa3xHHmEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.team.ReplaceWithHead" commandName="Replace with HEAD revision" category="_YRa5VXHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa3xXHmEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.refactor.toggle.function" commandName="Toggle Function - Refactoring " description="Toggles the implementation between header and implementation file" category="_YRa5MXHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa3xnHmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.activeContextInfo" commandName="Show activeContext Info" category="_YRa5MHHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa3x3HmEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.tasks.ui.command.markTaskReadGoToPreviousUnread" commandName="Mark Task Read and Go To Previous Unread Task" category="_YRa5K3HmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa3yHHmEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.cdt.ui.editor.folding.auto" commandName="Toggle Active Folding" description="Toggle Active Folding" category="_YRa5LXHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa3yXHmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.select.textStart" commandName="Select Text Start" description="Select to the beginning of the text" category="_YRa5J3HmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa3ynHmEfCAZJ6UDgwwcw" elementId="org.eclipse.wst.sse.ui.cleanup.document" commandName="Cleanup Document..." description="Cleanup document" category="_YRa5JHHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa3y3HmEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.builds.ui.command.ShowBuildOutput.url" commandName="Show Build Output" category="_YRa5PnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa3zHHmEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.team.ConfigurePush" commandName="Configure Upstream Push" category="_YRa5VXHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa3zXHmEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.debug.ui.command.reverseResume" commandName="Reverse Resume" description="Perform Reverse Resume" category="_YRa5WXHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa3znHmEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.menu.freshenAllFiles" commandName="Freshen All Files in Index" category="_YRa5TnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa3z3HmEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.command.nextpage" commandName="Next Page of Memory" description="Load next page of memory" category="_YRa5NXHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa30HHmEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.command.gotoaddress" commandName="Go to Address" description="Go to Address" category="_YRa5NXHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa30XHmEfCAZJ6UDgwwcw" elementId="org.eclipse.remote.ui.command.newConnection" commandName="New Connection" category="_YRa5PnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa30nHmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.window.maximizePart" commandName="Maximize Active View or Editor" description="Toggles maximize/restore state of active view or editor" category="_YRa5MHHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa303HmEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.edit.text.c.find.word" commandName="Find Word" description="Selects a word and find the next occurrence" category="_YRa5Q3HmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa31HHmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.window.newEditor" commandName="Clone Editor" description="Open another editor on the active editor's input" category="_YRa5MHHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa31XHmEfCAZJ6UDgwwcw" elementId="org.eclipse.wst.sse.ui.format" commandName="Format" category="_YRa5PnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa31nHmEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.tmf.ui.views.uml2sd.handlers.GoToMessage" commandName="Go to associated message" description="Go to the associated message" category="_YRa5VnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa313HmEfCAZJ6UDgwwcw" elementId="org.eclipse.search.ui.openSearchDialog" commandName="Open Search Dialog" description="Open the Search dialog" category="_YRa5U3HmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa32HHmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.contentAssist.proposals" commandName="Content Assist" description="Content Assist" category="_YRa5JHHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa32XHmEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.edit.text.c.select.previous" commandName="Select Previous C/C++ Element" description="Expand the selection to enclosing C/C++ element" category="_YRa5JHHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa32nHmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.navigate.up" commandName="Up" description="Navigate up one level" category="_YRa5SXHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa323HmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.window.activateEditor" commandName="Activate Editor" description="Activate the editor" category="_YRa5MHHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa33HHmEfCAZJ6UDgwwcw" elementId="sed.tabletree.expandAll" commandName="Expand All" category="_YRa5PnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa33XHmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.delete.line.to.end" commandName="Delete to End of Line" description="Delete to the end of a line of text" category="_YRa5J3HmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa33nHmEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.commands.Terminate" commandName="Terminate" description="Terminate" category="_YRa5NXHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa333HmEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.team.ShowRepositoriesView" commandName="Show Git Repositories View" category="_YRa5VXHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa34HHmEfCAZJ6UDgwwcw" elementId="org.eclipse.help.ui.ignoreMissingPlaceholders" commandName="Do not warn of missing documentation" description="Sets the help preferences to no longer report a warning about the current set of missing documents." category="_YRa5TXHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa34XHmEfCAZJ6UDgwwcw" elementId="org.eclipse.compare.compareWithOther" commandName="Compare With Other Resource" description="Compare resources, clipboard contents or editors" category="_YRa5S3HmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa34nHmEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.edit.text.rename.element" commandName="Rename - Refactoring " description="Renames the selected element" category="_YRa5MXHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa343HmEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.managedbuilder.ui.cleanFiles" commandName="Clean Selected File(s)" description="Deletes build output files for the selected source files" category="_YRa5QHHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa35HHmEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.team.CreatePatch" commandName="Create Patch..." category="_YRa5VXHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa35XHmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.editors.revisions.author.toggle" commandName="Toggle Revision Author Display" description="Toggles the display of the revision author" category="_YRa5J3HmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa35nHmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.goto.windowEnd" commandName="Window End" description="Go to the end of the window" category="_YRa5J3HmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa353HmEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.edit.text.c.goto.prev.member" commandName="Go to Previous Member" description="Move the caret to the previous member of the translation unit" category="_YRa5Q3HmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa36HHmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.perspectives.showPerspective" commandName="Show Perspective" description="Show a particular perspective" category="_YRa5V3HmEfCAZJ6UDgwwcw">
    <parameters xmi:id="_YRa36XHmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.perspectives.showPerspective.perspectiveId" name="Parameter"/>
    <parameters xmi:id="_YRa36nHmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.perspectives.showPerspective.newWindow" name="In New Window"/>
  </commands>
  <commands xmi:id="_YRa363HmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.goto.line" commandName="Go to Line" description="Go to a specified line of text" category="_YRa5SXHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa37HHmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.editors.quickdiff.revert" commandName="Revert Lines" description="Revert the current selection, block or deleted lines" category="_YRa5J3HmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa37XHmEfCAZJ6UDgwwcw" elementId="org.eclipse.wst.xml.ui.gotoMatchingTag" commandName="Matching Tag" description="Go to Matching Tag" category="_YRa5JHHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa37nHmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.select.lineUp" commandName="Select Line Up" description="Extend the selection to the previous line of text" category="_YRa5J3HmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa373HmEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.team.submodule.add" commandName="Add Submodule" category="_YRa5VXHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa38HHmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.cut.line.to.end" commandName="Cut to End of Line" description="Cut to the end of a line of text" category="_YRa5J3HmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa38XHmEfCAZJ6UDgwwcw" elementId="org.eclipse.compare.copyLeftToRight" commandName="Copy from Left to Right" description="Copy Current Change from Left to Right" category="_YRa5S3HmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa38nHmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.project.openProject" commandName="Open Project" description="Open a project" category="_YRa5TnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa383HmEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.history.ShowBlame" commandName="Show Revision Information" category="_YRa5PnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa39HHmEfCAZJ6UDgwwcw" elementId="org.riscvstudio.ide.commands.Nuclei_Studio_FAQ" commandName="Nuclei Studio FAQ"/>
  <commands xmi:id="_YRa39XHmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.project.properties" commandName="Properties" description="Display the properties of the selected item's project " category="_YRa5TnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa39nHmEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.edit.text.c.surround.with.quickMenu" commandName="Surround With Quick Menu" description="Shows the Surround With quick menu" category="_YRa5Q3HmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa393HmEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.history.CompareVersionsInTree" commandName="Compare in Tree" category="_YRa5PnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa3-HHmEfCAZJ6UDgwwcw" elementId="org.eclipse.epp.package.common.contribute" commandName="Contribute" description="Contribute to the development and success of the Eclipse IDE!" category="_YRa5PnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa3-XHmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.select.columnPrevious" commandName="Select Previous Column" description="Select the previous column" category="_YRa5J3HmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa3-nHmEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.github.ui.command.cloneGist" commandName="Clone Gist" description="Clone Gist into Git repository" category="_YRa5PnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa3-3HmEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.internal.lttng2.ui.commands.control.enableEventOnDomain" commandName="Enable Event (default channel)..." description="Enable Event on Default Channel" category="_YRa5NHHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa3_HHmEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.RepositoriesToggleBranchHierarchy" commandName="Toggle Branch Representation" category="_YRa5VXHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa3_XHmEfCAZJ6UDgwwcw" elementId="org.eclipse.ltk.ui.refactoring.commands.deleteResources" commandName="Delete Resources" description="Delete the selected resources and notify LTK participants." category="_YRa5WHHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa3_nHmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.file.print" commandName="Print" description="Print" category="_YRa5LnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa3_3HmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.select.selectMultiSelectionDown" commandName="Multi selection down relative to anchor selection  " description="Search next matching region and add it to the current selection, or remove first element from current multi-selection " category="_YRa5J3HmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa4AHHmEfCAZJ6UDgwwcw" elementId="org.eclipse.wst.sse.ui.format.active.elements" commandName="Format Active Elements" description="Format active elements" category="_YRa5JHHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa4AXHmEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.deleteConfigsCommand" commandName="Reset to Default" category="_YRa5PnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa4AnHmEfCAZJ6UDgwwcw" elementId="org.riscvstudio.ide.import.package" commandName="Export to Package Management" category="_YRa5NnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa4A3HmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.showRulerContextMenu" commandName="Show Ruler Context Menu" description="Show the context menu for the ruler" category="_YRa5MHHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa4BHHmEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.tasks.ui.command.task.clearOutgoing" commandName="Clear Outgoing Changes" category="_YRa5K3HmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa4BXHmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.folding.collapse" commandName="Collapse" description="Collapses the folded region at the current selection" category="_YRa5J3HmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa4BnHmEfCAZJ6UDgwwcw" elementId="org.eclipse.epp.mpc.ui.command.showFavorites" commandName="Eclipse Marketplace Favorites" description="Open Marketplace Favorites" category="_YRa5PnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa4B3HmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.smartEnterInverse" commandName="Insert Line Above Current Line" description="Adds a new line above the current line" category="_YRa5J3HmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa4CHHmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.window.spy" commandName="Show Contributing Plug-in" description="Shows contribution information for the currently selected element" category="_YRa5MHHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa4CXHmEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.edit.text.c.align.const" commandName="Align const qualifiers" description="Moves const qualifiers to the left or right of the type depending on the workspace preferences" category="_YRa5Q3HmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa4CnHmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.help.helpSearch" commandName="Help Search" description="Open the help search" category="_YRa5TXHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa4C3HmEfCAZJ6UDgwwcw" elementId="org.eclipse.launchbar.ui.command.openLaunchSelector" commandName="Open Launch Bar Config Selector" category="_YRa5VHHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa4DHHmEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.builds.ui.command.ShowTestResults.url" commandName="Show Test Results" category="_YRa5PnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa4DXHmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.goto.lineDown" commandName="Line Down" description="Go down one line of text" category="_YRa5J3HmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa4DnHmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.gotoLastEditPosition" commandName="Previous Edit Location" description="Previous edit location" category="_YRa5SXHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa4D3HmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.open.hyperlink" commandName="Open Hyperlink" description="Opens the hyperlink at the caret location or opens a chooser if more than one hyperlink is available" category="_YRa5J3HmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa4EHHmEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.wikitext.ui.convertToEclipseHelpCommand" commandName="Generate Eclipse Help (*.html and *-toc.xml)" category="_YRa5PnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa4EXHmEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.command.prevpage" commandName="Previous Page of Memory" description="Load previous page of memory" category="_YRa5NXHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa4EnHmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.ide.configureFilters" commandName="Filters..." description="Configure the filters to apply to the markers view" category="_YRa5PnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa4E3HmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.navigate.previousTab" commandName="Previous Tab" description="Switch to the previous tab" category="_YRa5SXHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa4FHHmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.dialogs.openMessageDialog" commandName="Open Message Dialog" description="Open a Message Dialog" category="_YRa5N3HmEfCAZJ6UDgwwcw">
    <parameters xmi:id="_YRa4FXHmEfCAZJ6UDgwwcw" elementId="title" name="Title"/>
    <parameters xmi:id="_YRa4FnHmEfCAZJ6UDgwwcw" elementId="message" name="Message"/>
    <parameters xmi:id="_YRa4F3HmEfCAZJ6UDgwwcw" elementId="imageType" name="Image Type Constant" typeId="org.eclipse.ui.dialogs.Integer"/>
    <parameters xmi:id="_YRa4GHHmEfCAZJ6UDgwwcw" elementId="defaultIndex" name="Default Button Index" typeId="org.eclipse.ui.dialogs.Integer"/>
    <parameters xmi:id="_YRa4GXHmEfCAZJ6UDgwwcw" elementId="buttonLabel0" name="First Button Label"/>
    <parameters xmi:id="_YRa4GnHmEfCAZJ6UDgwwcw" elementId="buttonLabel1" name="Second Button Label"/>
    <parameters xmi:id="_YRa4G3HmEfCAZJ6UDgwwcw" elementId="buttonLabel2" name="Third Button Label"/>
    <parameters xmi:id="_YRa4HHHmEfCAZJ6UDgwwcw" elementId="buttonLabel3" name="Fourth Button Label"/>
    <parameters xmi:id="_YRa4HXHmEfCAZJ6UDgwwcw" elementId="cancelReturns" name="Return Value on Cancel"/>
  </commands>
  <commands xmi:id="_YRa4HnHmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.genericeditor.findReferences" commandName="Find References" description="Find other code items referencing the current selected item." category="_YRa5PnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa4H3HmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.goto.lineEnd" commandName="Line End" description="Go to the end of the line of text" category="_YRa5J3HmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa4IHHmEfCAZJ6UDgwwcw" elementId="org.eclipse.embedcdt.packs.ui.commands.showPerspectiveCommand" commandName="Switch to CMSIS Packs Perspective" category="_YRa5MnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa4IXHmEfCAZJ6UDgwwcw" elementId="org.eclipse.wst.xml.ui.referencedFileErrors" commandName="Show Details..." description="Show Details..." category="_YRa5PnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa4InHmEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.commands.RunLast" commandName="Run" description="Launch in run mode" category="_YRa5NXHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa4I3HmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.externalTools.commands.OpenExternalToolsConfigurations" commandName="External Tools..." description="Open external tools launch configuration dialog" category="_YRa5NXHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa4JHHmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.deletePrevious" commandName="Delete Previous" description="Delete the previous character" category="_YRa5J3HmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa4JXHmEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.managedbuilder.ui.buildFiles" commandName="Build Selected File(s)" description="Rebuilds the selected source files" category="_YRa5QHHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa4JnHmEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.internal.lttng2.ui.commands.control.connect" commandName="Connect" description="Connect to Target Node" category="_YRa5NHHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa4J3HmEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.team.CompareWithPrevious" commandName="Compare with Previous Revision" category="_YRa5VXHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa4KHHmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.select.columnNext" commandName="Select Next Column" description="Select the next column" category="_YRa5J3HmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa4KXHmEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.tmf.ui.command.new_experiment" commandName="New..." description="Create Tracing Experiment" category="_YRa5OnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa4KnHmEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.commands.TerminateAndRelaunch" commandName="Terminate and Relaunch" description="Terminate and Relaunch" category="_YRa5NXHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa4K3HmEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.team.Tag" commandName="Create Tag..." category="_YRa5VXHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa4LHHmEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.team.NoAssumeUnchanged" commandName="No Assume Unchanged" category="_YRa5VXHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa4LXHmEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.debug.ui.commands.viewMemory" commandName="View Memory" description="View variable in memory view" category="_YRa5PnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa4LnHmEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.edit.text.c.select.next" commandName="Select Next C/C++ Element" description="Expand the selection to next C/C++ element" category="_YRa5JHHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa4L3HmEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.commands.RemoveAllBreakpoints" commandName="Remove All Breakpoints" description="Removes all breakpoints" category="_YRa5NXHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa4MHHmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.navigator.resources.nested.changeProjectPresentation" commandName="P&amp;rojects Presentation" category="_YRa5PnHmEfCAZJ6UDgwwcw">
    <parameters xmi:id="_YRa4MXHmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.navigator.resources.nested.enabled" name="&amp;Hierarchical"/>
    <parameters xmi:id="_YRa4MnHmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.commands.radioStateParameter" name="Nested Project view - Radio State" optional="false"/>
  </commands>
  <commands xmi:id="_YRa4M3HmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.window.showKeyAssist" commandName="Show Key Assist" description="Show the key assist dialog" category="_YRa5MHHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa4NHHmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.genericeditor.togglehighlight" commandName="Toggle Highlight" category="_YRa5PnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa4NXHmEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.managedbuilder.ui.cleanAllConfigurations" commandName="Clean All Configurations" category="_YRa5QHHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa4NnHmEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.team.AddToIndex" commandName="Add to Index" category="_YRa5VXHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa4N3HmEfCAZJ6UDgwwcw" elementId="org.eclipse.wst.sse.ui.toggle.comment" commandName="Toggle Comment" description="Toggle Comment" category="_YRa5JHHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa4OHHmEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.debug.ui.command.saveTraceData" commandName="Save Trace Data " description="Save Trace Data to File" category="_YRa5WnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa4OXHmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.help.tipsAndTricksAction" commandName="Tips and Tricks" description="Open the tips and tricks help page" category="_YRa5TXHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa4OnHmEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.internal.lttng2.ui.commands.control.stop" commandName="Stop" description="Stop Trace Session" category="_YRa5NHHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa4O3HmEfCAZJ6UDgwwcw" elementId="org.riscvstudio.ide.commands.sampleCommand" commandName="SDK Configuration Tools" category="_YRa5UnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa4PHHmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.smartEnter" commandName="Insert Line Below Current Line" description="Adds a new line below the current line" category="_YRa5J3HmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa4PXHmEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.tmf.ui.command.select_trace_type" commandName="Select Trace Type..." description="Select a trace type" category="_YRa5OnHmEfCAZJ6UDgwwcw">
    <parameters xmi:id="_YRa4PnHmEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.tmf.ui.commandparameter.select_trace_type.type" name="Trace Type" optional="false"/>
  </commands>
  <commands xmi:id="_YRa4P3HmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.goto.lineStart" commandName="Line Start" description="Go to the start of the line of text" category="_YRa5J3HmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa4QHHmEfCAZJ6UDgwwcw" elementId="org.eclipse.oomph.setup.editor.synchronizePreferences" commandName="Synchronize Preferences" category="_YRa5RnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa4QXHmEfCAZJ6UDgwwcw" elementId="org.eclipse.wst.xml.ui.cmnd.contentmodel.sych" commandName="Synch" category="_YRa5PnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa4QnHmEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.RepositoriesViewConfigureBranch" commandName="Configure Branch" category="_YRa5VXHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa4Q3HmEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.commands.Suspend" commandName="Suspend" description="Suspend" category="_YRa5NXHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa4RHHmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.hippieCompletion" commandName="Word Completion" description="Context insensitive completion" category="_YRa5JHHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa4RXHmEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.builds.ui.command.AbortBuild" commandName="Abort Build" category="_YRa5JnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa4RnHmEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.debug.ui.command.StepIntoSelection" commandName="Step Into Selection" description="Step into the current selected statement" category="_YRa5NXHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa4R3HmEfCAZJ6UDgwwcw" elementId="org.eclipse.remote.ui.command.deleteConnection" commandName="Delete Connection" category="_YRa5PnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa4SHHmEfCAZJ6UDgwwcw" elementId="org.eclipse.team.ui.synchronizeLast" commandName="Repeat last synchronization" description="Repeat the last synchronization" category="_YRa5QXHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa4SXHmEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.RepositoriesViewConfigureGerritRemote" commandName="Gerrit Configuration..." category="_YRa5VXHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa4SnHmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.ide.configureColumns" commandName="Configure Columns..." description="Configure the columns in the markers view" category="_YRa5PnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa4S3HmEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.team.ReplaceWithCommit" commandName="Replace with commit" category="_YRa5VXHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa4THHmEfCAZJ6UDgwwcw" elementId="org.eclipse.tm.terminal.quickaccess" commandName="Quick Access" category="_YRa5UXHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa4TXHmEfCAZJ6UDgwwcw" elementId="org.eclipse.tracecompass.tmf.ui.command.convert_project" commandName="Configure or convert to Tracing Project" description="Configure or convert project to tracing project" category="_YRa5OnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa4TnHmEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.commands.DebugLast" commandName="Debug" description="Launch in debug mode" category="_YRa5NXHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa4T3HmEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.wikitext.ui.convertToHtmlCommand" commandName="Generate HTML" category="_YRa5PnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa4UHHmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.window.previousView" commandName="Previous View" description="Switch to the previous view" category="_YRa5MHHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa4UXHmEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.internal.lttng2.ui.commands.control.import" commandName="Import..." description="Import Traces to LTTng Project" category="_YRa5NHHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa4UnHmEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.edit.open.type.hierarchy" commandName="Open Type Hierarchy" description="Open a type hierarchy on the selected element" category="_YRa5SXHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa4U3HmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.ide.markers.copyDescription" commandName="Copy Description To Clipboard" description="Copies markers description field to the clipboard" category="_YRa5JHHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa4VHHmEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.debug.ui.command.removeRegisterGroups" commandName="Remove Register Groups" description="Removes one or more Register Groups" category="_YRa5KHHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa4VXHmEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.refactor.implement.method" commandName="Implement Method - Source Generation " description="Implements a method for a selected method declaration" category="_YRa5Q3HmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa4VnHmEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.commands.DropToFrame" commandName="Drop to Frame" description="Drop to Frame" category="_YRa5NXHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa4V3HmEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.edit.text.c.add.block.comment" commandName="Add Block Comment" description="Encloses the selection with a block comment" category="_YRa5Q3HmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa4WHHmEfCAZJ6UDgwwcw" elementId="org.eclipse.tm.terminal.view.ui.command.disconnect" commandName="Disconnect Terminal" category="_YRa5SnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa4WXHmEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.team.ShowBlame" commandName="Show Revision Information" category="_YRa5VXHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa4WnHmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.folding.expand" commandName="Expand" description="Expands the folded region at the current selection" category="_YRa5J3HmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa4W3HmEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.debug.ui.command.castToType" commandName="Cast To Type..." category="_YRa5PHHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa4XHHmEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.tasks.ui.command.openRemoteTask" commandName="Open Remote Task" category="_YRa5SXHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa4XXHmEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.commands.nextMemoryBlock" commandName="Next Memory Monitor" description="Show renderings from next memory monitor." category="_YRa5NXHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa4XnHmEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.CompareWithEachOther" commandName="Compare with Each Other" description="Compare two files selected in the Compare Editor with each other." category="_YRa5S3HmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa4X3HmEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.context.ui.commands.task.retrieveContext" commandName="Retrieve Context" category="_YRa5KXHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa4YHHmEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.team.Fetch" commandName="Fetch" category="_YRa5VXHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa4YXHmEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.tmf.ui.command.offset_traces" commandName="Apply Time Offset..." description="Shift traces by a constant time offset" category="_YRa5OnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa4YnHmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.window.togglestatusbar" commandName="Toggle Statusbar" description="Toggle the visibility of the bottom status bar" category="_YRa5MHHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa4Y3HmEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.task.ui.editor.QuickOutline" commandName="Quick Outline" description="Show the quick outline for the editor input" category="_YRa5K3HmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa4ZHHmEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.edit.text.c.comment" commandName="Comment" description="Turns the selected lines into // style comments" category="_YRa5Q3HmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa4ZXHmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.select.multiCaretUp" commandName="Multi caret up" description="Add a new caret/multi selection above the current line, or remove the last caret/multi selection " category="_YRa5J3HmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa4ZnHmEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.commands.eof" commandName="EOF" description="Send end of file" category="_YRa5NXHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa4Z3HmEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.AbortRebase" commandName="Abort Rebase" category="_YRa5VXHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa4aHHmEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.managedbuilder.ui.buildAllConfigurations" commandName="Build All Configurations" category="_YRa5QHHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa4aXHmEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.tmf.ui.views.uml2sd.handlers.ShowNodeStart" commandName="Show node start " description="Show the node start" category="_YRa5VnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa4anHmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.navigate.showInQuickMenu" commandName="Show In..." description="Open the Show In menu" category="_YRa5SXHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa4a3HmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.copyLineUp" commandName="Duplicate Lines" description="Duplicates the selected lines and leaves the selection unchanged" category="_YRa5J3HmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa4bHHmEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.commands.ToggleMethodBreakpoint" commandName="Toggle Method Breakpoint" description="Creates or removes a method breakpoint" category="_YRa5NXHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa4bXHmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.navigate.next" commandName="Next" description="Navigate to the next item" category="_YRa5SXHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa4bnHmEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.tasks.bugs.commands.newTaskFromMarker" commandName="New Task from Marker..." description="Report as Bug from Marker" category="_YRa5PnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa4b3HmEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.context.ui.commands.focus.view" commandName="Focus View" category="_YRa5PnHmEfCAZJ6UDgwwcw">
    <parameters xmi:id="_YRa4cHHmEfCAZJ6UDgwwcw" elementId="viewId" name="View ID to Focus" optional="false"/>
  </commands>
  <commands xmi:id="_YRa4cXHmEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.internal.lttng2.ui.commands.control.snapshot" commandName="Record Snapshot" description="Record a snapshot" category="_YRa5NHHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa4cnHmEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.commands.StepReturn" commandName="Step Return" description="Step return" category="_YRa5NXHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa4c3HmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.browser.openBundleResource" commandName="Open Resource in Browser" description="Opens a bundle resource in the default web browser." category="_YRa5MHHmEfCAZJ6UDgwwcw">
    <parameters xmi:id="_YRa4dHHmEfCAZJ6UDgwwcw" elementId="plugin" name="Plugin"/>
    <parameters xmi:id="_YRa4dXHmEfCAZJ6UDgwwcw" elementId="path" name="Path"/>
  </commands>
  <commands xmi:id="_YRa4dnHmEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.search.finddecl" commandName="Declaration" description="Searches for declarations of the selected element in the workspace" category="_YRa5Q3HmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa4d3HmEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.internal.lttng2.ui.commands.control.enableEvent" commandName="Enable Event" description="Enable Event" category="_YRa5NHHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa4eHHmEfCAZJ6UDgwwcw" elementId="org.riscvstudio.ide.commands.sdkManageCommand" commandName="NPK Package Management" category="_YRa5OXHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa4eXHmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.help.aboutAction" commandName="About" description="Open the about dialog" category="_YRa5TXHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa4enHmEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.team.stash.create" commandName="Stash Changes..." category="_YRa5VXHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa4e3HmEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.debug.ui.command.restoreDefaultType" commandName="Restore Original Type" description="View and edit properties for a given C/C++ breakpoint" category="_YRa5PHHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa4fHHmEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.tasks.ui.command.activateSelectedTask" commandName="Activate Selected Task" category="_YRa5SXHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa4fXHmEfCAZJ6UDgwwcw" elementId="org.eclipse.wst.sse.ui.format.document" commandName="Format" description="Format selection" category="_YRa5JHHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa4fnHmEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.dsf.debug.ui.disassembly.commands.gotoAddress" commandName="Go to Address..." description="Navigate to address" category="_YRa5NXHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa4f3HmEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.edit.text.c.sort.lines" commandName="Sort Lines" description="Sort selected lines alphabetically" category="_YRa5Q3HmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa4gHHmEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.team.RemoveFromIndex" commandName="Remove from Index" category="_YRa5VXHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa4gXHmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.newWizard" commandName="New" description="Open the New item wizard" category="_YRa5LnHmEfCAZJ6UDgwwcw">
    <parameters xmi:id="_YRa4gnHmEfCAZJ6UDgwwcw" elementId="newWizardId" name="New Wizard"/>
  </commands>
  <commands xmi:id="_YRa4g3HmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.window.newWindow" commandName="New Window" description="Open another window" category="_YRa5MHHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa4hHHmEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.builds.ui.commands.CopyDetails" commandName="Copy Details" category="_YRa5JnHmEfCAZJ6UDgwwcw">
    <parameters xmi:id="_YRa4hXHmEfCAZJ6UDgwwcw" elementId="kind" name="Kind"/>
    <parameters xmi:id="_YRa4hnHmEfCAZJ6UDgwwcw" elementId="element" name="Element"/>
  </commands>
  <commands xmi:id="_YRa4h3HmEfCAZJ6UDgwwcw" elementId="org.eclipse.e4.ui.importer.configureProject" commandName="Configure and Detect Nested Projects..." category="_YRa5PnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa4iHHmEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.builds.ui.command.RunBuild" commandName="Run Build" category="_YRa5JnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa4iXHmEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.search.finddecl.workingset" commandName="Declaration in Working Set" description="Searches for declarations of the selected element in a working set" category="_YRa5Q3HmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa4inHmEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.history.CompareVersions" commandName="Compare with Each Other" category="_YRa5PnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa4i3HmEfCAZJ6UDgwwcw" elementId="org.eclipse.equinox.p2.ui.sdk.install" commandName="Install New Software..." category="_YRa5PnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa4jHHmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.window.customizePerspective" commandName="Customize Perspective" description="Customize the current perspective" category="_YRa5MHHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa4jXHmEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.context.ui.commands.interest.decrement" commandName="Make Less Interesting" description="Make Less Interesting" category="_YRa5KXHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa4jnHmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.externaltools.ExternalToolMenuDelegateToolbar" commandName="Run Last Launched External Tool" description="Runs the last launched external Tool" category="_YRa5NXHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa4j3HmEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.CheckoutCommand" commandName="Check Out" category="_YRa5VXHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa4kHHmEfCAZJ6UDgwwcw" elementId="org.eclipse.remote.ui.command.closeConnection" commandName="Close Connection" category="_YRa5PnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa4kXHmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.moveLineUp" commandName="Move Lines Up" description="Moves the selected lines up" category="_YRa5J3HmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa4knHmEfCAZJ6UDgwwcw" elementId="org.eclipse.equinox.p2.ui.discovery.commands.ShowBundleCatalog" commandName="Show Bundle Catalog" category="_YRa5PnHmEfCAZJ6UDgwwcw">
    <parameters xmi:id="_YRa4k3HmEfCAZJ6UDgwwcw" elementId="org.eclipse.equinox.p2.ui.discovery.commands.DirectoryParameter" name="Directory URL"/>
    <parameters xmi:id="_YRa4lHHmEfCAZJ6UDgwwcw" elementId="org.eclipse.equinox.p2.ui.discovery.commands.TagsParameter" name="Tags"/>
  </commands>
  <commands xmi:id="_YRa4lXHmEfCAZJ6UDgwwcw" elementId="org.eclipse.userstorage.ui.showPullDown" commandName="Show Pull Down Menu" category="_YRa5TXHmEfCAZJ6UDgwwcw">
    <parameters xmi:id="_YRa4lnHmEfCAZJ6UDgwwcw" elementId="intoolbar" name="In Tool Bar" optional="false"/>
  </commands>
  <commands xmi:id="_YRa4l3HmEfCAZJ6UDgwwcw" elementId="org.eclipse.tracecompass.tmf.ui.command.analysis_remove" commandName="Remove External Analysis" description="Remove External Analysis" category="_YRa5OnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa4mHHmEfCAZJ6UDgwwcw" elementId="org.eclipse.compare.copyRightToLeft" commandName="Copy from Right to Left" description="Copy Current Change from Right to Left" category="_YRa5S3HmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa4mXHmEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.commands.OpenProfileConfigurations" commandName="Profile..." description="Open profile launch configuration dialog" category="_YRa5NXHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa4mnHmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.ide.markCompleted" commandName="Mark Completed" description="Mark the selected tasks as completed" category="_YRa5PnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa4m3HmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.recenter" commandName="Recenter" description="Scroll cursor line to center, top and bottom" category="_YRa5J3HmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa4nHHmEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.builds.ui.commands.OpenBuildElementWithBrowser.url" commandName="Open Build with Browser" category="_YRa5PnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa4nXHmEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.edit.text.c.toggle.source.header" commandName="Toggle Source/Header" description="Toggles between corresponding source and header files" category="_YRa5Q3HmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa4nnHmEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.edit.text.c.format" commandName="Format" description="Formats Source Code" category="_YRa5Q3HmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa4n3HmEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.edit.text.c.select.last" commandName="Restore Last C/C++ Selection" description="Restore last selection in C/C++ editor" category="_YRa5JHHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa4oHHmEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.dsf.debug.ui.disassembly.commands.rulerToggleBreakpoint" commandName="Toggle Breakpoint" description="Toggle breakpoint in disassembly ruler" category="_YRa5NXHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa4oXHmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.edit.text.scroll.lineDown" commandName="Scroll Line Down" description="Scroll down one line of text" category="_YRa5J3HmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa4onHmEfCAZJ6UDgwwcw" elementId="org.eclipse.wst.sse.ui.search.find.occurrences" commandName="Occurrences in File" description="Find occurrences of the selection in the file" category="_YRa5JHHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa4o3HmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.ToggleCoolbarAction" commandName="Toggle Main Toolbar Visibility" description="Toggles the visibility of the window toolbar" category="_YRa5MHHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa4pHHmEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.edit.text.c.copy.qualified.name" commandName="Copy Qualified Name" description="Copy a fully qualified name to the system clipboard" category="_YRa5Q3HmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa4pXHmEfCAZJ6UDgwwcw" elementId="org.eclipse.wst.sse.ui.outline.customFilter" commandName="&amp;Filters" category="_YRa5PnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa4pnHmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.navigate.openResource" commandName="Open Resource" description="Open an editor on a particular resource" category="_YRa5SXHmEfCAZJ6UDgwwcw">
    <parameters xmi:id="_YRa4p3HmEfCAZJ6UDgwwcw" elementId="filePath" name="File Path" typeId="org.eclipse.ui.ide.resourcePath"/>
  </commands>
  <commands xmi:id="_YRa4qHHmEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.internal.lttng2.ui.commands.control.enableChannel" commandName="Enable Channel" description="Enable a Trace Channel" category="_YRa5NHHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa4qXHmEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.edit.opendecl" commandName="Open Declaration" description="Opens an editor on the selected element's declaration(s)" category="_YRa5Q3HmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa4qnHmEfCAZJ6UDgwwcw" elementId="org.riscvstudio.ide.project.convertall.commands" commandName="org.riscvstudio.ide.project.convertall.commands"/>
  <commands xmi:id="_YRa4q3HmEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.debugActionSet/org.eclipse.cdt.debug.ui.actions.ResumeAtLine" commandName="Resume at Line (C/C++)" category="_YRa5PnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa4rHHmEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.debugActionSet/org.eclipse.cdt.debug.ui.actions.MoveToLine" commandName="Move to Line (C/C++)" category="_YRa5PnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa4rXHmEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.debugActionSet/org.eclipse.cdt.debug.ui.actions.ToggleInstructionStepMode" commandName="Instruction Stepping Mode" category="_YRa5PnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa4rnHmEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.cdt.make.ui.updateActionSet/org.eclipse.cdt.make.ui.UpdateMakeAction" commandName="Update Old Make Project..." description="Update Old Make Project" category="_YRa5PnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa4r3HmEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.cdt.make.ui.makeTargetActionSet/org.eclipse.cdt.make.ui.actions.buildLastTargetAction" commandName="Rebuild Last Target" category="_YRa5PnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa4sHHmEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.cdt.make.ui.makeTargetActionSet/org.eclipse.cdt.make.ui.makeTargetAction" commandName="Build..." category="_YRa5PnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa4sXHmEfCAZJ6UDgwwcw" elementId="org.eclipse.ltk.ui.refactor.show.refactoring.history" commandName="History..." category="_YRa5PnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa4snHmEfCAZJ6UDgwwcw" elementId="org.eclipse.ltk.ui.refactor.create.refactoring.script" commandName="Create Script..." category="_YRa5PnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa4s3HmEfCAZJ6UDgwwcw" elementId="org.eclipse.ltk.ui.refactor.apply.refactoring.script" commandName="Apply Script..." category="_YRa5PnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa4tHHmEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.cdt.ui.SearchActionSet/org.eclipse.cdt.ui.actions.OpenCSearchPage" commandName="C/C++..." category="_YRa5PnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa4tXHmEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.cdt.ui.buildConfigActionSet/org.eclipse.cdt.ui.buildActiveConfigToolbarAction" commandName="Build Active Configuration" description="Build the active configurations of selected projects" category="_YRa5PnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa4tnHmEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.cdt.ui.buildConfigActionSet/org.eclipse.cdt.ui.buildConfigToolbarAction" commandName="Active Build Configuration" description="Manage configurations for the current project" category="_YRa5PnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa4t3HmEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.cdt.ui.CElementCreationActionSet/org.eclipse.cdt.ui.actions.NewTypeDropDown" commandName="Class..." description="New C++ Class" category="_YRa5PnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa4uHHmEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.cdt.ui.CElementCreationActionSet/org.eclipse.cdt.ui.actions.NewFileDropDown" commandName="Source File..." description="New C/C++ Source File" category="_YRa5PnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa4uXHmEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.cdt.ui.CElementCreationActionSet/org.eclipse.cdt.ui.actions.NewFolderDropDown" commandName="Source Folder..." description="New C/C++ Source Folder" category="_YRa5PnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa4unHmEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.cdt.ui.CElementCreationActionSet/org.eclipse.cdt.ui.actions.NewProjectDropDown" commandName="Project..." description="New C/C++ Project" category="_YRa5PnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa4u3HmEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.debug.ui.launchActionSet/org.eclipse.debug.internal.ui.actions.RunWithConfigurationAction" commandName="Run As" category="_YRa5PnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa4vHHmEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.debug.ui.launchActionSet/org.eclipse.debug.internal.ui.actions.RunHistoryMenuAction" commandName="Run History" category="_YRa5PnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa4vXHmEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.debug.ui.launchActionSet/org.eclipse.debug.internal.ui.actions.RunDropDownAction" commandName="Run" category="_YRa5PnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa4vnHmEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.debug.ui.launchActionSet/org.eclipse.debug.internal.ui.actions.DebugWithConfigurationAction" commandName="Debug As" category="_YRa5PnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa4v3HmEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.debug.ui.launchActionSet/org.eclipse.debug.internal.ui.actions.DebugHistoryMenuAction" commandName="Debug History" category="_YRa5PnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa4wHHmEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.debug.ui.launchActionSet/org.eclipse.debug.internal.ui.actions.DebugDropDownAction" commandName="Debug" category="_YRa5PnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa4wXHmEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.debug.ui.profileActionSet/org.eclipse.debug.internal.ui.actions.ProfileDropDownAction" commandName="Profile" category="_YRa5PnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa4wnHmEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.debug.ui.profileActionSet/org.eclipse.debug.internal.ui.actions.ProfileWithConfigurationAction" commandName="Profile As" category="_YRa5PnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa4w3HmEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.debug.ui.profileActionSet/org.eclipse.debug.internal.ui.actions.ProfileHistoryMenuAction" commandName="Profile History" category="_YRa5PnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa4xHHmEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.egit.ui.SearchActionSet/org.eclipse.egit.ui.actions.OpenCommitSearchPage" commandName="Git..." category="_YRa5PnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa4xXHmEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.mylyn.doc.actionSet/org.eclipse.mylyn.tasks.ui.bug.report" commandName="Report Bug or Enhancement..." description="Report Bug or Enhancement" category="_YRa5PnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa4xnHmEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.mylyn.tasks.ui.navigation.additions/org.eclipse.mylyn.tasks.ui.navigate.task.history" commandName="Activate Previous Task" description="Activate Previous Task" category="_YRa5PnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa4x3HmEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.ui.cheatsheets.actionSet/org.eclipse.ui.cheatsheets.actions.CheatSheetHelpMenuAction" commandName="Cheat Sheets..." category="_YRa5PnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa4yHHmEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.search.searchActionSet/org.eclipse.search.OpenSearchDialogPage" commandName="Search..." description="Search" category="_YRa5PnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa4yXHmEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.team.ui.actionSet/org.eclipse.team.ui.synchronizeAll" commandName="Synchronize..." description="Synchronize..." category="_YRa5PnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa4ynHmEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.team.ui.actionSet/org.eclipse.team.ui.ConfigureProject" commandName="Share Project..." description="Share the project with others using a version and configuration management system." category="_YRa5PnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa4y3HmEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.ui.externaltools.ExternalToolsSet/org.eclipse.ui.externaltools.ExternalToolMenuDelegateMenu" commandName="External Tools" category="_YRa5PnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa4zHHmEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.CEditor.BreakpointRulerActions/org.eclipse.cdt.debug.ui.CEditor.RulerTobbleBreakpointAction" commandName="%Dummy.label" category="_YRa5PnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa4zXHmEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.cdt.ui.editor.asm.AsmEditor.BreakpointRulerActions/org.eclipse.cdt.debug.ui.CEditor.RulerTobbleBreakpointAction" commandName="%Dummy.label" category="_YRa5PnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa4znHmEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.ui.texteditor.ruler.actions/org.eclipse.ui.texteditor.BookmarkRulerAction" commandName="dummy" category="_YRa5PnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa4z3HmEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.ui.texteditor.ruler.actions/org.eclipse.cdt.internal.ui.text.correction.CSelectRulerAction" commandName="dummy" category="_YRa5PnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa40HHmEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.ui.texteditor.ruler.actions/org.eclipse.ui.texteditor.SelectRulerAction" commandName="Text Editor Ruler Single-Click" category="_YRa5PnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa40XHmEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.core.runtime.xml.source.ruler.actions/org.eclipse.ui.texteditor.BookmarkRulerAction" commandName="Add Bookmark..." category="_YRa5PnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa40nHmEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.core.runtime.xml.source.ruler.actions/org.eclipse.ui.texteditor.SelectRulerAction" commandName="Select Ruler" category="_YRa5PnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa403HmEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.debugview.toolbar/org.eclipse.cdt.debug.internal.ui.actions.ToggleInstructionStepModeActionDelegate" commandName="Instruction Stepping Mode" description="Instruction Stepping Mode" category="_YRa5PnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa41HHmEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.debugView.menu/org.eclipse.cdt.debug.internal.ui.actions.ShowFullPathsAction" commandName="Show Full Paths" description="Show Full Paths" category="_YRa5PnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa41XHmEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.breakpointView.menu/org.eclipse.cdt.debug.internal.ui.actions.ShowFullPathsAction" commandName="Show Full Paths" description="Show Full Paths" category="_YRa5PnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa41nHmEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.breakpointView.menu/org.eclipse.cdt.debug.ui.addWatchpoint" commandName="Add Watchpoint (C/C++)..." description="Add Watchpoint (C/C++)" category="_YRa5PnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa413HmEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.breakpointView.menu/org.eclipse.cdt.debug.internal.ui.actions.AddEventBreakpointActionDelegate" commandName="Add Event Breakpoint (C/C++)..." description="Add Event Breakpoint (C/C++)" category="_YRa5PnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa42HHmEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.breakpointView.menu/org.eclipse.cdt.debug.ui.addFunctionBreakpoint" commandName="Add Function Breakpoint (C/C++)..." description="Add Function Breakpoint (C/C++)" category="_YRa5PnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa42XHmEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.breakpointView.menu/org.eclipse.cdt.debug.ui.addLineBreakpoint" commandName="Add Line Breakpoint (C/C++)..." description="Add Line Breakpoint (C/C++)" category="_YRa5PnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa42nHmEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.expression.toolbar/org.eclipse.pinclone.expression.pinDebugContext" commandName="Pin to Debug Context" category="_YRa5PnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa423HmEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.expression.toolbar/org.eclipse.pinclone.expression.clone" commandName="Open New View" category="_YRa5PnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa43HHmEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.variable.toolbar/org.eclipse.pinclone.variable.pinDebugContext" commandName="Pin to Debug Context" category="_YRa5PnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa43XHmEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.variable.toolbar/org.eclipse.pinclone.variable.clone" commandName="Open New View" category="_YRa5PnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa43nHmEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.register.toolbar/org.eclipse.pinclone.register.pinDebugContext" commandName="Pin to Debug Context" category="_YRa5PnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa433HmEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.register.toolbar/org.eclipse.pinclone.register.clone" commandName="Open New View" category="_YRa5PnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa44HHmEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.cdt.debug.ui.memory.floatingpoint.preferenceaction" commandName="Floating Point Rendering Preferences ..." description="Floating Point Rendering Preferences ..." category="_YRa5PnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa44XHmEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.memoryBrowser.toolbar/org.eclipse.pinclone.memoryBrowser.pinDebugContext" commandName="Pin to Debug Context" category="_YRa5PnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa44nHmEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.memoryBrowser.toolbar/org.eclipse.pinclone.memoryBrowser.clone" commandName="Open New View" category="_YRa5PnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa443HmEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.memory.memorybrowser.MemoryBrowser.clearExpressionList/org.eclipse.cdt.debug.ui.memory.memorybrowser.ClearExpressionListActionID" commandName="Clear Expressions" category="_YRa5PnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa45HHmEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.debug.ui.MemoryView.findNext/org.eclipse.cdt.debug.ui.memory.search.FindNextAction" commandName="Find Next" category="_YRa5PnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa45XHmEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.debug.ui.MemoryView.findReplace/org.eclipse.cdt.debug.ui.memory.search.FindAction" commandName="Find/Replace..." category="_YRa5PnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa45nHmEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.memory.memorybrowser.MemoryBrowser.findNext/org.eclipse.cdt.debug.ui.memory.search.FindNextAction" commandName="Find Next" category="_YRa5PnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa453HmEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.cdt.debug.ui.memory.traditional.preferenceaction" commandName="Traditional Rendering Preferences..." description="Traditional Rendering Preferences..." category="_YRa5PnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa46HHmEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.cdt.debug.ui.memory.transport.actions.ExportMemoryAction" commandName="Export" description="Export" category="_YRa5PnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa46XHmEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.cdt.debug.ui.memory.transport.actions.ImportMemoryAction" commandName="Import" description="Import" category="_YRa5PnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa46nHmEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.memory.memorybrowser.MemoryBrowser.toolbar/org.eclipse.cdt.debug.ui.memory.transport.actions.ExportMemoryAction" commandName="Export" description="Export" category="_YRa5PnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa463HmEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.memory.memorybrowser.MemoryBrowser.toolbar/org.eclipse.cdt.debug.ui.memory.transport.actions.ImportMemoryAction2" commandName="Import" description="Import" category="_YRa5PnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa47HHmEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.cdt.dsf.gdb.ui.debugsources.view.refresh/org.eclipse.cdt.dsf.gdb.ui.debugsources.view.refresh" commandName="Refresh" category="_YRa5PnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa47XHmEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.cdt.dsf.debug.ui.viewmodel.breakpoints.update.Refresh/org.eclipse.cdt.dsf.debug.ui.breakpoints.viewmodel.update.actions.refresh" commandName="Refresh" category="_YRa5PnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa47nHmEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.cdt.dsf.debug.ui.viewmodel.variables.update.Refresh/org.eclipse.cdt.dsf.debug.ui.variables.viewmodel.update.actions.refresh" commandName="Refresh" category="_YRa5PnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa473HmEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.cdt.dsf.debug.ui.viewmodel.registers.update.Refresh/org.eclipse.cdt.dsf.debug.ui.registers.viewmodel.update.actions.refresh" commandName="Refresh" category="_YRa5PnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa48HHmEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.cdt.dsf.debug.ui.viewmodel.expressions.update.Refresh/org.eclipse.cdt.dsf.debug.ui.expressions.viewmodel.update.actions.refresh" commandName="Refresh" category="_YRa5PnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa48XHmEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.cdt.dsf.debug.ui.viewmodel.debugview.update.Refresh/org.eclipse.cdt.dsf.debug.ui.debugview.viewmodel.update.actions.refresh" commandName="Refresh" category="_YRa5PnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa48nHmEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.disassembly.toolbar/org.eclipse.pinclone.disassembly.pinDebugContext" commandName="Pin to Debug Context" category="_YRa5PnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa483HmEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.disassembly.toolbar/org.eclipse.pinclone.disassembly.clone" commandName="Open New View" category="_YRa5PnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa49HHmEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.mylyn.cdt.ui.cview.contribution/org.eclipse.mylyn.cdt.ui.cview.focusActiveTask.action" commandName="Focus on Active Task" description="Focus only on elements in active Mylyn task" category="_YRa5PnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa49XHmEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.debug.ui.PulldownActions/org.eclipse.debug.ui.debugview.pulldown.ViewManagementAction" commandName="View Management..." category="_YRa5PnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa49nHmEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.debug.ui.debugview.toolbar/org.eclipse.debug.ui.debugview.toolbar.removeAllTerminated" commandName="Remove All Terminated" description="Remove All Terminated Launches" category="_YRa5PnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa493HmEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.debug.ui.debugview.toolbar/org.eclipse.debug.ui.debugview.toolbar.collapseAll" commandName="Collapse All" description="Collapse All" category="_YRa5PnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa4-HHmEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.debug.ui.breakpointsview.toolbar/org.eclipse.debug.ui.breakpointsView.toolbar.removeAll" commandName="Remove All" description="Remove All Breakpoints" category="_YRa5PnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa4-XHmEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.debug.ui.breakpointsview.toolbar/org.eclipse.debug.ui.breakpointsView.toolbar.linkWithDebugView" commandName="Link with Debug View" description="Link with Debug View" category="_YRa5PnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa4-nHmEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.debug.ui.breakpointsview.toolbar/org.eclipse.debug.ui.breakpointsView.toolbar.workingSets" commandName="Working Sets..." description="Manage Working Sets" category="_YRa5PnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa4-3HmEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.debug.ui.breakpointsview.toolbar/org.eclipse.debug.ui.breakpointsView.toolbar.clearDefaultBreakpointGroup" commandName="Deselect Default Working Set" description="Deselect Default Working Set" category="_YRa5PnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa4_HHmEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.debug.ui.breakpointsview.toolbar/org.eclipse.debug.ui.breakpointsView.toolbar.setDefaultBreakpointGroup" commandName="Select Default Working Set..." description="Select Default Working Set" category="_YRa5PnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa4_XHmEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.debug.ui.breakpointsview.toolbar/org.eclipse.debug.ui.breakpointsView.toolbar.sortByAction" commandName="Sort By" description="Sort By" category="_YRa5PnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa4_nHmEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.debug.ui.breakpointsview.toolbar/org.eclipse.debug.ui.breakpointsView.toolbar.groupByAction" commandName="Group By" description="Show" category="_YRa5PnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa4_3HmEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.debug.ui.expressionsView.toolbar/org.eclipse.debug.ui.expresssionsView.toolbar.removeAll" commandName="Remove All" description="Remove All Expressions" category="_YRa5PnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa5AHHmEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.debug.ui.expressionsView.toolbar/org.eclipse.debug.ui.expresssionsView.toolbar.AddWatchExpression" commandName="Add Watch Expression..." description="Create a new watch expression" category="_YRa5PnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa5AXHmEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.PinMemoryBlockAction" commandName="Pin Memory Monitor" description="Pin Memory Monitor" category="_YRa5PnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa5AnHmEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.NewMemoryViewAction" commandName="New Memory View" description="New Memory View" category="_YRa5PnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa5A3HmEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.togglemonitors" commandName="Toggle Memory Monitors Pane" description="Toggle Memory Monitors Pane" category="_YRa5PnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa5BHHmEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.linkrenderingpanes" commandName="Link Memory Rendering Panes" description="Link Memory Rendering Panes" category="_YRa5PnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa5BXHmEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.tablerendering.preferencesaction" commandName="Table Renderings Preferences..." description="&amp;Table Renderings Preferences..." category="_YRa5PnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa5BnHmEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.togglesplitpane" commandName="Toggle Split Pane" description="Toggle Split Pane" category="_YRa5PnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa5B3HmEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.switchMemoryBlock" commandName="Switch Memory Monitor" description="Switch Memory Monitor" category="_YRa5PnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa5CHHmEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.memoryViewPreferencesAction" commandName="Preferences..." description="&amp;Preferences..." category="_YRa5PnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa5CXHmEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.mylyn.context.ui.outline.contribution/org.eclipse.mylyn.context.ui.contentOutline.focus" commandName="Focus on Active Task" description="Focus on Active Task (Alt+click to reveal filtered elements)" category="_YRa5PnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa5CnHmEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.mylyn.ui.projectexplorer.filter/org.eclipse.mylyn.ide.ui.actions.focus.projectExplorer" commandName="Focus on Active Task" description="Focus on Active Task (Alt+click to reveal filtered elements)" category="_YRa5PnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa5C3HmEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.mylyn.ui.search.contribution/org.eclipse.mylyn.ide.ui.actions.focus.search.results" commandName="Focus on Active Task" description="Focus on Active Task (Alt+click to reveal filtered elements)" category="_YRa5PnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa5DHHmEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.mylyn.ui.resource.navigator.filter/org.eclipse.mylyn.ide.ui.actions.focus.resourceNavigator" commandName="Focus on Active Task" description="Focus on Active Task (Alt+click to reveal filtered elements)" category="_YRa5PnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa5DXHmEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.mylyn.problems.contribution/org.eclipse.mylyn.ide.ui.actions.focus.markers.problems" commandName="Focus on Active Task" description="Focus on Active Task" category="_YRa5PnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa5DnHmEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.mylyn.markers.all.contribution/org.eclipse.mylyn.ide.ui.actions.focus.markers.all" commandName="Focus on Active Task" description="Focus on Active Task" category="_YRa5PnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa5D3HmEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.mylyn.markers.tasks.contribution/org.eclipse.mylyn.ide.ui.actions.focus.markers.tasks" commandName="Focus on Active Task" description="Focus on Active Task" category="_YRa5PnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa5EHHmEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.mylyn.markers.bookmarks.contribution/org.eclipse.mylyn.ide.ui.actions.focus.markers.bookmarks" commandName="Focus on Active Task" description="Focus on Active Task" category="_YRa5PnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa5EXHmEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.mylyn.tasks.ui.actions.view/org.eclipse.mylyn.tasks.ui.search.open" commandName="Search Repository..." category="_YRa5PnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa5EnHmEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.mylyn.tasks.ui.actions.view/org.eclipse.mylyn.tasks.ui.synchronize.changed" commandName="Synchronize Changed" description="Synchronize Changed" category="_YRa5PnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa5E3HmEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.mylyn.tasks.ui.actions.view/org.eclipse.mylyn.tasks.ui.tasks.restore" commandName="Restore Tasks from History..." category="_YRa5PnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa5FHHmEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.mylyn.tasks.ui.actions.view/org.eclipse.mylyn.tasks.ui.open.repositories.view" commandName="Show Task Repositories View" description="Show Task Repositories View" category="_YRa5PnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa5FXHmEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.mylyn.tasks.ui.actions.view/org.eclipse.mylyn.doc.legend.show.action" commandName="Show UI Legend" description="Show Tasks UI Legend" category="_YRa5PnHmEfCAZJ6UDgwwcw"/>
  <commands xmi:id="_YRa5FnHmEfCAZJ6UDgwwcw" elementId="AUTOGEN:::org.eclipse.mylyn.tasks.ui.actions.view/org.eclipse.mylyn.context.ui.actions.tasklist.focus" commandName="Focus on Workweek" description="Focus on Workweek" category="_YRa5PnHmEfCAZJ6UDgwwcw"/>
  <addons xmi:id="_YRa5F3HmEfCAZJ6UDgwwcw" elementId="org.eclipse.e4.core.commands.service" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.e4.core.commands/org.eclipse.e4.core.commands.CommandServiceAddon"/>
  <addons xmi:id="_YRa5GHHmEfCAZJ6UDgwwcw" elementId="org.eclipse.e4.ui.contexts.service" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.e4.ui.services/org.eclipse.e4.ui.services.ContextServiceAddon"/>
  <addons xmi:id="_YRa5GXHmEfCAZJ6UDgwwcw" elementId="org.eclipse.e4.ui.bindings.service" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.e4.ui.bindings/org.eclipse.e4.ui.bindings.BindingServiceAddon"/>
  <addons xmi:id="_YRa5GnHmEfCAZJ6UDgwwcw" elementId="org.eclipse.e4.ui.workbench.commands.model" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.e4.ui.workbench/org.eclipse.e4.ui.internal.workbench.addons.CommandProcessingAddon"/>
  <addons xmi:id="_YRa5G3HmEfCAZJ6UDgwwcw" elementId="org.eclipse.e4.ui.workbench.contexts.model" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.e4.ui.workbench/org.eclipse.e4.ui.internal.workbench.addons.ContextProcessingAddon"/>
  <addons xmi:id="_YRa5HHHmEfCAZJ6UDgwwcw" elementId="org.eclipse.e4.ui.workbench.bindings.model" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.swt/org.eclipse.e4.ui.workbench.swt.util.BindingProcessingAddon"/>
  <addons xmi:id="_YRa5HXHmEfCAZJ6UDgwwcw" elementId="Cleanup Addon" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.addons.swt/org.eclipse.e4.ui.workbench.addons.cleanupaddon.CleanupAddon"/>
  <addons xmi:id="_YRa5HnHmEfCAZJ6UDgwwcw" elementId="DnD Addon" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.addons.swt/org.eclipse.e4.ui.workbench.addons.dndaddon.DnDAddon"/>
  <addons xmi:id="_YRa5H3HmEfCAZJ6UDgwwcw" elementId="MinMax Addon" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.addons.swt/org.eclipse.e4.ui.workbench.addons.minmax.MinMaxAddon"/>
  <addons xmi:id="_YRa5IHHmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.workbench.addon.0" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.e4.ui.workbench/org.eclipse.e4.ui.internal.workbench.addons.HandlerProcessingAddon"/>
  <addons xmi:id="_YRa5IXHmEfCAZJ6UDgwwcw" elementId="SplitterAddon" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.addons.swt/org.eclipse.e4.ui.workbench.addons.splitteraddon.SplitterAddon"/>
  <addons xmi:id="_YRa5InHmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.ide.addon.0" contributionURI="bundleclass://org.eclipse.ui.ide/org.eclipse.ui.internal.ide.addons.SaveAllDirtyPartsAddon"/>
  <addons xmi:id="_YRa5I3HmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.ide.application.addon.0" contributorURI="platform:/plugin/org.eclipse.ui.ide.application" contributionURI="bundleclass://org.eclipse.ui.ide.application/org.eclipse.ui.internal.ide.application.addons.ModelCleanupAddon"/>
  <categories xmi:id="_YRa5JHHmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.category.edit" name="Edit"/>
  <categories xmi:id="_YRa5JXHmEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.wikitext.ui.editor.category" name="WikiText Markup Editing Commands" description="commands for editing lightweight markup"/>
  <categories xmi:id="_YRa5JnHmEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.builds.ui.category.Commands" name="Builds"/>
  <categories xmi:id="_YRa5J3HmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.category.textEditor" name="Text Editing" description="Text Editing Commands"/>
  <categories xmi:id="_YRa5KHHmEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.debug.ui.category.registerGrouping" name="Register Grouping commands" description="Set of commands for Register Grouping"/>
  <categories xmi:id="_YRa5KXHmEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.context.ui.commands" name="Focused UI" description="Task-Focused Interface"/>
  <categories xmi:id="_YRa5KnHmEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.changelog" name="Changelog" description="Changelog key bindings"/>
  <categories xmi:id="_YRa5K3HmEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.tasks.ui.commands" name="Task Repositories"/>
  <categories xmi:id="_YRa5LHHmEfCAZJ6UDgwwcw" elementId="AnsiConsole.command.categoryid" name="ANSI Support Commands"/>
  <categories xmi:id="_YRa5LXHmEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.cdt.ui.commands" name="CDT Context" description="CDT Task-Focused Interface Commands"/>
  <categories xmi:id="_YRa5LnHmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.category.file" name="File"/>
  <categories xmi:id="_YRa5L3HmEfCAZJ6UDgwwcw" elementId="org.eclipse.text.quicksearch.commands.category" name="Quick Search"/>
  <categories xmi:id="_YRa5MHHmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.category.window" name="Window"/>
  <categories xmi:id="_YRa5MXHmEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.category.refactoring" name="Refactor - C++" description="C/C++ Refactorings"/>
  <categories xmi:id="_YRa5MnHmEfCAZJ6UDgwwcw" elementId="org.eclipse.embedcdt.packs.ui.commands.category" name="C/C++ Packages Category"/>
  <categories xmi:id="_YRa5M3HmEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.make.ui.category.source" name="Makefile Source" description="Makefile Source Actions"/>
  <categories xmi:id="_YRa5NHHmEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.internal.lttng2.ui.commands.control.category" name="LTTng Trace Control Commands" description="LTTng Trace Control Commands"/>
  <categories xmi:id="_YRa5NXHmEfCAZJ6UDgwwcw" elementId="org.eclipse.debug.ui.category.run" name="Run/Debug" description="Run/Debug command category"/>
  <categories xmi:id="_YRa5NnHmEfCAZJ6UDgwwcw" elementId="org.riscvstudio.ide.import.package" name="Export to Package Management"/>
  <categories xmi:id="_YRa5N3HmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.category.dialogs" name="Dialogs" description="Commands for opening dialogs"/>
  <categories xmi:id="_YRa5OHHmEfCAZJ6UDgwwcw" elementId="org.eclipse.oomph" name="Oomph"/>
  <categories xmi:id="_YRa5OXHmEfCAZJ6UDgwwcw" elementId="org.riscvstudio.ide.commands.sdkManage" name="NPK Package Management"/>
  <categories xmi:id="_YRa5OnHmEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.tmf.ui.commands.category" name="Tracing" description="Tracing Commands"/>
  <categories xmi:id="_YRa5O3HmEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.commons.repositories.ui.category.Team" name="Team"/>
  <categories xmi:id="_YRa5PHHmEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.debug.ui.category.casting" name="Cast to Type or Array" description="Set of commands for displaying variables and expressions as other types or arrays."/>
  <categories xmi:id="_YRa5PXHmEfCAZJ6UDgwwcw" elementId="org.eclipse.oomph.commands" name="Oomph"/>
  <categories xmi:id="_YRa5PnHmEfCAZJ6UDgwwcw" elementId="org.eclipse.core.commands.categories.autogenerated" name="Uncategorized" description="Commands that were either auto-generated or have no category"/>
  <categories xmi:id="_YRa5P3HmEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.tmf.ui.commands.parser.category" name="Parser Commands" description="Parser Commands"/>
  <categories xmi:id="_YRa5QHHmEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.managedbuilder.ui.category.build" name="C/C++ Build" description="C/C++ Build Actions"/>
  <categories xmi:id="_YRa5QXHmEfCAZJ6UDgwwcw" elementId="org.eclipse.team.ui.category.team" name="Version control (Team)" description="Actions that apply when working with a version control system"/>
  <categories xmi:id="_YRa5QnHmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.category.views" name="Views" description="Commands for opening views"/>
  <categories xmi:id="_YRa5Q3HmEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.ui.category.source" name="C/C++ Source" description="C/C++ Source Actions"/>
  <categories xmi:id="_YRa5RHHmEfCAZJ6UDgwwcw" elementId="org.eclipse.mylyn.tasks.ui.category.editor" name="Task Editor"/>
  <categories xmi:id="_YRa5RXHmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.ide.markerContents" name="Contents" description="The category for menu contents"/>
  <categories xmi:id="_YRa5RnHmEfCAZJ6UDgwwcw" elementId="org.eclipse.oomph.setup.category" name="Oomph Setup"/>
  <categories xmi:id="_YRa5R3HmEfCAZJ6UDgwwcw" elementId="org.eclipse.embedcdt.debug.gdbjtag.restart.ui.category" name="Run/Debug" description="Run/Debug command category"/>
  <categories xmi:id="_YRa5SHHmEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.debug.ui.category.runControl" name="Run Control Commands" description="Set of commands for Run Control"/>
  <categories xmi:id="_YRa5SXHmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.category.navigate" name="Navigate"/>
  <categories xmi:id="_YRa5SnHmEfCAZJ6UDgwwcw" elementId="org.eclipse.tm.terminal.view.ui.commands.category" name="Terminal Commands"/>
  <categories xmi:id="_YRa5S3HmEfCAZJ6UDgwwcw" elementId="org.eclipse.compare.ui.category.compare" name="Compare" description="Compare command category"/>
  <categories xmi:id="_YRa5THHmEfCAZJ6UDgwwcw" elementId="org.riscvstudio.ide.project.convert.commands.category" name="RISC-V Project Convert Tool"/>
  <categories xmi:id="_YRa5TXHmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.category.help" name="Help"/>
  <categories xmi:id="_YRa5TnHmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.category.project" name="Project"/>
  <categories xmi:id="_YRa5T3HmEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.codan.ui.commands.category" name="Code Analysis"/>
  <categories xmi:id="_YRa5UHHmEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.debug.ui.category.debugViewLayout" name="Debug View Layout Commands" description="Set of commands for controlling the Debug View Layout"/>
  <categories xmi:id="_YRa5UXHmEfCAZJ6UDgwwcw" elementId="org.eclipse.tm.terminal.category1" name="Terminal view commands" description="Terminal view commands"/>
  <categories xmi:id="_YRa5UnHmEfCAZJ6UDgwwcw" elementId="org.riscvstudio.ide.commands.category" name="SDK Configuration Tools"/>
  <categories xmi:id="_YRa5U3HmEfCAZJ6UDgwwcw" elementId="org.eclipse.search.ui.category.search" name="Search" description="Search command category"/>
  <categories xmi:id="_YRa5VHHmEfCAZJ6UDgwwcw" elementId="org.eclipse.launchbar.ui.category.launchBar" name="Launch Bar"/>
  <categories xmi:id="_YRa5VXHmEfCAZJ6UDgwwcw" elementId="org.eclipse.egit.ui.commandCategory" name="Git"/>
  <categories xmi:id="_YRa5VnHmEfCAZJ6UDgwwcw" elementId="org.eclipse.linuxtools.tmf.ui.views.uml2sd.category" name="UML2 Sequence Diagram Viewer Commands" description="UML2 Sequence Diagram Viewer Commands"/>
  <categories xmi:id="_YRa5V3HmEfCAZJ6UDgwwcw" elementId="org.eclipse.ui.category.perspectives" name="Perspectives" description="Commands for opening perspectives"/>
  <categories xmi:id="_YRa5WHHmEfCAZJ6UDgwwcw" elementId="org.eclipse.ltk.ui.category.refactoring" name="Refactoring"/>
  <categories xmi:id="_YRa5WXHmEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.debug.ui.category.reverseDebugging" name="Reverse Debugging Commands" description="Set of commands for Reverse Debugging"/>
  <categories xmi:id="_YRa5WnHmEfCAZJ6UDgwwcw" elementId="org.eclipse.cdt.debug.ui.category.tracing" name="Tracing Commands" description="Category for Tracing Commands"/>
</application:Application>
