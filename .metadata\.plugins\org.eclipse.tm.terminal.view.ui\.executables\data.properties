#Sat Aug 02 23:59:56 CST 2025
2.Args=--distribution Ubuntu-20.04
2.Path=C\:\\WINDOWS\\System32\\wsl.exe
1.Name=xv6 (WSL)
2.Translate=true
0.Icon=D\:\\Program Files\\Git\\mingw64\\share\\git\\git-for-windows.ico
3.Translate=true
1.Path=C\:\\WINDOWS\\System32\\wsl.exe
0.Args=--login -i
4.Name=wsl2 (WSL)
4.Translate=true
3.Name=Ubuntu-22.04 (WSL)
4.Args=--distribution wsl2
0.Path=D\:\\Program Files\\Git\\bin\\sh.exe
3.Path=C\:\\WINDOWS\\System32\\wsl.exe
4.Path=C\:\\WINDOWS\\System32\\wsl.exe
0.Translate=true
3.Args=--distribution Ubuntu-22.04
2.Name=Ubuntu-20.04 (WSL)
1.Translate=true
0.Name=Git Bash
1.Args=--distribution xv6
