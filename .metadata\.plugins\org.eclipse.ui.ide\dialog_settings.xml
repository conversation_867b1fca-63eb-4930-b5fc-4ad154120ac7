<?xml version="1.0" encoding="UTF-8"?>
<section name="Workbench">
	<section name="ExternalProjectImportWizard">
		<item key="WizardProjectsImportPage.STORE_NESTED_PROJECTS" value="false"/>
		<item key="WizardProjectsImportPage.STORE_COPY_PROJECT_ID" value="false"/>
		<item key="WizardProjectsImportPage.STORE_ARCHIVE_SELECTED" value="false"/>
		<item key="WizardProjectsImportPage.STORE_CLOSE_CREATED_PROJECTS_ID" value="false"/>
		<item key="WizardProjectsImportPage.STORE_HIDE_CONFLICTING_PROJECTS_ID" value="false"/>
		<list key="WizardProjectsImportPage.STORE_DIRECTORIES">
			<item value="D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4"/>
		</list>
		<list key="WizardProjectsImportPage.STORE_ARCHIVES">
			<item value=""/>
		</list>
	</section>
	<section name="CleanDialogSettings">
		<item key="BUILD_NOW" value="true"/>
		<item key="BUILD_ALL" value="true"/>
		<item key="TOGGLE_SELECTED" value="false"/>
		<item key="DIALOG_WIDTH" value="500"/>
		<item key="DIALOG_HEIGHT" value="485"/>
		<item key="DIALOG_FONT_NAME" value="1|Microsoft YaHei UI|9.0|0|WINDOWS|1|-12|0|0|0|400|0|0|0|1|0|0|0|0|Microsoft YaHei UI"/>
	</section>
</section>
