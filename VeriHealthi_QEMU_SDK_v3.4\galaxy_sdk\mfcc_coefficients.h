/*
 * NMSIS MFCC系数头文件
 */

#ifndef MFCC_COEFFICIENTS_H
#define MFCC_COEFFICIENTS_H

#include <stdint.h>
#include "riscv_math.h"

#ifdef __cplusplus
extern "C" {
#endif

// ===== MFCC参数定义 =====
#define MFCC_SAMPLE_RATE     8000
#define MFCC_N_FFT           256
#define MFCC_WIN_LENGTH      200
#define MFCC_HOP_LENGTH      80
#define MFCC_N_MELS          26
#define MFCC_N_MFCC          13

// ===== 系数数组声明 =====
extern const float32_t mfcc_hamming_window[MFCC_WIN_LENGTH];
extern const uint32_t mfcc_filter_pos[MFCC_N_MELS];
extern const uint32_t mfcc_filter_len[MFCC_N_MELS];
extern const float32_t mfcc_filter_coeffs[271];
extern const float32_t mfcc_dct_matrix[MFCC_N_MFCC * MFCC_N_MELS];

#ifdef __cplusplus
}
#endif

#endif // MFCC_COEFFICIENTS_H
