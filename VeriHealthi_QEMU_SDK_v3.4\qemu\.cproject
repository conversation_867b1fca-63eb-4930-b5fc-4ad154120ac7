<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<?fileVersion 4.0.0?><cproject storage_type_id="org.eclipse.cdt.core.XmlProjectDescriptionStorage">
	<storageModule moduleId="org.eclipse.cdt.core.settings">
		<cconfiguration id="ilg.gnumcueclipse.managedbuild.cross.riscv.config.elf.debug.**********">
			<storageModule buildSystemId="org.eclipse.cdt.managedbuilder.core.configurationDataProvider" id="ilg.gnumcueclipse.managedbuild.cross.riscv.config.elf.debug.**********" moduleId="org.eclipse.cdt.core.settings" name="Release">
				<externalSettings/>
				<extensions>
					<extension id="org.eclipse.cdt.core.RV_ELF" point="org.eclipse.cdt.core.BinaryParser"/>
					<extension id="org.eclipse.cdt.core.GASErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="org.eclipse.cdt.core.GmakeErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="org.eclipse.cdt.core.GLDErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="org.eclipse.cdt.core.CWDLocator" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="org.eclipse.cdt.core.GCCErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
				</extensions>
			</storageModule>
			<storageModule moduleId="cdtBuildSystem" version="4.0.0">
				<configuration artifactExtension="out" artifactName="${ProjName}" buildArtefactType="org.eclipse.cdt.build.core.buildArtefactType.exe" buildProperties="org.eclipse.cdt.build.core.buildArtefactType=org.eclipse.cdt.build.core.buildArtefactType.exe,org.eclipse.cdt.build.core.buildType=org.eclipse.cdt.build.core.buildType.debug" cleanCommand="${cross_rm} -rf" description="" id="ilg.gnumcueclipse.managedbuild.cross.riscv.config.elf.debug.**********" name="Release" parent="ilg.gnumcueclipse.managedbuild.cross.riscv.config.elf.debug">
					<folderInfo id="ilg.gnumcueclipse.managedbuild.cross.riscv.config.elf.debug.**********." name="/" resourcePath="">
						<toolChain id="ilg.gnumcueclipse.managedbuild.cross.riscv.toolchain.elf.debug.14765353" name="RISC-V Cross GCC" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.toolchain.elf.debug">
							<option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.addtools.createflash.1412915236" name="Create flash image" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.addtools.createflash" value="true" valueType="boolean"/>
							<option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.addtools.createlisting.955443668" name="Create extended listing" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.addtools.createlisting" value="true" valueType="boolean"/>
							<option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.addtools.printsize.1995219894" name="Print size" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.addtools.printsize" value="true" valueType="boolean"/>
							<option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.optimization.level.567772452" name="Optimization Level" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.optimization.level" value="ilg.gnumcueclipse.managedbuild.cross.riscv.option.optimization.level.more" valueType="enumerated"/>
							<option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.optimization.messagelength.2116876993" name="Message length (-fmessage-length=0)" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.optimization.messagelength" value="false" valueType="boolean"/>
							<option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.optimization.signedchar.1736073814" name="'char' is signed (-fsigned-char)" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.optimization.signedchar" value="false" valueType="boolean"/>
							<option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.optimization.functionsections.1620329348" name="Function sections (-ffunction-sections)" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.optimization.functionsections" value="true" valueType="boolean"/>
							<option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.optimization.datasections.1250895151" name="Data sections (-fdata-sections)" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.optimization.datasections" value="true" valueType="boolean"/>
							<option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.debugging.level.1863267806" name="Debug level" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.debugging.level" value="ilg.gnumcueclipse.managedbuild.cross.riscv.option.debugging.level.default" valueType="enumerated"/>
							<option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.debugging.format.305290168" name="Debug format" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.debugging.format"/>
							<option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.toolchain.name.862245024" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.toolchain.name" value="RISC-V GCC/Newlib" valueType="string"/>
							<option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.toolchain.id.1445602459" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.toolchain.id" value="2262347901" valueType="string"/>
							<option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.command.prefix.249231584" name="Prefix" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.command.prefix" value="riscv64-unknown-elf-" valueType="string"/>
							<option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.command.c.1983034839" name="C compiler" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.command.c" value="gcc" valueType="string"/>
							<option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.command.cpp.925660936" name="C++ compiler" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.command.cpp" value="g++" valueType="string"/>
							<option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.command.ar.1570912839" name="Archiver" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.command.ar" value="ar" valueType="string"/>
							<option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.command.objcopy.1016025085" name="Hex/Bin converter" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.command.objcopy" value="objcopy" valueType="string"/>
							<option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.command.objdump.294221208" name="Listing generator" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.command.objdump" value="objdump" valueType="string"/>
							<option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.command.size.433507178" name="Size command" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.command.size" value="size" valueType="string"/>
							<option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.command.make.1911832545" name="Build command" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.command.make" value="make" valueType="string"/>
							<option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.command.rm.1160659909" name="Remove command" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.command.rm" value="rm" valueType="string"/>
							<option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.target.smalldatalimit.1328180547" name="Small data limit" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.target.smalldatalimit" value="" valueType="string"/>
							<option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.target.isa.base.1629937855" name="Architecture" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.target.isa.base" value="ilg.gnumcueclipse.managedbuild.cross.riscv.option.target.arch.rv32i" valueType="enumerated"/>
							<option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.target.isa.fp.1425267460" name="Floating point" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.target.isa.fp" value="ilg.gnumcueclipse.managedbuild.cross.riscv.option.isa.fp.single" valueType="enumerated"/>
							<option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.target.isa.multiply.64326463" name="Multiply extension (RVM)" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.target.isa.multiply" value="true" valueType="boolean"/>
							<option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.target.isa.atomic.854174946" name="Atomic extension (RVA)" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.target.isa.atomic" value="true" valueType="boolean"/>
							<option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.target.isa.compressed.1222736673" name="Compressed extension (RVC)" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.target.isa.compressed" value="true" valueType="boolean"/>
							<option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.target.abi.integer.1916699757" name="Integer ABI" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.target.abi.integer" value="ilg.gnumcueclipse.managedbuild.cross.riscv.option.abi.integer.ilp32" valueType="enumerated"/>
							<option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.target.abi.fp.1464871273" name="Floating point ABI" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.target.abi.fp" value="ilg.gnumcueclipse.managedbuild.cross.riscv.option.abi.fp.single" valueType="enumerated"/>
							<option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.target.tune.1494888145" name="Tuning" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.target.tune" value="ilg.gnumcueclipse.managedbuild.cross.riscv.option.target.tune.n300series" valueType="enumerated"/>
							<option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.target.codemodel.46493961" name="Code model" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.target.codemodel" value="ilg.gnumcueclipse.managedbuild.cross.riscv.option.target.codemodel.low" valueType="enumerated"/>
							<option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.target.saverestore.1421996591" name="Small prologue/epilogue (-msave-restore)" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.target.saverestore" value="false" valueType="boolean"/>
							<option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.optimization.nocommon.710433831" name="No common unitialized (-fno-common)" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.optimization.nocommon" value="true" valueType="boolean"/>
							<option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.warnings.allwarn.1939704964" name="Enable all common warnings (-Wall)" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.warnings.allwarn" value="true" valueType="boolean"/>
							<option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.warnings.toerrors.537705266" name="Generate errors instead of warnings (-Werror)" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.warnings.toerrors" value="true" valueType="boolean"/>
							<option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.target.isa.extensions.1980846563" name="Other extensions" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.target.isa.extensions" value="_xxldsp" valueType="string"/>
							<targetPlatform archList="all" binaryParser="org.eclipse.cdt.core.RV_ELF" id="ilg.gnumcueclipse.managedbuild.cross.riscv.targetPlatform.1774398575" isAbstract="false" osList="all" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.targetPlatform"/>
							<builder buildPath="${workspace_loc:/qemu}/Release" id="ilg.gnumcueclipse.managedbuild.cross.riscv.builder.511657692" keepEnvironmentInBuildfile="false" name="Gnu Make Builder" parallelBuildOn="true" parallelizationNumber="optimal" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.builder"/>
							<tool id="ilg.gnumcueclipse.managedbuild.cross.riscv.tool.assembler.977472253" name="GNU RISC-V Cross Assembler" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.tool.assembler">
								<option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.assembler.usepreprocessor.208801221" name="Use preprocessor" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.assembler.usepreprocessor" value="true" valueType="boolean"/>
								<option IS_BUILTIN_EMPTY="false" IS_VALUE_EMPTY="true" id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.assembler.defs.54179979" name="Defined symbols (-D)" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.assembler.defs" valueType="definedSymbols"/>
								<option IS_BUILTIN_EMPTY="false" IS_VALUE_EMPTY="false" id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.assembler.include.paths.1465988679" name="Include paths (-I)" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.assembler.include.paths" valueType="includePath">
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/galaxy_sdk}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/galaxy_sdk/bsp/include/arch/riscv/n309}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/galaxy_sdk/bsp/include}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/galaxy_sdk/config/include}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/galaxy_sdk/drivers/include}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/galaxy_sdk/modules/external/riscv_dsp/include}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/galaxy_sdk/modules/external/nnom/inc/layers}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/galaxy_sdk/modules/external/nnom/port}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/galaxy_sdk/modules/external/nnom/inc}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/galaxy_sdk/modules/external/riscv_dsp/PrivateInclude}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/galaxy_sdk/modules/include}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/galaxy_sdk/os/include}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/galaxy_sdk/osal/include}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/galaxy_sdk/prebuilts/bluetooth/health/include}&quot;"/>
								</option>
								<option IS_BUILTIN_EMPTY="false" IS_VALUE_EMPTY="true" id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.assembler.include.systempaths.**********" name="Include system paths (-isystem)" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.assembler.include.systempaths" valueType="includePath"/>
								<option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.assembler.other.**********" name="Other assembler flags" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.assembler.other" value="-x assembler-with-cpp" valueType="string"/>
								<inputType id="ilg.gnumcueclipse.managedbuild.cross.riscv.tool.assembler.input.**********" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.tool.assembler.input"/>
							</tool>
							<tool id="ilg.gnumcueclipse.managedbuild.cross.riscv.tool.c.compiler.594997893" name="GNU RISC-V Cross C Compiler" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.tool.c.compiler">
								<option IS_BUILTIN_EMPTY="false" IS_VALUE_EMPTY="true" id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.c.compiler.defs.255827229" name="Defined symbols (-D)" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.c.compiler.defs" useByScannerDiscovery="true" valueType="definedSymbols"/>
								<option IS_BUILTIN_EMPTY="false" IS_VALUE_EMPTY="false" id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.c.compiler.include.paths.885442491" name="Include paths (-I)" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.c.compiler.include.paths" useByScannerDiscovery="true" valueType="includePath">
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/galaxy_sdk}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/galaxy_sdk/bsp/include/arch/riscv/n309}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/galaxy_sdk/bsp/include}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/galaxy_sdk/config/include}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/galaxy_sdk/drivers/include}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/galaxy_sdk/modules/external/riscv_dsp/include}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/galaxy_sdk/modules/external/nnom/inc/layers}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/galaxy_sdk/modules/external/nnom/port}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/galaxy_sdk/modules/external/nnom/inc}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/galaxy_sdk/modules/external/riscv_dsp/PrivateInclude}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/galaxy_sdk/modules/include}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/galaxy_sdk/os/include}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/galaxy_sdk/osal/include}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/galaxy_sdk/prebuilts/bluetooth/health/include}&quot;"/>
								</option>
								<option IS_BUILTIN_EMPTY="false" IS_VALUE_EMPTY="true" id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.c.compiler.include.systempaths.**********" name="Include system paths (-isystem)" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.c.compiler.include.systempaths" useByScannerDiscovery="true" valueType="includePath"/>
								<inputType id="ilg.gnumcueclipse.managedbuild.cross.riscv.tool.c.compiler.input.502920024" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.tool.c.compiler.input"/>
							</tool>
							<tool id="ilg.gnumcueclipse.managedbuild.cross.riscv.tool.cpp.compiler.**********" name="GNU RISC-V Cross C++ Compiler" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.tool.cpp.compiler">
								<option IS_BUILTIN_EMPTY="false" IS_VALUE_EMPTY="true" id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.cpp.compiler.defs.**********" name="Defined symbols (-D)" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.cpp.compiler.defs" useByScannerDiscovery="true" valueType="definedSymbols"/>
								<option IS_BUILTIN_EMPTY="false" IS_VALUE_EMPTY="false" id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.cpp.compiler.include.paths.1796405796" name="Include paths (-I)" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.cpp.compiler.include.paths" useByScannerDiscovery="true" valueType="includePath">
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/galaxy_sdk}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/galaxy_sdk/bsp/include/arch/riscv/n309}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/galaxy_sdk/bsp/include}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/galaxy_sdk/config/include}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/galaxy_sdk/drivers/include}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/galaxy_sdk/modules/external/riscv_dsp/include}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/galaxy_sdk/modules/external/nnom/inc/layers}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/galaxy_sdk/modules/external/nnom/port}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/galaxy_sdk/modules/external/nnom/inc}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/galaxy_sdk/modules/external/riscv_dsp/PrivateInclude}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/galaxy_sdk/modules/include}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/galaxy_sdk/os/include}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/galaxy_sdk/osal/include}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/galaxy_sdk/prebuilts/bluetooth/health/include}&quot;"/>
								</option>
								<option IS_BUILTIN_EMPTY="false" IS_VALUE_EMPTY="true" id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.cpp.compiler.include.systempaths.621245314" name="Include system paths (-isystem)" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.cpp.compiler.include.systempaths" useByScannerDiscovery="true" valueType="includePath"/>
								<inputType id="ilg.gnumcueclipse.managedbuild.cross.riscv.tool.cpp.compiler.input.982748886" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.tool.cpp.compiler.input"/>
							</tool>
							<tool id="ilg.gnumcueclipse.managedbuild.cross.riscv.tool.c.linker.**********" name="GNU RISC-V Cross C Linker" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.tool.c.linker">
								<option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.c.linker.gcsections.**********" name="Remove unused sections (-Xlinker --gc-sections)" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.c.linker.gcsections" value="true" valueType="boolean"/>
							</tool>
							<tool commandLinePattern="${COMMAND} ${cross_toolchain_flags} ${FLAGS} ${OUTPUT_FLAG} ${OUTPUT_PREFIX}${OUTPUT} -Wl,--start-group ${INPUTS} -Wl,--end-group" id="ilg.gnumcueclipse.managedbuild.cross.riscv.tool.cpp.linker.1800096521" name="GNU RISC-V Cross C++ Linker" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.tool.cpp.linker">
								<option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.cpp.linker.gcsections.1909289239" name="Remove unused sections (-Xlinker --gc-sections)" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.cpp.linker.gcsections" value="true" valueType="boolean"/>
								<option IS_BUILTIN_EMPTY="false" IS_VALUE_EMPTY="false" id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.cpp.linker.scriptfile.149297491" name="Script files (-T)" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.cpp.linker.scriptfile" valueType="stringList">
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/galaxy_sdk/n309_iot_qemu.ld}&quot;"/>
								</option>
								<option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.cpp.linker.nodeflibs.25188217" name="Do not use default libraries (-nodefaultlibs)" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.cpp.linker.nodeflibs" value="false" valueType="boolean"/>
								<option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.cpp.linker.nostart.251085281" name="Do not use standard start files (-nostartfiles)" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.cpp.linker.nostart" value="true" valueType="boolean"/>
								<option IS_BUILTIN_EMPTY="false" IS_VALUE_EMPTY="false" id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.cpp.linker.libs.34988057" name="Libraries (-l)" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.cpp.linker.libs" valueType="libs">
									<listOptionValue builtIn="false" value="stdc++"/>
									<listOptionValue builtIn="false" value="bsp_riscv"/>
									<listOptionValue builtIn="false" value="driver_riscv"/>
									<listOptionValue builtIn="false" value="common_riscv"/>
									<listOptionValue builtIn="false" value="osal_riscv"/>
									<listOptionValue builtIn="false" value="os_riscv"/>
									<listOptionValue builtIn="false" value="ble"/>
									<listOptionValue builtIn="false" value="nmsis_dsp_rv32imafc_xxldsp"/>
									<listOptionValue builtIn="false" value="c_nano"/>
									<listOptionValue builtIn="false" value="gcc"/>
									<listOptionValue builtIn="false" value="semihost"/>
								</option>
								<option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.cpp.linker.other.815911637" name="Other linker flags" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.cpp.linker.other" value="-Wl,--no-warn-rwx-segments" valueType="string"/>
								<option IS_BUILTIN_EMPTY="false" IS_VALUE_EMPTY="false" id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.cpp.linker.paths.1728679988" name="Library search path (-L)" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.cpp.linker.paths" valueType="libPaths">
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/galaxy_sdk/modules/external/riscv_dsp}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/galaxy_sdk/bsp/lib}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/galaxy_sdk/drivers/lib}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/galaxy_sdk/modules/lib}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/galaxy_sdk/os/lib}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/galaxy_sdk/osal/lib}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/galaxy_sdk/prebuilts/bluetooth/health}&quot;"/>
								</option>
								<option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.cpp.linker.usenewlibnano.**********" name="Use newlib-nano (--specs=nano.specs)" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.cpp.linker.usenewlibnano" value="false" valueType="boolean"/>
								<option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.cpp.linker.usenewlibnosys.**********" name="Do not use syscalls (--specs=nosys.specs)" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.cpp.linker.usenewlibnosys" value="false" valueType="boolean"/>
								<inputType id="ilg.gnumcueclipse.managedbuild.cross.riscv.tool.cpp.linker.input.**********" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.tool.cpp.linker.input">
									<additionalInput kind="additionalinputdependency" paths="$(USER_OBJS)"/>
									<additionalInput kind="additionalinput" paths="$(LIBS)"/>
								</inputType>
							</tool>
							<tool id="ilg.gnumcueclipse.managedbuild.cross.riscv.tool.archiver.151652513" name="GNU RISC-V Cross Archiver" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.tool.archiver"/>
							<tool id="ilg.gnumcueclipse.managedbuild.cross.riscv.tool.createflash.1096002526" name="GNU RISC-V Cross Create Flash Image" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.tool.createflash"/>
							<tool id="ilg.gnumcueclipse.managedbuild.cross.riscv.tool.createlisting.643705233" name="GNU RISC-V Cross Create Listing" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.tool.createlisting">
								<option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.createlisting.source.1675995189" name="Display source (--source|-S)" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.createlisting.source" value="true" valueType="boolean"/>
								<option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.createlisting.allheaders.1844290696" name="Display all headers (--all-headers|-x)" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.createlisting.allheaders" value="true" valueType="boolean"/>
								<option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.createlisting.demangle.1617075497" name="Demangle names (--demangle|-C)" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.createlisting.demangle" value="true" valueType="boolean"/>
								<option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.createlisting.linenumbers.518001389" name="Display line numbers (--line-numbers|-l)" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.createlisting.linenumbers" value="true" valueType="boolean"/>
								<option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.createlisting.wide.1741087768" name="Wide lines (--wide|-w)" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.createlisting.wide" value="true" valueType="boolean"/>
							</tool>
							<tool id="ilg.gnumcueclipse.managedbuild.cross.riscv.tool.printsize.88553387" name="GNU RISC-V Cross Print Size" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.tool.printsize">
								<option id="ilg.gnumcueclipse.managedbuild.cross.riscv.option.printsize.format.789888377" name="Size format" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.option.printsize.format"/>
							</tool>
						</toolChain>
					</folderInfo>
					<fileInfo id="ilg.gnumcueclipse.managedbuild.cross.riscv.config.elf.debug.**********.**********" name="main.c" rcbsApplicability="disable" resourcePath="galaxy_sdk/main.c" toolsToInvoke="ilg.gnumcueclipse.managedbuild.cross.riscv.tool.c.compiler.594997893.**********">
						<tool id="ilg.gnumcueclipse.managedbuild.cross.riscv.tool.c.compiler.594997893.**********" name="GNU RISC-V Cross C Compiler" superClass="ilg.gnumcueclipse.managedbuild.cross.riscv.tool.c.compiler.594997893"/>
					</fileInfo>
					<sourceEntries>
						<entry excluding="galaxy_sdk/prebuilts/bluetooth/health/sample" flags="VALUE_WORKSPACE_PATH|RESOLVED" kind="sourcePath" name=""/>
					</sourceEntries>
				</configuration>
			</storageModule>
			<storageModule moduleId="org.eclipse.cdt.core.externalSettings"/>
			<storageModule moduleId="ilg.gnumcueclipse.managedbuild.packs"/>
		</cconfiguration>
	</storageModule>
	<storageModule moduleId="cdtBuildSystem" version="4.0.0">
		<project id="qemu.ilg.gnumcueclipse.managedbuild.cross.riscv.target.rvelf.**********" name="Executable" projectType="ilg.gnumcueclipse.managedbuild.cross.riscv.target.rvelf"/>
	</storageModule>
	<storageModule moduleId="org.eclipse.cdt.core.LanguageSettingsProviders"/>
	<storageModule moduleId="scannerConfiguration">
		<autodiscovery enabled="true" problemReportingEnabled="true" selectedProfileId=""/>
		<scannerConfigBuildInfo instanceId="ilg.gnumcueclipse.managedbuild.cross.riscv.config.elf.release.**********;ilg.gnumcueclipse.managedbuild.cross.riscv.config.elf.release.**********.;ilg.gnumcueclipse.managedbuild.cross.riscv.tool.cpp.compiler.**********;ilg.gnumcueclipse.managedbuild.cross.riscv.tool.cpp.compiler.input.41443317">
			<autodiscovery enabled="true" problemReportingEnabled="true" selectedProfileId=""/>
		</scannerConfigBuildInfo>
		<scannerConfigBuildInfo instanceId="ilg.gnumcueclipse.managedbuild.cross.riscv.config.elf.release.**********;ilg.gnumcueclipse.managedbuild.cross.riscv.config.elf.release.**********.;ilg.gnumcueclipse.managedbuild.cross.riscv.tool.c.compiler.**********;ilg.gnumcueclipse.managedbuild.cross.riscv.tool.c.compiler.input.**********">
			<autodiscovery enabled="true" problemReportingEnabled="true" selectedProfileId=""/>
		</scannerConfigBuildInfo>
		<scannerConfigBuildInfo instanceId="ilg.gnumcueclipse.managedbuild.cross.riscv.config.elf.release.146414802;ilg.gnumcueclipse.managedbuild.cross.riscv.config.elf.release.146414802.;ilg.gnumcueclipse.managedbuild.cross.riscv.tool.c.compiler.1275747160;ilg.gnumcueclipse.managedbuild.cross.riscv.tool.c.compiler.input.2082216429">
			<autodiscovery enabled="true" problemReportingEnabled="true" selectedProfileId=""/>
		</scannerConfigBuildInfo>
		<scannerConfigBuildInfo instanceId="ilg.gnumcueclipse.managedbuild.cross.riscv.config.elf.debug.797938212;ilg.gnumcueclipse.managedbuild.cross.riscv.config.elf.debug.797938212.;ilg.gnumcueclipse.managedbuild.cross.riscv.tool.cpp.compiler.1410214970;ilg.gnumcueclipse.managedbuild.cross.riscv.tool.cpp.compiler.input.125655413">
			<autodiscovery enabled="true" problemReportingEnabled="true" selectedProfileId=""/>
		</scannerConfigBuildInfo>
		<scannerConfigBuildInfo instanceId="ilg.gnumcueclipse.managedbuild.cross.riscv.toolchain.elf.debug.2078722675;ilg.gnumcueclipse.managedbuild.cross.riscv.toolchain.elf.debug.2078722675.479025577;ilg.gnumcueclipse.managedbuild.cross.riscv.tool.cpp.compiler.500519335;ilg.gnumcueclipse.managedbuild.cross.riscv.tool.cpp.compiler.input.1178764726">
			<autodiscovery enabled="true" problemReportingEnabled="true" selectedProfileId=""/>
		</scannerConfigBuildInfo>
		<scannerConfigBuildInfo instanceId="ilg.gnumcueclipse.managedbuild.cross.riscv.config.elf.debug.**********;ilg.gnumcueclipse.managedbuild.cross.riscv.config.elf.debug.**********.;ilg.gnumcueclipse.managedbuild.cross.riscv.tool.c.compiler.594997893;ilg.gnumcueclipse.managedbuild.cross.riscv.tool.c.compiler.input.502920024">
			<autodiscovery enabled="true" problemReportingEnabled="true" selectedProfileId=""/>
		</scannerConfigBuildInfo>
		<scannerConfigBuildInfo instanceId="ilg.gnumcueclipse.managedbuild.cross.riscv.config.elf.debug.797938212;ilg.gnumcueclipse.managedbuild.cross.riscv.config.elf.debug.797938212.;ilg.gnumcueclipse.managedbuild.cross.riscv.tool.c.compiler.257952901;ilg.gnumcueclipse.managedbuild.cross.riscv.tool.c.compiler.input.639984609">
			<autodiscovery enabled="true" problemReportingEnabled="true" selectedProfileId=""/>
		</scannerConfigBuildInfo>
		<scannerConfigBuildInfo instanceId="ilg.gnumcueclipse.managedbuild.cross.riscv.config.elf.debug.**********;ilg.gnumcueclipse.managedbuild.cross.riscv.config.elf.debug.**********.;ilg.gnumcueclipse.managedbuild.cross.riscv.tool.cpp.compiler.**********;ilg.gnumcueclipse.managedbuild.cross.riscv.tool.cpp.compiler.input.982748886">
			<autodiscovery enabled="true" problemReportingEnabled="true" selectedProfileId=""/>
		</scannerConfigBuildInfo>
		<scannerConfigBuildInfo instanceId="ilg.gnumcueclipse.managedbuild.cross.riscv.toolchain.elf.debug.231257610;ilg.gnumcueclipse.managedbuild.cross.riscv.toolchain.elf.debug.231257610.849724615;ilg.gnumcueclipse.managedbuild.cross.riscv.tool.cpp.compiler.408258706;ilg.gnumcueclipse.managedbuild.cross.riscv.tool.cpp.compiler.input.55530812">
			<autodiscovery enabled="true" problemReportingEnabled="true" selectedProfileId=""/>
		</scannerConfigBuildInfo>
		<scannerConfigBuildInfo instanceId="lg.gnumcueclipse.managedbuild.cross.riscv.toolchain.elf.debug.231257610;ilg.gnumcueclipse.managedbuild.cross.riscv.toolchain.elf.debug.231257610.849724615;ilg.gnumcueclipse.managedbuild.cross.riscv.tool.c.compiler.51048083;ilg.gnumcueclipse.managedbuild.cross.riscv.tool.c.compiler.input.596450011">
			<autodiscovery enabled="true" problemReportingEnabled="true" selectedProfileId=""/>
		</scannerConfigBuildInfo>
		<scannerConfigBuildInfo instanceId="ilg.gnumcueclipse.managedbuild.cross.riscv.toolchain.elf.debug.2078722675;ilg.gnumcueclipse.managedbuild.cross.riscv.toolchain.elf.debug.2078722675.479025577;ilg.gnumcueclipse.managedbuild.cross.riscv.tool.c.compiler.307883193;ilg.gnumcueclipse.managedbuild.cross.riscv.tool.c.compiler.input.1799538156">
			<autodiscovery enabled="true" problemReportingEnabled="true" selectedProfileId=""/>
		</scannerConfigBuildInfo>
		<scannerConfigBuildInfo instanceId="ilg.gnumcueclipse.managedbuild.cross.riscv.config.elf.release.146414802;ilg.gnumcueclipse.managedbuild.cross.riscv.config.elf.release.146414802.;ilg.gnumcueclipse.managedbuild.cross.riscv.tool.cpp.compiler.111311636;ilg.gnumcueclipse.managedbuild.cross.riscv.tool.cpp.compiler.input.1976753131">
			<autodiscovery enabled="true" problemReportingEnabled="true" selectedProfileId=""/>
		</scannerConfigBuildInfo>
	</storageModule>
	<storageModule moduleId="org.eclipse.cdt.make.core.buildtargets"/>
	<storageModule moduleId="refreshScope" versionNumber="2">
		<configuration configurationName="Release">
			<resource resourceType="PROJECT" workspacePath="/qemu"/>
		</configuration>
	</storageModule>
</cproject>