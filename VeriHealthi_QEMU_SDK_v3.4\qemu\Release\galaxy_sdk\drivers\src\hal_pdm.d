galaxy_sdk/drivers/src/hal_pdm.o: \
 D:/NucleiStudio/2025/01quant/VeriHealthi_QEMU_SDK_v3.4/galaxy_sdk/drivers/src/hal_pdm.c \
 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\drivers\include/vsd_error.h \
 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\drivers\include/hal_pdm.h \
 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\drivers\include/hal_common.h \
 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\include/sys_common.h \
 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\osal\include/osal.h \
 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\osal\include/osal_define.h \
 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\osal\include/osal_event_api.h \
 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\include/vpi_list.h \
 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\osal\include/osal_heap_api.h \
 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\osal\include/osal_lock_api.h \
 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\osal\include/osal_notify_api.h \
 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\osal\include/osal_semaphore_api.h \
 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\osal\include/osal_sys_state_api.h \
 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\osal\include/osal_task_api.h \
 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\osal\include/osal_time_api.h \
 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\osal\include/osal_queue_api.h \
 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\osal\include/osal_timer_api.h \
 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\osal\include/osal_spinlock_api.h \
 D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\include/bsp_common.h
D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\drivers\include/vsd_error.h:
D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\drivers\include/hal_pdm.h:
D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\drivers\include/hal_common.h:
D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\include/sys_common.h:
D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\osal\include/osal.h:
D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\osal\include/osal_define.h:
D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\osal\include/osal_event_api.h:
D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\include/vpi_list.h:
D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\osal\include/osal_heap_api.h:
D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\osal\include/osal_lock_api.h:
D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\osal\include/osal_notify_api.h:
D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\osal\include/osal_semaphore_api.h:
D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\osal\include/osal_sys_state_api.h:
D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\osal\include/osal_task_api.h:
D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\osal\include/osal_time_api.h:
D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\osal\include/osal_queue_api.h:
D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\osal\include/osal_timer_api.h:
D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\osal\include/osal_spinlock_api.h:
D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\include/bsp_common.h:
