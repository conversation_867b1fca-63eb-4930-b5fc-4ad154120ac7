################################################################################
# Automatically-generated file. Do not edit!
################################################################################

# Add inputs and outputs from these tool invocations to the build variables 
C_SRCS += \
D:/NucleiStudio/2025/01quant/VeriHealthi_QEMU_SDK_v3.4/galaxy_sdk/modules/external/nnom/src/layers/nnom_activation.c \
D:/NucleiStudio/2025/01quant/VeriHealthi_QEMU_SDK_v3.4/galaxy_sdk/modules/external/nnom/src/layers/nnom_avgpool.c \
D:/NucleiStudio/2025/01quant/VeriHealthi_QEMU_SDK_v3.4/galaxy_sdk/modules/external/nnom/src/layers/nnom_baselayer.c \
D:/NucleiStudio/2025/01quant/VeriHealthi_QEMU_SDK_v3.4/galaxy_sdk/modules/external/nnom/src/layers/nnom_concat.c \
D:/NucleiStudio/2025/01quant/VeriHealthi_QEMU_SDK_v3.4/galaxy_sdk/modules/external/nnom/src/layers/nnom_conv2d.c \
D:/NucleiStudio/2025/01quant/VeriHealthi_QEMU_SDK_v3.4/galaxy_sdk/modules/external/nnom/src/layers/nnom_conv2d_trans.c \
D:/NucleiStudio/2025/01quant/VeriHealthi_QEMU_SDK_v3.4/galaxy_sdk/modules/external/nnom/src/layers/nnom_cropping.c \
D:/NucleiStudio/2025/01quant/VeriHealthi_QEMU_SDK_v3.4/galaxy_sdk/modules/external/nnom/src/layers/nnom_dense.c \
D:/NucleiStudio/2025/01quant/VeriHealthi_QEMU_SDK_v3.4/galaxy_sdk/modules/external/nnom/src/layers/nnom_dw_conv2d.c \
D:/NucleiStudio/2025/01quant/VeriHealthi_QEMU_SDK_v3.4/galaxy_sdk/modules/external/nnom/src/layers/nnom_flatten.c \
D:/NucleiStudio/2025/01quant/VeriHealthi_QEMU_SDK_v3.4/galaxy_sdk/modules/external/nnom/src/layers/nnom_global_pool.c \
D:/NucleiStudio/2025/01quant/VeriHealthi_QEMU_SDK_v3.4/galaxy_sdk/modules/external/nnom/src/layers/nnom_gru_cell.c \
D:/NucleiStudio/2025/01quant/VeriHealthi_QEMU_SDK_v3.4/galaxy_sdk/modules/external/nnom/src/layers/nnom_input.c \
D:/NucleiStudio/2025/01quant/VeriHealthi_QEMU_SDK_v3.4/galaxy_sdk/modules/external/nnom/src/layers/nnom_lambda.c \
D:/NucleiStudio/2025/01quant/VeriHealthi_QEMU_SDK_v3.4/galaxy_sdk/modules/external/nnom/src/layers/nnom_lstm_cell.c \
D:/NucleiStudio/2025/01quant/VeriHealthi_QEMU_SDK_v3.4/galaxy_sdk/modules/external/nnom/src/layers/nnom_matrix.c \
D:/NucleiStudio/2025/01quant/VeriHealthi_QEMU_SDK_v3.4/galaxy_sdk/modules/external/nnom/src/layers/nnom_maxpool.c \
D:/NucleiStudio/2025/01quant/VeriHealthi_QEMU_SDK_v3.4/galaxy_sdk/modules/external/nnom/src/layers/nnom_output.c \
D:/NucleiStudio/2025/01quant/VeriHealthi_QEMU_SDK_v3.4/galaxy_sdk/modules/external/nnom/src/layers/nnom_reshape.c \
D:/NucleiStudio/2025/01quant/VeriHealthi_QEMU_SDK_v3.4/galaxy_sdk/modules/external/nnom/src/layers/nnom_rnn.c \
D:/NucleiStudio/2025/01quant/VeriHealthi_QEMU_SDK_v3.4/galaxy_sdk/modules/external/nnom/src/layers/nnom_simple_cell.c \
D:/NucleiStudio/2025/01quant/VeriHealthi_QEMU_SDK_v3.4/galaxy_sdk/modules/external/nnom/src/layers/nnom_softmax.c \
D:/NucleiStudio/2025/01quant/VeriHealthi_QEMU_SDK_v3.4/galaxy_sdk/modules/external/nnom/src/layers/nnom_sumpool.c \
D:/NucleiStudio/2025/01quant/VeriHealthi_QEMU_SDK_v3.4/galaxy_sdk/modules/external/nnom/src/layers/nnom_upsample.c \
D:/NucleiStudio/2025/01quant/VeriHealthi_QEMU_SDK_v3.4/galaxy_sdk/modules/external/nnom/src/layers/nnom_zero_padding.c 

C_DEPS += \
./galaxy_sdk/modules/external/nnom/src/layers/nnom_activation.d \
./galaxy_sdk/modules/external/nnom/src/layers/nnom_avgpool.d \
./galaxy_sdk/modules/external/nnom/src/layers/nnom_baselayer.d \
./galaxy_sdk/modules/external/nnom/src/layers/nnom_concat.d \
./galaxy_sdk/modules/external/nnom/src/layers/nnom_conv2d.d \
./galaxy_sdk/modules/external/nnom/src/layers/nnom_conv2d_trans.d \
./galaxy_sdk/modules/external/nnom/src/layers/nnom_cropping.d \
./galaxy_sdk/modules/external/nnom/src/layers/nnom_dense.d \
./galaxy_sdk/modules/external/nnom/src/layers/nnom_dw_conv2d.d \
./galaxy_sdk/modules/external/nnom/src/layers/nnom_flatten.d \
./galaxy_sdk/modules/external/nnom/src/layers/nnom_global_pool.d \
./galaxy_sdk/modules/external/nnom/src/layers/nnom_gru_cell.d \
./galaxy_sdk/modules/external/nnom/src/layers/nnom_input.d \
./galaxy_sdk/modules/external/nnom/src/layers/nnom_lambda.d \
./galaxy_sdk/modules/external/nnom/src/layers/nnom_lstm_cell.d \
./galaxy_sdk/modules/external/nnom/src/layers/nnom_matrix.d \
./galaxy_sdk/modules/external/nnom/src/layers/nnom_maxpool.d \
./galaxy_sdk/modules/external/nnom/src/layers/nnom_output.d \
./galaxy_sdk/modules/external/nnom/src/layers/nnom_reshape.d \
./galaxy_sdk/modules/external/nnom/src/layers/nnom_rnn.d \
./galaxy_sdk/modules/external/nnom/src/layers/nnom_simple_cell.d \
./galaxy_sdk/modules/external/nnom/src/layers/nnom_softmax.d \
./galaxy_sdk/modules/external/nnom/src/layers/nnom_sumpool.d \
./galaxy_sdk/modules/external/nnom/src/layers/nnom_upsample.d \
./galaxy_sdk/modules/external/nnom/src/layers/nnom_zero_padding.d 

OBJS += \
./galaxy_sdk/modules/external/nnom/src/layers/nnom_activation.o \
./galaxy_sdk/modules/external/nnom/src/layers/nnom_avgpool.o \
./galaxy_sdk/modules/external/nnom/src/layers/nnom_baselayer.o \
./galaxy_sdk/modules/external/nnom/src/layers/nnom_concat.o \
./galaxy_sdk/modules/external/nnom/src/layers/nnom_conv2d.o \
./galaxy_sdk/modules/external/nnom/src/layers/nnom_conv2d_trans.o \
./galaxy_sdk/modules/external/nnom/src/layers/nnom_cropping.o \
./galaxy_sdk/modules/external/nnom/src/layers/nnom_dense.o \
./galaxy_sdk/modules/external/nnom/src/layers/nnom_dw_conv2d.o \
./galaxy_sdk/modules/external/nnom/src/layers/nnom_flatten.o \
./galaxy_sdk/modules/external/nnom/src/layers/nnom_global_pool.o \
./galaxy_sdk/modules/external/nnom/src/layers/nnom_gru_cell.o \
./galaxy_sdk/modules/external/nnom/src/layers/nnom_input.o \
./galaxy_sdk/modules/external/nnom/src/layers/nnom_lambda.o \
./galaxy_sdk/modules/external/nnom/src/layers/nnom_lstm_cell.o \
./galaxy_sdk/modules/external/nnom/src/layers/nnom_matrix.o \
./galaxy_sdk/modules/external/nnom/src/layers/nnom_maxpool.o \
./galaxy_sdk/modules/external/nnom/src/layers/nnom_output.o \
./galaxy_sdk/modules/external/nnom/src/layers/nnom_reshape.o \
./galaxy_sdk/modules/external/nnom/src/layers/nnom_rnn.o \
./galaxy_sdk/modules/external/nnom/src/layers/nnom_simple_cell.o \
./galaxy_sdk/modules/external/nnom/src/layers/nnom_softmax.o \
./galaxy_sdk/modules/external/nnom/src/layers/nnom_sumpool.o \
./galaxy_sdk/modules/external/nnom/src/layers/nnom_upsample.o \
./galaxy_sdk/modules/external/nnom/src/layers/nnom_zero_padding.o 


# Each subdirectory must supply rules for building sources it contributes
galaxy_sdk/modules/external/nnom/src/layers/nnom_activation.o: D:/NucleiStudio/2025/01quant/VeriHealthi_QEMU_SDK_v3.4/galaxy_sdk/modules/external/nnom/src/layers/nnom_activation.c galaxy_sdk/modules/external/nnom/src/layers/subdir.mk
	@echo 'Building file: $<'
	@echo 'Invoking: GNU RISC-V Cross C Compiler'
	riscv64-unknown-elf-gcc -march=rv32imafc_xxldsp -mabi=ilp32f -mtune=nuclei-300-series -mcmodel=medlow -mno-save-restore -O2 -ffunction-sections -fdata-sections -fno-common -Werror -Wall -g -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\include\arch\riscv\n309" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\include" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\config\include" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\drivers\include" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\external\riscv_dsp\include" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\external\nnom\inc\layers" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\external\nnom\port" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\external\nnom\inc" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\external\riscv_dsp\PrivateInclude" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\include" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\include" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\osal\include" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\prebuilts\bluetooth\health\include" -std=gnu11 -MMD -MP -MF"$(@:%.o=%.d)" -MT"$@" -c -o "$@" "$<"
	@echo 'Finished building: $<'
	@echo ' '

galaxy_sdk/modules/external/nnom/src/layers/nnom_avgpool.o: D:/NucleiStudio/2025/01quant/VeriHealthi_QEMU_SDK_v3.4/galaxy_sdk/modules/external/nnom/src/layers/nnom_avgpool.c galaxy_sdk/modules/external/nnom/src/layers/subdir.mk
	@echo 'Building file: $<'
	@echo 'Invoking: GNU RISC-V Cross C Compiler'
	riscv64-unknown-elf-gcc -march=rv32imafc_xxldsp -mabi=ilp32f -mtune=nuclei-300-series -mcmodel=medlow -mno-save-restore -O2 -ffunction-sections -fdata-sections -fno-common -Werror -Wall -g -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\include\arch\riscv\n309" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\include" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\config\include" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\drivers\include" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\external\riscv_dsp\include" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\external\nnom\inc\layers" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\external\nnom\port" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\external\nnom\inc" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\external\riscv_dsp\PrivateInclude" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\include" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\include" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\osal\include" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\prebuilts\bluetooth\health\include" -std=gnu11 -MMD -MP -MF"$(@:%.o=%.d)" -MT"$@" -c -o "$@" "$<"
	@echo 'Finished building: $<'
	@echo ' '

galaxy_sdk/modules/external/nnom/src/layers/nnom_baselayer.o: D:/NucleiStudio/2025/01quant/VeriHealthi_QEMU_SDK_v3.4/galaxy_sdk/modules/external/nnom/src/layers/nnom_baselayer.c galaxy_sdk/modules/external/nnom/src/layers/subdir.mk
	@echo 'Building file: $<'
	@echo 'Invoking: GNU RISC-V Cross C Compiler'
	riscv64-unknown-elf-gcc -march=rv32imafc_xxldsp -mabi=ilp32f -mtune=nuclei-300-series -mcmodel=medlow -mno-save-restore -O2 -ffunction-sections -fdata-sections -fno-common -Werror -Wall -g -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\include\arch\riscv\n309" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\include" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\config\include" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\drivers\include" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\external\riscv_dsp\include" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\external\nnom\inc\layers" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\external\nnom\port" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\external\nnom\inc" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\external\riscv_dsp\PrivateInclude" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\include" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\include" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\osal\include" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\prebuilts\bluetooth\health\include" -std=gnu11 -MMD -MP -MF"$(@:%.o=%.d)" -MT"$@" -c -o "$@" "$<"
	@echo 'Finished building: $<'
	@echo ' '

galaxy_sdk/modules/external/nnom/src/layers/nnom_concat.o: D:/NucleiStudio/2025/01quant/VeriHealthi_QEMU_SDK_v3.4/galaxy_sdk/modules/external/nnom/src/layers/nnom_concat.c galaxy_sdk/modules/external/nnom/src/layers/subdir.mk
	@echo 'Building file: $<'
	@echo 'Invoking: GNU RISC-V Cross C Compiler'
	riscv64-unknown-elf-gcc -march=rv32imafc_xxldsp -mabi=ilp32f -mtune=nuclei-300-series -mcmodel=medlow -mno-save-restore -O2 -ffunction-sections -fdata-sections -fno-common -Werror -Wall -g -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\include\arch\riscv\n309" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\include" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\config\include" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\drivers\include" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\external\riscv_dsp\include" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\external\nnom\inc\layers" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\external\nnom\port" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\external\nnom\inc" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\external\riscv_dsp\PrivateInclude" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\include" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\include" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\osal\include" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\prebuilts\bluetooth\health\include" -std=gnu11 -MMD -MP -MF"$(@:%.o=%.d)" -MT"$@" -c -o "$@" "$<"
	@echo 'Finished building: $<'
	@echo ' '

galaxy_sdk/modules/external/nnom/src/layers/nnom_conv2d.o: D:/NucleiStudio/2025/01quant/VeriHealthi_QEMU_SDK_v3.4/galaxy_sdk/modules/external/nnom/src/layers/nnom_conv2d.c galaxy_sdk/modules/external/nnom/src/layers/subdir.mk
	@echo 'Building file: $<'
	@echo 'Invoking: GNU RISC-V Cross C Compiler'
	riscv64-unknown-elf-gcc -march=rv32imafc_xxldsp -mabi=ilp32f -mtune=nuclei-300-series -mcmodel=medlow -mno-save-restore -O2 -ffunction-sections -fdata-sections -fno-common -Werror -Wall -g -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\include\arch\riscv\n309" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\include" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\config\include" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\drivers\include" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\external\riscv_dsp\include" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\external\nnom\inc\layers" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\external\nnom\port" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\external\nnom\inc" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\external\riscv_dsp\PrivateInclude" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\include" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\include" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\osal\include" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\prebuilts\bluetooth\health\include" -std=gnu11 -MMD -MP -MF"$(@:%.o=%.d)" -MT"$@" -c -o "$@" "$<"
	@echo 'Finished building: $<'
	@echo ' '

galaxy_sdk/modules/external/nnom/src/layers/nnom_conv2d_trans.o: D:/NucleiStudio/2025/01quant/VeriHealthi_QEMU_SDK_v3.4/galaxy_sdk/modules/external/nnom/src/layers/nnom_conv2d_trans.c galaxy_sdk/modules/external/nnom/src/layers/subdir.mk
	@echo 'Building file: $<'
	@echo 'Invoking: GNU RISC-V Cross C Compiler'
	riscv64-unknown-elf-gcc -march=rv32imafc_xxldsp -mabi=ilp32f -mtune=nuclei-300-series -mcmodel=medlow -mno-save-restore -O2 -ffunction-sections -fdata-sections -fno-common -Werror -Wall -g -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\include\arch\riscv\n309" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\include" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\config\include" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\drivers\include" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\external\riscv_dsp\include" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\external\nnom\inc\layers" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\external\nnom\port" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\external\nnom\inc" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\external\riscv_dsp\PrivateInclude" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\include" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\include" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\osal\include" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\prebuilts\bluetooth\health\include" -std=gnu11 -MMD -MP -MF"$(@:%.o=%.d)" -MT"$@" -c -o "$@" "$<"
	@echo 'Finished building: $<'
	@echo ' '

galaxy_sdk/modules/external/nnom/src/layers/nnom_cropping.o: D:/NucleiStudio/2025/01quant/VeriHealthi_QEMU_SDK_v3.4/galaxy_sdk/modules/external/nnom/src/layers/nnom_cropping.c galaxy_sdk/modules/external/nnom/src/layers/subdir.mk
	@echo 'Building file: $<'
	@echo 'Invoking: GNU RISC-V Cross C Compiler'
	riscv64-unknown-elf-gcc -march=rv32imafc_xxldsp -mabi=ilp32f -mtune=nuclei-300-series -mcmodel=medlow -mno-save-restore -O2 -ffunction-sections -fdata-sections -fno-common -Werror -Wall -g -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\include\arch\riscv\n309" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\include" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\config\include" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\drivers\include" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\external\riscv_dsp\include" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\external\nnom\inc\layers" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\external\nnom\port" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\external\nnom\inc" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\external\riscv_dsp\PrivateInclude" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\include" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\include" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\osal\include" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\prebuilts\bluetooth\health\include" -std=gnu11 -MMD -MP -MF"$(@:%.o=%.d)" -MT"$@" -c -o "$@" "$<"
	@echo 'Finished building: $<'
	@echo ' '

galaxy_sdk/modules/external/nnom/src/layers/nnom_dense.o: D:/NucleiStudio/2025/01quant/VeriHealthi_QEMU_SDK_v3.4/galaxy_sdk/modules/external/nnom/src/layers/nnom_dense.c galaxy_sdk/modules/external/nnom/src/layers/subdir.mk
	@echo 'Building file: $<'
	@echo 'Invoking: GNU RISC-V Cross C Compiler'
	riscv64-unknown-elf-gcc -march=rv32imafc_xxldsp -mabi=ilp32f -mtune=nuclei-300-series -mcmodel=medlow -mno-save-restore -O2 -ffunction-sections -fdata-sections -fno-common -Werror -Wall -g -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\include\arch\riscv\n309" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\include" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\config\include" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\drivers\include" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\external\riscv_dsp\include" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\external\nnom\inc\layers" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\external\nnom\port" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\external\nnom\inc" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\external\riscv_dsp\PrivateInclude" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\include" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\include" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\osal\include" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\prebuilts\bluetooth\health\include" -std=gnu11 -MMD -MP -MF"$(@:%.o=%.d)" -MT"$@" -c -o "$@" "$<"
	@echo 'Finished building: $<'
	@echo ' '

galaxy_sdk/modules/external/nnom/src/layers/nnom_dw_conv2d.o: D:/NucleiStudio/2025/01quant/VeriHealthi_QEMU_SDK_v3.4/galaxy_sdk/modules/external/nnom/src/layers/nnom_dw_conv2d.c galaxy_sdk/modules/external/nnom/src/layers/subdir.mk
	@echo 'Building file: $<'
	@echo 'Invoking: GNU RISC-V Cross C Compiler'
	riscv64-unknown-elf-gcc -march=rv32imafc_xxldsp -mabi=ilp32f -mtune=nuclei-300-series -mcmodel=medlow -mno-save-restore -O2 -ffunction-sections -fdata-sections -fno-common -Werror -Wall -g -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\include\arch\riscv\n309" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\include" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\config\include" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\drivers\include" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\external\riscv_dsp\include" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\external\nnom\inc\layers" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\external\nnom\port" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\external\nnom\inc" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\external\riscv_dsp\PrivateInclude" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\include" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\include" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\osal\include" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\prebuilts\bluetooth\health\include" -std=gnu11 -MMD -MP -MF"$(@:%.o=%.d)" -MT"$@" -c -o "$@" "$<"
	@echo 'Finished building: $<'
	@echo ' '

galaxy_sdk/modules/external/nnom/src/layers/nnom_flatten.o: D:/NucleiStudio/2025/01quant/VeriHealthi_QEMU_SDK_v3.4/galaxy_sdk/modules/external/nnom/src/layers/nnom_flatten.c galaxy_sdk/modules/external/nnom/src/layers/subdir.mk
	@echo 'Building file: $<'
	@echo 'Invoking: GNU RISC-V Cross C Compiler'
	riscv64-unknown-elf-gcc -march=rv32imafc_xxldsp -mabi=ilp32f -mtune=nuclei-300-series -mcmodel=medlow -mno-save-restore -O2 -ffunction-sections -fdata-sections -fno-common -Werror -Wall -g -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\include\arch\riscv\n309" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\include" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\config\include" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\drivers\include" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\external\riscv_dsp\include" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\external\nnom\inc\layers" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\external\nnom\port" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\external\nnom\inc" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\external\riscv_dsp\PrivateInclude" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\include" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\include" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\osal\include" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\prebuilts\bluetooth\health\include" -std=gnu11 -MMD -MP -MF"$(@:%.o=%.d)" -MT"$@" -c -o "$@" "$<"
	@echo 'Finished building: $<'
	@echo ' '

galaxy_sdk/modules/external/nnom/src/layers/nnom_global_pool.o: D:/NucleiStudio/2025/01quant/VeriHealthi_QEMU_SDK_v3.4/galaxy_sdk/modules/external/nnom/src/layers/nnom_global_pool.c galaxy_sdk/modules/external/nnom/src/layers/subdir.mk
	@echo 'Building file: $<'
	@echo 'Invoking: GNU RISC-V Cross C Compiler'
	riscv64-unknown-elf-gcc -march=rv32imafc_xxldsp -mabi=ilp32f -mtune=nuclei-300-series -mcmodel=medlow -mno-save-restore -O2 -ffunction-sections -fdata-sections -fno-common -Werror -Wall -g -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\include\arch\riscv\n309" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\include" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\config\include" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\drivers\include" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\external\riscv_dsp\include" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\external\nnom\inc\layers" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\external\nnom\port" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\external\nnom\inc" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\external\riscv_dsp\PrivateInclude" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\include" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\include" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\osal\include" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\prebuilts\bluetooth\health\include" -std=gnu11 -MMD -MP -MF"$(@:%.o=%.d)" -MT"$@" -c -o "$@" "$<"
	@echo 'Finished building: $<'
	@echo ' '

galaxy_sdk/modules/external/nnom/src/layers/nnom_gru_cell.o: D:/NucleiStudio/2025/01quant/VeriHealthi_QEMU_SDK_v3.4/galaxy_sdk/modules/external/nnom/src/layers/nnom_gru_cell.c galaxy_sdk/modules/external/nnom/src/layers/subdir.mk
	@echo 'Building file: $<'
	@echo 'Invoking: GNU RISC-V Cross C Compiler'
	riscv64-unknown-elf-gcc -march=rv32imafc_xxldsp -mabi=ilp32f -mtune=nuclei-300-series -mcmodel=medlow -mno-save-restore -O2 -ffunction-sections -fdata-sections -fno-common -Werror -Wall -g -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\include\arch\riscv\n309" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\include" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\config\include" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\drivers\include" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\external\riscv_dsp\include" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\external\nnom\inc\layers" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\external\nnom\port" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\external\nnom\inc" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\external\riscv_dsp\PrivateInclude" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\include" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\include" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\osal\include" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\prebuilts\bluetooth\health\include" -std=gnu11 -MMD -MP -MF"$(@:%.o=%.d)" -MT"$@" -c -o "$@" "$<"
	@echo 'Finished building: $<'
	@echo ' '

galaxy_sdk/modules/external/nnom/src/layers/nnom_input.o: D:/NucleiStudio/2025/01quant/VeriHealthi_QEMU_SDK_v3.4/galaxy_sdk/modules/external/nnom/src/layers/nnom_input.c galaxy_sdk/modules/external/nnom/src/layers/subdir.mk
	@echo 'Building file: $<'
	@echo 'Invoking: GNU RISC-V Cross C Compiler'
	riscv64-unknown-elf-gcc -march=rv32imafc_xxldsp -mabi=ilp32f -mtune=nuclei-300-series -mcmodel=medlow -mno-save-restore -O2 -ffunction-sections -fdata-sections -fno-common -Werror -Wall -g -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\include\arch\riscv\n309" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\include" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\config\include" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\drivers\include" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\external\riscv_dsp\include" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\external\nnom\inc\layers" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\external\nnom\port" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\external\nnom\inc" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\external\riscv_dsp\PrivateInclude" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\include" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\include" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\osal\include" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\prebuilts\bluetooth\health\include" -std=gnu11 -MMD -MP -MF"$(@:%.o=%.d)" -MT"$@" -c -o "$@" "$<"
	@echo 'Finished building: $<'
	@echo ' '

galaxy_sdk/modules/external/nnom/src/layers/nnom_lambda.o: D:/NucleiStudio/2025/01quant/VeriHealthi_QEMU_SDK_v3.4/galaxy_sdk/modules/external/nnom/src/layers/nnom_lambda.c galaxy_sdk/modules/external/nnom/src/layers/subdir.mk
	@echo 'Building file: $<'
	@echo 'Invoking: GNU RISC-V Cross C Compiler'
	riscv64-unknown-elf-gcc -march=rv32imafc_xxldsp -mabi=ilp32f -mtune=nuclei-300-series -mcmodel=medlow -mno-save-restore -O2 -ffunction-sections -fdata-sections -fno-common -Werror -Wall -g -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\include\arch\riscv\n309" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\include" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\config\include" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\drivers\include" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\external\riscv_dsp\include" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\external\nnom\inc\layers" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\external\nnom\port" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\external\nnom\inc" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\external\riscv_dsp\PrivateInclude" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\include" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\include" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\osal\include" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\prebuilts\bluetooth\health\include" -std=gnu11 -MMD -MP -MF"$(@:%.o=%.d)" -MT"$@" -c -o "$@" "$<"
	@echo 'Finished building: $<'
	@echo ' '

galaxy_sdk/modules/external/nnom/src/layers/nnom_lstm_cell.o: D:/NucleiStudio/2025/01quant/VeriHealthi_QEMU_SDK_v3.4/galaxy_sdk/modules/external/nnom/src/layers/nnom_lstm_cell.c galaxy_sdk/modules/external/nnom/src/layers/subdir.mk
	@echo 'Building file: $<'
	@echo 'Invoking: GNU RISC-V Cross C Compiler'
	riscv64-unknown-elf-gcc -march=rv32imafc_xxldsp -mabi=ilp32f -mtune=nuclei-300-series -mcmodel=medlow -mno-save-restore -O2 -ffunction-sections -fdata-sections -fno-common -Werror -Wall -g -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\include\arch\riscv\n309" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\include" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\config\include" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\drivers\include" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\external\riscv_dsp\include" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\external\nnom\inc\layers" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\external\nnom\port" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\external\nnom\inc" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\external\riscv_dsp\PrivateInclude" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\include" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\include" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\osal\include" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\prebuilts\bluetooth\health\include" -std=gnu11 -MMD -MP -MF"$(@:%.o=%.d)" -MT"$@" -c -o "$@" "$<"
	@echo 'Finished building: $<'
	@echo ' '

galaxy_sdk/modules/external/nnom/src/layers/nnom_matrix.o: D:/NucleiStudio/2025/01quant/VeriHealthi_QEMU_SDK_v3.4/galaxy_sdk/modules/external/nnom/src/layers/nnom_matrix.c galaxy_sdk/modules/external/nnom/src/layers/subdir.mk
	@echo 'Building file: $<'
	@echo 'Invoking: GNU RISC-V Cross C Compiler'
	riscv64-unknown-elf-gcc -march=rv32imafc_xxldsp -mabi=ilp32f -mtune=nuclei-300-series -mcmodel=medlow -mno-save-restore -O2 -ffunction-sections -fdata-sections -fno-common -Werror -Wall -g -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\include\arch\riscv\n309" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\include" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\config\include" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\drivers\include" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\external\riscv_dsp\include" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\external\nnom\inc\layers" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\external\nnom\port" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\external\nnom\inc" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\external\riscv_dsp\PrivateInclude" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\include" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\include" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\osal\include" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\prebuilts\bluetooth\health\include" -std=gnu11 -MMD -MP -MF"$(@:%.o=%.d)" -MT"$@" -c -o "$@" "$<"
	@echo 'Finished building: $<'
	@echo ' '

galaxy_sdk/modules/external/nnom/src/layers/nnom_maxpool.o: D:/NucleiStudio/2025/01quant/VeriHealthi_QEMU_SDK_v3.4/galaxy_sdk/modules/external/nnom/src/layers/nnom_maxpool.c galaxy_sdk/modules/external/nnom/src/layers/subdir.mk
	@echo 'Building file: $<'
	@echo 'Invoking: GNU RISC-V Cross C Compiler'
	riscv64-unknown-elf-gcc -march=rv32imafc_xxldsp -mabi=ilp32f -mtune=nuclei-300-series -mcmodel=medlow -mno-save-restore -O2 -ffunction-sections -fdata-sections -fno-common -Werror -Wall -g -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\include\arch\riscv\n309" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\include" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\config\include" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\drivers\include" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\external\riscv_dsp\include" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\external\nnom\inc\layers" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\external\nnom\port" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\external\nnom\inc" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\external\riscv_dsp\PrivateInclude" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\include" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\include" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\osal\include" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\prebuilts\bluetooth\health\include" -std=gnu11 -MMD -MP -MF"$(@:%.o=%.d)" -MT"$@" -c -o "$@" "$<"
	@echo 'Finished building: $<'
	@echo ' '

galaxy_sdk/modules/external/nnom/src/layers/nnom_output.o: D:/NucleiStudio/2025/01quant/VeriHealthi_QEMU_SDK_v3.4/galaxy_sdk/modules/external/nnom/src/layers/nnom_output.c galaxy_sdk/modules/external/nnom/src/layers/subdir.mk
	@echo 'Building file: $<'
	@echo 'Invoking: GNU RISC-V Cross C Compiler'
	riscv64-unknown-elf-gcc -march=rv32imafc_xxldsp -mabi=ilp32f -mtune=nuclei-300-series -mcmodel=medlow -mno-save-restore -O2 -ffunction-sections -fdata-sections -fno-common -Werror -Wall -g -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\include\arch\riscv\n309" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\include" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\config\include" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\drivers\include" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\external\riscv_dsp\include" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\external\nnom\inc\layers" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\external\nnom\port" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\external\nnom\inc" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\external\riscv_dsp\PrivateInclude" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\include" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\include" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\osal\include" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\prebuilts\bluetooth\health\include" -std=gnu11 -MMD -MP -MF"$(@:%.o=%.d)" -MT"$@" -c -o "$@" "$<"
	@echo 'Finished building: $<'
	@echo ' '

galaxy_sdk/modules/external/nnom/src/layers/nnom_reshape.o: D:/NucleiStudio/2025/01quant/VeriHealthi_QEMU_SDK_v3.4/galaxy_sdk/modules/external/nnom/src/layers/nnom_reshape.c galaxy_sdk/modules/external/nnom/src/layers/subdir.mk
	@echo 'Building file: $<'
	@echo 'Invoking: GNU RISC-V Cross C Compiler'
	riscv64-unknown-elf-gcc -march=rv32imafc_xxldsp -mabi=ilp32f -mtune=nuclei-300-series -mcmodel=medlow -mno-save-restore -O2 -ffunction-sections -fdata-sections -fno-common -Werror -Wall -g -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\include\arch\riscv\n309" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\include" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\config\include" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\drivers\include" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\external\riscv_dsp\include" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\external\nnom\inc\layers" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\external\nnom\port" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\external\nnom\inc" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\external\riscv_dsp\PrivateInclude" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\include" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\include" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\osal\include" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\prebuilts\bluetooth\health\include" -std=gnu11 -MMD -MP -MF"$(@:%.o=%.d)" -MT"$@" -c -o "$@" "$<"
	@echo 'Finished building: $<'
	@echo ' '

galaxy_sdk/modules/external/nnom/src/layers/nnom_rnn.o: D:/NucleiStudio/2025/01quant/VeriHealthi_QEMU_SDK_v3.4/galaxy_sdk/modules/external/nnom/src/layers/nnom_rnn.c galaxy_sdk/modules/external/nnom/src/layers/subdir.mk
	@echo 'Building file: $<'
	@echo 'Invoking: GNU RISC-V Cross C Compiler'
	riscv64-unknown-elf-gcc -march=rv32imafc_xxldsp -mabi=ilp32f -mtune=nuclei-300-series -mcmodel=medlow -mno-save-restore -O2 -ffunction-sections -fdata-sections -fno-common -Werror -Wall -g -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\include\arch\riscv\n309" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\include" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\config\include" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\drivers\include" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\external\riscv_dsp\include" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\external\nnom\inc\layers" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\external\nnom\port" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\external\nnom\inc" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\external\riscv_dsp\PrivateInclude" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\include" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\include" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\osal\include" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\prebuilts\bluetooth\health\include" -std=gnu11 -MMD -MP -MF"$(@:%.o=%.d)" -MT"$@" -c -o "$@" "$<"
	@echo 'Finished building: $<'
	@echo ' '

galaxy_sdk/modules/external/nnom/src/layers/nnom_simple_cell.o: D:/NucleiStudio/2025/01quant/VeriHealthi_QEMU_SDK_v3.4/galaxy_sdk/modules/external/nnom/src/layers/nnom_simple_cell.c galaxy_sdk/modules/external/nnom/src/layers/subdir.mk
	@echo 'Building file: $<'
	@echo 'Invoking: GNU RISC-V Cross C Compiler'
	riscv64-unknown-elf-gcc -march=rv32imafc_xxldsp -mabi=ilp32f -mtune=nuclei-300-series -mcmodel=medlow -mno-save-restore -O2 -ffunction-sections -fdata-sections -fno-common -Werror -Wall -g -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\include\arch\riscv\n309" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\include" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\config\include" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\drivers\include" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\external\riscv_dsp\include" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\external\nnom\inc\layers" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\external\nnom\port" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\external\nnom\inc" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\external\riscv_dsp\PrivateInclude" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\include" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\include" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\osal\include" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\prebuilts\bluetooth\health\include" -std=gnu11 -MMD -MP -MF"$(@:%.o=%.d)" -MT"$@" -c -o "$@" "$<"
	@echo 'Finished building: $<'
	@echo ' '

galaxy_sdk/modules/external/nnom/src/layers/nnom_softmax.o: D:/NucleiStudio/2025/01quant/VeriHealthi_QEMU_SDK_v3.4/galaxy_sdk/modules/external/nnom/src/layers/nnom_softmax.c galaxy_sdk/modules/external/nnom/src/layers/subdir.mk
	@echo 'Building file: $<'
	@echo 'Invoking: GNU RISC-V Cross C Compiler'
	riscv64-unknown-elf-gcc -march=rv32imafc_xxldsp -mabi=ilp32f -mtune=nuclei-300-series -mcmodel=medlow -mno-save-restore -O2 -ffunction-sections -fdata-sections -fno-common -Werror -Wall -g -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\include\arch\riscv\n309" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\include" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\config\include" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\drivers\include" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\external\riscv_dsp\include" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\external\nnom\inc\layers" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\external\nnom\port" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\external\nnom\inc" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\external\riscv_dsp\PrivateInclude" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\include" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\include" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\osal\include" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\prebuilts\bluetooth\health\include" -std=gnu11 -MMD -MP -MF"$(@:%.o=%.d)" -MT"$@" -c -o "$@" "$<"
	@echo 'Finished building: $<'
	@echo ' '

galaxy_sdk/modules/external/nnom/src/layers/nnom_sumpool.o: D:/NucleiStudio/2025/01quant/VeriHealthi_QEMU_SDK_v3.4/galaxy_sdk/modules/external/nnom/src/layers/nnom_sumpool.c galaxy_sdk/modules/external/nnom/src/layers/subdir.mk
	@echo 'Building file: $<'
	@echo 'Invoking: GNU RISC-V Cross C Compiler'
	riscv64-unknown-elf-gcc -march=rv32imafc_xxldsp -mabi=ilp32f -mtune=nuclei-300-series -mcmodel=medlow -mno-save-restore -O2 -ffunction-sections -fdata-sections -fno-common -Werror -Wall -g -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\include\arch\riscv\n309" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\include" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\config\include" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\drivers\include" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\external\riscv_dsp\include" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\external\nnom\inc\layers" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\external\nnom\port" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\external\nnom\inc" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\external\riscv_dsp\PrivateInclude" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\include" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\include" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\osal\include" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\prebuilts\bluetooth\health\include" -std=gnu11 -MMD -MP -MF"$(@:%.o=%.d)" -MT"$@" -c -o "$@" "$<"
	@echo 'Finished building: $<'
	@echo ' '

galaxy_sdk/modules/external/nnom/src/layers/nnom_upsample.o: D:/NucleiStudio/2025/01quant/VeriHealthi_QEMU_SDK_v3.4/galaxy_sdk/modules/external/nnom/src/layers/nnom_upsample.c galaxy_sdk/modules/external/nnom/src/layers/subdir.mk
	@echo 'Building file: $<'
	@echo 'Invoking: GNU RISC-V Cross C Compiler'
	riscv64-unknown-elf-gcc -march=rv32imafc_xxldsp -mabi=ilp32f -mtune=nuclei-300-series -mcmodel=medlow -mno-save-restore -O2 -ffunction-sections -fdata-sections -fno-common -Werror -Wall -g -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\include\arch\riscv\n309" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\include" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\config\include" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\drivers\include" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\external\riscv_dsp\include" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\external\nnom\inc\layers" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\external\nnom\port" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\external\nnom\inc" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\external\riscv_dsp\PrivateInclude" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\include" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\include" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\osal\include" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\prebuilts\bluetooth\health\include" -std=gnu11 -MMD -MP -MF"$(@:%.o=%.d)" -MT"$@" -c -o "$@" "$<"
	@echo 'Finished building: $<'
	@echo ' '

galaxy_sdk/modules/external/nnom/src/layers/nnom_zero_padding.o: D:/NucleiStudio/2025/01quant/VeriHealthi_QEMU_SDK_v3.4/galaxy_sdk/modules/external/nnom/src/layers/nnom_zero_padding.c galaxy_sdk/modules/external/nnom/src/layers/subdir.mk
	@echo 'Building file: $<'
	@echo 'Invoking: GNU RISC-V Cross C Compiler'
	riscv64-unknown-elf-gcc -march=rv32imafc_xxldsp -mabi=ilp32f -mtune=nuclei-300-series -mcmodel=medlow -mno-save-restore -O2 -ffunction-sections -fdata-sections -fno-common -Werror -Wall -g -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\include\arch\riscv\n309" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\include" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\config\include" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\drivers\include" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\external\riscv_dsp\include" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\external\nnom\inc\layers" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\external\nnom\port" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\external\nnom\inc" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\external\riscv_dsp\PrivateInclude" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\include" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\include" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\osal\include" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\prebuilts\bluetooth\health\include" -std=gnu11 -MMD -MP -MF"$(@:%.o=%.d)" -MT"$@" -c -o "$@" "$<"
	@echo 'Finished building: $<'
	@echo ' '


