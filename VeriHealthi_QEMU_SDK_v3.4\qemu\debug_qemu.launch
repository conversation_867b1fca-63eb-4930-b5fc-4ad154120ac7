<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<launchConfiguration type="org.riscvstudio.debug.gdbjtag.qemu.riscv.launchConfigurationType">
    <stringAttribute key="ilg.gnumcueclipse.debug.gdbjtag.svdPath" value=""/>
    <stringAttribute key="org.eclipse.cdt.debug.gdbjtag.core.imageFileName" value=""/>
    <stringAttribute key="org.eclipse.cdt.debug.gdbjtag.core.imageOffset" value=""/>
    <stringAttribute key="org.eclipse.cdt.debug.gdbjtag.core.ipAddress" value="localhost"/>
    <stringAttribute key="org.eclipse.cdt.debug.gdbjtag.core.jtagDevice" value="GNU MCU QEMU"/>
    <booleanAttribute key="org.eclipse.cdt.debug.gdbjtag.core.loadImage" value="true"/>
    <booleanAttribute key="org.eclipse.cdt.debug.gdbjtag.core.loadSymbols" value="true"/>
    <stringAttribute key="org.eclipse.cdt.debug.gdbjtag.core.pcRegister" value=""/>
    <intAttribute key="org.eclipse.cdt.debug.gdbjtag.core.portNumber" value="1234"/>
    <booleanAttribute key="org.eclipse.cdt.debug.gdbjtag.core.setPcRegister" value="false"/>
    <booleanAttribute key="org.eclipse.cdt.debug.gdbjtag.core.setResume" value="false"/>
    <booleanAttribute key="org.eclipse.cdt.debug.gdbjtag.core.setStopAt" value="true"/>
    <stringAttribute key="org.eclipse.cdt.debug.gdbjtag.core.stopAt" value="main"/>
    <stringAttribute key="org.eclipse.cdt.debug.gdbjtag.core.symbolsFileName" value=""/>
    <stringAttribute key="org.eclipse.cdt.debug.gdbjtag.core.symbolsOffset" value=""/>
    <booleanAttribute key="org.eclipse.cdt.debug.gdbjtag.core.useFileForImage" value="false"/>
    <booleanAttribute key="org.eclipse.cdt.debug.gdbjtag.core.useFileForSymbols" value="false"/>
    <booleanAttribute key="org.eclipse.cdt.debug.gdbjtag.core.useProjBinaryForImage" value="true"/>
    <booleanAttribute key="org.eclipse.cdt.debug.gdbjtag.core.useProjBinaryForSymbols" value="true"/>
    <booleanAttribute key="org.eclipse.cdt.debug.gdbjtag.core.useRemoteTarget" value="true"/>
    <stringAttribute key="org.eclipse.cdt.dsf.gdb.DEBUG_NAME" value="${cross_prefix}gdb${cross_suffix}"/>
    <booleanAttribute key="org.eclipse.cdt.dsf.gdb.UPDATE_THREADLIST_ON_SUSPEND" value="false"/>
    <intAttribute key="org.eclipse.cdt.launch.ATTR_BUILD_BEFORE_LAUNCH_ATTR" value="2"/>
    <stringAttribute key="org.eclipse.cdt.launch.COREFILE_PATH" value=""/>
    <stringAttribute key="org.eclipse.cdt.launch.PROGRAM_NAME" value="Release/qemu.out"/>
    <stringAttribute key="org.eclipse.cdt.launch.PROJECT_ATTR" value="qemu"/>
    <booleanAttribute key="org.eclipse.cdt.launch.PROJECT_BUILD_CONFIG_AUTO_ATTR" value="false"/>
    <stringAttribute key="org.eclipse.cdt.launch.PROJECT_BUILD_CONFIG_ID_ATTR" value=""/>
    <booleanAttribute key="org.eclipse.debug.core.ATTR_FORCE_SYSTEM_CONSOLE_ENCODING" value="false"/>
    <listAttribute key="org.eclipse.debug.core.MAPPED_RESOURCE_PATHS">
        <listEntry value="/qemu"/>
    </listAttribute>
    <listAttribute key="org.eclipse.debug.core.MAPPED_RESOURCE_TYPES">
        <listEntry value="4"/>
    </listAttribute>
    <stringAttribute key="org.eclipse.dsf.launch.MEMORY_BLOCKS" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot; standalone=&quot;no&quot;?&gt;&#13;&#10;&lt;memoryBlockExpressionList context=&quot;Context string&quot;&gt;&#13;&#10;    &lt;memoryBlockExpression address=&quot;2415976140&quot; label=&quot;0x9000decc&quot;/&gt;&#13;&#10;&lt;/memoryBlockExpressionList&gt;&#13;&#10;"/>
    <booleanAttribute key="org.riscvstudio.debug.gdbjtag.qemu.disableGraphics" value="true"/>
    <booleanAttribute key="org.riscvstudio.debug.gdbjtag.qemu.doContinue" value="true"/>
    <booleanAttribute key="org.riscvstudio.debug.gdbjtag.qemu.doDebugInRam" value="false"/>
    <booleanAttribute key="org.riscvstudio.debug.gdbjtag.qemu.doFirstReset" value="false"/>
    <booleanAttribute key="org.riscvstudio.debug.gdbjtag.qemu.doGdbServerAllocateConsole" value="true"/>
    <booleanAttribute key="org.riscvstudio.debug.gdbjtag.qemu.doGdbServerPreferXpacksBin" value="false"/>
    <booleanAttribute key="org.riscvstudio.debug.gdbjtag.qemu.doSecondReset" value="true"/>
    <booleanAttribute key="org.riscvstudio.debug.gdbjtag.qemu.doStartGdbServer" value="true"/>
    <booleanAttribute key="org.riscvstudio.debug.gdbjtag.qemu.enableSemihosting" value="true"/>
    <stringAttribute key="org.riscvstudio.debug.gdbjtag.qemu.extension.vector.length" value="128"/>
    <stringAttribute key="org.riscvstudio.debug.gdbjtag.qemu.gdbClientOtherCommands" value="set mem inaccessible-by-default off&#10;set remotetimeout 250&#10;set arch riscv:rv32"/>
    <stringAttribute key="org.riscvstudio.debug.gdbjtag.qemu.gdbClientOtherOptions" value=""/>
    <stringAttribute key="org.riscvstudio.debug.gdbjtag.qemu.gdbCoreName" value="N307"/>
    <stringAttribute key="org.riscvstudio.debug.gdbjtag.qemu.gdbDownloadName" value="flashxip"/>
    <stringAttribute key="org.riscvstudio.debug.gdbjtag.qemu.gdbMachineBit" value="qemu-system-riscv32"/>
    <stringAttribute key="org.riscvstudio.debug.gdbjtag.qemu.gdbServerBoardName" value="nuclei_evalsoc"/>
    <intAttribute key="org.riscvstudio.debug.gdbjtag.qemu.gdbServerDelaySeconds" value="0"/>
    <stringAttribute key="org.riscvstudio.debug.gdbjtag.qemu.gdbServerDeviceName" value=""/>
    <stringAttribute key="org.riscvstudio.debug.gdbjtag.qemu.gdbServerExecutable" value="${qemu_riscv32_path}/${qemu_riscv32_executable}"/>
    <intAttribute key="org.riscvstudio.debug.gdbjtag.qemu.gdbServerGdbPortNumber" value="1234"/>
    <stringAttribute key="org.riscvstudio.debug.gdbjtag.qemu.gdbServerOther" value="-serial stdio -nodefaults -S"/>
    <stringAttribute key="org.riscvstudio.debug.gdbjtag.qemu.gdbServerQemuMachineName" value="nuclei_evalsoc"/>
    <stringAttribute key="org.riscvstudio.debug.gdbjtag.qemu.gdbServerSMPCount" value="1"/>
    <booleanAttribute key="org.riscvstudio.debug.gdbjtag.qemu.isGdbServerVerbose" value="false"/>
    <stringAttribute key="org.riscvstudio.debug.gdbjtag.qemu.other.extensions" value="_xxldsp"/>
    <stringAttribute key="org.riscvstudio.debug.gdbjtag.qemu.otherInitCommands" value=""/>
    <stringAttribute key="org.riscvstudio.debug.gdbjtag.qemu.otherRunCommands" value=""/>
    <stringAttribute key="org.riscvstudio.debug.gdbjtag.qemu.semihosting.cmdline" value=""/>
    <booleanAttribute key="org.riscvstudio.debug.gdbjtag.qemu.useNucleiProperty" value="true"/>
    <stringAttribute key="process_factory_id" value="org.eclipse.cdt.dsf.gdb.GdbProcessFactory"/>
    <stringAttribute key="saved_expressions&lt;seperator&gt;Unknown" value="0x9000decc,0x900101d0,0x90014910,0x9001aa10,0x9000DECC,0x900101D0,0x9001AA10,0x9000e824,0x90010350,0x90014a90,0x9001ab90"/>
</launchConfiguration>
