name: Python package

on: [push, pull_request]

jobs:
  test:

    runs-on: ubuntu-latest
    strategy:
      matrix:
        python-version: ["3.10"]

    steps:
      - uses: actions/checkout@v2
        with:
          submodules: true
      - name: Set up Python ${{ matrix.python-version }}
        uses: actions/setup-python@v2
        with:
          python-version: ${{ matrix.python-version }}

      - name: Install OS dependencies
        run: |
          sudo apt-get install -yqq python-tk scons git
      - name: Install Python dependencies
        run: |
          if [ -f requirements.txt ]; then pip install -r requirements.txt; fi
          if [ -f requirements.dev.txt ]; then pip install -r requirements.dev.txt; fi
      - name: Install as pip package
        run: |
          pip install ./ -v
      - name: Test installed package
        working-directory: examples/auto_test
        run: |
          python main.py -

