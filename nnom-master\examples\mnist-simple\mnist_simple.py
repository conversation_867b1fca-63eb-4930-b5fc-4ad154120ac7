'''
    Copyright (c) 2018-2020
    <PERSON><PERSON><PERSON><PERSON>@live.com
    SPDX-License-Identifier: Apache-2.0
    Change Logs:
    Date           Author       Notes
    2019-02-12     <PERSON><PERSON><PERSON><PERSON>   The first version
'''


import matplotlib.pyplot as plt
import os

from tensorflow.keras import *
from tensorflow.keras.datasets import mnist
from tensorflow.keras.layers import *
from tensorflow.keras.models import load_model, save_model
import tensorflow as tf
import numpy as np
from nnom import *

model_name = 'mnist_simple_trained_model.h5'

def image_to_cfile(data, label, num_of_image, file='image.h'):
    with open(file, 'w') as f:
        for i in range(num_of_image):
            selected = np.random.randint(0, 1000) # select 10 out of 1000.
            f.write('#define IMG%d {'% (i))
            np.round(data[selected]).flatten().tofile(f, sep=", ", format="%d") # convert 0~1 to 0~127
            f.write('} \n')
            f.write('#define IMG%d_LABLE'% (i))
            f.write(' %d \n \n' % label[selected])
        f.write('#define TOTAL_IMAGE %d \n \n'%(num_of_image))

        f.write('static const int8_t img[%d][%d] = {' % (num_of_image, data[0].flatten().shape[0]))
        f.write('IMG0')
        for i in range(num_of_image -1):
            f.write(',IMG%d'%(i+1))
        f.write('};\n\n')

        f.write('static const int8_t label[%d] = {' % (num_of_image))
        f.write('IMG0_LABLE')
        for i in range(num_of_image -1):
            f.write(',IMG%d_LABLE'%(i+1))
        f.write('};\n\n')


def train(x_train, y_train, x_test, y_test, batch_size=64, epochs=100):
    inputs = Input(shape=x_train.shape[1:])
    x = Conv2D(12, kernel_size=(3, 3), strides=(1, 1), padding='same')(inputs)
    x = ReLU()(x)
    x = MaxPool2D((2,2),strides=(2,2), padding="same")(x)

    x = Conv2D(24 ,kernel_size=(3,3), strides=(1,1), padding="same")(x)
    x = ReLU()(x)
    x = MaxPool2D((2,2),strides=(2,2), padding="same")(x)

    x = Conv2D(48, kernel_size=(3,3), strides=(1,1), padding="same")(x)
    x = ReLU()(x)
    x = Dropout(0.2)(x)
    x = MaxPool2D((2,2),strides=(2,2), padding="same")(x)

    x = Flatten()(x)
    x = Dense(96)(x)
    x = Dropout(0.2)(x)

    x = ReLU()(x)
    x = Dense(10)(x)
    predictions = Softmax()(x)

    model = Model(inputs=inputs, outputs=predictions)

    model.compile(loss='categorical_crossentropy',
                  optimizer='adam',
                  metrics=['accuracy'])
    model.summary()

    history =  model.fit(x_train, y_train,
              batch_size=batch_size,
              epochs=epochs,
              verbose=2,
              validation_data=(x_test, y_test),
              shuffle=True)

    # free the session to avoid nesting naming while we load the best model after.
    save_model(model, model_name)
    del model
    tf.keras.backend.clear_session()
    return history


if __name__ == "__main__":
    epochs = 2
    num_classes = 10

    # The data, split between train and test sets:
    (x_train, y_train_num), (x_test, y_test_num) = mnist.load_data()

    print(x_train.shape[0], 'train samples')
    print(x_test.shape[0], 'test samples')

    # Convert class vectors to binary class matrices.
    y_train = tf.keras.utils.to_categorical(y_train_num, num_classes)
    y_test = tf.keras.utils.to_categorical(y_test_num, num_classes)

    # reshape to 4 d becaue we build for 4d?
    x_train = x_train.reshape(x_train.shape[0], x_train.shape[1], x_train.shape[2], 1)
    x_test = x_test.reshape(x_test.shape[0], x_test.shape[1], x_test.shape[2], 1)
    print('x_train shape:', x_train.shape)

    # quantize the range to 0~255 -> 0~1
    x_test = x_test/255
    x_train = x_train/255
    print("data range", x_test.min(), x_test.max())

    # select a few image and write them to image.h
    image_to_cfile(x_test*127, y_test_num, 10, file='image.h')

    # train model, save the best accuracy model
    history = train(x_train, y_train, x_test, y_test, batch_size=64, epochs=epochs)

    # reload best model
    model = load_model(model_name)

    # evaluate
    evaluate_model(model, x_test, y_test)

    # save weight
    generate_model(model, np.vstack((x_train, x_test)), name="weights.h")

    # plot
    acc = history.history['accuracy']
    val_acc = history.history['val_accuracy']

    plt.plot(range(0, epochs), acc, color='red', label='Training acc')
    plt.plot(range(0, epochs), val_acc, color='green', label='Validation acc')
    plt.title('Training and validation accuracy')
    plt.xlabel('Epochs')
    plt.ylabel('Loss')
    plt.legend()
    plt.show()





















