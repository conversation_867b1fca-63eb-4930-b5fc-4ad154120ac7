#include "nnom.h"

/* Weights, bias and Q format */
#define TENSOR_CONV2D_KERNEL_0 {-20, 28, -1, -3, 25, -27, -33, -39, -18, -36, 16, 50, -51, -65, -20, 40, -6, -45, -67, -30, 63, 27, -17, 15, 39, -54, -3, -51, 31, 31, 27, -8, 66, -31, -22, -28, -41, -59, 35, -22, 47, 19, -37, 31, 58, -11, -37, -21, -10, 9, -44, 59, -61, 46, 21, 34, 13, -48, 54, 61, -87, 32, -28, 70, -28, 7, -20, -52, -42, -28, -33, 29, -42, -2, -34, 54, 49, 61, 32, -11, -35, 8, -30, -70, -54, -8, 30, 17, -46, 45, 29, 51, 39, 20, 0, -24, 35, -63, -45, 77, 38, 2, -40, -38, -48, 11, 16, 3}

#define TENSOR_CONV2D_KERNEL_0_DEC_BITS {8}

#define TENSOR_CONV2D_BIAS_0 {-5, 92, -15, 19, -119, -15, -82, 98, -18, 59, -45, -79}

#define TENSOR_CONV2D_BIAS_0_DEC_BITS {11}

#define CONV2D_BIAS_LSHIFT {4}

#define CONV2D_OUTPUT_RSHIFT {8}

#define TENSOR_CONV2D_4_KERNEL_0 {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}

#define TENSOR_CONV2D_4_KERNEL_0_DEC_BITS {8}

#define TENSOR_CONV2D_4_BIAS_0 {-22, -78, -28, -36, -8, -103, -79, -33, -70, -69, -64, -52}

#define TENSOR_CONV2D_4_BIAS_0_DEC_BITS {11}

#define CONV2D_4_BIAS_LSHIFT {4}

#define CONV2D_4_OUTPUT_RSHIFT {9}

#define TENSOR_CONV2D_3_KERNEL_0 {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}

#define TENSOR_CONV2D_3_KERNEL_0_DEC_BITS {8}

#define TENSOR_CONV2D_3_BIAS_0 {43, 40, -71, 94, 123, -59, -2, 114, -77, -106, -60, -35}

#define TENSOR_CONV2D_3_BIAS_0_DEC_BITS {12}

#define CONV2D_3_BIAS_LSHIFT {3}

#define CONV2D_3_OUTPUT_RSHIFT {9}

#define TENSOR_CONV2D_2_KERNEL_0 {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}

#define TENSOR_CONV2D_2_KERNEL_0_DEC_BITS {8}

#define TENSOR_CONV2D_2_BIAS_0 {-22, -78, -28, -36, -8, -103, -79, -33, -70, -69, -64, -52}

#define TENSOR_CONV2D_2_BIAS_0_DEC_BITS {11}

#define CONV2D_2_BIAS_LSHIFT {4}

#define CONV2D_2_OUTPUT_RSHIFT {9}

#define TENSOR_CONV2D_1_KERNEL_0 {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}

#define TENSOR_CONV2D_1_KERNEL_0_DEC_BITS {8}

#define TENSOR_CONV2D_1_BIAS_0 {43, 40, -71, 94, 123, -59, -2, 114, -77, -106, -60, -35}

#define TENSOR_CONV2D_1_BIAS_0_DEC_BITS {12}

#define CONV2D_1_BIAS_LSHIFT {3}

#define CONV2D_1_OUTPUT_RSHIFT {9}

#define TENSOR_CONV2D_7_KERNEL_0 {-5, -13, -4, 14, -42, -46, 13, -56, -8, -49, -36, -30, -9, -8, 29, 18, -32, -44, 7, -9, 19, 9, 14, -32, -10, -12, 35, -32, -38, -13, 37, -65, 5, 28, 46, -14, -29, -42, -45, 1, 19, -11, -16, -6, 39, -11, 21, 25, -19, -1, -41, 36, 32, 7, 1, 15, 5, -7, -13, -5, -36, -44, -5, 17, -4, -8, -32, 9, 17, -11, 7, 24, -4, -6, -9, 12, -27, 48, 17, 32, -61, -46, 19, 41, 4, 26, -25, 13, 24, -33, -5, -41, -31, -32, -32, -23, 3, -9, 23, -42, -10, 35, -40, 7, -9, -5, -44, -24, 2, -23, 6, -26, 22, -23, 19, -22, -18, 47, 22, 28, 53, -3, -35, 29, 27, 24, -37, -8, 10, 44, -1, -42, 0, -20, -27, -41, 27, 31, -12, -34, -28, -12, 22, -47, 20, -32, 40, 28, 18, 25, -56, -17, 0, -41, -30, 39, 74, -15, 1, 35, -20, 69, -34, 22, -32, -22, -1, -63, 20, -36, 36, -8, -12, 12, 9, -42, -46, -8, -29, 13, -2, -35, 2, 15, 29, -31, -43, -43, -21, -22, -18, 30, -20, 21, 36, -47, -40, -49, -59, -2, -17, -59, -14, 11, 21, -17, -24, 15, 12, -17, -9, -38, 29, -8, -15, 23, -16, 40, 15, 2, -28, 42, -39, -26, 28, -28, 37, 30, -9, 43, -25, -20, 7, 7, 29, -9, -31, -37, -17, 28, -42, -33, -39, -33, -53, -17, -41, -13, 31, -43, 7, 26, -10, -19, -21, 12, 42, 35, 36, 35, -26, 33, 37, -26, 6, -58, -10, -12, -41, 14, -53, 26, 16, -38, -12, 0, -34, -31, 22, -17, -13, -30, 11, -17, -23, -1, -47, -29, 19, -48, 9, -26, 17, 23, -44, 36, -23, 43, 23, 0, -12, -30, -25, -45, -19, -8, 8, 7, 20, -36, -34, -10, 5, -31, -9, -48, 4, -2, -17, 31, -11, 25, -9, 18, 25, 9, 11, -8, -13, 33, 16, 18, 35, 23, 7, 49, -31, 42, 16, -1, 17, -10, -2, -23, 14, 16, 37, -26, 38, 30, -56, -14, 26, -32, 9, 43, 37, 1, -13, 6, -3, -28, 50, -37, -34, 22, -42, -12, -47, -45, -53, 34, 3, -16, -17, -4, -5, -23, -11, -52, -4, -44, -24, 6, 20, -62, -24, 18, 1, -25, -14, -27, -54, 28, -51, -8, 14, -26, 13, 32, 5, 28, 21, -25, -8, -45, 25, 27, 65, 7, -78, -19, 11, 58, -20, -2, 12, 19, -9, -13, 8, 32, -37, -21, -8, 12, -18, 4, -13, 19, 4, -48, 38, -44, 15, 20, 29, 14, 21, -55, -48, 23, 47, -43, 22, 19, -23, -24, -8, 45, 31, 25, -40, -1, 7, 11, 15, 1, 45, -31, 16, -23, -2, -59, -8, -24, -11, 23, 9, 31, 25, -13, -26, 9, -35, -35, 32, 8, 4, 1, 23, 5, -44, -4, 46, 6, -4, -14, -27, 47, 0, -30, 28, -31, -56, -4, 26, 57, 32, -4, -10, -45, 21, 18, -45, 20, 4, 31, 42, -41, 15, -33, 13, 14, -56, -6, -26, -35, 12, 41, 58, -18, -31, 41, 16, -36, -50, -26, -24, 9, 35, -33, -20, 48, 3, 41, 21, 2, 21, 16, 14, -13, -18, 0, -40, -11, -15, -37, 19, -41, -12, 29, 7, 5, -12, -10, 11, -6, -14, 6, -17, -22, 17, -6, -12, 9, 30, -5, 15, -19, -38, -23, -40, -16, 22, 23, 31, -20, -34, -3, 15, -7, 14, -39, -13, -16, 29, -49, 62, 19, -70, -5, 44, 36, -18, -28, 10, 49, -25, -38, 36, -25, -34, -12, -27, 12, 18, -66, -16, 5, 46, -31, -21, 1, -27, -35, -15, 15, 9, -18, -46, 27, 20, 12, 37, 23, -23, 2, 31, -8, 0, -35, -41, -45, -9, -38, 36, -9, 9, 8, 4, 2, 30, -4, 8, 29, -20, -49, -3, -16, 26, 37, -36, 3, -16, -6, -29, -36, -12, 37, -57, 9, 24, 3, 45, 14, 10, -16, -8, 1, -20, -17, 19, -31, -31, 16, -41, -7, 3, -11, 20, -9, -32, 21, 47, 20, 14, 10, -19, -15, -48, 20, 22, 11, -57, 10, -7, -26, 19, 22, -14, 34, -23, 38, -49, -11, -5, -8, -50, 37, 30, -2, -7, -17, -9, -42, -31, 5, 19, 1, -34, 34, -4, -15, -18, -22, 42, -34, 13, -44, -1, -5, -2, -49, -34, -56, -19, 8, 6, 41, -34, -13, -26, -14, 29, 43, -4, -39, -37, 35, 22, -18, 23, 17, 31, 15, -1, 31, -2, -5, 6, -35, -20, 10, 53, 13, 44, 49, 44, -48, -29, 49, 32, -30, -43, -20, 23, -11, -51, 1, -50, 22, -15, 40, -31, 7, -61, -34, -27, 14, 4, 3, -1, 18, 7, 11, 12, 45, 10, 12, -9, -41, -49, 7, -1, -29, 0, -44, -29, 30, -39, 22, -29, 6, -46, 13, -9, -36, -3, -23, -29, -25, -14, -35, -9, -9, -28, -14, -24, -6, -6, 25, -46, -36, 13, 25, -7, -30, -32, -4, -53, -36, 3, -5, -11, -22, -25, -49, 19, 12, 62, -22, 24, -8, 0, -17, 5, -26, 33, 27, 25, 38, 6, 55, -28, 27, -9, -38, -9, 0, -5, -38, 6, 29, 37, -9, 11, 44, -24, 13, -40, -10, 29, 16, 22, -2, 20, -54, -25, -4, -34, -25, -4, -38, -22, -65, 24, -4, 13, 14, 8, -12, -48, -31, 15, -43, -11, 10, -9, 10, 3, -14, 34, 13, 19, 31, -12, 60, 39, 39, -6, -13, -1, -9, -31, -21, 30, -14, -63, -29, 21, -64, -23, -39, -20, 29, 16, -45, -42, 14, -7, -14, -32, 0, -13, 7, 10, -5, -3, 0, -37, -3, -29, -11, -2, 36, 0, -44, 35, -29, -71, -12, 43, 8, -45, 49, -15, -9, -7, -44, 2, -28, -2, 10, 49, -7, -10, 55, 25, 57, 23, 28, 13, -53, 3, -47, -5, 28, 0, -53, 10, -37, 18, -14, 10, 15, -13, 12, -30, 2, -24, -26, -54, -48, -10, 5, -1, 54, -6, -25, -52, -26, -15, 40, -35, -11, -14, -11, 7, 2, 13, 3, -21, -46, 25, -10, -27, 38, 22, -26, 1, 9, -15, -37, -7, -10, -44, -49, -3, 23, -4, -16, -32, 13, -9, -39, -13, -51, -51, -56, -24, 25, -62, -12, -15, -3, 5, 28, -26, 7, 24, 8, -3, -14, -56, -5, -8, -27, 27, 7, 7, 6, 12, -15, 39, 26, 51, 10, -5, -9, -22, 3, -32, -19, 13, 25, 21, -41, 6, 26, 7, 18, 2, 21, 1, 42, -42, -42, 4, -4, -13, -51, 22, -27, 30, 6, 9, -24, 25, 5, 41, -36, 8, -6, 28, -54, 3, -17, 1, 14, -24, 1, -26, 31, -15, -23, -38, 36, -35, -6, 75, 13, 9, 2, -60, -11, -30, 12, -11, -15, -25, -11, -1, -19, -27, 49, 13, -38, 23, 48, -10, 28, -29, 15, -11, -59, 39, -50, 22, -12, 20, 27, -31, -28, 35, 3, -29, -18, 43, -34, -33, -51, -42, -8, 2, 39, -45, -21, -19, 0, -42, -18, -35, 7, -43, -55, -28, 2, 54, 24, 23, -13, 14, 7, -11, 15, -7, 23, 54, -50, 29, 44, -8, -33, -3, 11, 27, -36, 0, 3, 64, 2, 26, 14, -23, -7, -43, 11, -14, -18, -17, 24, 38, -40, 72, -31, -72, 43, 12, 38, -38, 33, -29, -30, 0, -30, 4, -26, -9, 25, -31, 19, 18, 20, 26, -41, -8, 1, -9, -26, 11, -34, -3, 33, 33, -1, -26, -39, -14, -25, -3, -53, -22, -2, -2, 23, -57, -34, -35, -49, -25, 20, -48, 23, 6, 7, -35, 15, 15, -26, 5, 15, 11, 32, -2, 14, 12, -50, -2, 10, -30, 34, -24, 43, 31, -44}

#define TENSOR_CONV2D_7_KERNEL_0_DEC_BITS {8}

#define TENSOR_CONV2D_7_BIAS_0 {-45, -9, -42, -33, -58, -22, -90, -21, -30, -67, -19, -45}

#define TENSOR_CONV2D_7_BIAS_0_DEC_BITS {10}

#define CONV2D_7_BIAS_LSHIFT {4}

#define CONV2D_7_OUTPUT_RSHIFT {9}

#define TENSOR_CONV2D_5_KERNEL_0 {5, 59, -44, 48, -21, -28, -11, 12, -48, -17, 17, 1, 30, 29, 15, 40, -46, -21, 26, -15, -15, 27, 37, -11, -50, 22, -4, 1, 6, -35, 39, -2, 28, 18, 41, -15, 19, 44, -22, -1, -3, 1, 25, -10, -19, -38, -22, -44, 33, 21, -22, 22, -23, 20, 4, 27, -30, -26, 18, 28, -16, 17, 22, 22, 27, 22, 29, 26, -26, -11, -44, 22, -8, 9, 38, -28, 22, 56, 46, -7, 0, 13, 34, -33, -65, -31, 26, 0, 56, 55, 29, 17, 25, -16, -36, 17, -13, 29, 61, -32, 43, 26, -26, -21, 42, -36, -4, -9, 2, -23, -9, 39, -36, -34, -46, 45, 47, -7, -1, -3, 17, -21, -9, 42, -36, -67, -19, 0, 36, 43, 16, -42, 22, 32, -86, 41, -20, 22, 67, 25, -10, 54, -37, -27, 38, -11, -11, 52, 9, -5, 13, 45, 1, 35, 18, 19, -20, -32, -26, -16, 1, -2, 18, -30, 60, 31, -4, -13, -7, -34, 42, -2, -2, 52, -19, 39, 6, -25, 12, -22, 1, -7, -14, 0, 23, 12, -23, -1, -2, 15, 21, -4, 19, 8, -39, 4, 30, -10, 13, -35, 55, -29, 22, 41, 9, 18, -11, 33, 48, 13, -16, 18, 1, -43, -5, -8, -24, -37, 0, -5, -19, 20, 0, 48, 6, -30, -19, 30, 31, -48, -39, -49, -27, 18, -37, -26, 10, 44, 36, -5, -45, -8, -56, -4, -51, 4, -7, -17, 9, -5, 56, -26, -13, -3, 7, 50, -47, -8, -31, 30, 2, -5, -34, -11, -25, 31, -26, -4, 19, 11, 21, -1, 12, 60, 55, -15, -1, 11, 24, 28, -35, 14, 23, -59, -17, -22, 34, -66, 27, -18, 10, 56, -51, -30, -20, 26, 32, 52, 22, 10, -19, 25, -66, -31, -46, -29, 36, -8, 31, 39, 47, -26, -30, -3, 31, 9, 12, 25, 38, -32, 26, 0, -18, -6, 36, -29, -11, 5, -41, -24, -43, -44, -14, 4, -26, -26, -32, -25, 27, -15, 14, -2, 30, 6, -30, 6, 39, -12, 29, 21, -25, 22, 22, -33, -51, -66, -26, 6, 21, 7, 40, -10, -36, 43, -32, -42, 9, -37, -16, -1, -25, -4, -33, 14, -11, -2, 17, 34, -27, -16, 28, -25, 32, 18, -12, 33, -6, -2, 11, -44, 24, 22, -31, -15, 51, -13, -9, 11, 4, 29, -33, 43, 19, -12, 22, 28, 6, 39, -36, -18, -24, -34, 16, 30, 4, 6, 46, 18, 1, 19, -48, -35, 1, -25, 29, 56, -21, -53, -30, 11, -16, -39, -62, -75, 17, -29, 3, -12, 13, -36, 42, 1, -48, 46, -43, -3, -39, -19, 39, 23, 54, -35, 55, -39, 2, 7, -23, -23, 2, -36, -5, 19, 28, -40, 76, 15, -24, 50, 9, -7, -14, 35, 16, -71, 8, 47, -26, -19, -8, -19, 5, -33, -15, -20, -23, -30, 69, 58, 4, -2, -42, 32, 32, -77, 39, 15, 6, 16, 0, -25, -25, 34, -37, 74, 2, -27, 31, 55, -18, -20, 23, 34, 14, 2, -13, -55, 33, 36, 54, 45, -31, -5, -65, -16, 45, -8, -8, -3, -28, 36, 21, 40, -11, 11, -49, 39, -8, 39, 40, -64, 18, -24, -26, -25, 1, -32, 7, 10, -7, -58, 22, -7, 18, -47, -40, 4, 44, -47, 30, 3, -17, 31, 7, -23, -24, -25, -14, 18, -6, 10, 3, 20, -8, -64, -3, -26, -6, 18, 13, 24, -17, -54, -17, 27, -45, 14, -37, -56, 32, -18, -6, -13, -42, -67, -13, 14, -8, 23, 14, -58, 46, 16, -38, 47, -6, 16, -39, 29, -51, 3, -48, -40, 1, 48, -45, 29, -44, 15, -34, -34, 15, -11, 17, -32, 35, 35, -45, -39, -43, 44, 38, -16, -22, -7, 23, 6, 13, 31, 5, -32, -42, 12, 4, 2, 12, 30, 11, -16, -22, 1, 15, 8, 21, -20, -31, 39, -3, 11, 47, -51, -11, 0, -6, 26, -29, 49, 29, 7, 42, 56, 57, -42, -19, 14, 49, -40, 37, -4, 35, 21, -37, -13, 18, 0, -67, 12, -71, 36, 2, 43, 21, -30, 38, 52, 28, -54, 3, 39, 64, -46, 50, -12, -12, -19, -18, -28, 5, -54, 22, -23, 26, -31, -19, -22, -68, 17, 9, 15, 4, 6, 15, 22, 44, -3, 54, -22, -16, 23, 11, -38, -44, 2, 51, 10, 58, 1, 35, -20, -65, 11, -21, 28, -51, 8, 23, -35, -55, 41, -37, -19, -12, -2, -55, 25, -27, 1, -34, -16, 33, 18, 41, -8, 21, -31, -25, -9, -17, -31, -31, -23, -18, -28, -6, -29, 3, 53, -8, -1, 51, -17, -26, -18, 19, 33, -33, -6, 5, -35, 49, 44, -18, -32, -29, 41, -28, 1, -15, 11, -33, 7, -69, -68, 2, 37, -43, -48, -28, -11, -34, 22, -3, -14, -22, 10, -6, 15, 21, -12, -6, 41, -45, 18, -17, -11, 57, 37, -31, -13, 20, -7, 46, -28, 52, 3, 19, 23, -64, -6, -61, -49, -4, -39, 8, -22, -36, -12, 17, -42, -22, 33, -1, 59, -36, -37, -28, -23, 37, -24, -25, -17, 15, 20, 46, -26, 10, -3, -20, 41, -26, 40, -27, 8, 2, 43, -34, -27, -33, 39, -50, 24, -5, 51, 33, -6, 30, 0, 21, -19, 14, 15, -9, 30, 27, 14, -9, -2, -46, -59, -30, -50, 41, -51, -18, -38, -25, -1, 2, -37, 33, -43, -12, -1, -9, -25, 2, -12, -27, -38, -29, 37, -10, -3, 14, 22, -13, 12, 19, -44, 3, -23, -25, -17, 1, 3, -53, -33, 49, 35, 44, 47, 3, -10, -44, 1, 32, -24, -21, 8, 15, -10, -38, -13, 17, -54, -34, -8, 9, 38, -18, 23, 17, -3, 15, 20, 13, 45, 14, -35, 21, 43, -18, -49, -2, 31, 46, -47, 16, 12, 5, -18, -49, 9, 22, 22, 6, -29, 35, -12, -37, -48, -53, -31, 15, 38, -19, 22, 18, 45, 24, 38, -4, 37, -3, -1, -23, 39, -9, -34, 26, 32, -45, 27, -10, 13, -33, 34, -35, 3, -13, 8, 6, 33, -5, 31, 10, 18, 25, -27, -1, -26, 39, -40, 17, -3, 23, 5, -3, -17, 52, 3, -3, -49, -37, -6, 16, -28, -23, 4, -35, 26, 50, 0, 6, 4, 18, -8, 9, 23, 11, -4, -34, 2, 68, 16, 3, -61, 5, -26, 15, 2, 26, 35, 3, 46, -21, -17, -16, -7, -32, 13, 22, 67, 29, 8, 1, -19, -20, -28, -61, -3, -31, 33, 19, -54, -14, -16, -7, -34, -34, -18, -38, -12, -20, 17, -24, -6, 0, -24, -17, -13, 25, 21, 55, 41, 9, -9, -6, 30, 26, -42, 14, -10, -10, 6, 4, -43, -33, -23, 29, -42, -20, 17, -1, -46, -39, 25, 16, 0, 18, -2, 14, 41, -31, -38, 16, -19, 30, -22, 49, -5, 12, 40, -62, -6, 17, -9, 2, -31, -26, 45, -6, 20, 36, -21, -2, -19, -4, 2, -37, -8, -39, 35, -13, 12, -9, -11, -14, 9, 13, 6, 33, 26, 3, 8, 64, -8, 26, -18, 2, 35, -26, 48, -13, -10, 42, -41, 38, -10, 26, -12, 53, -17, 4, 21, -38, 27, 32, -22, 5, -37, 44, -32, 54, -20, -31, 39, -42, -45, 17, 33, -32, -42, 11, 9, 14, -15, -21, 12, 13, 30, 16, 10, -27, -44, 23, -23, 13, -6, -7, -31, -4, 35, 7, 34, -52, 36, 1, 1, 43, 0, 8, 35, -30, -39, 19, 16, -57, -44, 11, 3, -27, 28, 12, 8, 3, 19, 35, 18, -17, 7, -36, 10, -5, -33, 21, 27, 51, 2, -2, -28, -30, -26, -32, 32, 22, -1, -33, -35, 20, -34, 6, 21, 29, 38, -46, 58}

#define TENSOR_CONV2D_5_KERNEL_0_DEC_BITS {8}

#define TENSOR_CONV2D_5_BIAS_0 {-45, -9, -42, -33, -58, -22, -90, -21, -30, -67, -19, -45}

#define TENSOR_CONV2D_5_BIAS_0_DEC_BITS {10}

#define CONV2D_5_BIAS_LSHIFT {4}

#define CONV2D_5_OUTPUT_RSHIFT {9}

#define TENSOR_CONV2D_8_KERNEL_0 {-51, -27, -60, -9, -11, 21, 1, -34, 17, 3, -5, -13, -12, -20, 37, -24, 65, 4, -27, 27, 5, -75, -35, 17, -48, -55, 1, 23, -10, 12, 23, 72, -26, 64, 28, -36, -24, 13, -12, -26, -37, 3, -7, -19, 47, -12, 19, 26, 69, 0, 39, 2, 43, -12, 0, -2, -28, -16, -42, 1, 11, -8, 15, -4, -24, 1, 16, 2, -46, 49, -12, 1, 44, -23, 6, 16, 21, 25, 6, -39, -18, 8, -18, -12, -1, 23, -30, 9, -3, 11, 0, 25, -23, 35, 3, 23, 29, -9, 30, 24, -28, 31, 42, -21, -31, 11, 44, -9, -22, 40, 20, -29, 26, 54, -8, -9, -11, -44, 0, 0, -18, 27, -18, 35, 47, -25, -27, -36, 8, -9, -67, -5, 48, -12, 35, -30, 67, -53, -16, 43, -50, -2, -69, 6, -33, 40, 31, -39, 5, 29, -28, 0, -10, -30, -31, -28, -25, 28, 40, 57, 49, -17, 4, 17, -44, -8, -52, -41, 21, -11, 43, 63, 32, -50, 21, 59, 10, -13, -22, -3, 18, 25, 39, -43, 17, 31, -10, -20, 16, -20, -26, 14, 37, 19, 51, 4, -20, 24, 59, 26, -43, -19, 2, -14, 21, -39, 5, 29, 26, -4, 2, 19, 26, -22, 30, -11, 40, 42, -15, -48, -41, 13, -15, 24, 16, -21, -13, 1, 26, -8, -32, 19, -21, -2, -34, -5, 0, 5, 54, -44, 47, 57, 6, 38, -10, -8, -23, 5, 6, 22, 17, -35, -26, -29, 29, -52, -28, 9, 33, -53, -23, 39, -22, 61, 0, -18, 4, -6, 16, 31, -13, -79, -16, 3, 17, 68, -44, -32, -16, -27, -26, 14, 10, -4, 5, -16, -4, 13, 22, -69, -20, 39, 29, -3, 41, -23, -44, -34, -48, 29, 4, 15, -5, 5, 54, 1, 39, -25, -1, 5, -9, 58, -30, -58, 28, -5, 24, -29, -26, 3, 24, -8, -9, 14, -58, -37, -53, 55, -9, -15, 6, -33, 31, -26, 8, -24, 22, 5, -1, -27, 9, 15, -43, -26, 16, 43, -20, 24, 20, 38, 32, -31, 16, 31, -30, -29, -29, -34, 26, 25, 10, -20, 28, -6, -17, 26, 60, 41, 19, 15, 37, 4, -2, -10, -1, -42, -28, -58, 38, -25, 1, 22, 3, -39, 57, 29, -18, -24, -36, 1, -53, -36, 25, 22, 42, 58, 62, 23, 11, -5, 47, 27, 37, 14, -44, 14, -18, 39, -5, -12, -59, -27, -8, -30, 0, -8, -30, 29, 23, -47, 15, 22, -18, 10, -52, 9, -30, -38, 22, -29, 7, -51, 5, 34, 2, 8, -22, 38, -28, 3, 2, 37, -14, -9, -47, 22, 13, -7, -42, 25, 6, -50, -54, -14, 28, -41, 22, -8, -11, 11, -12, 10, 0, 20, 42, 2, 18, 6, -12, 31, -34, -16, -46, -30, -51, -5, 29, 28, -24, 19, 34, 18, 9, -10, -36, 3, 36, -39, -58, -19, -17, -15, -21, -44, 34, 3, 10, -11, 41, -26, -11, 42, -42, -32, 32, -36, -28, 19, -33, 12, -12, -1, 23, 42, -1, 18, -2, 33, 28, -46, 34, 28, -8, -46, -29, -17, -13, -16, -13, -11, -35, -17, 1, -7, 0, -29, -27, 5, 6, -7, -38, 14, -15, 39, -1, -16, 0, -13, -31, 16, -26, 22, -25, -19, -35, 22, 33, -32, -36, -11, -6, 19, -12, -14, -27, 21, 0, 2, -1, -40, -57, 9, -3, -6, -47, 23, -6, 47, -42, -19, -29, -45, 49, 6, -37, 23, 18, -14, -18, 6, -45, 12, -6, -32, 34, 14, -26, -8, -9, -30, -32, -31, -10, 27, 12, 10, -8, -23, -30, 12, 15, -14, 25, 60, 24, -44, -28, -18, 10, 10, 19, -38, 20, 28, -14, 22, 30, 31, -40, 10, -57, -44, 21, 15, 47, 38, 31, 7, -23, -8, 10, 19, 18, 12, -42, 15, 17, 16, -30, -17, -19, 15, 16, 0, -29, 24, 9, 45, 1, 27, -9, 4, 30, 22, -38, -7, 0, -66, -18, 3, 6, 18, -7, 15, 41, 33, 47, 43, 52, 12, -42, -52, 1, -3, -31, 21, 15, 10, -30, -72, -29, -3, 14, 32, 2, 22, 29, -8, -8, -26, -15, 20, 29, -57, 55, 0, 1, 25, 21, 16, 29, -5, 32, 33, -30, -4, -36, -60, -37, 26, 33, -60, -30, -20, -27, -13, 14, 7, 24, 48, 20, -9, -35, 35, -18, 36, -35, -3, -6, -58, 21, 14, -27, 31, 43, 41, 65, -6, 18, 27, -10, 19, 10, -67, -21, 40, -12, -16, 13, 26, 12, -21, -12, 32, -22, -5, -44, 47, 2, -27, 21, -1, 38, 12, 16, 1, 9, 4, 6, 20, -65, -22, 19, -21, -21, 23, 58, -109, 10, 8, -39, 20, -4, 36, -8, 26, 18, -40, 2, 63, -36, 17, -41, -44, -6, -10, 39, 38, -14, -88, 21, -1, -48, -33, -29, -35, 20, -20, 35, 59, 7, -64, 27, 25, 5, 16, -57, -10, 16, -3, -27, -5, -59, -9, -15, 41, -11, 28, 37, -38, 2, 8, -13, -5, 10, 15, 9, 37, 5, -10, -3, -18, 47, 13, -6, 35, -27, 18, -17, 20, -17, 17, -8, -37, -32, -1, 1, -5, -30, 23, 38, 33, -11, -17, 10, -38, -4, -29, 36, -49, 13, -8, 1, -4, -9, -10, -24, 7, -56, 26, 2, -11, 22, -31, 22, 7, 8, -17, 2, -9, 43, -9, 24, -12, 2, 32, 32, -31, 29, -29, 34, -53, -17, -37, 20, -37, 19, 37, 18, 12, 13, -41, 17, -31, -21, 2, -4, 40, 21, 26, -46, 46, 22, -5, 40, 17, 26, -4, -7, 14, 39, 33, 45, -36, -65, -37, -32, -9, -41, -35, -7, 8, 53, 10, -18, 30, -19, 17, -9, 32, 32, -30, -13, 5, 31, -3, 15, 23, 10, 20, -48, 34, 19, 7, -4, -25, 17, 49, -34, -8, 6, 29, 53, -7, -7, -24, -18, 15, -39, -5, -31, -15, -52, -38, -30, -29, -2, -29, 5, 19, -29, 8, -69, 8, -46, 27, -20, 19, -51, -43, -24, -58, 44, 38, 5, 14, -54, -45, -40, 10, -7, -28, 23, 7, -21, -13, 12, 11, -41, -2, 28, 23, -6, 36, -29, -31, 7, -16, -13, -4, 21, 46, -37, -29, 34, -34, 64, -27, 63, -17, -52, 46, -18, -5, -24, 49, 16, 26, -40, -35, 12, -43, -39, -31, 39, 50, -3, -17, -11, -10, 22, -27, -41, 18, -14, -17, 48, 9, 5, 20, 11, -4, 2, 9, -17, -31, -11, -48, 28, -12, 47, -42, -23, -26, -22, -36, -31, -38, -10, -3, -28, 47, 35, -20, -8, -21, -59, -24, -24, 46, -3, 33, 3, -10, 44, -31, -15, -19, -14, -6, 35, -23, -33, 17, -20, -18, 2, -35, -20, 38, -4, 23, -38, -4, 21, -4, -4, -39, -26, -19, -18, -36, -23, -43, 35, 10, 46, 36, 36, 13, 7, 32, -77, -28, -38, -44, -21, 12, -11, -48, -17, 19, 26, 9, -17, -25, -15, 9, 33, 9, -10, 26, -43, -48, 6, -20, 18, -7, 46, 17, 52, 26, 13, -47, -47, -31, -55, -32, -1, -10, -31, -12, -3, 21, 19, 73, -34, -46, 25, 18, 15, 21, -31, -28, -35, -34, -36, -3, 55, 44, 27, -24, -44, -13, -39, 16, -36, 9, -5, -23, -14, -10, -21, 27, -11, -50, -20, 51, 50, 15, -47, -17, -33, -27, -33, -22, -6, 1, -17, 39, 6, -3, 27, 29, 26, 65, 3, -28, 15, -27, 1, -20, 0, 40, 44, 39, 29, -23, 28, -35, -14, -43, 6, -31, -29, 0, -10, -12, 13, -33, 10, 2, 2, -10, -59, -12, 2, 4, -42, -19, 15, 28, 14, -36, 41, 7, -39, 41, 3, -23, 24, 33, -47, -15, -30, -13}

#define TENSOR_CONV2D_8_KERNEL_0_DEC_BITS {8}

#define TENSOR_CONV2D_8_BIAS_0 {-85, -6, -30, -1, 11, -37, -29, -48, -24, -41, -45, -37}

#define TENSOR_CONV2D_8_BIAS_0_DEC_BITS {10}

#define CONV2D_8_BIAS_LSHIFT {4}

#define CONV2D_8_OUTPUT_RSHIFT {9}

#define TENSOR_CONV2D_6_KERNEL_0 {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}

#define TENSOR_CONV2D_6_KERNEL_0_DEC_BITS {8}

#define TENSOR_CONV2D_6_BIAS_0 {-85, -6, -30, -1, 11, -37, -29, -48, -24, -41, -45, -37}

#define TENSOR_CONV2D_6_BIAS_0_DEC_BITS {10}

#define CONV2D_6_BIAS_LSHIFT {4}

#define CONV2D_6_OUTPUT_RSHIFT {9}

#define TENSOR_CONV2D_9_KERNEL_0 {-1, 5, 14, 42, -34, -53, 13, -27, -39, 18, -11, 21, -34, 23, 20, 31, 26, 18, -21, -38, -6, -35, 10, -7, -44, 47, 48, 21, -55, -14, -8, 0, 26, -7, 36, -11, -47, 35, -18, 31, 54, 24, 43, -43, 0, -16, 8, 34, 20, -17, -48, -1, -51, 13, 17, 14, -10, -10, -3, 43, 30, 8, 10, 6, 52, 25, 1, -20, 13, 9, -33, -8, -50, -12, -19, 23, -14, -4, -8, -30, 24, -29, -1, -44, -30, 18, -31, 22, 0, -1, 15, -36, -30, -1, 17, 27, 1, 25, 0, 7, -5, -28, 17, 18, 21, -11, 8, -11, -17, 38, -28, -1, -9, -16, 30, -56, -10, -9, -22, -17, -48, -17, 17, -2, 25, -39, -46, -47, -11, -27, -32, -19, 20, 22, 7, -35, -10, -6, 5, -6, 21, -2, -36, 16, -8, -21, 23, 20, 22, -1, 7, 35, -13, 37, 1, 40, 43, 6, 5, -15, -45, 31, 3, 29, 1, -15, -6, 18, 34, 28, 48, -28, 25, 8, -22, 25, 6, -8, 3, -8, 0, 6, -28, 19, -11, 26, 32, 36, 29, 18, 13, 1, -8, -34, 2, 20, -22, 19, 31, -5, -38, -23, 36, 12, -21, -25, -3, 6, 9, 7, 15, 33, 19, 7, -15, 16, -7, 42, 10, 30, 4, 17, 26, 50, -32, -15, 38, 27, 38, 18, -2, -39, -2, -10, 38, 21, 43, 60, 32, 35, 22, 44, -45, -19, 23, 16, 45, -22, -13, 17, -8, -9, -2, 12, 26, -54, -27, -17, 10, 40, 13, 8, -5, 25, -49, -4, -10, 15, -15, 13, -24, 5, 37, -35, -24, 4, 20, -25, 35, 9, -49, 11, -27, 69, -23, 26, -18, -37, 14, 41, -8, 10, 28, -24, -26, -9, -40, -9, 30, -16, 11, 29, 16, 20, 15, 2, 28, 35, -3, 28, -23, 30, -15, 21, -21, 6, 63, -31, 48, -52, 24, -36, 16, -40, -31, 28, 42, -16, -26, 8, -50, 46, -15, 0, -22, -26, -27, 24, 40, -2, 23, 18, 27, -35, -7, 18, 17, 56, -7, -29, 44, -44, 36, 50, 6, 30, -9, -9, 19, -58, -36, -30, 14, 25, 7, 3, -1, 3, -25, -17, -15, 16, 22, -17, -1, -15, -21, 43, 3, 4, -6, -25, -5, -25, 34, 0, 33, 3, -33, 19, 20, 16, -45, 23, -5, -17, -49, -50, 3, 31, 36, 3, 25, -12, -26, -8, 14, 18, 28, 7, 22, 12, -49, -36, 23, 49, -44, 24, 74, 23, -23, 4, -27, 13, 55, -20, 13, -62, 35, -2, 1, -4, 47, -2, 26, 40, -25, -24, 29, 15, -14, -7, -15, 30, 6, 15, -23, -20, 9, 22, -1, -18, 40, 15, 31, 50, 32, -38, 22, 46, 46, 29, 3, -12, 13, 22, 0, 7, -8, -29, -23, -14, -9, -53, -5, -1, 41, 23, -7, 30, 48, 11, 70, 45, -26, -9, 36, 10, 24, 6, -8, -26, 20, 23, -44, -38, -12, -33, 39, 8, 39, 50, -15, 1, 6, 19, -25, -9, -17, -13, -15, -22, 4, -42, 29, 10, 35, 23, -8, -1, 40, -29, -30, -32, 19, 15, 38, 0, -5, 42, -11, 5, -32, 7, 39, 38, 1, 32, 8, 20, 1, 35, 8, -21, -26, 15, -10, 45, 37, 37, 11, 4, -15, -17, 26, -53, -3, 23, -13, 40, -19, 11, 7, 46, -20, 3, 13, -13, -10, 28, -25, -5, 36, -6, -5, 6, -17, 17, 19, -12, -47, -16, -26, 22, -44, -28, -25, -18, 14, 8, 12, -39, -8, -4, 45, 28, -61, -19, -27, 17, 3, 9, -16, 3, -8, 2, -26, -21, 17, -19, 21, 11, -17, 30, 26, 15, 43, 8, -7, -6, -8, 5, -54, -6, -25, -51, -53, -9, -13, -33, -51, -50, -9, -40, 3, 6, -7, -37, 53, 12, 19, 10, -21, 0, -27, -47, -10, -42, 1, -16, -16, 29, 39, -15, -33, -3, -27, -44, 5, 7, -10, -27, 28, 12, 19, 17, -24, -9, 7, -12, -36, 25, -40, -35, -4, -14, 25, -1, -21, 23, 5, -18, 26, -44, -1, 40, 0, -9, -6, 16, -4, 28, -35, 3, 8, -16, 19, 44, 44, -23, 9, 29, -42, -18, 5, 19, -37, 28, 16, -28, 6, -42, 4, 29, 47, -14, -8, 12, 40, 0, -20, 41, -31, 8, -15, 32, 19, 44, 18, 34, 6, -31, -38, 29, 31, -4, -30, 7, 5, 38, 37, 4, 32, -15, -28, 40, -17, 30, 29, -36, -15, 23, 20, -43, 23, 12, -26, 13, -23, 21, -29, -14, 6, -11, -22, -24, 0, -25, 14, 36, -10, -5, 3, 27, -39, 18, 10, 6, -15, 36, 6, 22, -16, 15, 14, -4, 50, 32, 42, -1, -20, 24, 28, -20, 13, -13, 29, 41, -18, -13, -14, 2, -19, -22, 24, 26, 39, -36, 21, -25, 9, 9, 27, 21, 15, 2, 42, 33, 30, -23, -15, 35, -26, -17, 14, -1, 4, 22, 50, 6, -2, -4, -1, -6, 18, -5, -34, 45, -11, -14, 33, 33, 4, 5, 1, 1, -17, -7, 5, -4, 28, -20, 16, 6, 9, 20, 6, -5, 17, -15, 6, -10, -1, 20, -11, -14, -32, 2, 16, 10, -48, 9, 4, -9, -27, 0, 21, -16, -23, -12, -15, 26, -14, -5, -30, -25, 43, -16, -23, -25, -34, 30, 7, 50, -20, -47, -15, 33, -45, -26, 15, -20, 35, -16, -25, -8, 18, 22, -2, -52, 33, -8, -1, 4, 9, -2, 31, 31, 22, 12, -15, 28, -16, -19, 12, -7, 10, -2, -60, 17, -23, 21, 22, -24, -43, 7, 56, -24, -28, -39, -23, -22, -1, -40, -6, 16, 4, 2, -7, 5, 17, 3, 16, 30, -27, 32, -22, 29, 30, -4, 39, 38, 27, -8, 20, -19, 4, -31, -15, -11, 38, -2, -26, 2, 28, -10, -3, 35, -40, 7, -27, 34, 20, -33, 9, 8, -26, -19, 5, 17, -6, -28, 17, 3, 46, 41, 14, 21, -2, -10, -9, -27, -25, 39, 19, 57, 12, -31, -21, 2, 21, 31, -44, -43, -26, -24, -13, -44, 17, 7, -9, -2, 4, -9, -12, -43, 6, -42, -25, -1, 17, 31, -36, 51, -18, -7, 29, -38, -5, 36, 9, -2, 6, -1, -32, -24, -8, -11, 26, 6, 11, -34, -35, -13, -36, 21, -17, 37, 45, -33, 4, 8, 22, 30, -7, 34, -2, 9, 40, -5, -37, -40, 2, -20, 32, -27, 11, 18, -27, 2, 37, 36, 30, 14, 13, -19, 6, 11, 13, 45, -11, 27, -2, -11, -15, -24, 54, -27, 28, 17, -16, -10, 42, 1, 20, -15, 60, 19, 2, 49, -23, -7, 46, 25, 14, 33, -27, -38, -38, 24, 32, -3, 10, -1, 33, 53, -5, 45, 9, -9, 8, 9, -27, 11, -15, 11, -42, -42, 0, -2, -44, 2, 25, -27, -11, -17, 38, 22, 13, 31, 17, 19, -46, -5, 16, -7, 33, -27, -8, 23, -28, -47, 12, -45, 20, 30, -18, -35, -28, -24, -42, -10, 21, -45, 34, 53, 4, 13, 5, -17, -5, 0, 50, -28, 43, -17, 30, -25, 7, -14, 39, -29, -18, -55, -20, 5, -1, 48, -44, -18, -20, 4, -19, 9, -37, -36, -4, 6, 39, 2, 15, 6, 5, 23, 1, 2, -28, 10, 47, -42, -19, 18, -45, -27, -10, 8, -65, 28, 22, -10, -30, 22, -34, 27, -43, -23, 0, -22, 8, -21, -20, 18, -32, -14, -4, -35, 7, -47, 33, 24, -6, -38, 52, 28, 17, -19, 15, -32, -5, 9, 39, -37, -9, -15, -19, 4, -4, 7, -5, -11, -14, -18, -6, 17, 40, 26, 30, 24, 53, -11, 43, 41, 11, 47, -34, -22, -1, -12, 7, -28, 21, 16, 8, 17, -6, -8, -31, -20, -16, -15, 0, 16, 13, 21, -24, -24, 27, 2, -14, 13, 51, -25, 13, -25, -12, 58, 16, 9, 27, 25, 16, 11, -34, 34, 24, -25, 26, -19, 29, -17, 13, -35, -9, -12, 26, 27, 35, 13, 5, 27, 19, -10, -5, 2, 1, 40, 45, 16, 24, 16, -16, -3, -19, 33, -16, 55, 31, 33, 2, -5, 32, -29, 13, 33, -1, -25, 12, -7, 29, 28, -10, -13, -15, -7, -25, 13, 12, -15, -16, -15, -38, 23, 7, -7, 2, 24, -22, 12, 12, -10, -21, -3, -40, 16, -4, -24, -42, -38, -17, 13, 27, -44, -33, -30, 18, -11, -26, 51, -3, 44, 21, -36, 8, -53, -29, -46, -22, -20, -13, 17, -24, -49, -56, 23, -15, -16, 15, -28, 14, -26, 3, 19, 1, -32, 8, 12, -30, -65, -43, -6, 24, -30, 42, 21, -12, 20, -16, -4, 34, 25, 14, -2, -28, 11, 1, 49, -10, -15, 4, 7, -2, 45, -25, -5, 4, -43, 26, 17, -17, 1, 1, 4, 14, -20, -5, -36, -25, -9, 9, -19, 31, -36, 6, 3, 34, 3, -6, -6, -6, 4, -11, -16, 20, -37, -3, -16, -46, -7, 34, -17, -3, 2, -19, 9, 10, -43, 9, -16, -13, -10, -4, 32, -2, -9, -3, -38, 1, -13, 3, 37, -4, -19, 21, 20, 30, -14, -43, -1, -31, 6, -17, -22, -32, -40, -9, 32, 31, 16, -8, -2, 22, 13, 24, 9, 23, 7, 21, -23, 9, -41, -14, 14, -44, 3, 23, -11, 31, -35, 1, -14, 25, 12, 3, -31, 25, -10, 5, -32, -16, -50, -19, 7, 3, -42, 41, 25, -26, -25, -9, -13, -27, -29, -23, -56, -9, 8, -5, -19, -44, 25, 20, -33, 1, 16, -41, -17, 22, -44, -39, 29, -23, -1, -6, -41, -38, -4, 5, -1, 8, -41, 41, 33, 4, 2, 10, -18, -19, 25, 1, 24, -22, 30, -25, 31, -16, 6, 16, 19, -14, 16, -19, 10, 23, 14, 44, -6, -23, -43, 42, 8, -14, 32, 57, -1, 16, 9, -12, -9, -17, 7, 31, -36, 7, 25, 11, -4, -5, -3, 13, -41, -12, -26, -8, 23, 31, 17, 19, -39, 32, -36, 20, 25, -22, -34, -13, -11, -18, 23, 22, 2, 12, -6, 45, -3, -30, 4, -7, -31, 9, 15, -32, 15, 2, -8, 21, -5, 39, 30, 26, 2, -16, -23, -5, -24, 0, 54, -17, -26, -27, -27, 41, 49, 10, 12, 26, 13, 43, -3, -9, 18, 16, -26, 8, 17, 71, -2, 18, 9, -1, 8, 16, -22, 10, 2, -15, -19, 27, -5, -31, -11, -31, 4, 51, 14, 26, 22, 8, -1, 15, -17, -8, -36, 4, -20, -29, -35, 30, 11, -4, -11, 33, -14, 24, 8, -8, 5, 2, -2, 16, -23, 29, -28, -11, 18, 2, 0, -3, -23, 17, -34, 9, -17, -27, 6, -39, -10, -23, 28, 22, 15, -8, -9, -19, 8, 1, -21, 34, 4, -16, -21, -13, -8, 9, 10, 27, -34, -16, 30, -18, 10, 2, 28, -31, -5, -28, 4, -22, -21, 25, 10, -19, 26, -1, -14, -28, 39, 16, -26, -39, 13, -1, 19, 22, -18, 8, 17, 28, 23, -19, -13, 38, -33, 43, -31, 11, 24, 32, 47, -12, 39, 18, 42, -36, -20, 14, 14, 21, 7, 27, 41, 47, 38, -51, -2, 10, -33, 23, 3, -14, 26, -10, 8, -13, -3, 34, -27, -40, -26, 24, -2, -22, 22, 19, -19, -17, 32, -35, 13, -26, 28, 12, -42, 21, -10, 24, -10, -37, 23, 18, -13, -51, -18, 27, 38, -5, 25, 25, -9, 18, 27, -45, 44, -17, 9, 42, -18, -1, -15, 7, 17, -23, 6, 16, -12, -33, 28, -16, -1, -32, 9, 19, -27, 19, -14, -3, 25, 10, 26, -17, -14, -22, -7, 16, 23, -23, 9, -19, 39, 4, 28, 20, 3, 2, -17, -45, 15, -28, 19, 18, -36, -20, 24, 23, 17, -38, -12, 39, -6, -31, 17, -32, -22, 27, 5, -17, -6, 9, -10, 5, 23, 26, 28, 8, -26, -2, 12, -52, 10, 5, -37, -19, 31, -11, -8, -15, -44, 33, 0, 30, 55, -8, -30, 15, -4, 31, 25, 3, 17, -12, -5, 16, 7, 17, 10, 19, -29, 5, 17, -21, 30, 24, 23, 8, -15, 48, 30, -46, 25, 12, -49, 34, 3, -15, -1, -49, 15, -34, 12, 46, -19, -26, -48, -6, -41, -27, 13, -3, -31, 27, 2, -3, 10, -18, 22, -25, -35, -10, 32, 8, 0, 2, 19, -24, -13, -22, -22, 25, -46, -27, -17, -25, 7, -20, -14, -20, 17, 54, 27, -13, -32, -26, 34, -3, -16, -14, -26, 10, -36, 20, 8, 17, 13, -53, -18, 28, 6, 5, -13, -3, -21, -16, -48, 21, 45, 43, 11, 14, -43, 41, 18, -6, 34, 3, 16, 18, 18, -34, -16, 28, -8, 16, 4, -28, -29, -51, -2, -23, 17, 6, -21, 16, -58, 24, -11, -15, -20, 19, -53, -8, 3, -20, -27, 23, -3, -50, -5, 20, -10, 33, -45, 27, 42, 29, -50, 49, -8, 10, 3, -14, 9, -29, 8, -15, -18, 1, 18, 33, 2, -16, -1, -17, -6, -35, 17, -26, -4, 29, 21, 48, 19, -29, 7, -25, -30, 10, 7, -20, -27, 2, 21, -21, 24, -6, 14, 20, -28, -20, 25, 15, -32, -4, -6, 4, 1, 4, -11, 41, 18, 23, 43, 3, 3, 9, -31, 7, -18, -19, 11, 1, 8, -5, 23, -23, -28, 32, -41, -35, -21, -15, 55, -26, 12, -26, -5, 9, 30, -54, 13, 9, -22, 3, 33, 5, -30, -7, -19, 14, 2, -19, 6, 37, -43, -35, -23, -30, -15, 0, 6, 29, -13, 27, -20, -14, 20, 13, -17, -24, -3, 33, 36, -22, 15, 28, -8, 26, -39, 17, -47, 4, -3, 19, -18, 25, -3, -32, -3, 49, -28, 20, 24, -32, -32, -29, 8, -19, 35, -44, -22, -5, -15, -32, 27, 10, -10, -40, -44, -37, -7, 40, -10, 4, -3, 21, -22, -3, -3, 0, -17, 13, 36, -21, -8, -16, 15, 35, 1, 9, 26, 19, -38, 2, 42, -26, -6, -19, -39, -2, 2, -38, -25, -4, -42, 9, -11, 12, 19, -12, -15, -23, 13, -10, -12, 8, -8, 18, -27, -29, 12, 11, -29, 9, -6, 4, -16, -19, -5, 1, 9, 21, 5, -14, -11, -20, 18, 12, 5, 42, 43, -10, 16, -10, -5, 52, 11, 59, -20, 10, 17, -46, 40, 25, -37, 22, 34, -3, -14, -23, 56, -26, -26, -12, 40, 23, 34, 26, 9, -23, 5, -26, -1, -19, 34, 25, -3, -21, -4, -34, 6, 13, -36, 1, 14, 2, 38, -10, 35, -6, 3, -3, 13, -14, -8, -24, 22, -7, 60, -12, -42, -33, 16, 4, -44, -31, -1, 11, -34, 2, 46, -14, 40, 13, -25, -33, -15, -10, 32, -15, 2, 11, 10, 39, -24, 21, -6, -1, -6, -5, -25, 21, -6, 10, -8, -16, 4, -14, 13, 3, -5, -5, 7, -40, 14, 23, -5, 14, 26, -25, -5, -32, -15, -25, -45, -29, 31, 10, -31, -15, 15, 18, 2, -16, -12, -21, -53, -9, -20, -27, -58, 4, -26, 23, 18, -1, 35, -3, -7, -9, -30, 41, 9, 15, 15, 9, -15, -43, 2, 19, 29, 37, 15, 1, -13, -5, 3, -4, 23, -8, 31, 5, 36, -28, 7, -18, -25, 41, 7, -24, -4, 6, 45, 1, 13, -23, -14, -16, -30, -15, 14, 43, -40, -21, 5, -34, 38, -17, -15, 24, -5, 24, 18, 25, -31, -23, 19, 23, -29, 9, -23, 22, 42, -74, 9, 47, -29, -21, 3, 0, 28, 29, 9, 0, -7}

#define TENSOR_CONV2D_9_KERNEL_0_DEC_BITS {8}

#define TENSOR_CONV2D_9_BIAS_0 {11, -19, 6, 24, -10, -36, 77, 1, -11, -14, 5, -24}

#define TENSOR_CONV2D_9_BIAS_0_DEC_BITS {10}

#define CONV2D_9_BIAS_LSHIFT {3}

#define CONV2D_9_OUTPUT_RSHIFT {9}

#define TENSOR_DENSE_KERNEL_0 {-39, -40, 6, 2, 30, 17, -17, -28, 19, 25, -30, 24, 17, -19, 23, 13, 5, 61, 19, -48, -16, -8, 11, 39, 17, 26, 42, 19, 2, -52, -13, -17, -23, -5, 24, -2, 32, -41, 25, 8, 45, -3, 15, 5, -7, -3, -19, 32, -16, -16, 3, 30, 31, -14, -21, 11, 17, -16, 5, -37, 5, -43, -35, -33, -26, 37, 26, -8, -8, -8, -19, -1, -44, -13, -14, 2, -21, -42, 20, -51, 5, 35, 9, -31, 0, -35, -17, -8, 15, 11, -11, -35, 25, -12, 8, 21, -3, -30, 18, -7, -15, -4, 16, 8, 3, -19, 40, -6, 16, 8, -11, 38, -41, -19, 18, 6, -23, 4, -34, -12, -34, 0, 4, 8, 3, 54, 3, 20, -5, -6, -9, -21, -13, -18, 35, 10, 30, -20, 25, 26, -7, 7, -16, 56, 23, -30, -21, -4, 4, -2, -7, 3, -21, -17, 14, -24, 16, 32, -1, 4, -9, 19, 13, -20, -2, -23, -25, 12, -11, -10, 20, 20, -6, 14, -19, 10, -21, -16, -35, 50, -42, 50, 24, -2, -13, 22, -19, 30, -19, -16, 5, -18, 35, 1, -10, -16, -21, 30, 15, -7, -22, 3, 21, -26, -7, -29, 0, -33, 8, -1, 8, -2, 9, -25, -11, -27, 39, 7, 5, 0, 60, 19, -28, 23, 35, 0, -26, 16, -11, 15, -32, -17, 36, -16, -37, -15, -53, -8, 54, -39, 23, -46, 16, -12, 44, 30, -12, -3, 13, 13, 38, 31, -40, 6, 20, -12, 28, -16, 1, 14, -15, -28, 38, 14, 30, -1, 2, 27, -30, -20, 14, 18, -35, 1, -21, 5, -24, -45, 5, -28, -41, 23, 11, -19, -7, 25, 47, 2, -17, -35, 0, 38, -15, -45, -3, 15, 30, -23, -1, 2, 21, -35, -20, 4, 43, 22, 1, 31, 11, -9, -21, -5, 11, -1, 17, -7, 28, -19, 48, 2, -27, -14, -14, 15, -38, -32, 6, 4, 24, 17, -18, -48, 19, 45, 27, 6, -45, -24, 4, -49, -21, -34, 30, -33, -17, 28, -25, 1, 3, 5, -12, 25, 2, 1, -1, 14, 13, 29, -42, 7, -35, -24, -31, 18, 36, -3, -24, -39, 19, -27, 13, 27, -41, -23, 15, 5, -17, -9, -31, -1, 12, 9, 5, -19, -28, 7, 7, 18, 6, -12, 13, 54, 22, 41, 41, -11, -5, -21, -24, -4, 37, -9, 20, 18, 11, -26, 4, 36, 7, 22, 6, 11, -32, 60, 14, 21, -2, -9, 1, -2, -5, 6, -30, -34, -1, 1, -30, 14, 27, -50, -10, -18, 9, -46, 28, 12, 9, 8, 8, 25, -13, -8, -12, -25, -12, 31, 35, -18, 18, 37, -7, -40, 11, -24, 28, -31, 13, -24, -6, 7, 8, 20, 3, -17, -11, -20, 34, 12, -22, 48, 5, -19, -12, -12, 3, 3, 33, 2, -10, -22, 16, 38, -53, 7, -26, 28, 7, -6, 19, -59, 18, -25, -42, -14, -21, -56, -1, -30, 7, 27, 11, 53, 22, -49, 48, 29, 45, 12, 35, -31, -6, -11, 36, -48, -14, -20, -33, 12, 12, 13, -15, -10, -11, 11, -35, 8, 40, -33, 61, -10, -6, -39, -19, 1, 18, -21, 7, -8, 7, -18, -15, -3, -5, 35, 17, -10, -29, 9, 69, 7, -36, 12, 10, 5, -45, -27, 16, 1, -16, -46, 53, -35, -25, 12, 23, -25, -11, -22, -21, -5, -38, 26, 3, 38, 1, 28, 14, -11, -22, 15, -37, -39, 16, -7, 5, 21, 60, -31, 19, -6, 2, 1, -57, 19, 39, 14, 19, 20, -16, -7, -41, -45, -37, 3, -3, 12, -12, -39, 13, 9, 13, 30, 7, -5, 18, -19, 3, -23, -8, 0, -36, -7, -38, 13, 7, -6, 10, 7, -27, 31, -28, -13, -21, -24, -19, 13, 23, -17, -5, -18, -38, 1, 41, -23, 39, -45, -1, 29, -19, -15, -36, 27, -3, 20, -48, 27, 14, -42, 0, 18, 6, 37, -20, -12, -43, -33, 44, 7, -28, -32, 43, 7, -2, 10, -31, -2, -8, 59, -49, 21, -4, -26, -14, 19, 3, 27, -13, -10, -7, -13, -19, 6, 5, -49, -6, -12, -12, 30, -31, 9, -2, 39, 20, -14, 15, -47, 11, -32, 7, -10, 21, -12, 41, 26, 23, 6, -3, -47, 37, 2, -17, 9, 33, -40, 9, 26, 4, -24, 28, 19, -21, -9, 39, -13, 39, -40, 24, 38, -23, -7, 77, 5, -47, -27, -15, 4, 4, -7, -7, 12, 17, 43, 2, 21, 1, -3, -13, 6, -1, 29, 39, 13, 5, 1, 4, 33, -15, -5, 9, 2, -4, -1, 12, -6, 24, 15, 44, 15, 19, 21, 25, 7, 16, -14, 31, 41, -20, -21, 35, -3, -10, 24, 3, 4, -30, 24, -9, -31, -11, 3, -6, 11, -17, -53, 40, -6, -26, 41, 35, -46, 3, -16, -6, 44, -55, 51, -26, 8, 28, 4, -9, -13, 29, 24, 14, -9, 31, -31, 33, 25, 12, -15, 24, 61, -11, -41, -19, -10, -36, -11, -13, -23, -3, -8, 21, -22, -15, -35, 0, 2, -39, -22, 18, 37, -59, -31, 22, 5, 29, -5, 6, -21, 8, 60, 17, 11, 14, 22, 2, -12, 4, -12, -44, -22, -38, -1, 44, 14, 4, -5, 16, 41, 13, 4, 2, 1, 4, -8, -13, 17, -24, 19, 30, 38, -29, -19, -35, 3, -3, -34, -5, -4, -40, -26, -7, -13, 8, -29, -58, 3, 27, -13, -50, 46, 35, 43, -14, 34, -10, 13, 14, 22, -6, -9, 18, 6, 52, -34, 13, 17, -23, 17, 22, -14, 24, -21, 29, -7, 21, 0, -11, 30, -3, 7, -50, 16, 27, -28, -7, -24, 8, -8, -27, 26, 6, 35, 1, 5, -26, 32, 35, -15, -12, -27, 17, 4, 8, 30, -25, 10, 6, 25, 12, 18, 8, -39, -7, -23, -20, 33, 23, 35, -28, -56, -70, 29, 55, -1, 10, 29, 18, 40, 24, 23, 37, 33, 49, 29, 43, 4, 12, 12, -2, 10, 16, 18, 1, -32, 3, 11, -22, -24, -10, -27, 18, 16, 34, -25, -25, 21, 8, 33, 37, -9, 0, -5, 9, 2, -17, 44, -12, -12, -11, 21, 41, 56, 37, -48, -12, 12, 29, 5, -17, 19, 50, 7, 32, 24, 15, -27, 9, -1, 19, 51, 15, -16, 34, 24, 4, -8, -13, 4, -15, 9, -3, -2, -11, 9, 39, 5, 12, -24, -36, 3, 16, -18, -8, 3, 0, -1, 7, -5, 8, 26, -20, 5, 5, 11, 17, 1, 16, -5, -7, 20, 1, -15, 22, 8, 26, -21, 34, -4, 32, 25, -22, 32, -24, 3, -12, 8, -1, 12, 16, 27, -52, 15, 65, 27, 16, 22, -6, -59, 6, 18, -4, 36, 57, 1, -4, -6, 20, -17, -28, -1, -27, 17, 11, 28, 34, 38, 39, -36, 10, -8, 15, 19, 26, 7, 8, -21, 50, 21, 23, 24, -1, -28, 27, -10, -4, -13, 50, -39, 14, 46, 7, 44, -9, 16, -23, 8, 47, 32, 19, 4, 8, -31, -9, -9, 0, -9, 24, -27, 15, -25, -12, 64, 0, 9, -1, -19, -3, 21, -14, -39, -22, 13, -44, 46, 1, 1, 13, -19, -5, -9, -24, -13, 1, -11, 6, -30, 26, -3, -22, 8, -31, -23, 0, 20, 9, 14, -46, 2, 33, 21, 25, 1, 16, 11, -36, -25, -6, 41, -12, 1, -17, 42, -7, 9, -12, 8, 37, -48, 4, -30, 45, -38, 7, -13, 6, 13, -43, -38, -6, 5, 21, -14, 13, 24, 35, 5, -25, -12, 1, -7, 6, 3, 33, 28, 1, 23, -12, -6, -8, -11, -10, -4, -5, -3, -3, 6, -9, 33, 26, -5, -30, 10, -18, -30, 16, -40, 16, -1, 2, -19, 20, 19, -5, 39, -39, 18, 30, 37, 26, 18, 15, -5, 51, 19, 31, -31, 49, -29, -34, -8, 26, 31, -21, -15, -42, -19, -9, -11, -41, -21, 43, 3, -33, -27, 9, -18, 5, -11, 0, 3, 36, -17, -32, -45, 36, 44, -29, -30, 20, 24, 58, -2, -7, 1, 18, 2, 2, -22, 38, 9, 41, -16, 2, -12, 2, 0, -20, -62, -37, -37, 0, -40, -13, -4, 42, 24, -38, 17, 19, 8, -3, -5, -3, -19, -19, -44, 22, -17, 16, 1, -40, -34, 49, 24, -17, -46, -22, 4, 35, -16, 5, 30, -13, 40, -17, -30, 30, -28, 39, -18, 1, -12, -2, -36, -46, -34, -19, -50, -44, 13, -45, 20, 5, 33, -24, 19, 0, 2, -4, -17, 42, -30, 7, -23, 16, 11, 3, 32, -58, -12, -3, 40, -19, -44, -10, -6, -13, 37, -57, -55, 2, 27, 6, 6, 14, 30, 42, -4, 15, -7, -12, -33, 17, 11, 28, -22, -30, 6, 3, -16, -9, -15, 59, 57, -18, 12, -24, 47, 6, 48, -20, 4, -49, 3, -6, -15, 5, 30, 13, -16, 9, -13, 9, -16, 35, 27, -68, 24, -17, 0, -37, 23, -40, 29, 10, -23, 19, -8, 9, -23, 1, -6, 25, -7, 31, 21, -4, -12, -42, 33, -6, 34, -32, -44, -45, -25, 18, 42, 13, -26, 10, 14, 22, -38, 28, -18, 4, -16, -15, 27, -35, -14, 18, 31, 3, 33, -33, 29, 9, 26, 9, 21, -17, 18, -13, 7, 15, -11, 1, -33, -37, -33, 22, -19, 4, 11, 12, -9, -38, -32, -21, -19, 24, -32, 37, 32, -24, -36, 29, -12, 1, 8, 6, 1, 10, 20, 4, 16, 7, 8, 10, 26, 16, 22, 25, 28, 55, -40, 25, 40, 75, -11, -8, 20, -14, 34, -2, -13, -3, 22, 5, -34, -7, -2, -13, 20, -24, -27, 22, -26, 16, -19, 16, -5, 8, -17, 21, 30, -10, -23, -20, 12, -10, 21, -43, 4, 20, 26, -11, -24, -17, 51, -1, 31, 11, -45, 7, -4, -12, 14, 12, -29, -44, 0, -20, 6, -6, 12, 0, 18, -16, 43, -39, -9, 23, -37, 23, -27, -2, -6, -6, -42, -4, 9, -6, 2, 30, 23, 4, -24, -30, 30, 30, -11, -9, -19, 18, -23, -48, 6, 22, 44, -44, -5, -15, 5, 9, 6, 7, -17, -11, -24, 48, -7, -50, -4, -2, 28, 17, 21, -7, -42, 66, 2, -24, 6, -24, 2, 15, 26, 0, -10, -14, 45, -14, -7, -18, 55, -6, 27, 0, 24, -35, 7, 18, 1, 17, -6, -9, 13, 0, -13, 9, -53, -5, -27, 2, -18, 10, 2, 16, -6, -49, 9, -9, 7, 12, -37, -24, -30, 19, 3, 1, -35, -54, -40, 10, -2, 11, 71, 26, 51, -25, 30, -23, 21, -6, -1, 8, 9, 0, -6, -18, -16, -1, 3, -9, 0, -4, 45, 17, 40, 42, 19, -15, 20, 10, -25, -10, -37, -33, 3, 26, 35, 4, 0, -7, 19, 19, 4, -22, -11, -13, 17, 55, -25, -19, 33, 23, -5, 19, 38, -1, 3, -9, 44, 22, 15, 9, 9, 0, -1, -20, 0, -6, -12, -19, 3, 32, -5, 59, -22, 42, 37, -30, 29, -4, -6, 12, 42, 9, -25, -13, 7, -9, 16, -29, 25, 29, 12, 16, 25, 25, 7, 3, -37, 1, -14, 22, 10, 11, 21, -8, 18, -20, 15, 19, 8, 10, -46, -6, -73, -41, -22, -20, 5, -18, -19, 8, 28, -1, -4, -22, 31, 8, -32, -3, 29, 23, 23, -2, 6, 20, -62, 55, 2, 2, 24, 12, -6, 34, -23, 16, 26, -44, -12, 32, -15, 35, 11, 21, -18, 14, -14, 20, 14, 3, 40, 24, -3, 14, 23, 28, -50, -28, -20, 12, -19, 1, 1, -42, 0, 56, -5, -29, 1, 33, -52, 6, 42, 64, 16, -45, -21, 25, 6, -10, -9, 1, -9, -6, -10, -18, 28, 1, 38, -16, -36, -30, 28, 10, -19, -51, -8, 26, 5, 32, -27, 2, 37, -12, -10, -12, 21, 25, -15, 41, -56, 9, -9, -15, 41, 3, 12, -31, 2, 59, -3, 41, 33, 9, 17, 15, -4, -54, -31, 35, -25, -8, 3, -1, -13, 8, 21, -10, -52, 0, 11, -13, 40, 19, 6, -6, -19, 69, -23, 24, 6, -9, -39, 13, 13, 19, 13, 19, -28, 32, -20, -3, 6, 31, -19, 17, -22, 50, 38, 45, -15, -24, 35, -21, -46, -11, -21, 38, 13, -25, 8, -17, -14, 36, 9, 4, 22, 38, -43, -15, 0, -12, 21, 7, 33, 29, -14, -19, -26, -21, 9, 60, -12, 27, -31, 3, -42, -20, 7, 1, 54, 31, -10, 13, -23, -12, -5, 4, -20, 28, 7, -5, -11, 3, 11, 43, 13, -23, 18, 4, -2, 16, -3, -12, 16, -15, -7, -4, -43, -54, 35, 0, -35, 33, -7, -1, -23, 26, -32, 30, -22, -37, -10, -22, -20, 17, -30, -47, 19, 21, 5, 8, 15, 14, 16, -19, -28, -13, -14, -49, 23, 8, 37, 17, 8, -17, 37, 8, 24, -19, 43, 26, -15, -1, -37, 11, -45, -1, 26, -1, 13, 2, -8, 18, 38, -29, -44, 14, -7, 30, -6, 6, -5, 2, 20, 14, 21, -39, 19, -38, 17, -7, 19, 55, 9, 19, 20, -37, -42, 14, 16, 60, -5, -2, -33, 13, 11, 10, -33, 26, 7, -21, 8, -11, 18, 4, -4, 24, 38, 13, -8, 10, 31, -52, -10, -9, 19, 19, -22, -21, -45, 0, 36, -2, 9, 14, -17, -16, -5, 32, -9, 1, -12, -14, 3, 15, -13, -1, -30, 84, 23, 1, 4, -13, 20, -20, -28, 37, 18, 11, -47, 1, 6, 34, -8, 2, 12, 20, -1, -6, -16, -80, -22, 25, -23, 7, 24, 7, -17, -17, -17, -18, -10, -18, -2, -23, 30, 16, -19, -10, 8, -1, 11, 6, -29, 55, 12, 19, 47, -6, -45, -17, -38, 24, -8, 3, 22, 41, -4, 5, 10, -14, 14, 34, 25, -20, -36, -2, 8, -19, 29, 11, 12, 1, 10, -15, -10, 42, -22, -37, -33, -22, -24, 25, 20, 1, 0, -22, 27, 16, -16, -39, 16, 26, 11, 23, 39, 17, -23, 12, -2, 12, -6, -26, 3, 9, 7, -11, 34, -6, 7, 52, 7, -15, 17, 23, -14, 7, 10, 11, 12, -45, -30, -58, -7, 47, 9, -36, -8, 1, 10, -3, 22, 30, 3, -14, 18, 7, -2, -3, 8, 11, 16, 40, 16, 45, -22, 55, 19, 28, -11, -31, -30, 10, -34, 16, -14, -12, -20, 1, 1, 11, -29, 11, 23, 10, -59, 20, 22, -1, 23, 6, -12, -13, -23, -12, -14, -24, -13, -25, 15, -18, 36, -29, -33, 25, 5, 35, 36, 15, 40, -21, 22, 12, 23, 5, 28, 14, -12, 0, 14, -19, 10, -17, -25, -12, 14, -31, 44, 5, -2, 17, 51, 26, -16, -1, 1, -26, 18, -30, -9, 0, 33, 17, -28, -24, 11, -36, -26, -4, -16, 46, -25, 12, 33, 17, -45, -10, 32, -11, 32, -35, 14, -8, 24, -14, 29, -19, 15, -11, -1, 4, 7, 18, -5, 27, 8, -8, 20, -6, 59, -52, 23, 1, -6, -31, 7, 17, 24, 14, 24, 35, -47, -16, 59, -2, -30, 52, 13, 38, -5, 26, -1, 30, -16, 23, 8, 4, -12, 6, -1, -21, 27, -3, 2, 25, -7, -3, -12, 20, -41, 24, 27, 24, -9, -35, 29, -2, -28, 18, 23, 26, 9, -15, 13, 0, -35, 6, -32, 34, -36, 45, 34, -22, -38, 43, -26, -15, 12, 19, -24, -28, -13, -33, -45, -1, 4, 7, 14, 54, 17, -4, -31, 42, 26, -3, -21, 27, -38, -15, 19, 27, 31, -3, 25, -4, -25, -11, -20, 4, 5, 4, -1, 11, 5, -50, -10, 26, 20, -4, -27, 6, -1, 9, -21, -5, -13, 25, 19, -31, 62, -15, 21, 29, 8, 8, 15, -5, -25, 48, 25, 12, -27, 9, -3, 28, -37, -29, -32, 13, -18, -2, 0, -11, 20, -8, -28, -12, 47, -3, 16, 19, 17, 13, 37, 8, 2, -2, 1, -10, -7, -18, -23, -24, -49, -12, 28, -33, -60, -23, 33, -31, 20, 35, 25, 39, -2, -22, 63, -7, 32, -13, 30, 2, 48, 18, -13, 10, -31, 38, 9, -5, -21, 23, -21, -12, -2, -26, -28, -8, -21, -8, 31, -3, 37, 28, 26, 25, 5, 10, 21, -8, -47, -22, 25, -53, -26, 4, 6, 6, -9, -22, -50, -18, 6, -3, -11, 8, 34, -25, 34, 2, 9, 33, 34, -6, 20, -33, -22, 17, -7, -13, 34, -13, -29, -8, 8, -17, -59, -29, -40, -43, -1, 19, 56, 13, -17, 14, 27, 32, -2, 12, 1, -8, -31, -7, 18, -15, -11, 8, 12, -7, -29, 5, -33, -10, 13, -32, 0, 17, -9, 12, 44, 15, 16, 9, -17, 33, -31, -34, -9, -41, -37, -12, 34, -26, -12, 24, 27, -45, 6, -12, 38, -23, 9, -3, 30, 21, 11, 53, -25, -21, -40, -15, -28, 19, 28, -6, 6, 16, -48, 24, -20, -24, -16, -18, -19, -18, -4, 27, 23, -12, 41, -2, 11, -7, -13, -31, -17, -66, 15, 12, -22, -27, 17, 40, 28, -44, 28, -53, 16, 13, 9, 42, 57, -1, -8, 13, 16, 3, -4, -2, 4, 37, 29, 25, 41, 29, 3, 23, -7, 7, 8, -16, -8, 15, 34, 0, -19, -43, -15, -26, -6, -11, -6, 1, 36, 38, -31, 2, -3, 12, -44, 16, 17, -2, 23, 19, 20, -15, 2, 28, 6, -3, 18, 2, 52, -25, -17, 45, 23, -58, 24, -7, 40, 8, -15, -4, 34, 13, 13, -3, -40, -12, -28, 14, 10, -34, 38, -30, 14, -37, 3, 12, 23, -19, -11, -38, -7, -18, 1, -23, -7, -26, 15, 3, 20, 7, 28, 1, -47, -12, -35, 14, 12, -33, -16, -21, -9, -49, 29, -24, 21, -1, -16, -30, -16, -27, 26, 47, -4, 29, -15, 56, 37, -23, -17, -10, 19, -3, 24, 32, -29, -15, 9, 10, 20, -14, 40, -1, 12, -5, 16, -12, 23, -4, -19, 9, 11, -6, 16, 1, -39, -23, 39, -39, -19, -18, 33, -22, -22, -8, -17, 21, 55, 3, 0, 11, 6, 9, -45, 68, 5, -5, 25, 28, -20, -23, -25, -19, -8, -34, 7, 4, 45, -22, 32, 2, 41, 16, -28, -27, 16, -11, 19, -34, 28, 5, 19, -25, -41, -5, 19, 1, -18, -7, -18, 43, 15, -19, -10, 5, -55, 18, -43, -42, 48, -60, -34, 44, 6, 41, 41, 32, -42, 19, -18, -11, -26, 31, 14, -46, -25, 4, -29, 33, 25, -39, -20, -30, -3, -12, 2, -28, -9, 25, 46, 57, 28, 23, -32, 44, -9, -29, -6, 53, -31, 5, 51, 34, -33, 7, -3, -13, 4, -17, 26, -23, -17, -21, 40, -7, -23, -7, -4, 41, -15, 40, -16, -7, -19, -70, -27, -28, 30, 16, 6, -22, 23, -50, 6, 6, 30, -61, 31, 13, -11, -53, -8, -50, -31, -20, -19, -8, 1, -14, -22, 35, 9, -23, -25, -28, -27, 28, -8, 10, 5, -30, 45, -9, -45, 29, 33, 41, -7, 14, 6, 21, 1, 8, -41, -21, 26, 6, -15, 2, 23, -5, -51, -13, -22, 17, -48, 24, 1, -21, 22, 14, -6, 14, 63, -52, 2, -42, -20, 46, 24, 8, -2, 13, -27, 29, 38, -5, 15, -11, -7, 4, -23, -39, -17, 33, -2, 14, -26, 7, -2, -27, -39, -10, -34, 29, 10, 3, 28, 31, -48, -46, 6, -35, -59, -20, -31, 26, -4, -27, 8, -7, 15, -12, -6, -13, 31, -26, 18, 13, 0, -23, 66, -54, -29, 8, 5, 24, -14, 1, -23, -14, -5, -21, 0, -12, -1, -6, 52, 34, -39, 17, 25, -7, 27, 1, -7, 42, -38, -6, -17, 5, 3, 12, -5, -1, 37, -16, -4, -6, -17, 9, -32, -7, 20, 4, -1, 52, -1, -28, 30, -42, -35, 50, 32, 8, 31, 30, -33, 20, -4, 1, 30, -3, 2, -11, 5, -25, -16, 10, 17, -19, -26, -21, 16, 7, -2, -34, -29, -41, 27, -2, 3, 36, 9, -38, -22, 31, 44, 23, 23, -11, 8, -56, 20, 6, -32, 0, 2, -41, -20, -37, 0, -1, 55, 9, -19, 48, 19, -19, 4, 27, 1, 1, 16, -14, -5, -43, -5, -3, -7, 12, 4, 0, -20, -3, 18, 28, 15, -27, 43, 30, -16, -36, 5, -7, 12, 10, -37, -60, -9, -18, -27, 22, -14, -16, -54, 3, 1, 11, 23, -36, 25, 38, -50, -2, 20, 8, -4, 0, 7, 5, -9, -13, -12, -3, 25, -17, -14, 28, -14, 32, -24, -45, -30, 25, -31, -27, -37, -7, 6, 2, -13, 16, 60, -22, 14, 10, 12, 20, 6, -4, -7, 12, 11, 37, 4, -64, -23, -6, -31, -10, 10, 16, 10, -8, -2, 0, -18, -22, -9, 6, -9, -14, 30, 0, 29, 16, -19, 22, 32, -6, 6, 29, -24, 12, 9, -59, -11, -39, -6, 17, 40, -18, -16, -10, -3, -17, 53, 21, 6, -7, 48, 79, 39, 1, -8, 22, -40, 28, 13, -45, -9, -33, 24, -16, 14, 40, 13, 6, 29, 40, -28, -40, -3, -19, 17, 19, 22, 70, 13, 37, -15, 9, 9, -29, -21, -56, 39, 1, 32, 3, 15, 10, -18, -3, 24, 5, -36, 26, -6, 26, 56, 25, 2, -24, 14, -21, 32, 1, -1, 12, 51, -19, -3, 11, 13, 18, 0, 39, 18, -7, 26, 0, -10, 18, -47, -8, -14, -9, 3, 8, 32, 12, 3, 4, -7, -19, 22, 9, -2, -2, 20, -36, -35, -32, -12, -23, -22, -6, 19, 20, 28, 18, 11, -62, 25, 21, 1, -8, 5, -14, 26, -26, 16, -25, -17, 15, -32, 6, -32, -44, 30, -21, 15, 14, 44, -6, -20, 50, 43, -14, 2, 0, 35, 38, -10, 16, -5, 4, -1, -20, 54, -59, 25, 10, -29, -30, 4, -27, 35, 36, 5, -22, 12, 3, -31, -13, -29, -30, 10, -38, -12, -63, 6, -22, -6, 33, 8, -47, 20, -31, -25, 16, -21, -14, 23, -6, 4, 5, 48, 20, 28, 34, -3, -57, -12, 1, 10, -8, 2, -29, -10, -1, -20, -46, 38, 16, 25, 14, 7, -46, 19, 7, 37, 9, 7, -1, -2, -13, 13, -13, -21, -32, -26, -10, -19, -35, 16, 1, -8, 6, 30, -12, -19, -45, 28, 35, 5, -15, -7, 17, -5, -28, -20, -38, 30, 2, 7, -2, 13, 26, 13, -16, 36, 34, -48, -13, -32, 10, 24, 24, 38, 0, -1, 1, -3, 19, 12, -10, -35, 9, -30, -34, 37, -31, -26, -19, -27, 17, 23, 5, 8, -25, -31, -3, -14, -3, -9, 26, 10, -20, 17, -33, 35, 15, 14, -25, -4, 43, -11, -32, -20, 30, 21, 31, 14, 2, -3, 44, 26, -4, 39, 32, 18, 11, -12, 5, -26, 10, -5, -10, 10, -25, -4, -58, -15, -17, 33, 19, 5, 18, -7, 12, 1, 26, -15, 29, -17, -13, 5, -39, 35, 43, -5, -39, -17, 10, 16, 12, -16, 48, 49, 3, -9, -3, -3, 44, -68, -26, -4, -28, -23, -4, -40, -62, 40, -2, -39, -2, 3, -2, 32, -18, -13, 20, -15, -12, 15, 1, -17, 67, -15, -2, 12, 11, -24, 47, -18, 14, 2, 24, 27, 12, -8, -14, -27, 9, -17, -9, 25, 17, 16, 39, 18, 27, 13, -63, 25, -4, -3, -10, -4, 31, -12, 9, -8, 18, -18, -26, 3, -31, 1, 8, 15, 28, 6, 26, -28, -9, 31, 38, -30, 50, 6, 5, 39, 27, 37, 14, -4, 16, 25, -31, -2, 29, 3, 5, 3, -38, 38, 10, -10, 28, 16, -12, 14, 24, 36, -19, 20, 49, -67, -24, -25, 9, 2, -55, 44, -43, 3, -37, 14, 43, -33, 20, 35, 4, -28, -16, -1, -19, 9, 20, 26, 10, 32, 28, 27, -8, 23, 25, 5, -8, 5, 28, -35, 18, -8, 36, 25, -2, -8, 10, 23, 21, 12, 49, -8, -16, -34, -24, 12, -3, -42, -3, -30, -44, -13, 23, -8, 4, 19, -14, 15, 24, -10, 14, -11, -39, 4, -23, -17, 0, 3, -12, -12, 3, -34, 19, 22, -2, -18, 7, 7, 4, -17, 28, -41, -52, 35, -17, -6, 23, 13, -16, -21, 9, -3, 39, 15, -36, -8, -49, 24, 29, 5, 6, 11, 5, 44, -30, 25, -35, -7, -26, 5, 19, 5, -16, -3, -14, 28, -46, 3, 9, 17, -26, 29, -4, -19, -12, 38, 28, 9, -19, -47, 7, -18, -18, 10, -59, 8, -11, 4, 2, -46, -19, 18, -32, 61, 27, -8, 7, 1, -7, 14, 13, 8, -49, 50, -23, 12, 61, -37, -1, 20, 7, 12, -8, -22, 24, 10, 9, 7, 22, 18, -2, 8, -22, 17, 32, 23, -14, 4, -2, 19, -8, -13, 2, 5, -33, 11, 31, -21, 18, 5, -28, -39, 21, 3, 22, -26, -1, 37, 11, -14, -3, 0, -6, 32, 12, -12, -20, -12, 10, -3, 8, -4, 11, -10, -1, 8, -17, 5, 46, -29, -25, -23, -46, -37, -4, 5, -26, 40, 31, -17, 12, -48, 3, -32, 13, -22, -17, -23, 18, 48, -17, -6, 41, -1, -18, -15, 3, -54, -20, 31, -52, -4, -6, -18, -10, -14, -9, -4, -26, 3, 15, 13, 36, 25, 34, -17, 41, 9, -17, -7, -23, -14, 18, 14, -75, -6, -10, 41, 13, -8, -7, -48, -14, 38, -3, -11, 27, 24, 28, -21, 26, -36, -13, 11, 10, -15, -8, -20, -36, -1, -15, -22, -3, 7, -18, 34, -38, 12, 21, -6, 4, 25, 2, -33, 38, -5, -18, 21, 2, -4, -9, -17, -69, -19, -30, 4, -18, 10, -22, 13, -1, -26, 8, -7, 3, 38, 2, 12, 50, 14, -3, -5, 1, -7, 36, 6, -13, -25, 20, 25, 4, -19, -26, -24, -40, -1, 4, 29, 38, -37, 15, -27, 16, 8, -28, 25, 45, -43, -47, 18, -42, 45, 52, -21, -18, 38, -11, 6, -34, -30, -12, -3, 20, 28, 27, -39, 8, 13, 31, -15, -23, -40, 0, 58, 23, 3, 27, -22, 5, 25, 0, -2, -10, -9, 9, -7, 10, 33, -33, -47, -4, -18, 9, -4, -13, -30, -9, -22, 8, 11, 33, -8, 19, -25, -15, 11, -37, -40, -33, -3, 18, 13, 4, -18, -15, -16, -13, -19, -35, -6, 8, -8, -5, -42, 1, -30, -33, 60, -8, -28, 25, -12, 28, 25, -20, -22, -17, -27, -51, 10, -4, -19, 14, -11, -26, -30, 18, 12, -8, -42, -17, -14, -22, -28, 29, -24, -3, 21, 18, 2, -47, 15, -21, -4, -17, 27, -26, 8, 17, -4, 2, -20, 26, 14, 11, -25, 3, 7, -37, 2, 11, 11, -13, -12, -13, 19, -31, -14, 28, 26, -38, 8, -8, 0, -9, 5, -11, -15, 14, 35, -27, 12, 33, -43, 2, 32, 53, 16, 20, -9, 8, -27, 15, -24, 14, -13, 11, 23, 19, -20, -10, -23, 1, -25, -6, 7, -3, 39, 11, -13, 45, -35, -16, 19, -10, 37, 24, 11, 12, 17, 21, 26, 23, 12, -7, -15, -12, 33, 11, 40, 1, -16, -2, 13, 13, 20, 17, 39, -11, 31, 5, 31, 11, 11, -24, 6, 3, -12, 15, 27, 20, 31, 11, -43, -17, -1, 14, 27, -3, -4, 6, 39, -34, 19, -8, -3, 19, 2, -6, 12, 44, 12, 13, -32, -35, -17, -17, -17, -18, -5, 11, 10, -26, -17, 42, 16, -55, 11, 18, -39, -8, -9, -14, -45, -6, -5, -25, 51, -16, -9, -16, -2, -15, -14, 0, 20, 3, 9, 41, 42, -6, 39, -20, -16, 8, 10, 2, -21, 39, 3, -37, 3, 2, -21, 2, -26, -13, 8, -52, -26, -17, -20, 10, 2, -28, -9, -6, 15, 17, 33, 14, -16, 15, -10, 3, 9, -8, 22, 6, 36, 20, 19, 28, -21, -15, -31, 3, 40, 11, 0, -33, 40, 30, 8, -51, 5, -12, -32, -9, 7, 2, 17, -24, -5, 8, -19, -15, -25, -18, -33, 6, 13, 5, 37, 15, 15, 13, 1, 16, 49, 33, 10, -46, -1, 5, 12, 28, 9, 20, 22, -9, -18, 21, 0, 7, -8, -29, -1, 8, 2, 0, 36, 12, 20, -25, 0, 18, -23, 24, -34, -18, 28, 34, 9, 7, 1, -39, -6, 12, -5, -6, 30, -16, -30, -36, -19, -45, -16, -2, -17, 13, 5, -11, 42, 9, 22, -29, -38, 1, 41, 12, 34, 11, -1, -7, -2, -17, 17, 20, -29, -21, -52, 1, -10, -20, 40, -16, -63, 0, 9, 4, -32, 0, -31, 31, -12, 12, 37, 12, 38, 9, 25, -30, 16, 19, -20, 26, -16, 15, 18, 28, 13, -12, -16, -11, 8, -40, -33, 6, 17, 9, 25, 11, 32, 2, 2, 0, 46, -2, 23, -18, -12, 19, -22, 20, -19, 25, -10, -29, 16, -44, 32, -33, 2, 33, -24, -30, 19, -9, -9, -16, 2, 39, -47, 10, -31, 7, 47, 6, -21, 35, 17, -75, 46, -22, 15, 17, -29, 15, 10, -24, 28, 30, -8, -18, 54, -29, 23, -22, 33, 10, -20, -52, 16, 3, -1, -12, -2, 11, -7, -2, -11, 26, 34, 20, -16, 10, 20, 3, -12, -7, -16, 47, -48, -24, 25, 24, 19, -25, 34, -16, 22, 0, -13, -33, 13, -23, -17, -35, -25, 22, 13, 42, 22, -29, -22, -6, 27, -26, 46, -12, -6, -10, 6, -26, -14, 23, -8, -60, -11, -39, 31, -11, -9, 4, 18, 19, 21, -1, -29, 33, -6, 27, -29, 28, -14, 44, 5, 25, 23, 3, -3, -52, 10, 9, -16, 31, -33, -29, 37, 69, -27, 53, -16, 2, 26, -4, 25, 21, 8, 39, -17, -9, 21, 1, 24, -61, -3, -16, -40, -28, 6, 28, -50, -21, 14, -40, 3, 5, 53, 9, -6, 34, -18, -16, 9, 35, 8, 5, 0, -61, 15, 72, -27, -2, -61, 9, 26, 11, -31, 25, -33, 41, -6, 22, 54, -2, -38, 15, -42, -3, -18, 13, -12, -18, -6, -42, 3, -31, -13, 12, -30, 45, -8, -27, 28, -30, 28, -20, -15, 32, -1, -26, 11, 32, -3, -6, 32, -43, 50, 8, 17, 19, -3, 34, 4, 30, 36, 36, -6, -8, 30, 8, -3, -24, 0, -22, -27, -5, -17, -17, 34, 3, -14, -12, -23, 55, -2, -44, 9, -15, -45, 3, -31, 33, -17, -41, 18, 18, 24, 34, -6, -21, 9, -19, 19, -45, 2, -13, 14, -11, 16, -6, -14, 41, 6, -10, 24, -18, 40, -6, -24, -15, -20, 11, -11, 27, -20, 15, -37, -48, -41, -4, 39, 36, -14, -53, -10, 45, -22, 21, -14, 36, -66, 3, 50, 48, -31, 19, 17, -20, -6, -26, 33, -33, 12, 40, 48, -42, 18, -5, 2, 22, -5, 15, 3, 39, -5, 13, 34, 17, -29, 32, -4, 15, 2, 22, 22, 10, -6, 5, 23, -10, -10, -28, -18, 6, -30, -35, 19, 41, 0, -27, -10, 41, 17, -25, -9, 39, 8, -33, -36, -4, -23, -22, 46, -25, -26, 19, 53, 11, 17, -2, 3, 24, 20, 43, -5, 50, -5, 31, -14, -36, 16, -22, 6, -32, 36, 10, -3, -43, -21, -21, 18, 8, 16, -23, -16, -32, 30, 14, 13, -19, -36, -18, 16, 24, -18, 13, -3, -5, -25, 48, 48, -23, -33, 14, 17, -3, -35, -34, -6, 10, 29, -23, 10, 27, 19, 7, 15, 14, 72, 9, 7, -21, 9, 2, -4, -55, 24, 24, 29, -19, 24, 12, -16, 18, -3, -3, -41, 10, -42, -24, 36, 8, -18, -4, 15, 18, -12, -16, -35, -28, 6, 49, -16, 44, -4, -33, 22, 23, 36, -1, -10, -34, -1, -7, 11, -16, -69, -25, 23, 3, -45, -24, 16, 46, 0, -25, 25, 3, -28, 1, -17, -26, -21, -66, 29, 25, -15, 19, 16, -36, 38, 1, 2, 6, 22, 4, -13, -18, -19, -11, -28, -16, -13, -8, -42, -2, -22, 33, 40, 34, -40, 30, 4, 11, -7, -3, -1, 16, 27, 16, 37, 0, -9, 35, -66, -35, 5, 29, 29, 45, -29, -20, -7, 46, 2, 26, -14, 20, 24, -33, 42, -10, 27, -34, 25, 1, -3, 28, 4, 5, 33, 18, -24, 0, -31, 20, 2, 2, 18, 42, -31, 27, 11, 15, -16, 4, 28, -11, 13, -22, 26, -37, -32, -15, 5, -31, -13, 12, 28, -41, -19, 38, 5, 7, 15, -5, -31, -9, 1, -24, -21, -11, -7, -13, 19, -2, -9, -5, 12, 16, 10, -18, -6, 27, -40, 41, 33, 26, 0, 31, 28, -16, -11, 27, -5, 8, -23, -27, 43, -19, 39, 5, -11, -19, 6, -13, 1, -29, 41, 5, 34, 36, 15, -22, 23, -1, -27, -5, 2, 16, -19, -10, 12, 32, 19, 10, 13, -34, -8, -25, -1, 26, -22, 6, -21, 17, -4, 10, 16, -2, 11, -21, -15, -3, -51, 7, 8, 17, 22, 25, 2, 12, 19, -20, 22, 29, -36, -21, -22, -16, -51, 3, 4, 37, -3, 1, -30, 12, -39, -2, 40, -16, -2, -14, -43, -38, 2, -11, 62, -55, 21, -9, -13, 25, -38, 17, -26, 12, -16, 24, 16, 9, -11, -4, 14, -27, 39, -11, -16, -32, 5, -36, 37, -44, -30, 24, -20, 7, 33, 27, 1, -3, -21, 8, 38, -5, -21, 41, -18, 38, -34, -8, -17, -4, -24, -44, 21, -13, 18, -32, 4, 59, -30, 6, -15, 8, 11, -23, 21, 26, -22, 21, -6, 21, -15, -4, -43, 15, 46, -13, -3, -15, -17, 14, -27, -5, 36, 0, 1, -16, 39, -14, -9, 25, -7, -31, 22, 45, -7, 21, 17, 15, -42, 27, 27, 13, -13, -20, 1, 8, -32, 35, 42, 21, 38, -10, -4, 3, 4, 25, -35, -24, 42, -18, -25, 13, -16, 2, -30, -25, -3, -2, -25, -1, -13, -10, -17, -18, 33, 25, -26, 21, -6, -6, -10, -2, 4, -8, -11, 26, -19, -19, -25, 9, -3, -16, -27, -2, 34, 47, -5, -13, 7, -46, 30, -21, 1, 15, 8, -5, 51, -35, -11, 13, -13, 49, 10, -13, 38, 9, 11, 17, -37, 16, -4, 11, 13, 13, -2, -16, -16, 24, -12, 12, -18, -17, 8, -22, -4, -3, -4, -18, -43, 17, 6, -15, 6, -2, -21, 19, -25, 0, -5, 29, 25, -15, 24, -8, -15, 36, -17, 30, -11, 17, -16, -2, 21, 3, 29, 43, -24, 18, 11, -16, 48, -45, -66, -18, -18, -20, 36, 22, -28, -10, 4, 20, -44, -41, 12, -7, -23, 21, 1, 26, -50, 13, -2, -10, -2, 6, -16, -33, -8, -6, 36, -7, 6, 28, -6, 8, -9, -37, -2, 22, -29, 7, -39, 17, -19, 28, 14, 26, -24, 15, -3, -4, 16, 54, -29, -9, -21, 27, -27, 8, -16, -45, -4, 15, -15, -29, -12, -4, 17, -14, 32, 15, 15, 44, -4, -42, -50, 53, -29, -24, -12, 37, 5, -1, 20, -15, 5, 19, -24, 0, -24, -3, 25, -21, -24, 20, -14, -15, -17, 16, 1, 3, -10, 25, 22, -8, -37, -30, 10, -8, -38, 1, -55, 7, -33, 16, 7, 44, -47, -10, 30, -9, 41, -16, -31, 39, -5, 27, -21, 20, 15, 2, 6, -17, -29, 7, 27, 18, 15, 37, 27, -21, -6, -34, 30, -33, 16, 12, -21, 47, 16, -16, 15, -6, 26, 9, -16, -5, -6, -29, -39, 26, 20, -22, -4, -7, 11, 28, -35, 13, -8, -10, -8, 35, 13, 1, -10, 25, -12, -15, -20, -17, 16, -27, 5, 16, 1, -18, 24, -25, -9, 12, 13, 21, 25, -13, 34, 7, 12, 22, -18, -20, 24, -26, 26, -23, 26, -35, 44, 14, 20, -11, 10, -7, -4, -12, -29, 40, -44, 17, -20, 3, -24, 8, 0, 28, 35, 6, 4, -41, -1, 16, -23, -16, 47, -24, -20, 33, 6, 14, 6, 48, -21, 34, 35, 12, 31, -21, 21, -4, 14, -25, -8, 28, -14, -33, -17, -2, -35, -34, -19, 21, -2, -15, -27, 3, -11, -51, 5, 27, 19, 12, -12, 13, -45, 36, 26, -33, -30, -20, 3, -21, 6, -39, -36, 28, 18, -22, -6, -56, -16, 20, 2, 16, 31, -22, -60, -9, 1, -10, 35, 32, 40, 29, -62, -21, -33, -41, 9, -18, -18, -2, 15, -24, -19, 16, -33, -35, 1, -3, 52, -5, 0, -8, 8, -18, 5, 21, 4, -47, 15, -43, -12, -13, 40, -36, 29, 26, -14, 4, 40, -4, -23, 24, -4, 19, -16, -21, 16, 31, 29, 16, 10, -6, -21, 0, 0, 0, 13, -8, 7, -31, -14, 27, -19, -35, -23, 60, 23, 3, 38, -57, -10, -16, -5, -30, -7, -29, 9, 8, -19, 4, 29, -6, -9, -10, -38, 43, 22, 2, -39, -17, 1, 31, 12, -10, -10, -10, 12, -24, -10, -5, 24, 28, -21, -12, 2, -16, 43, -10, 25, 14, 10, 5, -20, 26, 3, -22, 22, -20, 16, -16, 18, -8, -12, -9, -29, 35, 20, 10, 62, 23, 5, 34, 2, 20, -47, 41, -47, 13, 43, -27, -3, 19, -60, -33, 25, -10, 22, 11, -21, -23, -32, -8, 15, -5, 39, 16, -14, 27, 39, 31, -5, -2, -36, 19, -23, 5, -25, -32, 0, 27, 31, -13, -4, 28, 26, 16, 20, 23, -40, -10, 17, -37, -4, -4, 2, 0, 38, 1, -18, -20, 15, 4, 31, -15, -36, 3, -3, -18, -39, 36, -3, 32, -4, 12, -8, -6, 9, 7, -44, -6, 43, -41, -10, 56, 4, -24, 40, -30, 31, 16, -2, -7, -18, 37, -41, -13, -7, -12, 8, -29, 16, 32, 39, 7, 16, -31, 5, 27, 10, 19, -1, 27, -14, -13, 9, -33, 12, 18, -52, -21, -10, 22, 31, -6, 22, 9, -58, 36, 22, -39, -7, 3, -16, -3, -50, -16, 38, -57, 52, 5, -4, -15, 2, 13, 16, 20, -6, 9, -6, -6, 67, 24, -2, 13, -13, -19, 4, 7, 22, -29, -34, -15, 18, 22, 15, -9, -35, -41, 22, -9, -6, 5, -24, 18, -40, 41, -9, 30, 26, -44, 7, -19, -15, -14, 8, 3, 8, -18, 6, 10, 42, -36, 39, -12, -18, 16, 30, -9, 13, 13, 5, 48, -11, -26, -20, -26, 32, -32, 18, 22, 18, 60, -25, 32, 49, 11, 53, 28, -11, 29, -6, -3, -62, 25, 18, -7, -13, -18, 41, -20, -16, 3, 28, -15, -17, -30, -24, 2, -11, 55, 14, 3, -2, 13, -17, -29, -20, -5, 32, 12, 57, -54, -54, -6, 32, -48, 19, 13, -73, 25, -19, 2, 13, -15, -4, 40, -19, 4, -20, 17, -27, 42, 17, -2, -15, 39, 15, 4, 40, -4, -18, 14, 34, 30, 16, -4, -13, -2, -14, 0, 8, -33, -18, 38, -15, -47, -10, -8, 36, 29, 24, 14, -35, 2, -4, -23, -27, 11, -18, -28, -34, -2, -28, 9, -20, 11, 7, 4, 10, -20, 19, -13, -42, 31, 5, 14, -15, 36, 18, 14, 55, -15, 5, 50, -7, 50, -15, 26, 46, -9, 4, -14, -10, 26, 1, -18, 18, 37, 40, 47, -32, 29, 42, -5, -10, -36, -24, 12, -3, -37, 33, -6, -61, -40, 35, 3, 10, 40, 15, 9, -40, -13, -6, -64, -28, 30, -33, -41, -6, 33, 46, 29, -16, -8, -12, 33, 39, -15, 41, 36, 2, 35, -18, -21, -34, -12, 27, 24, -4, -6, -16, 17, 0, 37, -28, -32, -17, -23, 7, -3, 6, -20, 12, -5, 36, 9, -1, -18, 30, 2, 5, 3, -10, -19, -6, -26, -2, 24, 12, 4, -12, -4, 50, -15, -25, -5, 9, -26, 8, 37, 16, -12, 3, -53, 27, -25, -46, -3, -22, 31, 18, 78, 21, 2, -36, 9, 9, -3, 20, 48, 16, 15, 18, 3, 6, -9, -16, -1, 16, 43, 26, -57, 13, 25, -10, -27, -22, 16, -25, -24, -19, 10, 13, -7, -35, -30, -21, -4, -11, -1, -27, -47, 6, 18, 13, -26, -1, -6, -20, -10, 17, -12, 14, -9, -38, -15, 28, -7, -35, -7, -13, -14, -9, 12, 9, 30, 19, 8, 39, -18, 44, 52, 8, 8, 39, 45, -3, 0, 22, -1, -36, -55, -3, -43, 11, -38, -25, 14, -2, 39, 13, -6, 57, -33, 17, 8, -49, -26, -39, 6, 15, -16, 14, 1, -5, -22, 20, -33, 10, -30, 37, 18, -48, 17, 15, 14, 12, 58, -26, 4, -16, 19, -4, 1, -10, 38, 11, -45, 50, 4, 29, -1, -29, 5, -30, -2, -23, 29, -28, 20, 7, 0, 3, 44, -45, -50, 3, 9, 41, -37, -28, 37, 13, -50, 10, 3, 27, 8, 18, -9, 6, 7, -11, 17, -2, 32, -3, 21, -3, 24, 14, 41, -1, 3, -40, 29, -5, -31, 36, -13, -7, -2, 19, -38, 12, -10, -8, -23, -22, 17, 6, 26, -9, 26, -29, -32, 11, 21, 33, 7, 14, -23, -5, 28, 1, 20, -18, 6, 22, 10, -6, -27, 8, -50, -12, 37, -7, 15, 15, 24, 40, -20, -27, -6, 8, 17, -24, -34, -25, 28, 15, 1, 27, 30, 18, -4, 0, 14, -14, -18, -3, 49, -10, 43, -14, 58, -10, 48, 34, -11, 8, 39, 26, 25, -2, 13, 4, 40, 13, -20, 6, -6, 25, 42, 10, -29, 28, -47, -17, -6, 16, 27, 38, 28, 0, -35, 4, 55, -28, 49, 23, -15, 24, 12, 24, 15, 42, 6, -51, -56, -21, -17, 14, 7, -25, -9, -26, -9, -3, 0, 2, -52, -12, 0, -3, 23, 3, 76, 25, 8, 28, 34, 39, 14, -17, -22, 8, -53, -46, -22, 9, -2, -4, -32, 11, -13, 5, 23, 5, -21, -32, -13, -1, -17, -33, 21, -2, -16, -16, -46, -12, 10, -15, 3, 27, -6, -27, -42, 30, -19, -15, 17, -2, 19, 2, 19, -21, 53, 36, -4, -19, -27, 40, 23, 26, 6, 10, 36, 12, -70, 21, 8, 0, -26, -36, -16, 15, -15, -35, 35, -23, -31, 10, 10, 34, -19, -6, -3, -4, -2, 27, 1, 23, 2, -17, -22, 0, 0, -8, -8, -35, -9, 8, 23, -17, 56, 14, 14, 20, 0, 29, 17, 48, 6, 38, 34, 8, -8, 6, 4, 15, -17, 23, 18, -14, 1, 18, -19, 26, 23, -15, 5, -6, 28, 17, 46, 19, 23, -15, -28, -2, -23, 33, 27, -18, 14, 25, 11, 20, -7, 26, -74, -3, -4, 25, 15, -8, -11, -16, 31, 19, -13, -9, 23, -21, 13, -26, 46, 10, -17, 9, 11, -2, -2, -44, -28, 26, -15, 14, -46, 4, -6, -17, 22, -22, 46, -5, -32, -1, 31, 29, -7, 13, 67, -13, 28, -11, 16, -15, -1, -15, -29, 17, 14, -15, 21, 24, -4, 34, -17, -40, 6, 9, 35, 17, -12, -12, 1, 24, 15, 4, 15, -31, -15, -11, 3, -4, 3, 16, -67, -33, 1, 4, 51, 4, 0, 9, -3, 13, -20, -29, 41, 18, 48, -41, 0, 17, 9, -11, -23, 21, -11, -29, 15, -16, 2, 1, -11, -14, -16, 50, -5, 14, 3, -31, 39, 31, -23, -4, -21, -24, 10, 35, 9, -14, -22, -11, 29, 30, -19, -4, -36, -22, 32, 11, 1, 18, 11, 20, 18, -17, 19, -9, 20, 24, -32, 1, 29, 39, -24, 47, -34, 1, -10, -2, 7, 12, -10, 21, -16, -17, 2, 0, -24, 7, -18, -4, 35, -25, 7, 21, -1, 3, 16, 24, 13, 6, -45, 18, -23, 17, 19, 16, -15, -16, 18, 7, -30, 19, 28, -44, -21, 26, 22, 1, -44, -36, 35, 22, 27, -22, 22, 7, 5, -38, -7, 29, 15, 10, -4, -6, -8, 21, -5, 16, 37, -12, 2, -19, -24, 10, 2, -17, -28, 12, 7, -3, -6, -60, 0, -1, -20, -14, -31, -30, -4, 1, 4, 14, 12, 0, 49, -5, -6, -10, 12, 37, 5, -19, 15, -37, -9, 2, -15, -9, -9, -12, -2, -25, 8, -7, 12, 27, 16, 4, -6, -18, 6, -16, -28, 46, -4, 10, 3, 10, -50, -45, 12, -9, 31, -17, -2, -12, -14, -10, -10, 16, 21, 10, 15, -6, -1, 24, -27, 42, -24, 3, 39, -59, 19, -10, 31, -31, -7, 38, -35, 21, -26, 32, 6, 4, -17, 12, -31, -28, 3, -4, -57, 28, -3, -15, -19, -10, -34, 12, 41, 9, 59, -2, -26, 0, 7, 42, -15, 7, 8, -32, -26, -34, -25, -10, 22, 11, 23, -17, -18, 5, -25, 34, -11, -21, 4, -21, 6, 8, -40, -36, 21, 40, 32, 9, 39, 29, 33, -14, -40, -20, 14, 24, -42, 11, -19, -16, -15, -26, 29, 22, -31, 29, -13, 15, -40, -6, 13, 46, -56, -21, -12, 9, 43, -14, 3, 31, 1, 46, 51, 15, 34, 0, 22, 6, 40, 28, 9, -35, -27, 26, 3, -9, 5, 20, -5, 18, -41, -15, -15, 31, 12, -9, 21, 33, -26, -2, -18, -10, 3, 44, -25, -32, -22, -9, -34, -2, -35, 10, -8, 2, 7, 4, -4, 37, 26, 10, 21, 4, -5, -18, 10, 1, -7, 9, 49, -22, -11, 26, -5, 24, 19, -10, 28, 11, 39, 11, -11, -11, 31, -13, 32, -18, -18, -18, 9, 4, -23, -8, -25, 23, 14, 14, -5, 21, -17, -12, -7, -12, 31, 11, 29, 14, -3, 0, -1, 26, 18, 19, 10, 34, -5, 1, 6, 4, 2, 23, -20, 5, 11, 19, 27, 27, 21, -2, -10, -14, -16, 1, -31, -34, -27, -44, 19, 28, -5, -5, -7, 2, -2, 25, 14, 17, -2, -11, -7, -6, 32, 12, -7, 11, -23, 5, 7, -20, -23, -28, -18, 1, 3, 19, 17, -3, 21, -7, -21, 2, 6, -14, 14, -14, 0, 11, 13, -15, 8, -48, 33, 16, -4, 0, -8, 1, 2, 13, 7, 15, -25, -23, 13, 37, -9, 28, 3, -30, 20, -17, -30, -14, -28, 18, -28, -25, -11, 21, -14, -16, -26, -44, 22, -44, 16, 25, 41, -37, -6, 14, -13, 30, 28, 14, -20, -11, -30, -46, -44, -27, -3, -17, 28, -4, 32, -33, 38, -9, -14, -24, -37, 33, -9, -20, -5, -36, -31, 33, -16, 28, -40, 23, -9, 56, 23, -9, 34, -23, 7, 10, 22, -14, -19, -19, -44, 25, -44, 26, 18, -15, -29, 3, -43, -11, 4, 15, 3, 21, -32, -56, 1, -12, 30, -20, 8, 20, -30, -17, 40, 21, -30, -8, 35, -17, -36, 35, 43, -7, -20, -6, 8, 16, 11, -9, 8, 61, -6, -22, 40, 7, 8, -43, 10, 14, -5, 11, 20, 22, -7, -33, -24, -40, -3, 13, -7, -5, -1, -23, -5, -42, 40, 14, -18, 0, -21, -21, 2, -12, 15, -14, 9, 44, -15, 24, 7, -30, 14, -8, -15, -7, 0, 7, -4, 11, -9, 11, 8, -11, -15, 5, 34, 40, 6, 20, 9, 37, -23, -9, -14, -23, -10, 29, -4, 24, -11, 26, -6, 11, 3, -1, 9, -19, -31, 34, -8, 0, 21, -14, -13, -1, 48, -34, 21, 22, -29, 6, 30, -29, -19, 14, 8, -26, 9, 7, -5, 9, 6, -18, 46, 35, 6, -11, 13, -5, -27, 5, 29, 17, 23, -23, 34, 42, 15, -9, -29, 16, 8, 28, -1, 7, -24, 15, 14, 23, 12, -4, -18, -7, -5, -17, 25, -35, -23, -6, -24, -36, -5, -34, 7, 13, -28, 20, -26, -5, -10, 0, 9, 3, -20, -17, -7, -49, -12, 25, -18, 17, 10, -21, -18, -42, 12, -4, -32, -16, 55, -23, -38, -42, -20, -15, 2, 39, 17, -20, 49, -36, 5, 45, -36, 23, -18, 23, -40, 6, 11, 4, 12, 3, -6, 28, -14, 5, -39, -23, -26, 12, -27, -11, -7, -29, 6, -11, -26, -4, 0, -44, -6, 29, 29, 14, 8, 19, 16, -11, -56, -7, -6, -20, 44, -12, -12, 21, -6, 20, -18, 45, 6, -15, 20, -25, -18, -13, -21, -20, -6, -33, -14, -50, -51, 29, -3, -3, 21, -16, 20, 3, 19, -20, -32, -2, 14, -30, -23, 19, 10, 19, 17, 22, 36, 5, 32, 11, -54, 15, 1, 31, -9, 12, -17, 38, -24, -14, 38, -18, 5, -9, 24, -30, 9, 40, -13, 7, -29, -15, 3, 2, 5, 21, -14, 12, 17, -7, 14, 13, 42, -13, 6, 44, 17, -15, 10, 37, 12, 8, -46, -12, 29, -15, -12, -9, -42, 0, -10, -15, -39, -34, 35, 8, -26, 13, 9, -24, 12, 33, 46, 10, 17, -28, 27, 10, 22, -24, -40, 45, 65, -13, 8, 30, 24, 17, 38, 4, 33, -27, 13, 5, -47, -16, 19, -17, 25, -17, -2, -17, 12, 61, 5, -33, -43, 40, -18, -29, 9, 10, 34, -15, -13, -26, -6, -1, 7, -1, 4, 53, -25, 3, 17, -19, 46, 16, -26, 23, 43, 29, 7, -12, -4, 32, 7, -44, -4, 8, -21, -27, -19, 23, 31, 31, 68, -18, 13, 8, -37, -12, 10, -14, 13, -4, 22, 13, 3, 11, -9, -2, 24, -19, 24, -40, 7, -8, 22, 18, 35, 17, -30, -36, -9, -4, 57, 26, -16, 14, -5, 36, 18, -23, 9, 29, 17, -36, 24, -13, 37, 22, -3, 18, 57, -11, -16, -6, -4, 22, -11, -22, 35, -25, -6, 23, -12, -12, 12, -5, -27, 31, 20, -2, -36, 7, 48, -33, 21, 11, 12, -8, 36, 13, -11, 27, 20, -11, 49, 38, -19, 16, -9, 24, 5, -42, -12, 38, 17, -21, 24, -37, -6, 2, -14, -2, 26, 31, -11, -4, 32, 12, -17, -6, -2, -11, 23, 18, -49, 65, -24, -13, 11, -40, 68, 2, 6, 29, 45, 15, 10, 25, 13, 12, 27, -38, 30, -35, -9, -6, -37, 36, -29, -8, -2, 5, -18, -13, 77, -14, 38, 12, -2, -4, 4, 6, -22, -1, 40, 10, 19, 27, -37, -1, -11, -25, -48, 14, -49, -21, 17, -27, 39, 57, 32, 18, 6, -18, 8, 10, 53, 23, 51, 32, -50, -14, -46, -52, -24, -26, 7, -15, -31, -9, -8, 26, 37, -61, -9, -19, 34, 6, 5, -21, 12, -36, 20, 6, 3, -11, -3, -13, -17, -5, 39, -3, -81, -44, 38, 30, 1, 23, 8, -13, 30, -4, -55, -12, 2, 26, 1, 52, -3, -36, 13, -15, 23, -34, -6, 6, 2, 47, -35, -40, 22, -43, 20, -5, -37, 23, 47, -3, -32, 35, -7, 25, -16, 23, -14, 0, 4, 16, 6, -41, -40, -33, 23, 35, -6, -67, -10, -29, 36, -16, 21, 21, -8, -52, 2, 11, 35, -5, -19, -1, 17, -39, 26, -39, 10, -20, 20, -16, 2, -8, -18, -8, -32, 12, 30, 4, -7, 22, -4, -40, 5, 32, 31, 28, 20, 6, -22, 24, 18, 1, 8, -25, -18, -20, -60, -30, 1, 18, -22, -14, -30, -24, -10, -1, 55, 46, -33, -15, -20, -7, -11, -17, -35, -29, 35, -25, 0, 2, -2, 1, -24, -5, 7, 3, 0, 28, 16, -36, -23, -24, 8, -13, -26, 14, -12, 14, 22, -8, -34, -11, 21, -8, 31, 7, -12, 21, -41, 6, -23, -35, -24, -12, 15, 36, -37, 0, 31, 18, 23, 29, -21, -29, 15, 22, -20, -58, 22, -48, 12, -42, 31, 12, 27, 38, 39, 28, -23, -37, 27, 0, -10, -14, -9, -10, 12, 0, -5, 11, -4, -4, -23, 4, -26, -22, 18, 17, -25, -6, 11, -2, 18, 51, 6, -2, 59, 3, -18, 5, -17, 10, 10, 31, -6, -27, 22, 23, -64, -22, 24, 25, 0, 34, 17, 19, 36, 34, -63, -38, 3, -14, -1, -30, 40, 47, -40, -40, 28, 28, -1, 0, -51, -27, 16, 5, 7, -35, 21, -9, -39, -15, -50, 1, -32, 8, -13, 27, -15, -18, -53, -23, -49, -13, 14, -22, 36, -33, -14, -15, -6, -2, 2, -14, 16, -16, -12, -26, 20, 38, -6, -65, -20, -28, -13, -20, 19, -27, -7, -27, -34, -30, 17, 2, -32, -13, -24, -17, 2, -32, -26, 6, 21, 25, 11, -30, 8, -16, 18, 20, -30, -16, 24, 10, 3, 26, -22, 29, -12, -20, 22, -18, 0, 43, 28, -21, -10, 19, -7, 7, -13, -2, -1, 6, -1, 2, -31, -3, 1, -20, -14, -14, 0, -41, 28, -13, 8, -17, 7, -37, 14, 30, -12, -6, -12, -14, 21, 36, -20, -10, 29, 17, 3, 35, 3, 12, -15, 2, -24, -26, 29, -10, -18, 1, -8, 40, 13, 4, -12, -16, 8, 16, 26, -14, 9, 11, -14, 1, -40, -42, -33, 2, 4, 0, 18, 46, 14, -11, 50, -11, 4, 2, 39, -46, -4, 42, -37, -33, 4, -34, -18, 6, 10, 20, 6, -21, -8, 15, -13, -33, 22, -35, 5, -37, -48, -3, 29, 24, 27, 29, -7, -12, -18, 13, -13, 21, 8, -4, 12, 28, 12, 11, 2, 10, 3, -21, 23, 6, -55, -14, 18, -35, -18, 41, -1, -12, 52, -2, 14, -5, -2, 36, -15, -53, 39, -17, 27, -47, -26, 20, -31, 0, 23, -63, 3, 21, -11, -29, 5, 13, 6, -17, 34, -23, 14, 51, 7, -9, 11, 29, -25, 24, 9, 15, 6, -27, -38, 30, 12, 18, 15, -18, 8, 6, -29, -3, 39, -38, -27, -21, 52, -14, -27, -1, 4, -7, -26, -59, 45, -65, -10, -36, -31, -32, -11, 1, 32, 25, 5, 28, -47, 11, -19, 12, -39, 49, 6, -13, 36, 16, 81, -5, 4, -48, -13, 17, 7, -24, 35, 14, -4, 37, 6, 29, 16, -38, 37, 18, -26, 8, -18, -28, 31, -11, -12, -34, 12, -1, -43, 17, -3, 4, -50, -28, 31, -34, -20, -61, 1, -16, -18, -20, 11, 25, 23, 17, 13, -3, 17, 2, 3, -22, 0, 5, -32, -15, 26, -3, 14, 5, 2, -16, -59, 0, 14, -1, 37, -1, -23, 6, -6, 47, -28, -42, -17, 0, -8, 2, 12, 5, 13, 6, 14, 0, -20, 19, 12, -8, 28, -30, -3, 46, 9, 3, -39, 22, -14, 8, -15, -44, -2, -25, -4, -28, -49, 5, -24, 20, 54, 1, -9, -29, -25, -9, -5, 24, -33, 24, 29, -5, 5, 27, 14, -3, -34, -24, -2, 6, 30, 56, 8, 8, 27, -7, -41, -28, 3, -29, -16, 18, 23, -13, -9, 1, 5, -6, -14, -25, -7, -29, 41, -22, -9, -27, -12, 18, 7, 13, -22, 29, -12, 17, -20, 27, -6, 19, 5, 15, -19, 11, -9, -25, 2, -30, -55, -13, 35, 6, 2, -11, 27, 9, 19, 11, 51, -6, -26, 24, 27, -10, -22, 30, 5, 22, 19, -47, 7, -15, -4, 6, 35, 16, 12, 8, -12, 30, 33, 7, -28, -28, 12, -16, -5, 36, -21, 0, -29, -52, -7, -37, -6, -10, -16, -11, -20, -7, 51, -3, 8, 26, -6, -5, -11, 5, 15, 25, -10, 10, -8, -18, -4, 26, 26, -28, -19, -15, 31, 17, 34, 7, -22, 43, -16, -28, 10, 28, 21, -3, 0, 39, 25, 29, 32, -28, -17, -24, -1, -10, -25, 6, 1, -3, 15, 26, 36, 22, -1, -9, -14, 17, 57, 13, -17, -30, 63, 17, -22, -14, 41, -33, 16, 11, -8, -43, -9, 15, 18, 8, -18, 41, -5, -25, -31, -3, 11, 9, -24, 12, -5, -29, 7, 11, 19, 16, -4, 30, 8, 24, -20, -15, -6, -5, 33, -8, -12, 28, -25, 8, 7, 37, 1, 16, 3, -32, -23, 28, 57, -1, -42, -19, -1, -16, -37, 9, -47, 12, -26, -24, -10, -31, -9, -28, -8, -42, -47, 17, 2, -41, -28, -9, 25, -31, -7, 4, 9, 30, -28, -10, -40, -38, 8, -20, 26, -38, 25, -11, -37, -34, 8, 4, -12, 0, -57, -32, -18, 17, -43, -15, 13, -54, 32, 17, -25, -48, 12, -27, 7, 9, 51, 31, -4, -12, -6, 32, -5, -18, 5, 7, 15, 1, 5, -20, 32, 10, -47, -18, 15, 45, -33, 35, 49, -17, 36, -15, -41, 47, 21, -8, 20, -9, -64, -30, -18, -12, -43, 33, 5, -29, -12, -17, 13, -27, 6, -59, 22, -19, 26, 14, -29, 0, 16, -37, -5, -2, -36, -31, 31, -6, -45, -34, -10, -2, 20, 3, 29, 46, -25, 4, 22, 30, 59, 36, -10, 17, -25, -12, 13, 11, -3, 0, 41, -12, 32, 46, 13, -35, 5, 51, 14, -17, -19, 5, 25, -20, 22, 23, -5, -23, 10, -37, -20, -15, 36, -36, 78, 16, -7, -13, -28, 45, 30, -33, 27, 9, 4, -16, 30, 34, 6, -1, 17, 23, 3, -13, -3, -4, 11, 2, -1, 19, -8, 27, 16, 37, 25, 8, 4, 13, -6, 9, -15, 15, 32, 35, -17, -29, 33, -2, -1, 1, -16, 5, 8, 11, -18, 28, -26, 28, 41, 2, -12, 21, 9, 13, -9, -3, -24, -8, -4, -29, -15, 3, 34, 19, -18, 16, -20, -11, 7, -21, 34, 33, 27, 11, -9, 2, 4, -31, -10, -31, 29, 10, -31, 45, -2, 24, -15, -16, 23, -4, -42, 9, -15, 29, -12, -26, 13, 21, 14, -37, -51, -2, -48, -5, 5, 24, 19, 30, 12, 30, 37, -3, -8, -24, -16, -32, -40, -10, -1, 15, 73, 29, 4, -11, 35, -26, -33, 2, 15, 3, 14, 17, 38, 15, -10, -32, -29, 1, -17, 14, -19, 18, 41, -14, -15, -1, 17, 17, -96, 27, 40, -14, -5, 38, -11, -27, 47, -14, -20, -2, -2, -5, 4, -6, -11, -1, 39, -8, -16, 25, 27, 13, -35, 12, -17, -30, 2, 22, -10, 19, -21, 20, 7, -23, -14, -3, 18, -13, -27, 0, 17, 36, -17, -30, -11, -1, -42, -36, -6, 35, -7, 39, 3, -28, -40, -23, 46, 0, 21, -8, -19, 35, -12, -11, 28, -17, 21, -9, -15, 8, -19, 42, -2, 2, 14, 34, -31, -29, 15, 29, 7, -2, 5, -14, 3, 1, 25, 49, -24, 36, -20, 12, -25, 10, -19, 6, -27, -23, -24, 11, -5, -10, -3, 14, 35, 23, 13, 30, -13, -23, -3, 7, -12, -27, -7, -10, -13, -20, 15, -35, 33, 7, -8, -15, 34, -1, 9, -13, -16, -12, -3, -10, -6, 9, 25, 28, -34, -1, -1, 20, -17, 21, -42, 21, 22, -16, 12, 19, -14, 19, -12, 29, -25, 3, 20, -15, -29, -27, 16, 17, -36, -1, -16, 12, 28, -25, 1, 23, -26, 33, 29, 24, 18, 33, 2, -17, -15, 2, -4, -9, -12, -5, 6, 5, 35, -38, -24, 11, -30, 9, 22, 44, -11, -50, 8, -9, -22, 19, -15, -16, -36, -18, 28, 22, -21, -7, 31, 27, -6, -24, -9, 23, -4, 18, -46, -36, -35, -7, 4, 23, 43, 19, -16, -9, -4, 33, -9, -10, 11, -18, -27, 4, -18, 9, -27, 37, 22, -36, -12, 31, 9, -17, 22, 2, -27, 11, 36, -33, -35, 20, 3, 9, -44, 17, 6, -30, 8, -1, 20, -30, 6, -8, 1, 16, -33, -19, -26, -12, 46, 12, 36, 60, -40, -23, 0, 26, -10, 13, 9, 0, -43, 0, -35, -3, 18, 19, -13, 4, -32, 19, 13, -29, 48, 7, 29, -14, -32, -30, 7, 0, 7, -33, 4, -1, 1, 9, -12, 22, 32, 18, 3, -27, -13, 19, -3, -35, 46, 22, -24, 12, 3, -37, -32, 17, -13, -33, -26, -4, -38, 21, -23, -2, -38, 9, -43, 21, 4, -42, 14, 23, -1, 3, -5, 17, -18, -1, -4, 20, -3, -4, -12, -4, 1, 3, -18, 39, -15, 4, 9, -30, 21, 13, 2, -6, 5, 2, 22, 16, -2, -5, -43, 1, 26, 35, -18, 22, 5, -35, -34, 14, -7, 3, 7, 20, -6, -6, 7, 19, -33, 26, -3, 30, 5, -3, -2, 20, -63, 23, -26, 0, 17, -58, 10, -39, -4, -44, -8, -9, -30, 45, 20, -17, -4, -19, 0, 7, -32, 20, -5, -34, -10, 11, -18, 3, 1, -17, 24, 10, 17, 0, 4, 13, 4, -23, 47, 34, 27, 11, 29, -7, -33, -2, 11, -2, -43, -2, 23, 64, -32, -52, -16, -30, -12, 1, -44, 17, 3, 21, 31, 7, -20, -28, -25, 11, -27, 45, 16, -8, 33, 17, -10, -45, 5, 10, 17, 28, 7, 10, 15, 40, -8, 6, 15, -17, -1, 28, -2, 10, -19, 27, -17, 18, -41, 9, 14, 30, -22, 7, 32, 20, -3, -27, 21, 46, 3, -25, 13, 37, -24, -69, -5, 6, -3, -33, -7, -11, -1, 33, 10, -27, -15, -17, 5, -5, 0, -9, -15, 58, 4, 31, -13, -8, -5, 31, 9, -4, 14, -36, 33, -23, 5, 8, -49, 9, 19, 47, 23, 5, -5, -3, -33, 0, 7, 18, 6, 14, -3, -8, -17, -35, 42, -21, -16, -23, 9, 26, -13, -3, -42, 24, 2, 18, 17, -27, -15, 39, 7, 27, 17, 6, -10, 56, -18, 2, 18, 24, -22, -31, -41, 33, -8, 16, -9, -11, -31, -80, -28, -15, -2, -5, 52, -13, -4, 35, 14, -40, -6, 5, 3, 6, -53, -1, 7, 19, 18, 17, -40, 11, 14, -10, -9, -38, 22, 10, -11, -37, 15, -17, 23, -32, -44, -16, -18, 15, -27, 33, 16, 11, 1, 33, 8, -5, -23, -36, -16, -40, 17, -21, -18, 33, 16, 2, 8, 4, 18, -22, 26, 9, 13, -40, -40, -15, 26, -10, 24, -3, 20, 26, 18, 43, -10, 42, -7, 19, 9, 5, -21, -1, 9, 1, 18, 38, 22, 45, 18, 38, -4, 27, 26, -9, 5, -11, -12, 33, -41, 17, -19, -13, 10, 9, -20, 6, 10, -22, 15, 5, 3, -29, 15, -22, 25, -16, 2, 34, -29, 10, -17, 21, 1, 11, 4, -12, -40, -9, 12, -7, -2, -9, 7, 34, -35, -22, 25, 13, 20, 42, 2, -2, -28, -9, 15, -26, -9, -36, 27, -13, -5, 40, -7, -36, 10, -42, 38, 23, -28, -22, 51, -13, 1, 10, -44, 11, 22, 27, 5, -15, 27, -14, 19, 50, 34, -33, 37, 5, 5, 48, -22, 19, -7, -5, -1, 26, -6, 30, -44, 3, 7, -19, -24, -16, -3, 0, 14, -58, -11, 2, 6, -42, 23, -7, 4, -38, 18, 7, 30, 34, -45, -21, 17, 4, 28, -9, -19, 3, 6, 10, -16, 47, -19, -42, 18, 40, -9, 23, 19, 26, -35, 37, 13, 10, -32, -9, 7, 46, 15, -29, -12, 3, 20, -4, -13, -27, 3, 22, 17, 13, 11, -34, 12, 25, 26, 51, -21, -21, -36, 11, 23, 0, -4, 17, 15, -42, 24, -4, -35, 6, -20, -24, 23, -20, -9, 41, 28, -1, 18, 13, 12, -55, 23, -41, 21, -23, 0, 35, 0, -3, 19, 11, -4, 1, 29, -48, -10, -3, 2, 0, -3, 13, -11, 26, 5, -48, 9, 0, 21, 12, -20, 40, -18, -9, 43, -31, -23, -22, -11, 14, 11, -10, 29, -15, 1, -2, 28, 9, 17, -31, -24, -31, 1, -5, 14, -9, -36, 19, -26, -14, -45, -13, 24, 9, 6, 17, 10, 14, 2, -24, -3, -8, 10, 16, 19, -28, 9, 43, -27, -10, -5, -17, 0, 6, 24, 32, -30, 24, 0, 33, -15, -38, 17, 13, 19, 39, 27, 1, 0, 21, -70, 13, -31, -15, 13, -13, 25, -45, 24, -3, -42, -11, 39, 14, 3, -17, -7, -6, 19, 3, -55, 22, 0, 8, 33, 13, -36, 12, 18, 0, 20, -10, 14, 31, -20, 12, 26, -18, -19, -22, 7, -8, -22, 28, -1, -15, -31, -19, -3, 26, 24, 32, -57, -26, -11, 18, 32, -25, -2, -26, -41, -8, 46, 7, -18, -47, 2, 3, -32, -31, -34, -38, 41, -4, 8, 4, 56, -9, 10, 7, 5, -25, -21, -8, 17, 12, 26, 3, -1, -5, 11, -11, 35, 15, 13, -26, -17, 7, 14, 18, -25, -29, 27, -12, 5, -26, 17, -14, -10, 33, 24, 22, 11, 30, 3, -5, 7, 14, -25, -3, 4, -13, 7, -24, 3, -7, 22, 4, -19, 29, -4, -22, 11, 34, -3, -13, 16, -9, 26, -26, -5, -41, 17, 1, -4, -18, 35, -4, -10, 44, -3, -34, -11, -43, -15, 16, 7, 1, -32, -2, 17, -20, 8, -11, 32, -11, 0, 13, -25, -33, -30, 31, -1, 5, 5, -55, -15, -22, 21, 8, -16, 21, 34, -30, -7, 33, -3, -36, -24, -37, -10, -2, 20, 6, 14, -27, 0, -1, -5, 19, -5, -22, 23, 23, 10, -35, -29, -12, -21, 9, 6, -9, 2, 15, 6, 44, -17, 37, -1, 9, -26, -4, 22, -30, 6, 30, -11, 9, -17, 17, -3, -38, -4, -7, -9, -2, -18, 4, -24, -14, -27, 3, 36, 14, -14, 18, -30, 29, 18, 13, -38, 2, 79, 7, 35, 18, 54, -6, -29, 17, 12, 62, -12, -20, 6, 4, -3, 16, 10, 13, 32, 13, -12, -8, 11, 12, 48, -55, 30, -19, -31, 32, 14, 24, 30, -43, 4, -29, -4, -5, 25, -16, -44, -10, -5, 0, 39, -30, 9, 33, -3, 33, -32, 40, -30, -40, -54, -50, 10, 3, 9, -3, 19, -17, -3, 38, -6, 1, 33, -30, 12, 26, -21, 3, 7, -28, -41, 5, 14, 21, -39, 62, 17, 2, -4, 10, 65, 23, -52, -26, -48, 12, -1, 7, -24, -24, 1, -29, -5, 31, -2, 24, -11, -20, -52, -11, 20, -4, 17, 38, 4, -26, -12, 57, 25, 6, -11, -27, 2, -6, 5, -31, 28, -34, -49, 23, 12, 20, -20, 55, 58, -13, 5, 10, 18, -32, -31, 5, 5, 55, 14, -34, -9, -26, 45, -24, -54, 11, -6, 21, -23, 3, 35, 31, -26, -34, 21, -22, -33, -13, -44, 19, 13, 35, 15, -24, -13, -18, 24, -18, -25, 46, 12, 35, 20, -1, 8, 7, 63, 12, 1, -15, 10, -2, 9, 18, -7, 11, -19, -46, -35, 4, 37, -6, -51, -71, -47, 24, 28, 63, -3, 22, -16, 31, -23, -1, -26, 26, -29, 39, 31, 9, -21, 15, 1, 49, -25, 10, 12, 4, -51, 3, -6, -28, 29, 34, -8, -19, 28, 1, -17, 25, -33, -33, 6, -28, 7, 28, 22, 12, -30, -41, 29, 1, -4, 1, 7, -9, 26, 15, -33, 1, -13, 10, 6, 6, 24, 26, -32, 39, 36, 31, 36, -38, -33, 44, 12, 10, 20, 27, -31, -29, 9, 24, -25, -19, 41, -11, 24, 19, 3, -56, 26, -15, 5, -6, 6, -7, 14, -50, 2, -21, 7, 47, 16, -7, 3, -9, -23, 25, 1, 9, 1, -4, 23, 3, -21, 26, 79, 36, 6, 22, -1, 33, 4, -3, -31, -33, 43, -33, 38, 20, 20, 11, 23, -44, 23, -17, -8, -22, 6, 10, 18, 9, -23, -26, 39, -85, 14, 21, 6, 41, -19, -5, 25, 2, -28, 0, 1, 20, -22, 14, -7, 3, -16, 43, -2, -30, -14, -36, -7, 32, -32, 65, -29, -22, 25, 24, -1, 3, 35, 31, 10, -24, 9, -33, -14, 30, 4, 12, 48, 15, 3, -24, 8, -56, 35, 5, -22, 32, 6, -4, -15, -29, -65, -23, -12, -17, -7, -7, -8, -26, 12, -13, 3, 4, -13, -32, -19, -23, 1, 67, 23, 26, -1, 12, 3, -38, -14, 63, 34, 26, -63, -36, 11, 18, 26, -37, -19, -44, 55, -5, -20, 6, 37, -12, 16, -14, 5, 0, -21, 64, -7, 17, -27, -55, -35, -13, -33, 3, -31, 16, 33, 28, -12, -23, 23, -24, 14, -3, 37, 30, -7, 4, 11, 13, -31, -15, 15, 5, -9, -58, -23, -26, 0, 15, -4, -8, 13, -5, -31, -19, 28, -21, -25, -30, 21, -5, 36, 27, -16, -19, -56, -17, -12, 18, -3, -6, -3, -13, 36, -10, 17, 11, -45, -44, 22, -16, 44, -11, 14, 34, 34, 28, -12, 20, 9, 59, 5, -25, -16, -19, -21, -39, -13, 12, 2, 7, -48, 8, -15, -32, -46, -20, -24, 5, 12, 36, -24, -15, -18, 9, -19, 5, 22, 5, -44, 4, 36, 11, 53, 25, -13, 21, -26, 25, -13, 2, -2, 45, 22, -30, -32, 35, 13, -5, -7, 18, 9, 15, 54, -21, 26, -10, 20, 12, 8, 23, 28, -16, -12, -3, 25, -14, -19, -16, -40, 1, -2, -16, 5, -20, 37, -5, -7, -17, 26, -20, 7, -50, 15, -14, 2, -10, 4, -2, -18, 28, 44, -7, -11, 51, -24, 4, -19, -1, -5, -32, 32, -26, -22, -21, 25, -14, -25, 50, 30, 0, -15, -8, 23, -21, -41, 17, -21, 12, -17, 16, -8, 15, 34, -35, -8, -35, 12, 34, 6, -4, 16, -15, 10, 2, -2, 15, 2, 16, 13, 38, -78, 15, -39, -48, -27, 7, 6, 18, 56, -49, -54, -49, 40, 42, 8, 37, 10, -5, 41, 28, -16, -1, -20, 13, 9, 13, -6, 7, -18, -32, 14, -37, 6, -9, 26, 12, 14, -23, -24, 2, 4, -20, 22, -46, 11, 0, 3, 3, 28, 12, 10, 12, -15, -19, -21, 5, 9, -3, -19, -5, 18, 12, 47, 17, -52, -45, -25, -19, 49, 0, -7, -19, -20, -31, 23, -21, -6, 17, -28, -40, 25, 2, 35, 6, -14, 0, 20, 30, 22, 7, 13, 33, 36, -12, 17, -4, 19, 16, 5, -35, 6, 29, 28, 48, -16, 0, -47, -49, -9, 0, -9, 9, -10, -8, -2, -3, -11, -11, 3, -7, 1, -7, 19, -22, -22, 1, -21, 30, -2, 20, -20, 17, 0, -21, 1, 24, -15, -26, -18, 8, 26, -24, -6, 2, -31, 23, 0, 5, -7, 23, 22, 30, 5, 3, 1, -57, -20, -21, -25, 7, -27, -7, 22, -5, 1, 15, -4, 19, -36, -23, -48, -4, 17, 27, 18, 15, -13, -11, 29, 5, -9, -27, -35, 10, 3, -11, -3, -8, 18, -3, 7, -11, -29, 34, 27, 4, 9, 2, 2, -2, 13, -11, -17, 12, 16, -17, -4, 2, 16, 13, 6, 17, 10, -33, -1, -21, 48, -50, -14, -21, 10, 16, 0, 35, 35, -16, -6, -15, 16, 39, 6, -13, 44, 36, 24, 30, 37, -15, 14, 34, 8, -16, 5, 40, 22, 10, 14, 19, 26, -12, -17, -13, 30, -6, 37, -35, -10, 14, 8, -38, 14, -4, -9, -29, 48, 13, 48, 26, -17, 33, -14, 27, -47, -16, -7, -9, -34, -5, -5, 32, 34, -19, -20, 4, 25, 36, -2, -70, 2, 21, -9, 29, -45, -10, 62, -20, -21, -58, -8, -39, 16, 42, -4, -20, -23, 24, 12, -26, -23, -36, -26, 15, -40, -13, -36, 0, 36, -24, 0, 13, 40, 19, 24, 45, 22, 14, 12, 9, -22, -18, 20, -21, -11, -22, -5, 10, 7, -5, 27, 16, -34, -11, 35, -11, 17, 32, 10, -17, 28, -26, 37, -18, -8, -12, -1, 9, -5, 24, 10, -30, -10, 26, 26, 22, -3, -22, 11, 8, 26, 5, 13, 18, -15, -9, 48, -1, -12, -3, -7, 17, 21, 22, -40, -23, 15, 2, 5, 30, -22, 16, -17, -21, -3, -27, -45, 2, -9, -29, 4, -30, -5, 50, -7, -28, -6, 40, -6, -25, 9, -8, 20, -4, -26, -16, 35, 16, -17, -13, 23, 28, 17, -8, 7, 2, -10, 0, -7, -31, 1, -11, -31, -24, -27, -41, -7, -8, -2, -22, 11, -23, 28, 33, -19, -22, 8, -35, 12, -37, 20, 37, -20, -28, 13, 33, 6, -15, 40, -1, -52, -29, -7, -24, 10, 18, 19, -19, -5, 4, 14, 35, 34, -5, 10, 62, 6, 30, 13, 23, -18, -34, 15, -19, -36, 28, 20, -7, 31, -12, 5, -12, 32, 3, 4, -13, -27, 27, -37, 20, -13, -3, 8, -19, 25, 16, 47, 29, -5, 10, -6, 7, -10, 19, -6, 21, -33, -22, 22, 4, -6, -11, 36, 39, -15, 61, 13, 8, -60, -25, 0, 2, 0, -3, 13, 10, -23, -10, -37, -9, -18, 0, -14, 8, -15, 4, 29, 26, 37, 8, 1, 3, -7, 22, -45, -18, 15, -14, -2, 30, -12, 2, -8, 3, -29, -21, -28, 12, 4, 1, -8, -21, -1, -11, -20, 67, -8, 0, -22, -14, 8, -49, -49, -16, -26, -8, 1, 32, -35, 14, 10, 11, 11, 25, 27, -15, 41, 1, 43, -4, -29, 28, -5, 7, -32, -4, -3, -13, -53, -19, 24, 20, 34, -35, 0, -2, -4, 21, 9, -24, 4, 13, -42, 19, -40, 28, -18, -27, 55, 7, 10, 19, -35, 16, 29, 18, -7, 12, 41, -34, 12, -23, 18, 33, 26, -6, 36, -6, -2, -28, 28, 38, -48, 8, 16, -3, 2, -12, 26, -14, 42, 3, -30, -19, -41, -12, -27, 1, -15, 30, 17, -16, 10, 3, 8, -5, 19, -4, -6, 3, -15, -79, -47, -9, 44, -45, 9, -23, 18, -4, -10, -5, 29, -23, 19, -7, 14, -3, 12, -13, -26, -33, -24, 0, 1, 46, -3, 1, 52, -37, 0, 12, -45, 12, -34, -11, 4, 35, 26, -2, -36, 27, 58, -6, -41, -19, 53, 17, 13, -69, 14, 31, -8, -32, -43, 26, -15, -4, -45, 39, 32, 9, -54, 37, -14, -23, -3, -16, 12, 7, -6, -20, -22, 36, -12, -13, 19, 12, 2, -4, -31, -25, -15, 8, -2, 9, 24, 26, 2, -26, -42, 10, 1, -42, 5, 38, -4, 15, -2, -21, -15, 21, 17, -3, 14, 42, 27, -38, 6, 8, -13, 28, -10, -6, -39, -1, -28, -37, -11, 19, -23, -3, -21, 26, 6, -19, 19, -7, -54, -25, -20, -35, 22, -10, -25, -2, 14, -33, -3, 36, 17, -26, 7, 11, -18, -29, 27, -6, 2, 7, -16, 30, 28, -11, -53, 25, -10, 0, 1, -11, -11, 16, -28, 19, -16, 18, -23, -10, 8, 3, -9, 5, 33, 12, -7, -4, -4, 0, -4, 0, 3, -14, -21, -12, 32, -7, 51, 3, 19, 26, -6, 33, -20, -73, -25, -16, 12, -20, 29, 0, 14, -27, -7, 4, -15, 42, 45, -8, -26, 12, 17, -25, -18, -22, 10, -10, -31, 57, 9, -28, 11, -11, 3, -21, 24, -18, -7, -19, 9, -7, 52, 2, -25, 23, 39, -1, 1, -30, -3, 24, 0, -38, -46, -21, 10, -3, -26, 3, -7, -37, -26, -2, -17, -21, -7, 17, -9, -38, -39, -20, 6, -10, 34, -25, 21, 54, -38, 22, 40, 24, 24, -12, -19, -20, -32, 12, 0, 34, 15, 0, 45, 4, 62, -46, 4, 21, 36, -29, 32, 29, 3, 18, -21, 1, -22, 54, -61, -28, 3, 30, -15, -6, -1, 38, 54, -18, 1, 20, 44, -16, -5, -30, -27, -12, 1, -19, 8, 1, 15, 11, -14, -45, 29, 35, -53, -2, -10, 12, 5, 0, 1, 34, 7, -12, 30, 15, -4, -3, 7, 26, -32, -39, 3, -41, -25, -7, -17, -19, 2, -35, -18, -13, 9, 3, -8, -31, 3, 1, 13, -38, 4, 19, -8, 11, 1, -22, -4, -22, -42, -9, 19, 27, 30, 22, 39, -1, 6, 24, -9, -1, -15, 12, -30, 8, -8, -38, 6, -3, -20, 1, -9, -2, 16, 21, -38, 2, -13, -9, 19, 18, 41, 11, 31, 13, 14, 1, 26, -38, 22, -32, -19, 2, 17, 1, 32, -10, 11, -23, -7, 34, -10, -49, 46, 50, -18, 4, -29, -7, 11, 9, 31, 21, -2, 4, 29, -41, -48, -33, 17, -1, -8, 13, -6, -25, -7, 28, -7, -34, 3, 26, -3, 8, 0, 42, -18, -37, -3, 16, -2, -3, -16, 14, 4, -16, -18, 27, -28, -17, -22, -41, 38, 8, 20, 3, -7, 6, 27, 15, -25, 24, 18, 12, -14, 9, -25, -12, 6, -15, 4, -7, 16, 14, -22, -15, -14, -23, -39, 24, -23, 1, 1, 15, 7, 26, -27, 20, 29, -22, 11, 27, 31, 3, 32, 31, -27, 19, 18, 33, 17, -16, 20, 19, -61, -24, 17, 12, 10, -34, 40, 5, 39, -2, 28, -16, -3, 19, -13, 30, 17, 36, -33, 3, 20, 3, 7, -38, -17, 26, 17, 7, -8, -2, -18, -16, 32, 32, -10, 12, 34, 24, 32, 35, -2, 38, -27, -9, -16, -4, -27, 20, -35, -11, 13, -3, -20, 0, 2, -23, 30, -3, -15, -8, 14, -9, 47, 5, -28, -38, -12, -35, -10, -13, -23, 0, -20, -22, 38, 14, 6, -39, -20, 27, 23, -1, 4, -35, -11, -11, -14, -27, 16, 3, 7, -16, 7, 34, 15, 30, 1, 21, 49, -1, -3, -49, 37, -10, -4, -14, -8, 11, 7, 36, 20, 13, -48, -34, 15, -14, -62, -14, 8, -14, -30, 12, -25, 5, 34, -26, -9, -1, 16, -6, -45, -43, 5, -16, 14, -12, 9, -24, 34, -46, -25, 42, -17, 15, -43, -18, 10, 35, 10, -38, 38, 12, -1, -40, 1, 34, 10, -2, 16, -2, -33, -11, -10, 27, -18, 26, 21, 3, -18, 22, -21, -13, -1, 1, 17, 14, -18, -34, -31, -26, -47, 27, 0, 25, -27, -3, 8, -30, -12, -5, -10, 2, -39, -24, 49, 48, 4, -33, 54, -6, -5, 41, -41, 0, -34, 32, 15, -36, 7, -34, -5, 4, -7, 11, 10, 23, -3, -23, -12, 52, -6, 12, 11, 46, -6, 18, 20, 8, 23, 6, 16, -16, 14, -16, -42, -5, 1, 1, -6, 18, -65, 0, -10, 44, 17, 30, 20, -39, 27, 2, 11, 5, -20, -27, 16, 13, 13, -21, 9, -24, -14, -42, -16, -18, 19, 6, 29, 14, -14, 26, -19, 27, 6, 13, -13, 42, -5, -11, 42, -40, 11, 20, 14, -12, 0, 26, -67, 18, 0, -33, -15, -62, -12, -23, 38, -4, 6, 3, 42, 34, 3, 12, -14, 0, -27, -22, 16, -10, -3, -36, -26, 3, 17, 36, 28, -3, 42, 37, 6, 19, 5, 62, 15, 12, -15, -3, 45, -10, -34, 0, 22, -2, -38, -35, -43, 10, 29, -30, -32, -25, 4, -2, 28, 44, 23, -2, 0, 48, -9, -10, -14, -10, 23, -7, 41, 12, -2, -6, -38, -17, 16, 32, 4, -16, 37, 26, 9, 16, 31, 29, 32, -21, -8, -3, 41, 2, -25, 12, 12, 29, -33, 8, -33, 16, 6, 5, 0, 3, -13, -15, -7, -24, -32, 29, -10, -38, -11, 4, 13, -35, 6, 24, -10, -13, -27, 0, -11, 19, -20, 21, 12, 6, 0, 17, 11, 13, -5, 18, 33, 38, -11, -5, 0, 10, -15, 0, 19, 21, 5, -38, -8, -10, -4, 13, 16, 6, -9, -26, -6, -16, 15, -25, 42, 10, 34, 55, -29, 28, 8, 18, -11, 17, -1, 6, -4, -2, 0, 23, -6, -33, 12, 33, -33, -25, 28, -19, 2, 40, 12, 29, -25, -12, 28, 23, -35, 7, -14, -9, 21, 35, 38, 39, -35, -2, -30, -8, -16, 7, 7, -3, 4, -27, -38, 4, -22, 19, -31, -24, -1, -36, -3, -28, 14, 45, 49, 40, 30, 38, -24, -7, -23, 2, 0, 3, -31, 38, -2, -8, 0, -12, -11, 10, 8, 11, -46, 21, -30, -21, -2, 17, 39, -6, -5, 48, 18, 5, 15, 32, -8, -60, -8, -75, 34, 28, 24, 36, -14, -18, -1, 34, -15, 10, 67, 32, 33, -35, 42, -63, 7, -10, -23, -18, 1, -64, 34, -25, 61, -13, -12, 18, 14, -22, -40, 37, -7, 7, 24, -4, 38, -17, 21, -13, 24, -8, -11, 20, -23, -34, -13, -33, 8, 11, 39, 15, -5, -13, 14, 25, -29, 1, 44, 35, 27, 3, -11, -20, 8, -41, -16, 3, 38, -67, -1, 13, -3, 22, -41, 2, -20, -54, 18, 34, 8, 24, 14, 30, 49, -9, 11, -3, 6, -22, -14, -18, -27, 3, -32, 23, -6, -28, -18, 25, -33, -1, -30, -46, -23, -7, -7, 9, 20, 29, -1, 3, 31, -14, 3, 7, 27, -7, 7, -11, 35, 19, 10, 22, -31, 17, 13, 20, -10, 32, -5, 19, 44, 35, 15, 10, 1, -38, -3, 7, 9, -45, 21, -10, 18, 21, 20, -4, -51, 19, -15, -13, -2, 23, -30, 4, 69, 4, -5, 27, 33, -19, -10, -21, 24, -6, -30, -22, -6, 1, 14, 27, -73, -15, 65, 1, -3, 19, 9, 39, 27, 44, 22, 3, -25, -39, 7, 34, 27, -20, -9, -17, 11, -4, 19, -24, -45, 3, -36, -35, 35, -11, -30, 0, 53, -31, -33, 32, 30, 15, 2, 33, -21, 31, -1, 21, -12, 1, 12, -11, -41, 38, 39, -23, -28, -8, -30, 17, 52, -1, 39, -18, -18, -14, -1, 7, 18, -14, 13, 9, 25, -16, 2, 13, 15, -33, -24, -22, -8, 49, -4, -3, -23, -6, -10, -44, 16, 9, 30, -39, 11, -8, -22, -44, 0, -20, -37, 14, -4, -2, -11, -12, 47, 17, 35, -10, 11, 3, 17, 30, -56, -19, -9, 20, 4, 5, -14, 8, -6, -1, 25, -34, -13, 35, 11, -31, 8, 29, 2, 6, -32, -23, -3, -74, -16, -3, 1, -40, 13, -17, 20, -36, 12, -30, -11, 18, 36, -37, 10, 24, 39, 11, -4, -12, 2, 21, 68, -1, -11, -21, 19, 2, -10, -5, 19, 48, -22, -27, 55, -14, -6, -22, 11, 18, 18, 6, -19, -28, -25, 41, -37, -20, -4, -5, -3, -22, 3, 4, -6, -39, -11, -40, 14, 24, 50, -23, 49, 21, 39, 28, -33, -20, 7, 12, 7, -30, 0, 2, 12, 3, 37, -12, -3, -32, 10, -24, 15, -18, 4, 1, -44, 9, 21, -24, 14, -42, 20, -5, -18, -31, 28, 22, 1, -22, -15, 31, -10, 15, -14, -1, 27, 3, 21, -2, 32, -26, 9, 11, 20, -34, -7, 52, -41, -12, 1, 14, 16, 21, -6, -49, 4, -32, -18, 15, -3, 15, 16, -18, -10, -48, -25, 41, -21, 7, 6, 31, 12, -46, 19, 27, -9, -5, -35, -2, 15, 31, -38, 0, 30, 28, -14, -6, -40, -16, 29, -13, -24, 15, 33, -36, -6, -34, -30, 26, 2, -10, 12, 37, -3, 33, 9, 26, 43, -4, -13, -13, 33, 14, -5, 34, -8, -36, -35, -23, 0, -28, 38, 9, -5, 5, 0, -24, 7, 19, 31, 10, -11, -4, 11, 46, -8, 16, -5, -22, 28, 11, -6, -10, 5, 2, -21, -22, -9, 19, -12, 43, -4, 54, -35, 5, -12, -9, -8, 17, -29, 76, -18, 11, -3, -11, -15, -48, -4, -38, -7, 5, 38, 8, 23, -35, -6, -36, -6, 6, 12, 24, -20, 29, 10, 12, -37, 26, -19, 42, -24, 22, 30, 21, -23, 49, 52, 11, -5, -15, -14, 33, -12, 11, 28, -33, 7, 30, -1, 27, 22, 44, -10, 6, 7, 13, 1, 21, 8, 23, -26, -41, -30, -2, -20, -33, -18, -47, 24, 3, 0, -22, 16, -28, 51, -22, -38, -2, -15, 18, -7, 14, -17, 32, 24, 16, 46, -20, -1, -1, 7, -15, 17, -22, 61, -39, -27, 27, -18, 1, -10, -27, 38, -15, 13, 3, 26, -29, -21, -38, -47, -21, 0, 19, -19, 11, 14, 0, 26, 26, 26, 17, 15, -27, 7, 6, -1, -10, -2, -8, -6, 0, -15, 35, -6, 17, -21, -3, 5, -1, -62, 19, 49, 4, 5, 12, 22, -25, -16, 9, 2, 46, 12, 14, -4, 2, 26, -3, -12, -17, -4, -5, -40, -19, -3, 19, 49, 5, -43, 22, -13, 15, 18, -22, 25, -8, -38, -46, -67, 45, -14, 20, -28, 30, -14, 1, -53, 28, 15, 4, -27, 15, 46, -35, 42, -10, 9, -38, 22, -29, -30, 0, -19, 43, -17, 49, 35, -29, -7, 46, -3, 49, 21, -35, -20, 56, 2, 30, 1, -49, -26, 24, -1, 1, 3, -57, -39, -1, -63, -24, -55, 20, -3, 4, 41, -4, -25, 17, -22, 3, 37, 29, -19, -41, -11, -20, -46, -18, -32, 18, -21, 36, -15, 31, 1, -8, -1, 2, 20, 31, 15, -26, 13, 13, -8, 18, -26, -28, -3, -5, -5, -42, -51, -36, 21, 52, 25, 2, -21, 20, 21, -25, -2, -16, 39, -4, -7, 23, 2, 1, 25, -5, -8, 12, -31, -11, -11, 7, 1, 26, 32, 5, 13, -13, -2, 19, 21, 39, -5, -18, 4, -35, 34, -7, 39, -45, -43, 23, -27, -2, -32, 2, -38, 32, 98, -15, 20, -10, 7, 0, 38, -10, 76, 39, 0, 37, -17, 29, 3, -6, -25, -3, -22, 2, 0, 6, 61, -12, 17, 13, 3, 1, -13, -23, 10, 26, -12, 43, -12, 24, 8, -20, -11, -42, -7, -10, 39, -19, 13, -1, -13, 24, -30, 28, -43, 4, 19, -5, -34, -30, 13, -11, 2, -21, -17, -36, 10, -22, 6, 20, 13, -3, -30, 15, 26, 20, 34, 34, 3, -16, -2, -22, 2, -29, -22, -10, -6, -13, -12, 8, -15, -15, 7, -19, 29, -14, -2, -25, -9, 15, -26, -5, 17, 32, -35, -29, 38, -28, -18, -8, -15, 39, 31, -67, 39, -26, -12, 4, 1, -10, -1, 24, 12, 8, -13, 23, -34, -24, 23, 35, 21, -15, -34, 11, -19, 20, 23, -4, -7, -4, 25, -3, 6, -3, -15, -12, 37, 13, -1, -8, -28, -8, -10, 15, 31, -22, -56, 59, 11, -4, -32, -7, 22, -13, 0, -19, -3, -38, -14, 35, -35, -20, -24, 48, 12, 20, -36, -16, 3, 14, -31, 46, 18, 6, -10, -30, -12, -46, -23, 19, 17, -13, 3, 12, 5, -33, 26, 10, 21, 2, -1, 17, -23, -24, 11, 23, 4, 8, 4, 35, 24, -12, -28, 21, 22, -5, 15, -1, 31, -9, -4, 47, -40, 16, 18, 0, -12, 58, -40, 10, -25, 9, -20, -25, 41, -26, 2, 3, -38, -10, -13, -9, -52, 8, -4, -1, -16, 35, 22, -8, 12, -13, 30, -2, 53, -9, 3, 36, -32, 1, -11, 39, 23, -27, 4, -36, 29, -41, -14, 68, 15, -28, -5, 3, 13, 34, 63, 20, 44, 37, -11, -31, 25, -22, -29, -35, 25, -31, 32, -19, 24, -29, -33, -31, -1, 20, 10, -4, -27, -25, 17, 35, 6, 1, 24, 41, -4, -3, 57, 11, 51, -6, 11, 0, -15, -29, 25, 45, 16, 8, 2, -8, 18, 37, -1, -10, 24, 50, -24, -13, 48, 18, -23, 31, 20, -7, 2, -9, 23, -20, 35, -7, -8, -27, -46, -2, -27, 6, 20, 12, -10, 0, 0, 16, -32, 0, 13, 0, -24, -12, -31, -3, 43, -10, 32, 20, 19, -6, -8, 13, 12, -34, -1, 4, 14, -13, -2, 12, 8, -26, 11, -28, -11, -6, 3, 22, -20, 30, 4, 8, -2, 7, 18, 22, -5, 29, 14, 10, -35, 3, 29, -20, 0, 12, 6, -26, -4, 18, 11, -38, 30, -29, 17, -32, -2, 27, 6, 10, -43, -23, 34, 4, -19, -21, -6, -1, 12, 13, 49, -18, -24, 22, -19, 53, -48, 8, -8, 19, 2, -13, 15, 4, 17, 17, -9, 30, -29, -10, -3, 15, -30, 0, -38, -21, -18, -40, -12, 20, -8, -25, -7, 13, 5, 1, 13, -4, -28, 19, 24, -18, -44, -18, 27, -15, -18, 10, 42, -1, 9, -35, 26, 56, -50, 3, -30, -1, -20, -20, -45, 3, 12, 7, -23, 27, 10, 27, -31, 15, 24, -8, -17, 5, 0, 20, 24, -2, -34, -10, 23, -22, -18, -25, -34, 34, -28, -3, 17, 29, -38, -24, -11, -16, -12, 14, 27, -38, -23, -34, -11, 16, -5, -46, -20, 3, 5, 31, 1, -2, 20, -30, -11, 17, -49, 15, -38, -37, 65, -1, -14, -13, -4, 24, 24, 5, -23, -5, 3, 35, 17, -14, 0, 50, -16, 12, 5, -3, 0, -14, -4, -11, 38, -20, 28, -31, 0, 31, -40, 11, -57, -9, -15, -12, -12, -13, -35, -20, -64, 28, 26, -30, 24, -44, 4, 28, 33, 11, -13, -16, -4, -2, 15, 0, -10, -34, 10, -40, -34, -23, 24, 14, 7, -27, -4, -27, 34, 21, 24, 31, 20, -1, -6, -24, 30, 1, -8, -9, -14, -39, 7, 31, 38, -13, -1, 46, -9, -12, -28, -6, 27, -3, 15, 8, 3, -16, 31, -3, 27, -13, -43, -37, 15, 6, 19, -39, -3, 0, -7, 11, -19, 7, 23, -27, -9, -25, 12, 1, -15, 27, 2, -1, 9, 5, 28, -28, 34, -12, -20, 12, -35, 16, 25, 30, 18, 15, 2, -37, 11, 28, 14, -8, 15, -40, -30, 28, -27, 2, 30, -13, -39, 4, -1, 28, -7, 11, 14, 7, 17, 3, -6, -18, 0, -25, 4, 16, 30, 34, 5, -24, -57, 9, 34, 4, 22, -6, -9, 19, -23, -3, -35, -6, 19, -21, 18, 5, -8, 11, -37, 31, -26, -34, -39, -6, 22, 24, 20, -22, -11, 32, 17, -30, -8, 14, -5, -36, -9, -29, 7, 17, 30, -31, -20, 0, 16, -4, 16, -11, 0, 0, 36, 9, 42, -4, 19, 0, 4, 22, -33, -3, 22, 7, 45, 18, -28, -6, -24, -27, 38, -5, -62, -10, 63, 39, -27, -31, 9, 33, -25, 20, -46, -21, 3, -47, -21, -5, 23, 26, -42, 21, 20, 30, 16, -14, 21, 40, 3, 6, -4, -30, -9, 18, -2, 22, 0, 8, 15, -10, -44, 16, 6, 23, 33, 11, -5, -59, -11, 28, -12, -18, 1, 23, -4, 7, -4, 17, -5, 8, -9, -40, 12, 3, -8, -14, -11, 25, 0, -18, 15, -2, -2, 35, -7, 48, 17, 33, -29, -43, 18, 29, -24, -3, 13, -1, 24, -5, 29, -26, 2, -14, -12, -18, 3, -12, -7, 15, -14, -63, -3, 34, 5, -16, 11, -11, 11, 4, -13, -20, -17, 5, -9, -23, -53, 42, -24, 2, 24, 4, 26, -52, 30, 25, 24, 10, -9, -43, 36, -12, 19, -23, -20, 35, 14, 4, 23, 22, 17, -62, 42, 14, -30, -8, -29, 14, -32, -21, -13, -18, 26, 24, 28, 26, 11, 35, 15, 21, -1, -15, -7, -19, -36, -25, -13, -7, 22, -11, 14, -43, -8, 13, 21, 36, 20, 12, -3, 8, 16, -10, 42, 33, 6, 2, -2, -5, 1, -1, 27, 26, 42, 28, -5, 2, -9, 9, -24, -57, 6, -7, -7, 5, -23, -9, -42, 5, -4, 6, 31, -10, 15, 15, 41, 27, -1, -38, -29, -27, -42, -2, 0, 4, 34, -13, 13, 17, 15, -1, 12, 0, -19, 27, 28, 30, 10, 11, 6, -3, 14, 29, -5, 5, -28, 13, -32, 4, -41, -9, -25, 31, 11, 17, -1, -16, -19, -50, 2, 21, 13, -5, -10, 8, 9, -18, 6, 17, 14, 11, -35, -48, -12, -4, 5, 31, 14, 0, -2, 25, 16, -47, -5, 16, -21, 30, -25, 43, -26, -14, 15, -47, -11, -38, 9, -36, 20, 37, -28, 7, 17, -27, 0, 17, 44, 9, 4, 38, -20, -18, 52, 5, 9, -21, -35, 19, 6, -19, -14, -56, 28, -8, -7, 36, 14, 30, 15, 2, 33, 13, 23, -16, -1, 16, 4, 10, -11, 16, -1, -19, 28, -58, -18, -24, -19, 21, 9, 11, 5, 7, 3, -5, 23, 19, 9, 5, -10, 52, -25, -18, -6, 1, 38, 36, -25, 11, 21, -11, -39, -2, -28, 18, 2, 10, 13, -30, -7, 19, -16, 43, 7, 29, -12, 3, -6, 7, 9, 42, -9, -31, 20, 5, 45, -13, 3, -26, 31, 8, -23, 13, -4, -14, 10, -28, 32, 25, 0, 59, -16, 3, 12, 16, -37, -7, 3, 15, -38, -13, -1, 25, -12, -16, -33, 16, 2, -30, -23, -27, 16, -1, -35, -25, 17, 24, -22, 30, -6, 7, -12, -27, -16, 24, 26, -30, 14, -20, 31, 19, -18, 39, -18, -13, 13, 21, 25, 57, -3, -17, -36, -19, -47, -9, 30, 23, -64, -43, -11, -30, 16, -34, -67, -17, 42, 4, 21, 30, 5, -9, 30, -6, 21, 40, -38, 18, -15, 11, -41, -9, -11, 31, -35, 14, 10, -27, 49, -14, 3, 19, 42, 20, 10, -12, -23, 25, -1, 5, -17, -21, -15, -21, -34, -68, 7, -1, -4, 14, -18, -21, -5, 22, -21, -27, 10, 9, -24, -15, 2, -11, -11, 8, -2, -13, -18, 16, -15, -13, 3, 3, -12, 2, -6, 66, 7, 4, 19, -7, 27, 11, 22, 21, -29, -26, 39, -22, -33, 26, 0, -36, 9, -14, 9, -26, 69, 9, -23, -23, -44, -12, 10, 14, 20, 23, 23, 40, 11, 28, -49, 5, -15, -16, 10, -2, -13, -16, 17, -4, 5, 1, 3, 17, 6, 18, 8, 13, 32, 44, -16, 7, -5, 15, -19, 4, 18, -12, -7, -50, -26, 24, -7, -12, -30, 51, 30, -16, 23, -35, -22, -28, 44, 21, -2, 6, 6, 22, -43, -38, -1, -13, -18, 38, 23, 25, 29, 11, -31, 7, -17, -25, -11, -19, 3, 36, 27, 17, -8, 22, 26, 26, 0, -16, -15, -15, -31, -35, 5, 19, 16, -23, 9, -6, -32, 33, -33, -24, -31, 2, 47, 9, -3, -30, 37, -38, -27, -50, -11, 10, 12, 11, 31, 28, 33, -1, 16, 25, 21, -22, 30, 10, 25, -9, 3, 28, -27, 15, 32, -11, -17, -23, -29, 22, 17, 18, 31, 6, 9, -23, -14, -36, 8, -32, -21, -40, 29, 6, 15, 0, 17, -4, -48, -3, -1, -44, -26, -10, 24, -8, 25, -40, 18, 55, -8, 28, 2, -47, 5, -23, -5, 30, 12, 20, 4, -24, 10, -23, 41, -6, 26, 1, -6, 24, -16, 2, -20, -6, -5, 36, -23, -10, 14, 11, 4, -1, 24, -13, 9, 4, -13, -13, -4, 6, 1, 22, -10, 10, -16, -6, 29, 22, -29, -5, 15, 14, -26, 22, 3, 17, -18, 9, 13, 3, 17, 7, 8, 31, 5, 13, 29, 17, -14, 1, 28, 49, 0, 12, -26, 23, -22, -29, -18, -41, -17, -21, 0, -7, 5, -32, -14, -26, 6, -29, 23, -32, 14, 20, -14, 10, -13, -19, -21, 37, -17, -2, -6, 15, -27, 52, 27, 46, 13, 10, 0, 25, -18, -13, 0, -16, 29, -10, -28, 17, -21, 3, 7, 1, 30, 2, 50, -41, 12, 16, -24, 13, 14, 18, -8, 11, 2, -34, -20, -45, 8, -33, -35, -23, -19, -21, -19, -9, 23, -44, 21, 28, 35, 22, 25, -3, -20, 15, 16, 21, -33, -35, -37, -18, 24, -9, -12, 23, 5, -33, -36, -24, -5, -47, 52, -32, -4, -37, 8, 40, 12, -22, -16, 2, 18, -10, -14, 23, -14, -28, -21, -33, -13, -34, -13, 37, 29, -46, -7, 8, 23, 40, -32, -8, -9, -13, 2, -10, 13, -30, -8, -13, 16, 2, -13, 27, -45, -8, -40, -17, 9, -41, 10, 12, 30, -6, -18, 13, 10, 11, -63, -10, -4, -29, 29, -15, 6, 47, -16, -35, -29, -8, -13, -23, -4, -44, -37, 4, 39, 36, -25, 39, -2, -16, -31, 24, 3, -17, 5, -33, 24, -14, -7, 23, -37, -1, 5, -31, -19, -2, -5, 17, -3, 20, 9, 9, -45, -43, 27, -13, 30, -17, -15, 16, -52, -61, 27, -8, -41, -21, 18, -35, -38, 25, 19, -11, -19, -9, -11, 6, 10, -6, -13, -10, 12, -4, -14, -3, -17, 21, -17, 23, 8, -32, -5, -26, -27, -1, -36, -40, -20, 7, -11, -13, -1, 9, 11, 15, 9, -21, -17, 26, -18, -26, 2, -3, -25, 7, -16, -29, -37, -33, -12, 7, -31, -22, -8, -21, 5, 12, -38, 56, 6, -42, 33, 1, -24, -4, -28, 3, 3, -37, 42, -34, 15, 15, -3, -28, 12, 25, 7, 11, -17, -15, 6, 0, 16, 9, -38, 31, -20, 14, 11, 1, -11, -16, 13, -30, 17, 0, -2, -30, -45, 3, 9, 18, 7, 9, 17, -3, 1, -47, 15, 15, -11, 5, 27, 13, -7, -1, 28, -33, 32, -13, -16, -6, 34, 21, 3, 20, -6, 34, 18, 27, 5, -36, -21, -20, -21, -17, 18, -8, 17, -20, -5, 4, 6, -6, -28, 12, -25, 9, 24, -21, -32, 5, -32, 25, 6, -28, 12, -35, 11, -27, 25, 15, -9, -35, 4, 36, 27, 10, -1, 3, 29, 32, -36, 13, 10, 29, 15, -4, -8, -5, -32, 3, 26, -18, 25, -44, 28, -59, -27, 1, -4, -6, -15, -20, -23, 37, -27, -24, -24, -18, 22, -5, 0, -12, -18, 7, -24, -19, -22, -13, 11, -10, 27, 32, -18, -15, -25, 6, 2, -2, -37, 28, 54, -28, -11, 13, 56, -2, -35, 9, -22, -10, -31, -1, 1, 15, 44, 1, -39, -18, 2, -3, -19, 7, 39, 19, -38, 3, 5, 10, 38, 42, 21, -31, 8, -28, 19, -19, -10, -7, 4, -10, -17, 30, 4, 7, -23, -35, 4, 2, 37, -18, -17, -42, 25, 13, -19, 32, -9, 44, 5, -4, 6, 12, -4, 8, -4, -44, -25, 0, -3, 24, -3, 55, 3, -36, -7, 35, 28, 23, -30, 35, 9, 34, -28, 3, -22, 20, 3, 25, -20, 7, 38, -15, -22, 20, 4, -34, 24, 23, -2, -6, -36, 13, 14, -2, -15, 25, 17, -27, 12, 17, 23, -22, 22, -6, -35, -12, 20, -1, 6, 11, 27, -18, -1, -8, 13, 27, 13, 26, -32, 13, -37, -41, 23, 3, 9, 22, 23, 22, -33, 42, -29, 14, 41, -5, -8, 1, -10, -15, 4, 20, -20, -9, 21, -31, -5, -33, 10, 8, 20, -10, -28, 15, -21, -24, -25, 15, 54, 8, 5, 22, -39, 31, 12, 15, -38, 15, -31, 23, 30, -31, 20, -30, -14, 22, 39, -26, -42, -1, -19, 19, -23, -12, -45, 33, -2, -8, 85, -47, -21, -33, -5, 11, 16, -32, 26, 7, -7, -5, 22, 9, 11, -23, -31, 18, -4, -57, 2, -1, -1, 10, -22, -3, 36, 17, -23, -11, 2, -15, 16, -41, -11, -9, 23, 31, 15, 5, 15, 23, -24, 11, 51, 14, -38, 0, -14, 16, 82, -1, 11, 12, -6, -33, -7, -30, 16, 2, 29, -28, -10, -19, 46, -26, 8, -2, 18, -18, -19, -8, -27, 37, 53, -10, -22, -4, -1, 19, -22, -16, 11, -10, 19, 10, -1, -5, 68, -16, 8, -38, 7, 9, 29, 13, -37, 47, 9, 28, 12, -26, -2, 16, 47, -31, -14, -5, 7, -7, -47, -23, -1, 0, -30, 18, 45, -28, -2, -6, -49, -3, 27, 33, 5, -21, -2, -21, -41, 1, -37, -3, 11, 7, -1, -35, 36, 30, -45, -49, -12, 27, 3, -24, -22, -13, -5, 2, -21, -17, -19, -2, 10, 36, -8, 0, 19, 22, 0, -16, 71, -16, 11, -9, 1, -5, -12, -9, -12, 21, 25, 1, -25, -16, 56, 12, -16, 23, -21, -3, 5, -20, -14, 11, 7, 2, 32, 16, 37, -13, 26, 33, -56, 40, 31, -17, 2, 1, 11, -6, -40, 23, -3, -40, 10, -12, -8, -14, -6, -50, -10, 28, 10, 2, -40, 20, 1, -13, -32, -3, -5, -28, 45, -33, 22, -31, 46, 41, 27, 2, 28, -12, -5, -36, 23, -29, 42, -17, 17, 4, -19, -23, -1, -19, 24, -1, -13, 3, -5, 71, 19, 9, -1, 3, 12, -11, 23, -53, 9, 30, 29, 14, -17, 28, -14, -16, -15, -27, 16, 12, -36, -32, -25, 33, 6, -24, 12, -6, 31, 18, -2, 18, 17, -58, 0, -18, 26, 31, 2, -5, 54, 41, -5, -14, 7, 29, 7, -4, 43, 41, 43, 14, 33, 2, 17, -10, -4, -57, 34, -18, -35, -8, 31, 4, 4, -52, 7, 30, 21, -9, -21, -3, -33, 15, -62, 37, 45, -26, 3, 23, -9, -45, -30, 33, 7, -30, 17, -6, 7, 28, 29, -6, -10, -17, 16, 31, 17, -9, 63, -9, -32, -13, 7, 21, 19, 2, -6, 19, 23, -22, 18, 32, 15, 6, 44, 25, -8, 16, -16, 54, -29, 35, 9, -23, 10, 30, 12, -29, -22, -9, -38, 16, -19, 5, 9, -17, 6, -2, 17, -1, 15, 9, 33, 39, -1, 20, 11, 24, 38, 17, 9, 33, 10, 5, -23, 16, 46, 12, 5, 12, -24, -3, 16, -35, 23, -21, 6, -3, -22, -5, 41, -8, 21, -8, -13, -40, 18, 11, -14, 7, -7, 0, -40, 12, 3, -27, -10, -11, -24, -14, 19, 30, 30, -15, 19, -1, 35, 2, 21, 25, 4, 32, -25, 2, -31, 24, -34, 24, -23, 53, 10, 7, 30, -22, -15, 20, 2, -25, 10, -21, 29, -44, 10, -51, -13, 4, -6, 0, 24, 17, -13, 44, 24, -32, -30, 2, -13, 38, -8, 37, 28, 16, 50, 30, 20, 11, -2, -4, -7, 17, -32, -15, 6, -24, -18, -47, 4, -3, -43, -13, -25, -23, -38, 4, 9, -10, -10, -23, -29, -16, -15, 52, 35, -22, -8, 34, 1, 27, 23, 52, -43, -6, 6, -20, 7, 30, -27, -7, -4, -11, -50, 14, -19, -7, 22, -39, -43, -2, -27, -11, -8, 1, 39, -23, -36, -23, 5, 46, -12, -26, -27, 1, -21, 8, 20, -8, 17, -39, -24, 9, 27, -27, 30, 10, -31, 15, 10, 16, -27, 10, 8, -28, -11, 37, -15, 18, 24, 22, -19, 34, 7, -16, -38, -4, -1, -18, -12, -25, 34, -53, 46, 9, 28, -18, 14, 7, -28, -3, 14, -25, 19, 24, 19, -31, -32, -5, -20, -11, 21, 43, 14, 38, -10, -8, -11, -33, -22, 22, 51, -24, 20, -8, 31, 49, 5, 20, -41, 20, 9, 4, -40, 25, 49, -27, 19, -32, -8, 15, -4, -3, -3, -27, 27, 27, -42, 20, 16, 7, 24, 49, 30, -4, 15, -34, -11, 0, -22, -34, 4, -1, 27, 1, 29, -6, 21, -9, -4, -33, -13, -23, 15, 91, -28, -36, -8, 13, 9, 19, 5, -25, 28, 14, 25, -14, -1, -11, -36, 13, 13, 48, -13, -19, -8, 20, -32, -13, -11, -38, -30, -17, -11, -45, 24, 35, -9, -60, 52, 24, 32, -24, -21, -16, 12, -6, -8, 11, -14, 4, 21, 65, -26, -21, 39, -27, 11, -19, 3, -33, -4, -12, 47, 4, 0, 8, 34, -24, -41, -26, -1, -4, 0, 27, 22, 2, -15, 31, -19, -36, -13, -3, -44, 19, 10, 26, -9, -67, -11, -7, 28, -15, -17, 32, 10, -44, -8, -9, -20, 11, 30, 25, -51, 19, -25, 46, 33, -17, -6, -26, -2, -13, 10, -11, 19, -3, 9, 52, 0, 24, 6, 15, -22, -12, 24, -23, -2, -16, 12, -32, -42, -30, 27, 0, -23, -53, 4, 2, -27, 35, -31, -4, 23, -29, -42, -5, 29, 41, 16, -13, -31, 39, -13, 10, 1, 0, -31, -43, -6, 31, 0, 3, 1, 4, -35, 22, 19, -15, -46, 0, -24, -32, -12, -25, -19, -16, 0, -8, -15, -55, 42, -36, 4, -4, 6, 19, -7, -3, -27, -14, 13, -12, 30, -4, 32, 20, 19, 15, 2, -1, -28, 39, -7, 39, 30, 14, 23, -13, -4, 20, 32, 28, -32, 9, -5, 15, -16, 52, 30, 8, 23, 34, -44, 3, -10, 19, 23, -45, -4, -40, 20, 9, -9, 17, -1, 6, -14, 11, -19, 19, 1, 2, 29, 48, 20, 34, -1, 16, 19, 22, -27, 11, -15, 6, 27, 17, -32, 42, 35, 7, 45, -7, 34, -8, -21, 2, -27, 66, -39, -19, -50, 4, 12, 17, -26, -23, -25, 19, -22, 25, -10, -3, 11, -2, -54, 46, -24, -9, 2, -11, 12, -6, 15, -25, 35, -15, 25, -41, -49, -5, 4, -4, 12, -27, -13, 21, -18, -3, -11, 28, -53, 33, -27, 14, 24, 27, -4, 9, 67, -64, 7, -22, -27, -16, -2, -3, 41, -40, -10, 13, 16, -9, 36, 20, -40, -29, 16, -55, 17, 15, 16, 6, -15, -22, 21, 16, 46, 22, 6, 25, 22, 13, 42, 15, 9, 8, 15, 36, -15, -4, 0, -57, -39, -57, -18, -10, -16, -17, 33, -41, 6, 23, 29, 37, 3, -31, 15, 5, -32, 14, 38, 2, 29, -3, -26, 0, 17, -19, -7, -26, -9, -47, 8, -4, 32, -7, -9, -24, -2, -32, 11, 48, -38, -41, -41, -14, -14, 2, -34, 12, 19, 16, -8, -9, 8, 17, 5, -18, -30, 0, 30, -32, 23, 5, 9, 8, -31, 15, -10, -15, -16, 26, -8, 3, -8, -9, 38, 48, 24, -6, 3, -18, 29, 22, -14, 7, 45, -42, 15, 26, 24, 3, -2, 26, 12, -22, 33, 7, -54, -4, -13, -39, -23, 0, -14, 12, -17, -2, 34, 11, -22, 5, -15, 30, -20, -8, -6, -13, 12, -47, 37, 37, -7, -54, 6, -3, 14, 21, 43, 26, 6, 4, -25, 49, 31, -17, 5, -21, -7, 7, -8, 22, -11, -16, 39, -26, -10, 45, 22, -31, 18, 1, 1, 16, -35, -28, -13, 8, -6, 4, -21, 30, -19, -23, 21, 60, 21, -29, 28, 14, -20, -8, 6, 32, 11, 28, 9, 12, 23, 14, 23, -9, -26, 14, 13, -25, -16, 0, 4, 12, -25, 18, -5, -40, 16, 7, -6, 49, 27, -4, 35, 7, 55, 54, -2, -34, -19, 32, -7, -54, -26, -27, 18, 39, -59, -24, 23, 0, -26, -4, -14, -7, 11, -18, 2, -36, 1, 3, 37, 16, 52, -20, 43, 1, -27, 40, -19, 0, -30, 27, -5, -15, 14, -3, -17, -25, -15, -2, -18, -1, 52, 28, -40, 4, 10, 51, -19, -45, -17, -23, 41, -8, -2, -15, 18, 8, -40, -11, -4, -13, -8, -5, 18, -32, -21, 37, 41, -42, -14, 0, 24, -35, -11, -25, 39, 25, -19, 15, -5, 16, 0, 21, 12, -37, 16, -14, -26, 4, -1, -2, 27, -55, -28, 49, -28, -9, 27, -25, -27, -2, -5, 28, -10, 2, -41, 23, -22, -21, -36, -15, -11, -56, -40, 14, 10, 14, 42, -11, 29, -30, -27, 2, -10, -17, 12, 8, -8, 27, -43, -10, -11, 21, 1, -55, -48, -1, -4, -4, 15, 6, 5, 9, -33, -25, -39, -4, -9, 37, -22, 10, -32, -39, -1, -24, 5, 26, -42, -2, -16, -16, 27, -2, 25, -11, 49, -45, -1, -11, -35, -18, 3, -27, -2, 46, 19, 18, 12, -40, 10, -37, -1, -12, 3, -28, 60, 23, 9, 11, 9, -27, -18, -18, -30, 19, 4, 3, -4, 9, 12, -19, -28, -13, 15, -27, -40, 37, 45, 2, 40, -28, -17, -51, -19, 7, 21, 66, 23, 45, 8, -34, 11, -1, 18, -4, 1, -6, -1, -19, -65, 23, -12, 5, 17, -31, -55, 26, -36, -6, -37, -17, 61, 22, -29, -7, -15, -20, -20, -3, 22, -13, -22, 10, -28, 57, 10, 35, -25, 16, -49, -7, -18, 26, 19, -15, -6, 40, 16, -23, 22, -30, 5, 22, 39, -43, -9, -11, -2, 7, -50, 6, 20, 7, -15, -8, 12, 40, -8, 26, -13, -23, -49, -43, -40, 15, -10, -18, 19, -17, 12, -33, -13, 7, 5, -19, -5, -8, -8, 34, -8, 36, -9, -21, 39, -49, -14, -29, -10, -6, 56, -16, 40, -25, -10, -32, 27, 10, -4, 44, 10, 2, -16, -16, -5, -31, 34, 11, 14, 15, -7, -10, 32, -4, 11, -7, 25, -18, -15, 33, 12, -6, 20, -7, 2, 25, -5, 2, 25, 53, 14, -7, -18, -39, -38, 37, 9, -35, 9, -14, -11, 3, -7, 18, -23, 22, -31, 38, 13, -8, -6, -15, 29, -7, 2, 71, -7, 33, 3, -19, 21, -30, 3, -40, 17, -1, -48, 16, -39, 31, -31, 25, -52, -3, 8, 6, -29, 3, -23, 16, 12, 45, -57, 17, -43, 14, -35, -65, -16, -34, -4, 12, 24, 39, -18, 25, 55, 6, -45, 19, 32, -36, -13, -26, -6, 17, 6, -5, 7, -5, -30, 10, 2, 7, 12, -33, -22, -2, 17, 20, -11, -15, -16, 27, -37, 1, 7, 8, -27, 11, 2, -23, 11, -15, -25, -6, 41, -35, -27, -26, 0, 21, -18, -6, 26, -31, -26, 34, 39, 22, 40, 21, -26, -8, 40, 36, 19, -1, 22, 4, 6, -22, -4, 9, -15, 7, 29, 18, 31, 34, 8, -39, 19, -32, 18, -40, -11, 13, 23, -17, -18, 4, -3, 1, 3, -51, 14, -10, -23, -14, -7, -2, -15, -14, -21, 8, 39, -3, -26, -13, -9, 9, 3, -17, -33, -30, -22, 19, 6, -3, -30, -18, 2, 27, 1, 7, 6, 4, -13, 28, -32, 6, 9, 21, -1, 70, 7, 40, 22, 34, 15, 19, 0, -52, -13, 38, -16, 16, -23, 28, -18, -32, 22, 26, 20, -39, -26, 46, -1, -14, -15, -1, -1, 36, 2, -13, 7, 8, -26, 34, 21, 39, 14, 1, 40, -11, 16, 39, -36, 20, -26, 32, -6, 64, -17, 16, 27, 24, -8, -35, -22, 4, 16, 36, 4, 32, -33, -14, 32, -43, 42, -24, 11, -28, -20, 2, -26, 32, -4, -10, 21, 22, -2, -23, -27, 22, -5, -25, -24, 13, 3, 3, 32, -67, 0, 60, -20, 15, 34, 52, 26, -4, -30, 38, 61, -11, -7, 21, -42, 1, 23, 17, -18, 17, 5, -31, 26, -54, -10, -19, -40, 1, -18, -55, -26, 18, 3, -4, 19, 14, 11, 4, 15, -6, 25, -26, -22, 49, 6, 28, -18, -38, 16, -23, -35, -26, -9, 22, 7, -3, 34, -11, 51, -10, -9, 30, -3, -27, 8, 1, -7, -10, 4, 2, -1, -1, 0, 2, 9, -27, 14, 21, 11, 14, 0, 12, 1, -11, 51, 17, -46, 2, -45, -7, -5, 17, 25, -5, 10, 34, 20, 13, 21, 44, 13, 17, -13, 14, -7, 5, 39, -20, 1, 30, 1, 4, -8, 0, -40, -42, 1, -15, 34, -59, 33, -4, 5, -8, 8, 5, -11, -7, 11, -55, -1, -37, 8, 8, 30, 7, -40, 41, 24, 32, 3, -7, -20, 5, -19, 5, 28, -3, -8, -21, -6, 32, 11, -4, 25, 26, -16, 1, -20, -35, 28, -9, -27, -7, 16, 38, -5, -6, 27, -11, -6, 32, -45, -18, -24, -1, 5, -14, 0, 21, 35, -40, 9, -5, 1, -12, 51, 28, 47, -23, -36, -28, -8, -3, -2, 36, -35, -4, 29, 27, -7, 31, 17, 8, 14, 3, -30, -21, 0, 49, -22, -28, -8, 2, -19, -20, 9, 9, -36, -11, -53, 4, -39, 46, -35, -40, 21, 24, -35, 6, 22, 4, 10, -30, 15, 33, -3, 15, -9, -14, 11, 37, 7, -10, 14, 38, 7, 18, -34, -29, -3, 30, 27, 18, -21, -11, 12, 5, 32, -7, 6, -49, 15, -16, 7, 15, -34, 4, -19, -47, -23, 10, 13, 24, -14, 46, -31, 3, 5, 22, 36, -32, 19, -9, -38, 12, 5, -3, 29, -4, 6, -1, -5, -7, 19, 24, -52, -9, 30, 13, 3, -2, 24, 8, -5, 21, 5, -59, -17, 17, 40, 23, 16, -28, 11, -22, 8, -7, 45, -27, 12, 3, 43, 29, -11, -5, 4, 34, -27, 3, -24, -44, -15, 1, -20, 36, 5, 7, -41, -10, -3, -16, 31, -12, 9, -18, 15, 14, -5, -22, 2, -15, 10, 10, 33, 11, 24, 7, 17, -5, -42, 1, -15, 34, -4, -1, 16, -19, -18, -27, 7, 5, 9, -15, 53, 29, 17, -8, 23, -15, -27, 51, -24, -19, -29, 19, 7, 4, -12, -27, 38, 11, -2, -21, 18, -23, 19, 15, -21, -8, -3, 13, -3, 30, -54, -18, -50, 22, 12, -18, 23, 39, -6, -3, -25, -29, -34, -19, 9, -43, -33, 23, 27, 2, -9, 27, 8, 40, 15, 0, 16, -26, -19, 1, 26, 31, 21, -9, 43, 10, -33, 4, -22, -22, 37, 33, -13, -5, 39, 32, 18, 9, -3, -11, -29, 7, 1, -32, -4, -11, -13, -25, -31, -5, -27, -39, 5, 29, -3, -2, 2, 8, -27, 18, -33, 31, 60, -23, 2, 28, 38, 38, 19, -12, 6, 30, -1, 21, -6, 30, -60, 10, 4, 20, -38, -2, 26, -17, 22, 5, -4, -37, 11, -17, -26, -3, 14, -26, 20, 23, 15, -21, -12, 12, 17, 38, 20, 31, 19, 26, 3, 1, -66, 9, 20, -5, 4, -39, 10, -30, 6, -7, 13, 34, 5, 14, 23, 12, -34, 49, -3, -51, 33, 39, -3, 23, -15, 19, 3, 7, -6, 20, 21, 1, 33, -5, -14, -8, 6, -17, 16, 22, 1, 29, 14, 8, 11, 26, 12, -26, 15, 4, 7, 25, 42, 24, -32, 5, -12, -16, -23, -31, 33, -9, 13, 32, -7, 1, 52, 26, 13, -37, -27, -4, -6, 38, 20, 20, 8, 28, -27, -9, -29, 8, -28, 13, 5, -8, -20, 1, 13, -21, -9, -25, 75, 21, 29, -43, -12, 25, -14, -51, 12, 10, -5, -25, -27, 30, 35, 5, -10, 23, 26, 0, 41, -37, 27, -6, -20, -9, 51, 6, -3, -62, -10, 0, -3, 18, -35, 9, 16, 7, -39, -20, -25, -42, -12, -25, 61, -14, 19, -27, 9, 14, 25, -9, 14, 16, -24, 7, 27, 31, 48, 26, -28, 29, 1, 30, 3, 8, 13, 11, -28, 0, -17, -14, -44, 32, -6, 9, 45, 36, -20, -10, -19, -26, 6, -15, 2, -12, -15, 13, -34, 2, 35, -47, 26, -31, -2, 20, 8, -1, 11, 24, -30, -41, 5, 1, -41, -2, 34, 26, 5, 33, -39, 7, -7, 18, 7, -9, -23, -36, -45, 22, 10, 20, -16, 37, -32, 25, -4, 8, 19, -3, -15, 19, 38, -5, -34, 20, 27, 1, 18, 14, -4, -18, 46, 19, -28, 3, 42, 7, -38, -2, -9, 1, -7, -19, -15, 4, 14, 11, 9, 5, 23, -26, -16, -9, -6, -9, 44, 49, -15, -21, 28, 30, 17, -20, -11, -15, -15, 16, 24, -13, 3, 16, 28, 33, -36, -15, 2, 58, 11, -8, -24, -18, 40, 28, 19, 6, -19, 28, 25, 10, 31, 51, -22, -8, -31, 8, 44, 22, 40, 43, 18, 16, -10, 35, -10, 4, -13, -18, 11, -7, -17, -52, -6, 43, -11, 19, -30, -27, -19, -31, -20, -2, 15, 17, 17, 0, 25, -7, -42, 8, 20, 19, 35, -14, -34, -10, 9, 18, -7, 37, 4, 3, -16, -27, -20, 48, -9, 26, 30, -11, 16, -4, 39, -10, -2, 14, 30, 37, 15, -31, 15, -13, 17, -35, -16, 9, 11, 12, 28, 19, -29, -2, -36, -34, 0, -26, 10, -22, -23, 8, 11, -14, -27, -9, -32, 37, 11, -2, 12, 21, -29, -32, -6, 0, 15, 19, 27, 24, 9, 16, 23, -9, 16, 19, -37, 23, -27, -18, -11, -44, 12, -24, 14, 7, 12, -7, 9, 23, -27, -33, 36, 22, -4, 25, -44, 16, -49, 10, -1, 11, -12, -25, -4, -16, 20, 4, 12, 1, -15, 19, -7, 1, -3, -6, -3, -47, -17, -35, 30, 1, 9, 7, 3, -20, -24, -41, -25, -41, 31, 2, -1, 11, -6, 9, -2, 9, 22, 40, 26, 9, 5, -23, -19, 5, -22, -3, 17, -7, -6, -5, -15, 19, 1, 15, -24, 25, -26, 4, -30, 6, 11, -26, -10, -22, 10, 28, 32, 13, -23, 27, -64, 22, 12, -7, -2, 3, -25, 39, 40, -15, 15, 5, -26, 5, 32, -44, -21, 40, 51, 0, -40, 20, -9, 0, -22, -14, -25, -25, -1, -2, -9, 35, 23, -2, -4, -28, 22, -13, 11, -23, 31, 45, 21, 12, 10, -24, -19, 24, 23, -39, -32, -6, -6, 35, -2, 25, 28, -50, -45, -30, -28, -9, -11, 13, -14, 11, 50, 8, 8, 16, 16, 23, -21, -46, -26, 27, -13, -8, -36, -18, 21, 22, -14, -8, 37, 54, 32, 5, 20, 12, 0, 24, 27, -19, -10, -34, 38, -25, -17, 26, -46, 10, -42, -9, 8, -30, -24, 0, 37, -17, 35, 23, 22, -2, -36, -14, -7, 20, -4, -14, -1, -19, -11, 3, 15, -26, -4, 28, 11, 15, 1, 16, -30, 2, 20, -33, 33, 9, 45, 25, 9, 30, -8, 5, -28, -66, -26, -7, 11, -4, -53, 45, 7, 8, -23, -28, 40, 13, 1, -3, -1, -12, 28, 11, -45, -22, 13, -39, 28, -18, 4, 36, -5, -25, -18, 7, 3, -14, 10, -29, 37, -12, -4, -15, 22, 13, 12, 10, -6, 13, -10, 10, -36, -24, -34, -26, -22, 9, -31, 3, -13, 21, -19, 30, -15, 11, 16, -21, -10, 6, 31, 2, -17, -19, -31, -52, 0, 29, -37, 30, 3, 33, -4, 13, 8, -21, 1, 21, 32, -23, 5, 25, -10, 54, 20, -14, 9, 32, 13, -33, -51, -8, -9, 25, 2, 7, 3, -20, 0, 22, -1, 17, 9, -9, -65, 23, -6, 10, -7, -4, -12, 41, 2, 7, 17, -16, -12, 0, -28, -14, -3, 15, -6, -3, 26, -34, 30, 18, -5, -2, -20, -3, 2, -18, -19, 3, 3, -31, 48, -3, -2, -35, -4, 27, -7, 15, 7, 14, -7, -5, 16, -7, -27, 19, -28, 30, -23, -28, -9, -6, -16, -22, -6, 59, -12, 9, -13, -20, -22, 2, 19, 21, 43, -23, 38, 30, 50, 14, 33, -2, -40, 8, -28, 28, -13, 13, 34, -20, -31, 12, -26, 12, -18, 30, 31, 13, 3, 28, 13, -16, -26, -11, 15, -6, -65, -11, -55, 6, 11, -15, -3, 16, 24, 40, -40, -19, 10, -20, -1, -6, 10, -19, -20, -23, 20, 17, 25, -21, -13, 17, -29, 11, -28, -3, -13, -24, 11, 22, 17, 21, 12, 6, -4, 19, 15, -26, 64, 6, 4, 24, -4, -15, -35, -8, -20, 7, 0, 10, 16, 19, -33, 11, 29, -10, 18, -27, 3, -7, 29, -3, 3, 7, -21, 15, -15, -38, -52, 28, -76, 0, -44, -8, -29, -21, 16, 17, 42, 17, 22, -22, 41, -15, 30, 15, 57, 25, 48, -18, -23, -23, 14, -19, -19, -11, 20, 2, -15, 13, -12, 5, -7, 41, 4, 3, -22, 28, 26, 34, 12, -15, -3, 14, 19, 10, -52, 40, -6, -9, 9, -17, -7, -1, -4, -1, -35, -33, -25, -37, 24, 52, 11, 50, -17, -26, -20, -39, -5, 27, -30, -30, 18, -2, 9, 1, 15, 56, -3, 60, -16, 30, 62, 25, -5, 6, -21, -25, 10, -43, -37, -11, 7, -26, -27, 34, 17, -16, 16, 14, 37, -50, 3, 29, -25, -29, 1, -6, 67, -30, 5, 6, 20, -63, -18, -14, 9, 25, 2, -12, 14, 23, 13, -24, -25, -15, 13, 13, -12, 22, 15, 26, -11, -31, 21, 10, 13, 7, 24, 10, -33, 12, 9, 19, -38, 0, -13, 27, 53, -66, -37, -33, -20, 17, 33, 15, 23, -40, 34, -32, -8, -8, 42, -18, 18, 5, -10, -19, -47, -40, 4, 21, 0, 0, 3, 29, 21, 3, -35, 19, 22, -29, 10, 25, 30, 33, 24, -21, -26, -4, -14, -4, -2, -20, 39, 24, 38, 34, 5, -23, 20, 10, 13, 57, 1, 4, 2, -19, -5, -20, 8, -26, 2, 6, -10, 22, -22, 9, 2, 6, -14, -10, 10, 12, 23, -24, 24, -7, 26, -12, 0, -7, 22, 6, 15, 18, -3, -38, -10, -4, -8, -32, -23, -13, 47, 42, -45, -29, -3, 10, 2, 22, 36, -34, 19, 17, -26, -31, -16, 32, -35, -13, 30, -12, -28, -11, -7, -15, -17, -7, -17, -10, -5, -42, 1, -17, -25, 6, -29, -47, 28, 28, 10, 20, -33, -17, 37, 33, 16, 9, 7, 5, -19, 22, -46, -29, -45, 31, -23, -36, -14, 13, 6, -35, 24, -18, -59, -39, -16, 76, -14, -13, 29, -26, -27, 36, 15, -21, -21, 55, 18, 59, 50, 28, -4, -24, -34, 27, -2, 2, -7, 20, -84, -16, 5, 1, -5, 35, -34, -14, -1, -30, -23, 3, -41, -4, 1, 16, 12, -24, -37, -10, -21, -8, -72, -29, 8, -3, -19, -7, -11, 9, -42, 51, 10, -23, 8, -13, 31, 5, 43, 27, 30, -37, -23, 49, 3, -2, 26, -22, 16, -49, 28, -35, 35, 23, -15, 37, -2, -23, -15, 22, 21, -19, -30, -17, 19, 20, 18, 25, 32, -16, -29, -10, -16, 20, 34, 21, 4, 25, 0, -3, -25, 14, -12, 6, -2, 18, 0, 35, 32, 3, -40, 23, 4, 58, -18, 19, 11, -45, -19, -22, 39, -7, -26, 40, 23, 37, -8, -6, 7, 27, 4, 14, 12, 19, -18, 10, 3, -1, -8, -7, -7, 18, 6, 0, -68, 10, 23, -15, -5, -10, 9, -22, 9, 9, 21, 41, 38, 41, -54, -11, 20, 48, 3, -23, -8, -23, -19, 10, 35, -6, -17, -7, -6, 2, 16, 26, -18, -13, -33, -9, -23, 2, 35, 16, -9, -14, -15, -35, -48, 2, 47, 28, -5, 24, 16, 19, -22, 16, -30, -33, -25, -20, 6, 37, -23, -12, -81, -53, 14, -34, -29, -13, -6, -29, 43, 24, -49, 21, 2, -47, -11, -3, 25, -55, 1, 0, 18, -45, 13, 38, 14, -45, -37, -23, 51, 26, 12, 17, -18, -14, -7, -13, -35, 37, -42, 8, 12, 1, -15, 20, -20, 36, -16, -13, 15, -16, -17, -6, -44, 32, -18, 10, 16, 18, 5, -1, -48, -39, 18, -8, 37, 12, 3, -38, -8, -16, -27, 1, 29, 25, -2, -29, 4, -3, 0, 19, 9, -22, -55, -16, 23, -14, -18, 20, 24, -25, 20, 50, -16, -25, 2, 25, 39, -30, -27, 26, -7, 2, 20, -4, -16, -34, 11, 22, -42, -22, -15, -6, -5, 6, 14, 0, 51, -19, 6, -1, 15, 3, 20, -28, -8, -2, 6, 51, -47, 17, -58, -3, -35, 6, 7, -5, 25, -9, 5, 40, -25, -16, 3, 29, 61, -16, 16, 23, -17, -10, 8, 26, 9, -46, 2, 7, -15, -5, -11, -8, 35, 40, -3, 7, 13, 21, 42, -6, 6, 29, 2, 11, -8, -26, -7, 72, 10, -21, -12, 7, 13, -15, 9, 50, 26, -24, -8, 6, -19, -5, 22, -7, 41, 6, 51, -8, -7, 44, 41, 5, -13, -23, 4, 36, 2, -20, -23, 3, 51, 4, 26, -24, 27, -26, 41, -26, 21, 15, 22, 32, -18, 22, 26, 30, -6, 20, -20, 22, 0, -12, 1, -18, -22, -14, -7, -12, 32, 5, 2, 37, -20, 13, -32, -15, 20, -51, -28, 17, -24, -20, 25, 11, -20, -32, 13, -48, 13, 1, 21, 1, 20, 3, -25, 4, 12, -16, 42, 4, 19, 5, 7, -33, 18, -7, 21, 17, -7, -52, -15, -33, -4, 20, -23, -20, -2, -23, -4, 35, 36, 27, -54, -8, 23, 15, -16, 14, 5, -29, -23, -20, 19, -26, 12, -34, -32, 3, 10, 29, 33, 13, 36, -19, -11, 31, -14, 12, -5, 18, 6, 20, -9, -39, -18, 11, 24, -24, 3, 17, 56, -9, 16, 49, -45, 16, -7, -10, 23, 21, -56, -45, 44, 4, 37, 16, -3, 20, 27, -35, -18, -14, -22, 19, -13, 13, 5, 53, 7, -25, -21, -28, 32, -2, 15, 30, 11, -16, -28, -9, 9, -43, 37, 0, -6, -23, -31, 4, 40, 48, 29, -20, -1, -25, -13, -3, -26, -7, 18, -32, 19, 9, 26, -22, -13, 21, 36, 14, -12, 35, -53, 19, 33, 6, -20, 3, 38, -4, -24, -21, 13, 8, 4, 24, -12, -18, 7, 5, 13, -8, -14, 4, 26, 12, 28, 6, 28, 55, -7, 10, -35, 30, -24, -29, 1, 5, 20, 34, -1, 6, 13, -29, 1, -23, 8, 46, -9, -16, 1, 16, -10, 11, -38, -22, 14, 5, -10, 41, 5, 4, -29, -30, -11, 13, -4, 25, 32, 22, -1, 10, 48, 5, 15, 59, 24, 11, -13, -47, -33, 12, 23, 24, 10, 7, -3, 4, 20, -32, 14, -18, 60, 15, -4, -47, -7, -30, -3, -21, -21, 3, 25, 32, 2, 1, -10, -27, 22, 39, 39, -12, 18, 33, 17, -53, -3, 6, 7, 24, 4, -67, 21, -19, 48, -6, 29, 9, -34, -44, 43, 12, 13, -9, 12, -19, 3, 35, 18, 1, -2, 20, -22, 15, -24, -14, 20, -2, -6, -4, 31, -21, -15, -11, -14, -13, -40, 11, 4, -24, 2, 54, 11, -24, -17, -23, 8, -20, -6, -30, 19, 6, 23, -5, 8, -4, 17, -37, -31, -8, -5, 24, 42, -11, 17, 31, 7, -9, 49, 13, -5, 15, -5, -23, 10, -27, -15, -32, -35, -16, -14, -16, 38, -33, -29, 12, 6, -3, -16, -1, -22, -28, -27, 7, -7, 14, 35, -5, 10, 6, 3, 34, -9, -19, -5, -12, 21, -14, 48, 26, -12, -34, 61, -13, -18, 8, 14, -21, 41, -13, 4, -10, 29, -29, 6, 33, 1, 3, -3, 10, 3, 8, -38, -12, 8, -11, 11, 9, 20, 7, 12, 1, 12, 21, 33, -12, 13, -27, -14, 28, 30, 22, 13, 19, 0, -3, -10, -24, 0, -15, 4, 12, 19, -44, 16, -15, 20, 38, 51, -44, 26, -16, 37, 28, 8, -9, -11, 12, -24, 0, 9, -24, 4, 17, -12, 7, -40, 19, 29, 26, -31, -18, 36, 26, 33, 8, -11, -10, -13, -27, -25, 31, 35, 10, -17, -12, 39, 14, -56, -29, 60, -9, 3, -9, -27, 57, 65, 24, 27, 30, -2, -27, 25, -5, 30, 3, -15, 6, -1, 20, 1, -27, -17, -18, 23, -30, -6, 11, 25, 41, -13, 21, -7, 4, 27, -13, 23, -1, 18, 30, 19, -13, 0, 0, 5, 31, -28, 24, -5, 31, -30, -7, 4, 44, 21, 24, 15, 25, -30, -36, 21, 12, 40, -7, -1, 2, -33, 9, -7, -22, -15, -17, -9, -38, -33, -6, -34, 19, 16, -18, -43, -56, -4, -9, 14, 16, 39, -12, -38, 41, 23, 2, 14, -30, -7, -34, -25, 14, -28, 30, -9, 18, 17, -30, 24, -20, -39, -6, -3, 28, -13, -25, 16, 6, 32, -4, 16, 41, -61, -2, 28, 9, 18, 1, -12, 20, -25, -21, 26, -19, 1, -12, 2, 5, 56, -1, 24, -22, -7, -21, 11, 17, -44, -17, -10, 15, -32, 46, 20, -30, -46, 8, -21, 4, -14, -34, -12, -23, 30, 6, 66, 14, -8, -13, -23, -30, -62, 5, -15, -8, -24, -13, 32, -5, -6, -23, 40, 27, 22, 10, -8, 17, 30, -5, 6, -3, -16, 22, -33, -5, -20, -48, -39, 23, 8, 6, 4, 12, -11, 20, -26, 1, 2, 49, -33, -40, 0, 7, 14, -16, 4, 5, -20, 17, -7, 22, 16, 35, 21, -6, 28, 19, -20, -5, 45, -20, -25, -18, 32, 18, 43, -40, 11, -3, -51, -3, 36, 56, 21, -41, 33, -12, 14, -14, -21, 32, 12, 15, -17, 27, -27, -30, 33, -7, 28, 28, 30, 4, 13, 26, 16, -9, -10, -78, 0, 10, 8, 23, 33, -27, 14, 3, 9, 49, 9, -43, -18, 7, -9, 34, 5, 29, 3, -3, 29, 19, -14, -54, -16, 29, 15, -3, -3, -32, -8, 26, -6, 2, 0, 46, -8, -26, 2, 32, 26, 35, 4, -10, -13, 2, 18, 32, 21, -1, -20, 37, 46, 19, -18, -1, -11, -22, 0, -20, -27, -5, -42, -27, 19, 32, 13, 35, 17, -21, 6, 1, -23, -4, 41, 31, 3, 8, 7, 21, -15, -39, -40, -26, 5, 26, 12, 31, 11, 10, -12, -5, 16, 40, 29, 36, -21, 7, -10, -53, -30, -18, 7, -23, -7, -34, 5, -7, -37, -20, -13, 39, -2, -10, -6, 13, -35, -11, 34, 8, 9, 11, 23, 17, -11, 22, -41, 27, -37, -45, -31, -21, -6, -43, 11, 38, 2, 10, -25, 11, 12, 15, -22, 27, 16, -3, -22, -65, -29, 47, -26, -27, -8, 12, 1, -7, 38, 17, -43, -2, 24, -33, -11, 9, -2, 23, 11, -15, 22, -7, 23, -41, -4, -10, -14, 27, -5, 5, 10, 43, 13, -7, 49, 48, -8, -6, -9, 18, 29, -21, -40, 31, 7, -28, 15, 19, 14, 1, -6, -25, 19, -33, 14, -35, 29, 5, 18, 10, -21, -36, -7, -5, 14, -14, 65, 18, -25, -3, -18, 59, -5, 6, -25, -7, -28, -34, 1, 60, 19, 21, -12, 4, 13, -7, -19, 53, -2, 25, 20, -22, 8, -7, 48, 14, 34, 13, 3, -17, -15, 7, -7, 17, 22, 11, 7, 4, -3, 29, 0, 34, 12, -26, 6, 13, 32, -44, 18, 12, 33, -18, 15, -29, -7, 3, -9, -16, -26, 34, -41, -13, -14, -4, -14, -37, 7, 49, 33, -17, 15, 32, 37, 10, -8, -16, -10, 8, -12, 38, 27, 10, 17, -22, 32, -2, -15, 34, -17, 21, -13, -25, 19, 12, 30, -11, 28, 8, -5, -13, -17, 1, 33, -14, 9, -16, 25, -10, -7, 0, 35, 3, -28, -13, 8, 24, -16, -17, 5, 28, 5, 14, -18, 20, 37, 8, 10, 15, 20, 49, -7, -28, 9, 20, 25, 10, 41, -1, -7, 29, 27, 21, 13, -40, -10, 5, -20, -30, 10, -17, -14, 5, -3, 16, -14, -16, -12, 13, 11, -5, -10, -27, -5, -21, 50, -42, -14, 16, 3, -26, -20, 21, 44, 24, -5, 10, -16, 44, 36, 17, 11, -14, -2, 20, -25, -12, 16, -37, 36, 13, 1, -25, 19, 5, -62, 31, 0, -1, -1, 15, 6, 6, 14, -2, -43, 1, -4, 37, 9, -4, 29, -13, -12, -28, -35, -7, 4, 6, 17, 23, 21, 15, -14, -24, 13, 20, 30, 54, -32, -12, 4, -19, 26, -5, 10, -18, 11, -14, -7, 20, -16, -20, -12, 9, 12, -14, -26, -15, 18, -9, -8, -17, -12, 11, -2, 40, 11, 4, 3, 44, -9, 19, -6, 12, -21, -46, 9, 0, 13, -36, 1, 5, 2, -24, 13, 26, -1, -25, 7, 27, 61, 5, 9, -26, 17, -16, 23, -16, 7, 37, -20, -3, -28, -28, -12, 11, -24, -5, 30, 6, -11, 24, -29, 9, -23, -18, -9, -4, 6, 29, -63, -23, -25, -11, -14, -13, 8, 21, -8, -11, -12, 37, -9, -20, -14, 1, -14, 0, -20, 22, -2, 11, 8, -17, -30, -21, -15, 4, -21, 11, 44, -7, -12, -10, -36, -21, -8, 25, 16, 17, 16, 4, 9, -9, 22, 23, 25, -14, 29, 15, -28, -3, 8, -23, -38, -42, 28, 22, 1, -24, 2, 2, 2, -21, -9, 14, 12, -28, 15, 49, 13, 27, -31, -40, -11, -14, 20, -9, -20, 7, -42, -47, -12, 22, 15, 12, 1, 9, 7, 22, 20, -13, -5, -11, -19, -15, 28, 8, 32, -13, -4, 15, 22, 0, 14, 49, -8, -15, -19, 25, 11, -10, -17, 14, -12, 25, 43, 2, -33, 34, 15, 4, 4, -2, -17, -28, 8, -20, 23, -16, -18, 27, 11, -13, 6, -28, 38, -5, 22, 50, 3, 2, -15, 29, -33, -7, 12, -36, -2, -8, 34, -5, 22, -37, 15, -26, 33, 1, -15, -18, 23, -3, -19, 20, -2, -28, -25, 17, 21, -11, -25, -22, -62, -63, 1, 8, -1, -23, -19, -26, 13, 15, 10, 2, 7, -16, -5, -16, 16, 21, -50, 1, -8, -18, 11, -14, 3, 19, -16, 2, 12, -21, 25, -13, 19, -4, 15, 14, -39, 5, 19, 12, -5, 25, -4, -15, 3, 25, 32, 3, -43, -17, 1, -48, -4, 17, 21, 23, 17, -5, -7, 10, 13, -32, -5, 6, -24, 39, 0, 31, -35, -10, 26, -35, 23, -17, 16, 3, 42, 44, 26, -2, 15, 31, -11, -9, 20, 3, -31, 31, -16, 0, 11, -4, 11, 14, -12, -18, 6, -40, 22, -8, 17, -32, -14, -33, -44, -14, 23, -36, 18, -15, -3, 22, 0, -5, -13, -24, 10, 27, -27, -33, -11, 20, -19, 5, 3, -7, 1, -28, 32, -33, 9, -4, 19, 5, -22, -30, -27, 8, 6, -23, -23, 30, -37, -17, 9, -16, -34, -37, -2, -13, 2, -8, -9, 0, -10, 19, 7, -43, 3, 17, -19, 9, 1, -17, 40, -1, -7, 35, 12, -12, 1, -33, -16, 8, 9, -5, 6, 17, 55, -8, 4, -1, 6, -9, 28, -10, 7, -9, -33, 6, 6, 20, 28, 9, -53, -16, 1, 63, 6, -15, -8, -15, 15, -10, -6, 1, 29, 39, -24, 11, 1, 9, -3, -27, 28, -23, 36, 24, 46, 11, -12, 4, -10, 5, -11, -19, 29, 11, -17, 29, -1, -6, -17, -25, 32, -29, -28, 24, 2, -12, -62, -17, 6, -65, -22, -46, 12, 22, 36, 58, 26, -44, -22, 61, 1, 27, -14, -22, -15, 12, -3, -41, 20, -23, 9, 55, 16, -58, -18, 8, 20, 8, 35, -6, 23, -29, -14, 55, -26, 0, -20, 12, -35, -2, -47, 1, 28, 16, -31, -9, 17, -33, -2, 25, -3, -22, -3, 24, 4, -9, 0, -10, 19, 18, 30, -2, 5, 31, -22, -33, -23, -43, 5, -4, 15, -34, 22, 11, 16, 53, -21, 3, -27, -48, 58, 12, 15, 29, -10, -16, -9, 35, 28, 18, 6, -18, -9, -19, -28, -12, 4, -10, -13, -25, -40, 3, -6, 27, -9, -15, 8, -4, -32, 28, 17, 11, 25, -39, -35, 5, 23, -22, -11, -27, -34, -6, 31, 1, 33, 19, -10, 0, 0, 17, -28, 4, 28, 22, 9, 15, -13, 1, -17, 15, 2, 11, -47, 16, 16, 11, 27, 3, 8, 10, -12, 37, 18, 12, -22, -9, -2, 16, 18, -11, 36, 27, 35, 19, -24, -23, 20, -2, 25, 44, -15, -11, -13, -13, -2, 29, 12, 18, 8, 1, 39, -7, -8, -4, 44, 25, -22, 20, 8, 9, 7, 38, 34, 49, 7, -10, 13, 14, -22, 31, 20, 38, 2, -34, -37, -8, 19, 22, 22, -32, 31, 12, 22, -14, 3, 9, 32, 1, -19, -3, -10, -3, -36, -1, -15, 11, -22, -30, 19, 13, 1, -11, -25, -43, -57, -36, 39, 13, 7, 4, -7, 23, 7, 20, 9, 32, 19, 11, -42, -31, -11, -4, 13, -1, -32, -30, 24, -23, -21, -18, 14, -15, 24, -40, -5, -33, 40, 21, 56, 14, 18, 25, 25, -4, 22, 15, 25, 27, -38, 28, 20, -30, 36, 45, 31, 5, -34, 34, 20, 0, -52, -37, -51, -27, 12, 39, -10, -33, 30, -32, -20, 5, -10, 29, -18, 19, 0, 24, -10, 7, -24, -28, -20, -51, -13, 56, 31, 18, -48, 11, 38, -20, -36, -32, -30, 3, 15, -7, 36, -53, 17, 1, -3, -6, -3, 30, -18, -4, 3, 10, -6, -11, 11, -21, 10, 12, -7, 18, 13, -23, 36, 14, -18, 13, 24, 3, -4, -11, 16, 12, 18, -6, -10, 18, 28, 22, -42, -29, -14, -25, -21, -34, -6, 19, 30, 41, -25, -45, -17, 27, 22, -5, -20, -9, 8, 4, -25, 16, -12, 30, 1, 10, -17, -26, -6, -1, -1, -2, 12, -21, 5, -55, -12, 15, -1, -36, -15, 38, 17, -2, -11, 21, 5, -33, -1, 11, -21, 39, 16, 4, 5, -7, 23, 15, 19, 0, -4, 33, -25, 2, -2, 15, 5, 2, 48, 14, 12, -11, -12, -8, -47, -13, 7, -10, 5, -10, -25, 10, -31, -12, 2, -7, 24, -22, 36, -11, -22, -20, -37, 29, -42, 20, -16, 24, 47, 36, 8, -20, 24, 7, 24, -68, -20, 14, 9, -6, 48, -9, -23, 32, 12, 40, -45, -20, -15, 6, -29, 28, -11, -39, -54, -6, 0, 28, 29, -37, 10, -1, 37, 40, -56, 27, -3, 26, 29, 7, -26, 22, 23, 24, 47, 7, 7, -27, -38, -15, 51, -8, -11, -32, 20, 28, 2, 44, 1, -15, -20, 6, -15, 2, -18, -16, -68, -21, -36, 76, -3, 6, -13, -19, -41, -34, -2, -37, 18, 24, 33, -11, 9, 18, -20, -3, -4, 1, -35, -9, 3, 51, 32, 18, 35, -3, 24, -11, -33, -3, 27, -7, -3, -7, -6, 32, 22, 3, -1, 10, -14, 1, 57, 6, 6, -10, -4, 13, 24, -29, -5, -25, -45, -24, 17, -30, 24, 38, -19, -3, 4, -2, -19, 15, 26, 31, 4, 17, -14, 29, 19, 28, -24, -23, 28, 3, -16, 0, -34, -35, 1, 3, -20, 13, -17, 11, 24, -25, 65, 23, 24, 37, 5, 38, -35, -2, -19, -1, -18, 12, 10, 8, -37, -30, 2, 18, -29, -25, 3, 23, 17, 33, 31, -15, -25, 3, 17, -16, 16, 18, -5, -5, 9, -16, -13, 29, -4, 4, 1, -19, -20, 54, -10, 11, -44, 17, -22, 12, -30, -26, -8, -7, -20, -6, 8, 16, 12, -59, -22, 19, 1, 16, 4, 12, -4, -6, -22, -12, 14, 10, 27, -2, 5, 32, -2, -18, 8, -14, 9, 17, -4, 15, 11, -8, -14, 9, 38, 25, -32, -21, -13, -27, -21, 21, -19, -10, -33, -26, -16, -41, -4, 1, -11, 20, 24, -37, 31, 1, -23, -37, 33, -1, 17, -24, -16, -14, 6, 0, 24, 16, -23, -3, -21, 11, -11, -62, 14, 23, 14, 18, -25, 20, 26, -16, 22, -19, -20, -17, -7, -4, 27, 7, 0, -33, -19, -9, 4, -1, 16, 3, 35, -1, 47, 2, 17, -8, -15, 42, 33, -30, 3, -2, -7, 10, -22, 4, 6, -27, 26, 12, 35, 28, 2, -52, -32, -8, -19, 30, -37, -8, -18, 11, -35, 3, 21, 1, -43, 19, 2, -10, 20, 3, 11, 0, 18, 31, -3, 34, -5, -13, -23, 12, 44, -10, 5, 15, 20, -27, -45, 15, -31, 25, 20, -43, 1, 4, 29, -9, 25, -12, 19, -15, -20, -6, -42, -29, 16, -2, 30, 27, 5, 0, 11, 20, 39, 41, -20, -22, 8, -15, 21, -15, -3, 0, 22, -7, 17, 19, -25, -53, 4, -12, -18, 17, -21, -13, -18, 38, 23, 17, 15, -32, -31, -5, 46, -2, -29, -44, -10, 27, 8, -4, -45, -16, 32, 14, -22, -30, 8, -54, 32, -12, 52, 3, -11, -58, -28, 1, -8, 8, -16, -14, -11, 14, 14, 66, -34, 2, -35, 1, -18, -10, -11, 26, 56, 10, -24, -26, -34, -14, -34, 5, -15, 11, -18, -22, 4, 24, -6, -43, -7, -61, -1, 29, -33, 25, 9, 26, 29, -3, -28, 17, 19, -3, -3, -29, -9, 11, 20, -18, -10, 30, 7, -24, -23, 13, 9, -24, 8, -15, 20, -7, 14, -25, 9, -22, -34, -18, 20, -54, 11, -22, 18, 6, 29, 31, 4, 4, 5, 6, -13, 14, -4, 1, -24, -12, 2, -14, -44, 33, -44, 19, -50, -18, 36, -11, 40, 35, -2, 19, 6, -13, -24, -3, 6, -15, -21, -30, -1, 8, -12, -2, -3, -7, 2, 4, -14, -15, 26, -4, 14, -16, -10, 4, -33, 35, 22, -3, -26, -11, -4, -22, -34, 23, 49, 14, 3, 34, 15, -1, 4, 8, 6, 6, 34, 4, 13, 15, -28, -7, 11, 33, 16, -32, -39, 0, -37, -33, -20, -13, -13, 21, -19, 14, 33, -8, 60, 11, -15, -33, -17, -36, -24, 26, 19, 2, -12, -14, -15, 10, 14, -11, 29, 28, -7, 70, 43, -41, -3, -6, 23, -16, -25, -37, -3, -26, 18, 3, -17, -28, 20, -4, 25, 43, 26, 17, 5, 17, 23, -32, -25, 11, 23, -12, 35, 0, 4, 19, 21, 31, 12, -22, 14, -34, -9, 28, 21, 40, -21, -67, 23, -18, 52, 14, -3, 1, -9, 28, 6, 13, -2, 6, -4, -5, -7, 5, -7, 2, 28, 19, -34, -49, 53, 5, 21, 48, -41, -26, -18, 13, 27, 13, 1, -23, 7, 0, 25, 39, -25, -30, -4, -22, 47, 32, -26, 13, 33, 18, -5, -40, -4, 28, -2, 22, 10, -15, -5, -1, -12, -42, -8, -19, 28, 15, -22, 0, -10, 29, 0, -48, -29, -33, -43, -10, 38, 28, -11, 13, 7, 1, 33, -44, -32, -11, 21, 42, 9, -11, 1, 37, -10, -14, 18, -26, 20, 2, -35, -5, 30, -16, -21, -4, -19, 6, -10, 16, 47, 11, 24, -1, -13, 10, 40, 17, 33, 33, -32, -17, -31, 9, 0, -22, -41, -11, 20, 16, 24, -6, 19, -2, 10, 18, 14, -12, 18, 24, 15, 20, 16, -40, 8, 3, -17, -14, -23, 43, 9, -15, 15, 31, 48, 19, -7, -8, 25, -17, 14, -16, -35, 8, -26, -23, 28, 1, 19, 22, -17, -7, -31, -10, 37, -9, 10, -20, -11, -18, 21, -2, 52, -16, 21, 3, -3, 36, 0, -19, 53, 24, 7, -13, -12, -11, -2, 19, -4, 1, -15, 8, -7, -12, -2, -8, -29, -15, 21, 0, -31, -14, 25, -29, 18, 33, -17, 27, 50, 12, 4, -11, 32, -50, -11, -11, -20, 7, -29, 10, -17, -9, -30, -28, -6, 0, -2, -38, -22, 31, 10, 11, -18, 5, -2, 42, -12, -14, -7, -42, -4, 5, 7, 26, 8, 10, 4, -32, -31, -27, -14, 26, 21, 13, 3, -15, 35, -2, -33, 9, 12, -1, 26, -3, -21, -9, -16, -11, -10, 6, 30, -10, -22, 18, 9, -45, -1, 25, 22, 35, -51, -42, 12, -55, 6, -28, 33, -12, 0, 1, -12, -29, 46, -36, 34, -1, 35, 17, 5, 12, 10, 5, 46, -15, 23, 3, 17, 3, 7, -18, -17, 26, 7, -11, 15, -42, -30, -10, 20, -23, -16, 13, 20, 5, 14, -36, 19, -5, 0, -46, 0, 16, -15, -33, 54, 19, -22, -11, -28, -33, -19, 37, 59, 10, -9, -12, -2, 5, -24, 11, 37, 30, -7, 22, 10, 16, 14, -23, 26, -13, -55, -5, -24, 5, -28, 22, 1, 8, 16, 35, 15, 14, 11, 31, -10, -20, 19, 13, 5, 45, 52, -10, -25, -9, -2, -3, -20, -48, 3, 2, 29, 20, 13, 29, -17, 7, -40, 4, -10, -9, 1, 29, 4, -13, -29, 14, 28, -4, -49, -34, -22, 16, -9, 4, 25, 13, -13, -3, -2, -5, -1, 16, 1, -15, 29, -8, -8, 15, 14, -25, -58, -36, -18, -1, -35, 35, -43, 36, 35, -13, -21, -35, -7, -34, 2, 14, -20, 18, -21, 24, -3, 7, -36, -33, 35, 31, -14, -37, -26, -10, 13, 36, -8, 17, -5, -36, -14, 13, 25, 43, -12, -10, 30, 13, -16, 19, 27, -11, -10, 3, 2, -11, 15, 36, 31, 53, -6, -8, -25, -30, 40, 30, -6, 7, 11, -3, 4, -14, 10, 69, 19, -35, 2, 44, -4, 31, -38, -44, 17, -14, -29, 8, -25, 38, -18, -13, -22, -18, 31, 14, -36, -35, 32, 22, -12, -31, 23, 27, 7, -10, -28, 4, 6, -52, 22, 4, 4, -28, -14, 19, -7, -22, -24, 3, 58, 26, -15, 36, -15, 46, 56, 2, 0, 14, 41, 22, 29, 38, -7, -5, -54, 1, 9, 37, 22, -49, 28, -29, -41, -13, 17, -15, -16, 9, 31, 49, 58, 3, 2, 39, 21, 42, -25, -9, 39, 1, -4, 29, 36, 25, 32, -28, 16, -7, 22, 0, 1, 16, 73, 45, -22, 3, 46, -22, 0, -13, 26, -13, 5, -27, 25, 0, -3, -22, 33, -9, -7, 17, -12, -10, 3, -7, 1, 2, 38, -9, -16, -9, 28, 2, -39, -9, 16, 20, -7, 52, -7, -28, -13, 25, 0, -15, 29, -25, 4, 24, 30, 27, 2, -2, 40, 47, 12, 25, -15, 21, -7, 21, 18, 1, -32, 3, 42, 18, 47, -32, 12, -17, 11, -32, 41, 20, 32, -8, -3, 26, 25, 36, -59, -18, -13, 6, -18, 8, -19, -29, -7, -21, -2, -9, 18, -38, -5, 13, 63, 24, 27, 55, 26, 12, -26, 30, -27, 28, 43, 19, -24, 25, -12, 33, 50, -2, 37, 32, 29, -6, 10, 31, -6, 41, -8, -4, -19, 48, -35, 44, -10, -45, 24, 0, -15, 39, 28, 79, 11, 35, -3, -6, -3, -23, -35, 13, 16, 38, -14, 21, 26, -15, 7, -34, 16, 31, -3, 37, 23, 5, 3, -8, -16, -38, 0, 13, -1, -12, -48, 16, -18, -2, -16, -2, 8, 29, 11, -20, -21, 42, 3, -9, -26, 30, 1, 9, -27, 3, -40, -29, -20, -44, -24, 13, -39, -44, -26, -38, -39, -4, -9, 9, -8, -37, 6, 61, -16, 27, 33, -31, -25, 15, 36, -26, 25, -5, -25, 58, 8, 4, -15, -38, 27, -7, 30, -51, -17, 39, -5, -7, 18, -6, 8, 26, 8, 16, 10, -44, -16, -14, -9, -13, 8, -14, -46, -33, -15, -13, -22, 23, 1, -24, -24, 27, -44, 36, 39, -38, -7, 5, 8, 40, 18, 20, 42, -22, -14, -25, -45, -31, 23, 24, 18, -44, -9, -13, -19, -57, -30, 32, -34, 8, -30, 1, -12, -4, 18, 12, 17, 8, 3, -25, 12, -3, 32, -37, -14, -1, 16, 26, -16, 8, -40, -6, 13, 30, -34, 30, 13, -5, -5, -5, 32, 3, 5, -3, 25, -5, -35, -6, -25, -40, 15, 20, 36, 38, -30, 8, 31, 7, -8, -21, -19, 33, 14, -12, 0, 4, 4, -20, -7, 0, 19, 18, 5, 4, 17, -2, 12, -37, 27, -6, 9, -31, 12, 24, -18, -35, 28, -27, -36, 21, -24, -53, 33, -7, 15, -37, -12, -6, -6, 2, 23, 34, -19, -29, -12, -13, 30, -20, 3, -28, -21, 15, -20, -2, -27, -30, 35, 16, 6, -32, 36, 3, 28, 4, -8, -44, -17, -4, -38, 41, -11, -8, -24, 26, -11, 19, -34, 16, -36, 9, 19, -12, -51, -39, -14, 11, -36, 12, -22, -12, 23, -44, -51, 34, 73, -27, -6, 9, -3, 20, 10, -5, -27, -3, 11, -51, -18, 6, -19, 21, 5, 13, 37, 42, -9, -32, 25, -6, 37, -1, -31, 0, -23, -21, -30, -2, -6, 8, 25, 13, 34, 9, 17, 4, 1, 38, 25, -15, -1, -22, -11, -24, -4, 8, -20, -25, -19, 18, 2, -37, -32, -2, -44, -1, -17, 13, 14, 21, 0, -33, 26, 8, -23, 16, 11, 27, -11, -8, -57, 9, 17, -9, -2, -9, -1, 26, 8, 23, -11, -10, 26, -28, -27, 8, 8, -34, -7, -20, -14, -8, -49, 36, -32, 7, 2, 13, -15, -2, -18, -25, 2, 7, 3, -4, -20, 6, -19, -11, -8, 6, -51, -25, -5, -16, 20, -25, -4, 13, 34, -11, -29, -2, 0, -48, -18, -38, -6, -61, 4, -38, -25, 33, -21, -10, 24, -3, -9, -32, 1, -28, -33, 8, 22, 22, 16, 12, 18, 32, -5, 18, 9, 23, -7, -9, -9, 11, -21, -22, 0, 28, -25, -30, 19, 29, -4, -6, -9, -8, -2, 18, -35, -47, 37, 17, 5, -13, -35, -11, 0, 8, -28, 20, -18, -6, -12, -16, 31, -11, -1, -58, 3, 21, -25, -44, 17, 19, 9, -2, 18, 15, 13, -28, -8, 29, 11, -14, 28, -29, 3, 12, -27, 11, -33, 27, -17, 30, 16, 11, 71, 21, -11, 2, 50, 10, 15, -36, 35, -2, -34, 23, 58, -40, -29, 34, 4, -33, 22, 2, 10, 7, -11, -14, 18, 35, -3, 44, 1, -39, 7, -43, -5, 21, 2, -20, -26, -3, 36, 35, -11, -42, 2, -14, -3, 13, -4, -42, 23, 34, -17, 19, 44, 1, 9, -5, 40, 11, -28, -15, 5, 17, 2, -23, -12, 24, -12, -28, 6, 43, 0, -25, -10, -6, -6, 25, 15, -54, -10, -48, 38, -4, 7, 26, 34, -13, -35, 37, 0, 33, -34, -5, 17, 44, 17, 8, -9, 26, 24, 25, -21, -46, 25, -13, 26, 2, 30, 18, -2, 44, -12, 25, -4, 47, 17, 10, -21, 21, 0, -5, 30, 29, 7, -57, 1, 10, -28, -36, 8, 29, -37, -26, 42, -9, 27, -20, -17, -28, -38, -4, -9, 0, -17, -18, -30, -28, 15, -14, 5, 15, 35, 7, 27, 4, 30, 10, -33, 21, 31, -21, -9, 6, -19, 22, 11, -34, -35, 4, 25, 30, -6, -24, 49, 20, 20, 47, 24, -19, -2, -22, -59, -2, 43, -30, -5, -9, -35, 4, 41, -15, 13, -11, 3, -10, -11, -2, 5, 3, 30, -4, 4, -6, 6, -17, -26, 11, -8, 41, 14, -13, 26, -30, 70, -14, 6, 4, 0, 29, 0, -17, 33, -3, -8, 28, 4, 1, 4, 8, -35, -14, 29, -27, -16, -5, 33, -14, -27, -50, 1, -28, -5, -12, -28, -7, 35, 31, 19, 35, 29, 11, -3, 27, -3, 71, -9, -19, 0, -9, 33, 45, -49, -45, 27, 2, 36, 21, 5, 16, 24, -6, 7, 1, 1, 28, 9, 12, 32, 35, -5, 3, -12, 4, 17, 25, 15, -24, 38, 9, -37, 10, -42, -5, -14, -14, 7, 39, -2, 57, -56, -9, 36, 12, -9, -18, 6, 7, 15, -22, -26, -27, -4, -9, -2, 2, 18, 4, 9, -12, -2, -40, -2, -40, 12, 33, 13, -2, -6, -15, -29, 20, 13, 35, -23, 34, 33, -42, 7, -2, -27, -25, -32, 3, -9, -16, -21, 33, -14, -18, -1, -10, -30, -19, 24, -15, -9, 5, -1, -1, 0, 12, -17, -5, -20, 31, 13, -3, 9, -37, 14, 29, -2, 33, 35, -48, 52, -2, -6, -3, -35, -22, 11, 0, 24, -28, -27, -2, -17, -1, -23, 3, -13, 2, -12, 9, 2, -11, -39, -26, -21, -47, 7, 24, -1, -2, -30, 18, 8, -6, -11, -6, 23, -24, -33, 40, 40, -20, 24, -15, -2, -24, -20, -19, 46, 41, -15, 34, -22, 2, -3, 37, 3, -37, 13, 13, -28, -19, 17, -28, 16, 15, 10, -1, 27, 1, -5, -8, 33, -33, 29, 19, 5, 14, 36, -36, 15, 0, -14, 5, 29, 8, -46, 33, 6, -3, 24, -10, -2, -12, -7, 20, 6, 33, 17, -9, -4, -11, -7, -35, 26, 15, -28, 39, -23, 8, 1, 17, 35, 2, -13, 54, 38, -34, 19, -17, -24, 40, 26, -10, -9, 14, 3, 47, 0, -31, -1, 15, 3, 28, -12, -26, 18, 45, -6, 0, -12, -16, 1, -16, -15, 31, 49, 4, 49, 22, 29, 6, 33, 31, -11, 7, -27, -4, 12, 15, -22, -9, -1, -10, -6, -22, -27, 20, -33, -19, 21, 12, -1, 17, -23, -37, -17, 30, -18, 25, 16, 9, 10, 20, 4, 38, -8, 4, 18, 1, 8, 26, 18, 39, 22, 21, 18, -3, 6, -26, 28, 15, 41, -2, -6, 6, 46, 29, -24, 17, -24, 1, 4, 33, 39, 18, -3, -12, -15, -4, 11, -33, -25, -44, -4, -18, -25, 23, -20, -29, -13, 3, 17, 48, -12, 39, -4, 14, 10, 7, -19, 12, -17, -17, 13, -28, -2, -34, 10, 4, 9, 46, -9, 4, 52, -11, 1, 21, -24, 18, -1, 21, -9, -8, -11, -11, 0, -10, 12, -9, -7, -5, -32, -14, -40, 5, -24, 6, -16, 12, -2, 13, 7, 10, 30, 39, 30, 20, 25, 19, -32, -44, -3, -15, -1, 5, -5, 26, -20, 17, -25, -2, 34, 15, 8, -2, 64, 45, -5, -1, 8, -20, -2, -13, 16, -7, 52, 48, -12, 37, -8, 17, -19, -42, 14, 36, -38, -2, -13, -20, 6, 34, -5, -20, 26, -26, -26, -28, 5, 56, -14, 0, -29, 8, -20, 4, 12, -22, 13, -4, -5, 30, 17, 11, 17, 15, 30, -7, -37, 21, -11, -8, -40, 3, 2, 21, 18, 26, 18, -3, 13, 6, 0, 42, 9, -25, 2, 5, 16, 34, -5, 15, -48, -22, 25, 35, 25, -2, 6, -12, 46, 17, -11, -16, 8, 10, -30, 22, -39, 7, -39, -23, -33, -42, 5, 22, -15, -48, 16, 0, -53, 23, 15, -6, 26, 25, 23, 30, 5, 6, -14, 5, 13, -18, -15, -21, 14, 15, 34, 8, -17, -1, -7, -2, 16, 48, 36, 15, 30, 24, -3, -33, -37, -18, 1, 5, 5, -27, -4, 9, -24, -3, 41, 44, -8, -17, -28, 13, -32, -23, -24, 8, 35, -55, 15, -28, -4, 26, -10, -1, -38, -14, -4, 13, 29, -1, 13, 12, -6, -14, 27, 3, -9, 5, -16, -14, 61, -35, 7, -3, 5, -19, -10, 12, -70, -12, -16, -30, -29, -14, 2, -1, -27, 26, -13, 23, 24, -16, -21, 13, -23, 10, -7, -6, -32, -6, 40, 22, 7, 17, -9, 23, -24, 8, 25, 34, -10, 10, -7, -23, 3, -21, 14, -10, 25, -24, 5, -14, -14, -12, 6, 27, -10, -30, 10, -21, -19, -10, -26, -8, 4, -7, 21, 25, 21, 12, 50, -50, 22, 19, -14, -29, 19, 15, 11, -11, 14, 33, -11, -3, 3, 11, 6, 20, -2, -13, -14, 11, -28, -10, -19, 37, 43, 8, -22, 16, 8, -5, -17, 11, -44, 16, -5, -3, -27, -12, 25, 28, -23, 29, -14, -13, -2, -32, 25, 29, 20, 38, -10, 8, -1, 8, 2, 9, 19, 23, -9, -34, 1, 24, -1, 25, -17, -27, 26, 21, -5, 9, -27, -13, 15, -1, 3, 1, -25, -58, -5, 25, 5, 10, 33, -7, -18, -22, -10, 23, 24, 6, 14, -8, -21, 8, 19, -25, 44, -54, 13, -18, 22, 24, -7, 18, -21, 22, -9, -43, 32, -34, 20, 32, -56, -25, 34, -19, -42, 31, -4, 31, 39, 0, 20, 14, -13, 11, -25, -33, -9, -39, 28, -13, 12, 3, -2, -3, -30, 21, -26, 24, -41, -18, -29, 20, 15, 11, 2, -40, 12, -3, -33, -17, 22, 8, -10, -18, 7, 32, -18, -6, -10, 5, 34, -11, -42, -23, 21, 4, 47, 49, 14, -38, 4, 21, 2, -38, 30, 1, 0, 17, 16, 25, -10, 35, -6, 2, -37, 23, -17, -43, 1, -35, 7, -16, -37, -25, -17, -23, -25, 9, 9, 28, -17, -15, -33, 8, -3, 0, -13, 7, 2, -15, -13, -40, -1, 7, 44, 56, -18, -14, -27, 4, 11, 9, -23, 1, -3, 12, 37, 49, 0, -11, -23, 28, -24, 23, 17, 13, 25, -11, 2, -1, -6, 20, -36, -41, 28, -17, 50, 24, 3, -14, 3, -46, -9, -21, -11, 7, -11, 1, 5, 10, -9, 21, -33, -3, -11, -25, -66, -31, 4, -11, 9, 43, -4, 2, -2, -8, 3, -19, -20, -3, -41, 42, 55, -15, -6, 9, -1, 17, -1, -2, 7, 16, 11, -5, 24, 20, 32, -17, -24, -26, 3, 7, 60, -10, -27, -11, 58, -21, -22, 16, -22, 36, -1, 23, -38, -36, 19, 18, 46, 5, -15, 9, 16, 14, 15, 11, 37, -8, -1, -17, 20, 24, 32, -4, -2, 43, -18, 0, 15, -29, 34, -8, 8, 35, 23, -20, -17, 5, -12, 15, 33, 25, 36, -6, -7, 50, 21, -24, -44, -1, 30, -9, 16, 0, 21, -7, 17, -26, -36, -20, -25, -15, -16, 35, 23, 17, -24, -14, -16, -4, -31, -37, -12, 16, -36, 24, 22, 1, -18, -38, -3, 2, -8, 17, -7, -43, 13, -29, 1, -20, 17, 40, -7, 30, 4, -44, -36, 12, 30, -20, 8, -12, 50, -28, 37, -20, 40, 12, 1, 8, 41, -4, 9, -19, 40, 34, 3, -23, -52, 8, 23, 22, -23, 1, 43, -8, 6, -6, 13, 41, 19, 16, -43, -18, -27, 11, 27, -17, 14, -16, 29, 8, -1, -38, -20, -13, -2, 15, -28, 27, -33, 21, 22, -5, 6, 11, -17, 14, 25, 29, -12, -24, 10, -14, 17, 33, -20, -17, 11, -12, -4, 19, 31, 21, 19, -14, 2, 4, -37, -8, 23, -41, -4, 20, -4, -18, 60, -8, -25, 2, 51, 6, -15, 33, 9, 10, -60, -21, -4, -1, -11, 2, -29, 23, 17, 47, -31, 4, -25, -31, -13, 45, 6, -17, -21, -6, 7, -61, 26, 1, -11, 2, 0, -20, 11, 40, 10, 27, -29, 5, -37, -10, -1, -3, 39, -52, -3, 13, -32, -65, -47, -14, -4, -36, -10, -16, -12, 10, -53, -15, 9, 8, 24, -47, -11, 35, 0, -18, 16, -17, 34, -9, -10, 5, -35, 13, -15, 3, 29, -10, 1, 15, 26, 20, -16, 4, 2, -3, 9, 15, -27, 25, 34, -17, -7, 24, -12, -29, -18, 5, -12, -19, 25, -8, 24, 15, -28, 24, 24, -14, -24, -13, 19, 21, 11, 12, -16, 13, 33, 18, -56, 14, -34, 18, 19, -24, 9, -4, -5, -19, 7, 37, 25, -6, -8, -30, 0, -28, 18, 36, 24, 35, 23, -11, 4, 0, -17, 8, 0, 30, 26, -8, -21, -12, 3, -16, 14, 26, 28, -9, 2, 35, -1, -5, 7, -11, -54, -30, -25, -9, -9, -1, 37, -19, 8, 21, -12, 11, -11, -14, 30, 10, -3, 10, 19, 7, -4, -27, 10, 15, -15, 14, -50, 8, 4, 11, -6, 6, 31, 6, 12, 51, -27, 11, 11, -14, 48, 11, 13, 46, 13, -32, -13, -28, -4, -10, 18, -2, -17, 30, 55, 22, -2, -29, 11, -13, 11, -7, 27, 30, 26, 6, 51, 22, -14, 6, -17, -18, -39, -34, 51, -42, 39, 23, -21, -35, 23, 33, -19, -41, -20, -21, -19, 7, 57, 26, -21, -53, 2, -60, -26, 26, -19, -21, 0, 2, -27, -49, -16, -8, 23, -12, 6, -32, 0, 25, 20, 0, 37, -14, -13, -38, -12, 37, -17, 21, -36, -37, -25, 0, 17, -23, 28, 1, 42, -28, 22, 23, 38, -35, -36, 20, 3, 49, -3, -28, -25, -18, -12, -1, 7, -34, 23, 24, 2, -24, 6, 37, 28, 20, 41, 35, -18, 7, 6, 41, 16, 19, -11, 18, 10, 13, -27, 44, -6, -10, -50, -59, 30, 2, 9, -10, -12, 6, 6, 11, 8, 15, -12, 21, 12, -10, -38, -5, 10, -21, 12, -9, -20, -17, 46, 6, 35, -18, -7, 11, 50, -15, 19, -15, 23, -11, -42, 24, 18, 16, 26, 4, 20, -23, 22, 15, 18, 17, -21, -30, -21, -28, -7, -15, 30, 21, 12, 31, 28, 12, -15, -20, -8, -11, -12, -39, 5, 25, 26, 5, -44, -25, 18, -26, 25, -3, -50, 39, 10, -3, 1, 16, -19, -25, -22, 29, 14, 41, -13, 30, 15, 6, -9, -7, 21, 17, -28, -14, -12, -20, -10, -29, -14, 33, -24, 24, 13, -9, -8, -42, -14, 29, 21, 42, 52, 25, -38, -14, -21, -45, 14, -11, 18, -2, 23, 2, 38, 32, -3, 1, -25, -22, 2, -24, -2, -1, 20, -15, 48, 19, -1, -30, 11, 19, 8, -23, 31, -2, 12, 13, 0, 9, -3, 17, -54, 4, -42, 14, -36, -10, 6, -2, -38, -5, 67, 8, 4, 37, 13, 43, -7, -21, -19, 12, -6, 25, 35, -25, -13, -26, -13, 21, 39, -71, 5, 31, -14, 10, 46, -30, 15, 5, -12, -39, 28, -9, 40, -7, 11, 7, -30, -16, -1, -31, -20, -7, -34, -41, -11, -31, -17, -12, -9, -17, -29, 12, -41, 13, 9, -18, -5, 11, -8, 12, -25, 21, -3, -19, 29, 25, -33, -22, 9, 49, -53, 28, -2, -4, -38, 25, 11, -9, 18, 4, 18, -5, -28, -12, -11, -8, -37, 26, -39, 30, -30, 16, 6, -29, 2, -19, 21, 47, 40, 2, -42, 20, -14, -28, 29, -6, -22, -1, -14, 23, -13, 31, -12, 3, -3, -1, -6, 15, -24, -28, 19, 25, -25, 2, 29, -23, 11, 21, 13, -20, 18, -6, -37, 40, 19, 41, 0, 71, 11, 7, -17, -1, 1, 5, 8, -46, 24, 17, 24, -24, -29, 26, 16, 6, -29, 19, 17, -21, -9, 32, -15, -20, -3, -8, -13, 9, -22, -2, 22, 9, -5, 21, -6, 2, 33, 7, -10, 24, 19, -49, 14, -20, 10, 11, 42, -43, -21, -17, -9, -2, 8, 3, -9, -52, 42, -27, 38, 26, 10, -3, 8, 43, -7, 39, 33, 6, -18, 31, -25, -55, -27, -16, 16, 0, 18, -16, -2, 12, 10, -26, -20, 15, 36, 7, 11, 4, -2, 18, -2, -22, -3, -32, -40, -24, -43, 23, 36, -17, -7, -7, 29, 49, -13, 15, 15, 28, -10, -11, -19, 9, 1, 39, 48, 41, -46, -14, -10, 8, 14, -1, 8, -30, 2, 29, 27, -7, 30, 31, -37, -14, 25, 23, 20, -50, -3, 16, -42, 16, -15, 12, -1, 23, 17, 27, 30, -14, -30, 22, -32, -22, -40, 24, 1, 3, 58, -23, -15, 10, -14, 2, -14, 1, -22, 22, -60, 7, -12, 30, -1, -3, -16, -3, 36, 24, 3, 25, -7, 24, 41, -13, 15, -31, -8, -24, 14, -4, -8, 2, -46, 32, -3, -28, -10, 15, 24, -35, 3, 9, -3, -1, -2, -15, 28, -15, 3, 5, 0, 7, -19, 16, -19, 22, 21, -13, 16, -23, -31, -17, -36, 21, 17, -21, -1, 27, 8, -14, 13, 22, 4, 15, 9, 41, 6, 13, 18, 23, -19, 32, 40, -18, -2, 33, 30, 16, -17, -16, -38, 14, -29, 5, 19, -34, 1, -34, -45, 20, 1, 29, 27, -18, -18, -22, 45, 7, 3, -1, -19, -15, -15, -30, 3, 55, -18, 22, -20, -19, -13, 31, -32, 36, 11, -17, -25, 60, 22, 3, 22, -32, 31, -20, 24, 34, -2, -17, -43, -16, -6, 5, 18, -4, 2, 40, -17, 26, -17, 3, 24, -31, -13, -1, -12, 5, -7, 17, 8, -1, 37, 14, -26, -40, -3, 4, -22, -19, -12, -18, -34, 30, 21, 9, 15, 24, -21, -29, 19, -11, -21, 0, -24, 21, 3, -27, -14, -31, 10, 35, -6, -10, -7, 31, -2, 43, -26, 59, -32, -30, -17, -23, -44, 18, 9, -3, 25, 19, -1, 6, 18, 9, -30, 6, -26, 0, -6, 26, 7, -27, 6, -34, 26, -36, 37, -9, 38, 37, -15, -33, -1, 59, 17, -5, 20, 9, -10, -17, 55, 30, -59, -15, -42, 47, 20, 17, -30, 42, -15, -41, -16, 39, -25, 41, -7, 15, 47, -20, -29, 4, 3, 5, 24, 13, 1, -2, 19, 37, 17, 9, 17, -9, 8, 22, 23, -3, -17, -24, 40, 10, -44, 9, -20, -34, 0, 1, 34, 19, -46, 27, 21, 14, -19, 58, -25, 6, -31, 33, -18, 62, -27, 21, -26, 5, -13, -14, -15, -12, 8, -5, -18, -12, -11, 31, -32, 21, 46, 2, 2, 3, 20, 31, 4, -9, -8, 29, 11, 4, 5, 11, 38, 22, 14, 33, -28, 29, -15, 24, 17, 1, -1, 12, 6, 22, 20, -28, -38, 35, 22, 26, 28, -18, -29, -2, 16, 9, -23, -34, 30, 27, 16, -11, -1, -24, -33, -30, -26, 41, 22, -17, -1, 21, -11, 1, -18, -44, -18, 20, -21, 0, 32, 33, -19, 35, -1, 2, -1, 10, -10, 2, 52, 25, -12, -21, 13, -12, -18, 1, -27, 11, 17, 14, -20, -7, -27, 33, -60, -14, -10, -27, 35, 6, 26, -28, 13, 17, 4, 13, 1, -15, -8, 17, -3, 11, -5, 29, 18, 0, 21, 43, -48, -15, -39, 0, -7, 20, -14, -27, -38, 27, -16, 30, -16, 23, -10, -9, 21, 22, -29, 21, 3, 25, -18, 5, -12, 5, -55, -7, -3, -23, -4, -30, 6, -7, -14, 15, 12, 0, -7, 13, -14, 19, 35, 0, 0, 29, 26, -12, -12, -10, -18, 4, -2, -40, -15, 43, -40, 9, 28, -4, 13, 25, -3, 8, -9, -13, 48, -12, 13, -35, -2, 5, -31, 19, -50, 31, -35, -1, 4, -14, 38, -5, -5, -11, -10, 15, 5, 20, -5, 9, 40, -26, -2, 1, -12, 0, 14, 36, 15, 9, 6, -11, 0, 0, -18, -6, 3, 2, 42, -6, 34, 21, -12, 18, -1, 30, 8, -9, -31, 19, 12, 24, -18, 49, 25, -18, 5, 11, 8, -5, 4, -27, -30, 2, 32, -11, -12, -7, 13, -9, 42, 4, -18, 3, 33, 9, -13, -6, 45, -15, 5, 9, 38, -8, 48, -9, -34, 17, 20, 18, 38, -22, 20, -8, -6, -11, 14, 11, 6, 8, -46, 12, -11, -22, -45, -49, 15, -12, 24, -7, -30, 40, -15, 27, -53, 15, 45, -24, 9, 20, 21, 37, -12, -2, 2, -44, 7, -31, -4, 11, 37, -3, 13, -29, 0, -2, -8, 12, 30, -4, 51, -9, 7, 57, -2, -36, -39, -20, -3, 0, 15, -10, 34, -7, -19, 10, -1, -22, 1, -13, -4, -22, 24, -2, -12, -19, 44, -6, 56, -22, -8, -5, 5, 31, -14, 2, 7, -10, -17, 15, 18, 15, 29, -11, -25, -21, -7, 18, -7, -10, -1, 0, 37, -7, -31, -1, -4, -24, -2, 27, -29, 11, -17, -50, 10, -2, 23, 19, -7, -24, 9, -32, -29, -23, -5, -58, 6, -22, -24, 38, 45, 43, 1, -11, 32, -3, 2, 52, 6, 9, 19, -32, -28, -31, 45, 10, 6, 21, 18, 26, -10, 30, 7, -21, -57, 18, -5, 0, 40, 10, 3, -13, 7, 10, -16, 39, -8, -2, -1, 12, 4, 3, -17, -32, -19, -1, -3, 1, 27, -18, 3, 23, 6, -12, -17, 8, -31, -14, 16, 18, -4, -30, 41, 3, -21, 1, 12, 7, -10, -8, 23, 50, -53, 2, 11, 0, 34, -18, -6, 33, -7, 45, -18, -24, 57, -16, 24, 3, -4, 47, -43, -9, 43, -21, 11, -29, 1, -4, 45, -6, -11, -19, -16, 44, 0, -20, 29, -18, 28, 25, 42, 9, -26, 8, 29, 23, 34, -5, -3, 54, -16, -9, 37, -10, 14, -44, -54, 3, -25, 13, -21, -10, -25, -42, 2, -2, 23, 34, 41, 15, 22, -35, -14, -8, 57, -13, -1, -10, 18, -10, 6, 41, 6, 11, -13, -18, 3, 17, 39, 13, -4, 47, -42, 52, -6, -16, -4, 10, -10, -9, -10, -45, -40, -26, 26, -54, -31, -15, 20, -2, 12, -13, -32, 1, 35, 1, -30, 22, -4, -55, -20, 1, 25, -6, -21, -33, -12, -7, 20, 37, 36, -4, 20, -56, -4, 54, 25, -39, 50, -20, -5, 23, -15, 6, -33, 45, 8, 2, 6, 22, -3, -7, -21, 3, -18, -6, -10, -49, 15, 17, 50, -6, -17, -26, 16, 16, -10, -12, 0, -6, 23, -4, 25, -11, 12, -20, -3, 1, 28, 40, 25, -9, -22, 29, -19, -3, -9, 44, -36, 5, -4, 31, -15, 19, 14, 16, -11, -11, -2, -32, 9, 34, 16, -10, -18, -6, -37, 23, -2, -41, -7, 5, 4, 14, 24, -20, 11, -23, -11, 14, -2, 0, 2, 25, 7, -4, 7, -21, 20, 0, 20, -3, 28, -21, -15, 36, 8, 6, -13, -19, 41, -17, 2, 22, -26, -14, -39, -27, 16, -27, -24, -25, 8, 23, 0, -11, 42, -14, 18, -6, 19, 4, 28, 27, 11, 33, 19, 30, 1, 17, 34, 24, 26, -3, -12, -19, 41, -19, 24, -19, 25, 3, 5, -8, 23, 9, 21, 0, 38, 14, 11, -5, 47, 8, 40, 35, -39, -55, -11, -63, -32, -11, 14, 30, 3, -1, 28, -43, 6, -37, 1, -27, 2, -3, -5, 36, -7, 32, 11, 16, 20, -35, -15, 29, -23, -12, 28, 0, -31, -9, -5, 26, -32, -8, -18, -16, -20, 48, 31, -26, 24, -11, -17, -9, -10, 17, -18, 38, -16, 5, 19, -24, 16, -19, -21, 15, 0, -39, -22, -17, 13, -1, -46, 12, -30, 9, 13, -36, -6, 18, 4, 1, 27, 7, -20, 25, -5, 6, 23, 3, -34, -26, -11, -11, -34, 18, 1, -38, 21, 19, 1, -21, 9, 18, -17, 32, 24, -3, -3, -10, 22, 23, -21, 12, 31, 11, 2, -34, 50, -6, -27, 40, 16, -13, 24, 2, -27, -7, 27, 6, -7, -44, -25, -24, -48, 13, -19, 6, -8, 21, -9, -4, 15, -1, 25, 46, 14, 3, -17, -16, 19, -7, 37, -22, 7, 24, 1, 13, 28, 1, -44, -37, -24, 3, 6, 7, 25, -19, -8, -29, 11, 17, 20, 1, -15, -19, -5, 2, -5, -31, -20, 2, -10, -15, -29, 10, -36, 36, 49, -58, 16, -41, 35, 46, -46, -4, -27, -24, -1, -1, -33, -27, 15, -13, -2, -10, -2, 47, -29, -11, -26, 34, -38, 11, -8, -19, 18, 11, -3, 11, -9, 6, 6, -25, 32, -22, -21, -12, -25, -2, -24, 32, 1, 18, 2, 53, 40, -15, 0, -2, -25, 7, 13, -5, 6, -7, 8, -5, 19, 31, 33, 10, -29, 11, -14, -2, -24, 16, 8, -40, 47, -25, -14, 7, -28, 8, -38, 10, 38, 21, 25, 15, 30, -43, 1, 6, 30, 15, -22, -1, 12, -32, 18, 4, -30, 30, -19, -33, -21, -13, -16, 4, 1, -1, -41, -38, -10, 20, 9, 29, -2, 14, 22, 10, -18, 1, 47, -3, 23, -10, 3, 5, -29, 15, -27, -2, -9, -15, 6, 5, -13, -1, -34, 44, 27, 4, -14, -21, -21, 13, 36, -9, -12, 28, 6, 9, 31, -16, -10, 32, -33, 13, 11, -6, -20, -16, -30, 15, -21, 52, -2, 25, -20, -22, 31, -12, -45, 8, 41, -11, 10, 30, 16, -37, 5, 13, -6, 31, 10, -15, 3, -14, -9, 22, -5, -31, 21, 34, -22, -32, 16, -18, 40, 10, 45, -35, -47, 19, -38, -5, 5, 17, 10, -10, 26, 37, 11, 17, -37, -8, -4, -18, -9, 24, 12, -2, 26, 6, -9, -23, -13, -4, 37, -27, 2, 27, -25, 29, 18, 14, 29, 14, 15, -6, -8, 49, 8, 16, 12, 19, 23, 24, -3, 0, 36, -36, -55, -2, 7, 55, -28, -30, 20, 15, 27, 14, -39, 3, -19, 26, 5, -24, -13, 9, 32, -14, 10, 15, 2, -6, -9, -22, -7, -4, -8, -27, 0, 24, -16, -6, 4, 3, 19, -2, 13, 33, 21, -4, 8, 8, 56, -2, 53, -2, 15, -16, 1, -6, -5, -6, -1, 15, -21, -28, -29, 34, 0, 3, 28, 26, -12, -19, 16, -42, -45, 17, -30, 22, 20, 37, 15, -10, 5, -51, 18, -8, -4, 23, 6, 8, 44, -27, 4, -14, -9, 26, -34, -23, -25, 24, 33, 11, -45, -29, 25, -40, 26, 11, 49, 12, -49, -9, 6, 9, 10, 20, -29, -55, 29, -4, 1, 18, 0, -26, -13, 35, -23, 9, -16, -4, 22, 51, 11, 11, -26, -23, 3, 24, 18, -13, 11, -19, 20, -45, -17, -19, 32, -24, -30, -9, 22, -11, 0, -12, 20, -1, 23, 1, -23, 23, 42, -9, -5, 14, 38, -14, -8, -27, -21, 24, -23, 14, -27, -38, 9, 24, 2, -4, -28, 39, -48, 2, 8, 10, 34, 13, 31, -8, 33, -25, -46, -48, -33, 7, 28, -37, 17, 11, 4, 13, -11, 16, 19, -21, 10, -14, -37, 20, 12, -17, 32, -12, 44, 34, -38, -20, -23, 35, -34, 14, -40, -31, -30, 41, 20, 20, 17, -9, -16, 29, 0, -28, 7, -32, 17, -2, 31, 16, -63, 7, 14, 24, 7, 2, 35, 25, -4, -10, 44, -18, 40, -11, -11, -22, 29, 18, -32, 2, 42, 0, 13, 24, -3, -19, -19, 17, -20, 17, -17, 30, 15, 25, 35, 33, -35, -29, -3, 21, -19, -8, -6, -25, 31, -9, -11, -11, -40, -29, -6, 45, 34, -46, -40, -25, 15, -26, 23, -32, -33, 16, 13, 3, -8, 17, -28, -10, 12, -47, -37, -24, 36, 6, 10, -40, 17, 5, 14, 24, -4, -13, -29, 69, 26, -18, 42, -24, -5, 18, -35, -39, 15, -6, -6, -6, 17, -23, -22, -2, -14, 8, 15, -10, 18, -48, 16, -20, 1, -33, -3, -13, 32, -38, 59, -1, -42, 22, -23, 26, 14, -23, 13, -14, 34, -18, -7, -13, 45, -13, 33, 3, 48, -14, 41, -29, 21, -9, -4, -5, 14, -27, 3, 10, -36, -19, -28, -1, 39, -3, 24, -3, 23, 21, 53, -21, -21, -9, -23, 6, 1, -38, 32, 0, -16, -20, -46, -29, 31, 12, -33, -38, 7, 5, -23, -39, 8, 50, -11, -16, 20, 31, 20, -10, 24, -10, 44, 7, -22, -23, -40, -7, -22, -6, -29, -36, 11, 34, -19, 5, -23, -29, 41, -9, -17, -7, -29, -13, -21, 38, -25, 7, -3, -32, -30, 4, 8, 7, 13, 1, -25, 18, -10, -24, -19, 22, 30, 4, -19, 41, 38, 39, -2, 5, 44, -14, -2, 22, -27, -24, -1, -2, 18, -4, -26, 40, -53, 9, 38, 10, -10, 18, -18, 12, 10, 3, -16, -41, 24, 21, 41, 8, 14, 32, 41, 40, -61, -10, -16, 27, 56, -8, 19, 1, 2, 12, -1, 19, -18, -2, -25, 3, 1, 19, 39, 33, -3, -40, 27, -28, 10, 40, 2, 38, -101, -13, 2, -42, 31, 27, 17, 27, -51, 20, -55, 39, 33, -39, 11, -2, 2, -22, 66, 6, -7, 56, -8, 8, 65, -13, 21, 12, -36, 62, 40, -23, -47, 9, -24, -15, 19, 10, -13, 71}

#define TENSOR_DENSE_KERNEL_0_DEC_BITS {8}

#define TENSOR_DENSE_BIAS_0 {-20, -23, 11, 0, 65, -13, 19, -82, -49, -41, -86, -4, -84, 7, -51, -55, 24, -66, -10, -5, -50, -110, -11, 3, -79, -10, -19, -69, 21, -34, -19, -30, -35, -5, -51, -44, 13, -32, -36, -66, 20, -64, -30, -38, -68, -34, -51, -16, -28, -40, -62, -10, -67, -33, 30, -65, -10, -35, -18, -78, -50, -61, 0, -64, -29, -97, -102, -33, -16, -26, -17, 2, -19, -26, -22, 16, -30, -50, -34, -13, -6, -31, -8, -69, -18, -11, -56, -51, -16, -40, -74, 30, -36, -47, -22, -49}

#define TENSOR_DENSE_BIAS_0_DEC_BITS {10}

#define DENSE_BIAS_LSHIFT {2}

#define DENSE_OUTPUT_RSHIFT {10}

#define TENSOR_DENSE_1_KERNEL_0 {-18, -43, -24, -33, 51, -6, -1, 1, -74, -64, 31, 60, -25, -10, 12, 2, -61, 58, -58, 2, -67, 49, -53, 21, -44, -97, 49, -43, 3, -50, 11, 19, -52, -49, 45, -63, 5, 14, 0, -20, 60, -25, 40, 22, -40, -30, -50, -11, -37, -56, -27, -49, -41, -43, 26, -26, -33, 25, 55, 65, -32, -78, -36, 34, -62, 41, -59, 35, 9, -88, 25, 48, 45, -98, 39, -66, 22, 45, -93, -35, 8, -64, 11, 7, -7, -88, -80, -75, 3, -89, 38, -29, 36, 58, 30, -46, -43, 7, -75, 47, 48, -62, -3, -77, -59, 42, -10, 47, 32, 3, -55, 52, -25, 18, -54, 31, 26, 4, 50, -13, -57, 26, -54, -69, 22, 8, 26, 28, 32, -33, -45, 0, 28, -24, 27, 61, -75, 32, 41, 0, 13, 30, 20, 8, 18, 41, -11, 20, -4, -22, -78, -100, -54, 42, 20, -58, -10, -35, 10, -6, -14, 65, -28, 24, -14, 67, -35, -35, 3, -7, 25, -4, 4, 73, -49, 38, 8, -37, 29, -55, -18, -65, -63, 19, 5, -21, -33, 75, 25, 19, 44, 53, 8, 65, -39, 3, -49, -51, -55, 28, -52, -40, -82, 32, -4, 45, 25, 4, -16, -44, -25, 52, 55, -59, -8, -43, 40, -11, 17, -68, -5, 59, -34, -29, -61, -20, 37, 23, 22, -25, 57, 37, -15, -43, -21, -52, -67, 9, 9, -8, 50, -2, -85, 35, 58, -49, 22, 21, -61, 71, -105, -3, -5, 11, -8, -80, -21, 40, 51, -50, -29, 46, 39, -71, 62, -68, 29, 8, 22, -3, 4, -51, -27, -78, 13, 36, 47, -36, -42, -83, -76, -41, -27, 49, 50, 53, -42, -23, 45, 40, 9, -9, -5, 21, -8, -83, -47, -23, 68, 49, -79, 40, -16, -35, -17, -41, 39, 36, -38, -23, 17, -50, 17, -56, -7, 64, 3, 32, -79, -48, -5, -30, 50, 49, 52, -22, 62, -28, -40, -5, -65, -12, 15, 7, -37, 41, -74, 22, -58, 8, -45, -11, 58, -11, 53, 42, 56, -12, -32, -14, 46, 22, -58, -42, 10, 33, -22, -46, 37, 50, 26, -81, 48, 43, -2, -52, -56, 5, 1, -32, -68, 15, -70, 41, 22, 0, 10, 48, 38, -66, 55, 59, -54, 26, -10, -14, 32, 59, -45, 64, -62, 50, 25, 2, 24, -74, -62, 16, 15, 34, 28, -60, -43, 14, -46, 60, 12, 46, 33, 15, -18, -36, 22, -70, 70, -62, 18, -36, -27, -43, -102, 1, 9, -109, 54, -17, -88, 35, -11, 55, 40, 41, 29, 28, -23, 31, 44, -46, 43, -1, -6, -60, -57, -59, -34, -12, -18, -19, 1, 25, -68, -48, 45, -50, -35, 32, -4, 36, 32, 25, 4, -30, 48, 51, 49, -50, -73, -38, -71, 31, -65, -3, 16, -28, 51, -60, 27, -19, 10, 58, 10, -46, 26, -32, -13, -53, -69, -5, 28, -33, -53, 64, 29, -32, 50, -20, 28, -40, -33, -64, -17, 31, -23, -10, -65, -66, -65, 22, -37, 17, 37, 26, -15, -22, -10, -30, -65, -11, -26, 65, 9, -38, 57, -69, -62, -50, 39, -58, -47, 57, -29, -13, -50, 45, 6, -65, 68, 39, -61, -55, -63, 31, -7, 18, 13, 65, 53, 37, 35, 24, -69, -40, -34, -60, -25, 11, 3, -26, -19, 5, 56, -26, -57, 35, -59, 3, 44, 12, -82, 18, -15, -32, 21, -59, -49, 75, 8, 54, -69, 32, 51, 0, 25, -54, -104, -12, -52, 49, -14, -8, 3, -31, -41, 24, 49, -66, 42, 62, -36, 56, 6, 53, -45, 35, -36, 62, -25, -42, 35, -1, 19, -64, -18, -62, -24, 30, 8, -39, 67, 38, -7, -79, -10, 12, -27, -54, 11, 20, -39, 39, 31, 5, -29, -21, 18, -15, 37, -54, -7, -38, -72, 41, -87, 4, -35, -18, -39, -18, -48, 30, -14, -64, 40, -54, 22, -76, 10, 0, 54, -32, 23, -10, 1, -64, -51, -1, 69, -30, -88, 53, -67, 65, 3, -60, 24, 8, -84, 18, -1, -57, 17, 32, -47, 21, -90, -31, 27, 49, 49, -61, 48, 6, 0, 47, 35, -12, -16, -40, -35, 64, -28, 21, -44, 57, -25, 41, -34, -9, -61, -23, -11, 33, -73, -57, 53, 46, -56, 24, -60, -46, -55, 51, -55, 5, -12, 66, 53, -23, 38, 58, -56, 16, -33, -49, -41, -14, -34, 41, -16, 50, 15, 57, -27, -3, 22, -30, 34, 47, 35, -62, 22, 49, -24, 50, -33, -55, -25, -17, -81, 33, 13, 27, -47, 40, -15, -41, 32, 19, 10, 36, -57, -80, -58, -32, 33, -43, 17, -16, 45, -23, 41, 45, 36, -15, -33, 29, -97, -34, -42, 37, 30, -21, 16, 46, -85, 56, -69, -48, -17, 20, 48, 56, -28, -33, -62, -12, -35, 33, 15, -10, -41, -80, -70, -61, 31, -66, -20, -25, 38, 39, 10, 25, 32, -27, -90, -9, 33, -49, -6, -37, 33, 47, -72, 33, -4, -36, -37, 34, -14, 37, 27, -49, 29, 53, 9, -34, -61, -56, 27, -28, -17, 38, -28, -21, -46, 5, -40, -30, -8, -9, 44, 14, -67, 34, -51, 29, 35, -13, -15, 27, -67, -13, 20, -10, -27, -15, 24, -34, -64, -15, -33, 28, -66, -5, 43, 53, -34, -64, 25, -18, 35, 41, -65, 36, -61, -67, -22, 31, 42, 48, -8, -30, 13, 26, -57, -17, 40, 35, 52, -48, -57, -69, 35, -61, -36, -26, -93, 59, 20, 28, -51, -77, -14, 60, -10, 6, -78, -34, -114, 18, 3, -4, -80, -49, 36, 47, 43, 22, -80, 1, 51, 48, -6, 25, -14, 20, -31, 59, -11, 26, -41, -72, 37, 14, 29, -37, 16, 25, 24, 8}

#define TENSOR_DENSE_1_KERNEL_0_DEC_BITS {8}

#define TENSOR_DENSE_1_BIAS_0 {11, 11, -18, -31, -12, 3, -56, -2, 72, 4}

#define TENSOR_DENSE_1_BIAS_0_DEC_BITS {9}

#define DENSE_1_BIAS_LSHIFT {1}

#define DENSE_1_OUTPUT_RSHIFT {8}


/* output q format for each layer */
#define INPUT_1_OUTPUT_DEC 7
#define INPUT_1_OUTPUT_OFFSET 17
#define CONV2D_OUTPUT_DEC 7
#define CONV2D_OUTPUT_OFFSET -2
#define RE_LU_OUTPUT_DEC 7
#define RE_LU_OUTPUT_OFFSET 0
#define MAX_POOLING2D_OUTPUT_DEC 7
#define MAX_POOLING2D_OUTPUT_OFFSET 0
#define CONV2D_4_OUTPUT_DEC 6
#define CONV2D_4_OUTPUT_OFFSET -6
#define CONV2D_3_OUTPUT_DEC 6
#define CONV2D_3_OUTPUT_OFFSET 0
#define CONV2D_2_OUTPUT_DEC 6
#define CONV2D_2_OUTPUT_OFFSET -3
#define MAX_POOLING2D_1_OUTPUT_DEC 6
#define MAX_POOLING2D_1_OUTPUT_OFFSET 0
#define CONV2D_1_OUTPUT_DEC 6
#define CONV2D_1_OUTPUT_OFFSET -3
#define UP_SAMPLING2D_OUTPUT_DEC 6
#define UP_SAMPLING2D_OUTPUT_OFFSET 0
#define ADD_1_OUTPUT_DEC 5
#define ADD_1_OUTPUT_OFFSET -3
#define ADD_OUTPUT_DEC 6
#define ADD_OUTPUT_OFFSET -3
#define MAX_POOLING2D_3_OUTPUT_DEC 6
#define MAX_POOLING2D_3_OUTPUT_OFFSET 0
#define MAX_POOLING2D_2_OUTPUT_DEC 6
#define MAX_POOLING2D_2_OUTPUT_OFFSET 0
#define CONV2D_7_OUTPUT_DEC 5
#define CONV2D_7_OUTPUT_OFFSET -5
#define CONV2D_5_OUTPUT_DEC 5
#define CONV2D_5_OUTPUT_OFFSET -2
#define UP_SAMPLING2D_1_OUTPUT_DEC 5
#define UP_SAMPLING2D_1_OUTPUT_OFFSET 0
#define CONV2D_8_OUTPUT_DEC 5
#define CONV2D_8_OUTPUT_OFFSET -1
#define ADD_2_OUTPUT_DEC 5
#define ADD_2_OUTPUT_OFFSET -7
#define CONV2D_6_OUTPUT_DEC 5
#define CONV2D_6_OUTPUT_OFFSET -5
#define MAX_POOLING2D_4_OUTPUT_DEC 5
#define MAX_POOLING2D_4_OUTPUT_OFFSET 0
#define MAX_POOLING2D_5_OUTPUT_DEC 5
#define MAX_POOLING2D_5_OUTPUT_OFFSET 0
#define ADD_3_OUTPUT_DEC 5
#define ADD_3_OUTPUT_OFFSET 3
#define CONCATENATE_OUTPUT_DEC 5
#define CONCATENATE_OUTPUT_OFFSET 0
#define CONV2D_9_OUTPUT_DEC 4
#define CONV2D_9_OUTPUT_OFFSET 1
#define FLATTEN_OUTPUT_DEC 4
#define FLATTEN_OUTPUT_OFFSET 0
#define DENSE_OUTPUT_DEC 2
#define DENSE_OUTPUT_OFFSET -9
#define DROPOUT_OUTPUT_DEC 2
#define DROPOUT_OUTPUT_OFFSET 0
#define RE_LU_1_OUTPUT_DEC 2
#define RE_LU_1_OUTPUT_OFFSET 0
#define DENSE_1_OUTPUT_DEC 2
#define DENSE_1_OUTPUT_OFFSET -10
#define SOFTMAX_OUTPUT_DEC 7
#define SOFTMAX_OUTPUT_OFFSET 13

/* bias shift and output shift for none-weighted layer */
#define ADD_1_OUTPUT_RSHIFT (CONV2D_2_OUTPUT_DEC-ADD_1_OUTPUT_DEC)
#if ADD_1_OUTPUT_RSHIFT < 0
#error ADD_1_OUTPUT_RSHIFT must be bigger than 0
#endif
#define ADD_OUTPUT_RSHIFT (CONV2D_1_OUTPUT_DEC-ADD_OUTPUT_DEC)
#if ADD_OUTPUT_RSHIFT < 0
#error ADD_OUTPUT_RSHIFT must be bigger than 0
#endif
#define ADD_2_OUTPUT_RSHIFT (CONV2D_5_OUTPUT_DEC-ADD_2_OUTPUT_DEC)
#if ADD_2_OUTPUT_RSHIFT < 0
#error ADD_2_OUTPUT_RSHIFT must be bigger than 0
#endif
#define ADD_3_OUTPUT_RSHIFT (CONV2D_6_OUTPUT_DEC-ADD_3_OUTPUT_DEC)
#if ADD_3_OUTPUT_RSHIFT < 0
#error ADD_3_OUTPUT_RSHIFT must be bigger than 0
#endif

/* tensors and configurations for each layer */
static int8_t nnom_input_data[784] = {0};

const nnom_shape_data_t tensor_input_1_0_dim[] = {28, 28, 1};
const nnom_qformat_param_t tensor_input_1_0_dec[] = {7};
const nnom_qformat_param_t tensor_input_1_0_offset[] = {0};
const nnom_tensor_t tensor_input_1_0 = {
    .p_data = (void*)nnom_input_data,
    .dim = (nnom_shape_data_t*)tensor_input_1_0_dim,
    .q_dec = (nnom_qformat_param_t*)tensor_input_1_0_dec,
    .q_offset = (nnom_qformat_param_t*)tensor_input_1_0_offset,
    .qtype = NNOM_QTYPE_PER_TENSOR,
    .num_dim = 3,
    .bitwidth = 8
};

const nnom_io_config_t input_1_config = {
    .super = {.name = "input_1"},
    .tensor = (nnom_tensor_t*)&tensor_input_1_0
};
const int8_t tensor_conv2d_kernel_0_data[] = TENSOR_CONV2D_KERNEL_0;

const nnom_shape_data_t tensor_conv2d_kernel_0_dim[] = {3, 3, 1, 12};
const nnom_qformat_param_t tensor_conv2d_kernel_0_dec[] = TENSOR_CONV2D_KERNEL_0_DEC_BITS;
const nnom_qformat_param_t tensor_conv2d_kernel_0_offset[] = {0};
const nnom_tensor_t tensor_conv2d_kernel_0 = {
    .p_data = (void*)tensor_conv2d_kernel_0_data,
    .dim = (nnom_shape_data_t*)tensor_conv2d_kernel_0_dim,
    .q_dec = (nnom_qformat_param_t*)tensor_conv2d_kernel_0_dec,
    .q_offset = (nnom_qformat_param_t*)tensor_conv2d_kernel_0_offset,
    .qtype = NNOM_QTYPE_PER_TENSOR,
    .num_dim = 4,
    .bitwidth = 8
};
const int8_t tensor_conv2d_bias_0_data[] = TENSOR_CONV2D_BIAS_0;

const nnom_shape_data_t tensor_conv2d_bias_0_dim[] = {12};
const nnom_qformat_param_t tensor_conv2d_bias_0_dec[] = TENSOR_CONV2D_BIAS_0_DEC_BITS;
const nnom_qformat_param_t tensor_conv2d_bias_0_offset[] = {0};
const nnom_tensor_t tensor_conv2d_bias_0 = {
    .p_data = (void*)tensor_conv2d_bias_0_data,
    .dim = (nnom_shape_data_t*)tensor_conv2d_bias_0_dim,
    .q_dec = (nnom_qformat_param_t*)tensor_conv2d_bias_0_dec,
    .q_offset = (nnom_qformat_param_t*)tensor_conv2d_bias_0_offset,
    .qtype = NNOM_QTYPE_PER_TENSOR,
    .num_dim = 1,
    .bitwidth = 8
};

const nnom_qformat_param_t conv2d_output_shift[] = CONV2D_OUTPUT_RSHIFT;
const nnom_qformat_param_t conv2d_bias_shift[] = CONV2D_BIAS_LSHIFT;
const nnom_conv2d_config_t conv2d_config = {
    .super = {.name = "conv2d"},
    .qtype = NNOM_QTYPE_PER_TENSOR,
    .weight = (nnom_tensor_t*)&tensor_conv2d_kernel_0,
    .bias = (nnom_tensor_t*)&tensor_conv2d_bias_0,
    .output_shift = (nnom_qformat_param_t *)&conv2d_output_shift, 
    .bias_shift = (nnom_qformat_param_t *)&conv2d_bias_shift, 
    .filter_size = 12,
    .kernel_size = {3, 3},
    .stride_size = {1, 1},
    .padding_size = {0, 0},
    .dilation_size = {1, 1},
    .padding_type = PADDING_SAME
};

const nnom_pool_config_t max_pooling2d_config = {
    .super = {.name = "max_pooling2d"},
    .padding_type = PADDING_SAME,
    .output_shift = 0,
    .kernel_size = {2, 2},
    .stride_size = {2, 2},
    .num_dim = 2
};
const int8_t tensor_conv2d_4_kernel_0_data[] = TENSOR_CONV2D_4_KERNEL_0;

const nnom_shape_data_t tensor_conv2d_4_kernel_0_dim[] = {3, 3, 12, 12};
const nnom_qformat_param_t tensor_conv2d_4_kernel_0_dec[] = TENSOR_CONV2D_4_KERNEL_0_DEC_BITS;
const nnom_qformat_param_t tensor_conv2d_4_kernel_0_offset[] = {0};
const nnom_tensor_t tensor_conv2d_4_kernel_0 = {
    .p_data = (void*)tensor_conv2d_4_kernel_0_data,
    .dim = (nnom_shape_data_t*)tensor_conv2d_4_kernel_0_dim,
    .q_dec = (nnom_qformat_param_t*)tensor_conv2d_4_kernel_0_dec,
    .q_offset = (nnom_qformat_param_t*)tensor_conv2d_4_kernel_0_offset,
    .qtype = NNOM_QTYPE_PER_TENSOR,
    .num_dim = 4,
    .bitwidth = 8
};
const int8_t tensor_conv2d_4_bias_0_data[] = TENSOR_CONV2D_4_BIAS_0;

const nnom_shape_data_t tensor_conv2d_4_bias_0_dim[] = {12};
const nnom_qformat_param_t tensor_conv2d_4_bias_0_dec[] = TENSOR_CONV2D_4_BIAS_0_DEC_BITS;
const nnom_qformat_param_t tensor_conv2d_4_bias_0_offset[] = {0};
const nnom_tensor_t tensor_conv2d_4_bias_0 = {
    .p_data = (void*)tensor_conv2d_4_bias_0_data,
    .dim = (nnom_shape_data_t*)tensor_conv2d_4_bias_0_dim,
    .q_dec = (nnom_qformat_param_t*)tensor_conv2d_4_bias_0_dec,
    .q_offset = (nnom_qformat_param_t*)tensor_conv2d_4_bias_0_offset,
    .qtype = NNOM_QTYPE_PER_TENSOR,
    .num_dim = 1,
    .bitwidth = 8
};

const nnom_qformat_param_t conv2d_4_output_shift[] = CONV2D_4_OUTPUT_RSHIFT;
const nnom_qformat_param_t conv2d_4_bias_shift[] = CONV2D_4_BIAS_LSHIFT;
const nnom_conv2d_config_t conv2d_4_config = {
    .super = {.name = "conv2d_4"},
    .qtype = NNOM_QTYPE_PER_TENSOR,
    .weight = (nnom_tensor_t*)&tensor_conv2d_4_kernel_0,
    .bias = (nnom_tensor_t*)&tensor_conv2d_4_bias_0,
    .output_shift = (nnom_qformat_param_t *)&conv2d_4_output_shift, 
    .bias_shift = (nnom_qformat_param_t *)&conv2d_4_bias_shift, 
    .filter_size = 12,
    .kernel_size = {3, 3},
    .stride_size = {1, 1},
    .padding_size = {0, 0},
    .dilation_size = {1, 1},
    .padding_type = PADDING_SAME
};
const int8_t tensor_conv2d_3_kernel_0_data[] = TENSOR_CONV2D_3_KERNEL_0;

const nnom_shape_data_t tensor_conv2d_3_kernel_0_dim[] = {3, 3, 12, 12};
const nnom_qformat_param_t tensor_conv2d_3_kernel_0_dec[] = TENSOR_CONV2D_3_KERNEL_0_DEC_BITS;
const nnom_qformat_param_t tensor_conv2d_3_kernel_0_offset[] = {0};
const nnom_tensor_t tensor_conv2d_3_kernel_0 = {
    .p_data = (void*)tensor_conv2d_3_kernel_0_data,
    .dim = (nnom_shape_data_t*)tensor_conv2d_3_kernel_0_dim,
    .q_dec = (nnom_qformat_param_t*)tensor_conv2d_3_kernel_0_dec,
    .q_offset = (nnom_qformat_param_t*)tensor_conv2d_3_kernel_0_offset,
    .qtype = NNOM_QTYPE_PER_TENSOR,
    .num_dim = 4,
    .bitwidth = 8
};
const int8_t tensor_conv2d_3_bias_0_data[] = TENSOR_CONV2D_3_BIAS_0;

const nnom_shape_data_t tensor_conv2d_3_bias_0_dim[] = {12};
const nnom_qformat_param_t tensor_conv2d_3_bias_0_dec[] = TENSOR_CONV2D_3_BIAS_0_DEC_BITS;
const nnom_qformat_param_t tensor_conv2d_3_bias_0_offset[] = {0};
const nnom_tensor_t tensor_conv2d_3_bias_0 = {
    .p_data = (void*)tensor_conv2d_3_bias_0_data,
    .dim = (nnom_shape_data_t*)tensor_conv2d_3_bias_0_dim,
    .q_dec = (nnom_qformat_param_t*)tensor_conv2d_3_bias_0_dec,
    .q_offset = (nnom_qformat_param_t*)tensor_conv2d_3_bias_0_offset,
    .qtype = NNOM_QTYPE_PER_TENSOR,
    .num_dim = 1,
    .bitwidth = 8
};

const nnom_qformat_param_t conv2d_3_output_shift[] = CONV2D_3_OUTPUT_RSHIFT;
const nnom_qformat_param_t conv2d_3_bias_shift[] = CONV2D_3_BIAS_LSHIFT;
const nnom_conv2d_config_t conv2d_3_config = {
    .super = {.name = "conv2d_3"},
    .qtype = NNOM_QTYPE_PER_TENSOR,
    .weight = (nnom_tensor_t*)&tensor_conv2d_3_kernel_0,
    .bias = (nnom_tensor_t*)&tensor_conv2d_3_bias_0,
    .output_shift = (nnom_qformat_param_t *)&conv2d_3_output_shift, 
    .bias_shift = (nnom_qformat_param_t *)&conv2d_3_bias_shift, 
    .filter_size = 12,
    .kernel_size = {3, 3},
    .stride_size = {1, 1},
    .padding_size = {0, 0},
    .dilation_size = {1, 1},
    .padding_type = PADDING_SAME
};
const int8_t tensor_conv2d_2_kernel_0_data[] = TENSOR_CONV2D_2_KERNEL_0;

const nnom_shape_data_t tensor_conv2d_2_kernel_0_dim[] = {3, 3, 12, 12};
const nnom_qformat_param_t tensor_conv2d_2_kernel_0_dec[] = TENSOR_CONV2D_2_KERNEL_0_DEC_BITS;
const nnom_qformat_param_t tensor_conv2d_2_kernel_0_offset[] = {0};
const nnom_tensor_t tensor_conv2d_2_kernel_0 = {
    .p_data = (void*)tensor_conv2d_2_kernel_0_data,
    .dim = (nnom_shape_data_t*)tensor_conv2d_2_kernel_0_dim,
    .q_dec = (nnom_qformat_param_t*)tensor_conv2d_2_kernel_0_dec,
    .q_offset = (nnom_qformat_param_t*)tensor_conv2d_2_kernel_0_offset,
    .qtype = NNOM_QTYPE_PER_TENSOR,
    .num_dim = 4,
    .bitwidth = 8
};
const int8_t tensor_conv2d_2_bias_0_data[] = TENSOR_CONV2D_2_BIAS_0;

const nnom_shape_data_t tensor_conv2d_2_bias_0_dim[] = {12};
const nnom_qformat_param_t tensor_conv2d_2_bias_0_dec[] = TENSOR_CONV2D_2_BIAS_0_DEC_BITS;
const nnom_qformat_param_t tensor_conv2d_2_bias_0_offset[] = {0};
const nnom_tensor_t tensor_conv2d_2_bias_0 = {
    .p_data = (void*)tensor_conv2d_2_bias_0_data,
    .dim = (nnom_shape_data_t*)tensor_conv2d_2_bias_0_dim,
    .q_dec = (nnom_qformat_param_t*)tensor_conv2d_2_bias_0_dec,
    .q_offset = (nnom_qformat_param_t*)tensor_conv2d_2_bias_0_offset,
    .qtype = NNOM_QTYPE_PER_TENSOR,
    .num_dim = 1,
    .bitwidth = 8
};

const nnom_qformat_param_t conv2d_2_output_shift[] = CONV2D_2_OUTPUT_RSHIFT;
const nnom_qformat_param_t conv2d_2_bias_shift[] = CONV2D_2_BIAS_LSHIFT;
const nnom_conv2d_config_t conv2d_2_config = {
    .super = {.name = "conv2d_2"},
    .qtype = NNOM_QTYPE_PER_TENSOR,
    .weight = (nnom_tensor_t*)&tensor_conv2d_2_kernel_0,
    .bias = (nnom_tensor_t*)&tensor_conv2d_2_bias_0,
    .output_shift = (nnom_qformat_param_t *)&conv2d_2_output_shift, 
    .bias_shift = (nnom_qformat_param_t *)&conv2d_2_bias_shift, 
    .filter_size = 12,
    .kernel_size = {3, 3},
    .stride_size = {1, 1},
    .padding_size = {0, 0},
    .dilation_size = {1, 1},
    .padding_type = PADDING_SAME
};

const nnom_pool_config_t max_pooling2d_1_config = {
    .super = {.name = "max_pooling2d_1"},
    .padding_type = PADDING_SAME,
    .output_shift = 0,
    .kernel_size = {2, 2},
    .stride_size = {2, 2},
    .num_dim = 2
};
const int8_t tensor_conv2d_1_kernel_0_data[] = TENSOR_CONV2D_1_KERNEL_0;

const nnom_shape_data_t tensor_conv2d_1_kernel_0_dim[] = {3, 3, 12, 12};
const nnom_qformat_param_t tensor_conv2d_1_kernel_0_dec[] = TENSOR_CONV2D_1_KERNEL_0_DEC_BITS;
const nnom_qformat_param_t tensor_conv2d_1_kernel_0_offset[] = {0};
const nnom_tensor_t tensor_conv2d_1_kernel_0 = {
    .p_data = (void*)tensor_conv2d_1_kernel_0_data,
    .dim = (nnom_shape_data_t*)tensor_conv2d_1_kernel_0_dim,
    .q_dec = (nnom_qformat_param_t*)tensor_conv2d_1_kernel_0_dec,
    .q_offset = (nnom_qformat_param_t*)tensor_conv2d_1_kernel_0_offset,
    .qtype = NNOM_QTYPE_PER_TENSOR,
    .num_dim = 4,
    .bitwidth = 8
};
const int8_t tensor_conv2d_1_bias_0_data[] = TENSOR_CONV2D_1_BIAS_0;

const nnom_shape_data_t tensor_conv2d_1_bias_0_dim[] = {12};
const nnom_qformat_param_t tensor_conv2d_1_bias_0_dec[] = TENSOR_CONV2D_1_BIAS_0_DEC_BITS;
const nnom_qformat_param_t tensor_conv2d_1_bias_0_offset[] = {0};
const nnom_tensor_t tensor_conv2d_1_bias_0 = {
    .p_data = (void*)tensor_conv2d_1_bias_0_data,
    .dim = (nnom_shape_data_t*)tensor_conv2d_1_bias_0_dim,
    .q_dec = (nnom_qformat_param_t*)tensor_conv2d_1_bias_0_dec,
    .q_offset = (nnom_qformat_param_t*)tensor_conv2d_1_bias_0_offset,
    .qtype = NNOM_QTYPE_PER_TENSOR,
    .num_dim = 1,
    .bitwidth = 8
};

const nnom_qformat_param_t conv2d_1_output_shift[] = CONV2D_1_OUTPUT_RSHIFT;
const nnom_qformat_param_t conv2d_1_bias_shift[] = CONV2D_1_BIAS_LSHIFT;
const nnom_conv2d_config_t conv2d_1_config = {
    .super = {.name = "conv2d_1"},
    .qtype = NNOM_QTYPE_PER_TENSOR,
    .weight = (nnom_tensor_t*)&tensor_conv2d_1_kernel_0,
    .bias = (nnom_tensor_t*)&tensor_conv2d_1_bias_0,
    .output_shift = (nnom_qformat_param_t *)&conv2d_1_output_shift, 
    .bias_shift = (nnom_qformat_param_t *)&conv2d_1_bias_shift, 
    .filter_size = 12,
    .kernel_size = {3, 3},
    .stride_size = {1, 1},
    .padding_size = {0, 0},
    .dilation_size = {1, 1},
    .padding_type = PADDING_SAME
};

const nnom_upsample_config_t up_sampling2d_config = {
    .super = {.name = "up_sampling2d"},
    .kernel = {2, 2} 
};

const nnom_matrix_config_t add_1_config = {
    .super = {.name = "add_1"},
    .output_shift = ADD_1_OUTPUT_RSHIFT 
};

const nnom_matrix_config_t add_config = {
    .super = {.name = "add"},
    .output_shift = ADD_OUTPUT_RSHIFT 
};

const nnom_pool_config_t max_pooling2d_3_config = {
    .super = {.name = "max_pooling2d_3"},
    .padding_type = PADDING_VALID,
    .output_shift = 0,
    .kernel_size = {2, 2},
    .stride_size = {2, 2},
    .num_dim = 2
};

const nnom_pool_config_t max_pooling2d_2_config = {
    .super = {.name = "max_pooling2d_2"},
    .padding_type = PADDING_VALID,
    .output_shift = 0,
    .kernel_size = {2, 2},
    .stride_size = {2, 2},
    .num_dim = 2
};
const int8_t tensor_conv2d_7_kernel_0_data[] = TENSOR_CONV2D_7_KERNEL_0;

const nnom_shape_data_t tensor_conv2d_7_kernel_0_dim[] = {3, 3, 12, 12};
const nnom_qformat_param_t tensor_conv2d_7_kernel_0_dec[] = TENSOR_CONV2D_7_KERNEL_0_DEC_BITS;
const nnom_qformat_param_t tensor_conv2d_7_kernel_0_offset[] = {0};
const nnom_tensor_t tensor_conv2d_7_kernel_0 = {
    .p_data = (void*)tensor_conv2d_7_kernel_0_data,
    .dim = (nnom_shape_data_t*)tensor_conv2d_7_kernel_0_dim,
    .q_dec = (nnom_qformat_param_t*)tensor_conv2d_7_kernel_0_dec,
    .q_offset = (nnom_qformat_param_t*)tensor_conv2d_7_kernel_0_offset,
    .qtype = NNOM_QTYPE_PER_TENSOR,
    .num_dim = 4,
    .bitwidth = 8
};
const int8_t tensor_conv2d_7_bias_0_data[] = TENSOR_CONV2D_7_BIAS_0;

const nnom_shape_data_t tensor_conv2d_7_bias_0_dim[] = {12};
const nnom_qformat_param_t tensor_conv2d_7_bias_0_dec[] = TENSOR_CONV2D_7_BIAS_0_DEC_BITS;
const nnom_qformat_param_t tensor_conv2d_7_bias_0_offset[] = {0};
const nnom_tensor_t tensor_conv2d_7_bias_0 = {
    .p_data = (void*)tensor_conv2d_7_bias_0_data,
    .dim = (nnom_shape_data_t*)tensor_conv2d_7_bias_0_dim,
    .q_dec = (nnom_qformat_param_t*)tensor_conv2d_7_bias_0_dec,
    .q_offset = (nnom_qformat_param_t*)tensor_conv2d_7_bias_0_offset,
    .qtype = NNOM_QTYPE_PER_TENSOR,
    .num_dim = 1,
    .bitwidth = 8
};

const nnom_qformat_param_t conv2d_7_output_shift[] = CONV2D_7_OUTPUT_RSHIFT;
const nnom_qformat_param_t conv2d_7_bias_shift[] = CONV2D_7_BIAS_LSHIFT;
const nnom_conv2d_config_t conv2d_7_config = {
    .super = {.name = "conv2d_7"},
    .qtype = NNOM_QTYPE_PER_TENSOR,
    .weight = (nnom_tensor_t*)&tensor_conv2d_7_kernel_0,
    .bias = (nnom_tensor_t*)&tensor_conv2d_7_bias_0,
    .output_shift = (nnom_qformat_param_t *)&conv2d_7_output_shift, 
    .bias_shift = (nnom_qformat_param_t *)&conv2d_7_bias_shift, 
    .filter_size = 12,
    .kernel_size = {3, 3},
    .stride_size = {1, 1},
    .padding_size = {0, 0},
    .dilation_size = {1, 1},
    .padding_type = PADDING_SAME
};
const int8_t tensor_conv2d_5_kernel_0_data[] = TENSOR_CONV2D_5_KERNEL_0;

const nnom_shape_data_t tensor_conv2d_5_kernel_0_dim[] = {3, 3, 12, 12};
const nnom_qformat_param_t tensor_conv2d_5_kernel_0_dec[] = TENSOR_CONV2D_5_KERNEL_0_DEC_BITS;
const nnom_qformat_param_t tensor_conv2d_5_kernel_0_offset[] = {0};
const nnom_tensor_t tensor_conv2d_5_kernel_0 = {
    .p_data = (void*)tensor_conv2d_5_kernel_0_data,
    .dim = (nnom_shape_data_t*)tensor_conv2d_5_kernel_0_dim,
    .q_dec = (nnom_qformat_param_t*)tensor_conv2d_5_kernel_0_dec,
    .q_offset = (nnom_qformat_param_t*)tensor_conv2d_5_kernel_0_offset,
    .qtype = NNOM_QTYPE_PER_TENSOR,
    .num_dim = 4,
    .bitwidth = 8
};
const int8_t tensor_conv2d_5_bias_0_data[] = TENSOR_CONV2D_5_BIAS_0;

const nnom_shape_data_t tensor_conv2d_5_bias_0_dim[] = {12};
const nnom_qformat_param_t tensor_conv2d_5_bias_0_dec[] = TENSOR_CONV2D_5_BIAS_0_DEC_BITS;
const nnom_qformat_param_t tensor_conv2d_5_bias_0_offset[] = {0};
const nnom_tensor_t tensor_conv2d_5_bias_0 = {
    .p_data = (void*)tensor_conv2d_5_bias_0_data,
    .dim = (nnom_shape_data_t*)tensor_conv2d_5_bias_0_dim,
    .q_dec = (nnom_qformat_param_t*)tensor_conv2d_5_bias_0_dec,
    .q_offset = (nnom_qformat_param_t*)tensor_conv2d_5_bias_0_offset,
    .qtype = NNOM_QTYPE_PER_TENSOR,
    .num_dim = 1,
    .bitwidth = 8
};

const nnom_qformat_param_t conv2d_5_output_shift[] = CONV2D_5_OUTPUT_RSHIFT;
const nnom_qformat_param_t conv2d_5_bias_shift[] = CONV2D_5_BIAS_LSHIFT;
const nnom_conv2d_config_t conv2d_5_config = {
    .super = {.name = "conv2d_5"},
    .qtype = NNOM_QTYPE_PER_TENSOR,
    .weight = (nnom_tensor_t*)&tensor_conv2d_5_kernel_0,
    .bias = (nnom_tensor_t*)&tensor_conv2d_5_bias_0,
    .output_shift = (nnom_qformat_param_t *)&conv2d_5_output_shift, 
    .bias_shift = (nnom_qformat_param_t *)&conv2d_5_bias_shift, 
    .filter_size = 12,
    .kernel_size = {3, 3},
    .stride_size = {1, 1},
    .padding_size = {0, 0},
    .dilation_size = {1, 1},
    .padding_type = PADDING_SAME
};

const nnom_upsample_config_t up_sampling2d_1_config = {
    .super = {.name = "up_sampling2d_1"},
    .kernel = {2, 2} 
};
const int8_t tensor_conv2d_8_kernel_0_data[] = TENSOR_CONV2D_8_KERNEL_0;

const nnom_shape_data_t tensor_conv2d_8_kernel_0_dim[] = {3, 3, 12, 12};
const nnom_qformat_param_t tensor_conv2d_8_kernel_0_dec[] = TENSOR_CONV2D_8_KERNEL_0_DEC_BITS;
const nnom_qformat_param_t tensor_conv2d_8_kernel_0_offset[] = {0};
const nnom_tensor_t tensor_conv2d_8_kernel_0 = {
    .p_data = (void*)tensor_conv2d_8_kernel_0_data,
    .dim = (nnom_shape_data_t*)tensor_conv2d_8_kernel_0_dim,
    .q_dec = (nnom_qformat_param_t*)tensor_conv2d_8_kernel_0_dec,
    .q_offset = (nnom_qformat_param_t*)tensor_conv2d_8_kernel_0_offset,
    .qtype = NNOM_QTYPE_PER_TENSOR,
    .num_dim = 4,
    .bitwidth = 8
};
const int8_t tensor_conv2d_8_bias_0_data[] = TENSOR_CONV2D_8_BIAS_0;

const nnom_shape_data_t tensor_conv2d_8_bias_0_dim[] = {12};
const nnom_qformat_param_t tensor_conv2d_8_bias_0_dec[] = TENSOR_CONV2D_8_BIAS_0_DEC_BITS;
const nnom_qformat_param_t tensor_conv2d_8_bias_0_offset[] = {0};
const nnom_tensor_t tensor_conv2d_8_bias_0 = {
    .p_data = (void*)tensor_conv2d_8_bias_0_data,
    .dim = (nnom_shape_data_t*)tensor_conv2d_8_bias_0_dim,
    .q_dec = (nnom_qformat_param_t*)tensor_conv2d_8_bias_0_dec,
    .q_offset = (nnom_qformat_param_t*)tensor_conv2d_8_bias_0_offset,
    .qtype = NNOM_QTYPE_PER_TENSOR,
    .num_dim = 1,
    .bitwidth = 8
};

const nnom_qformat_param_t conv2d_8_output_shift[] = CONV2D_8_OUTPUT_RSHIFT;
const nnom_qformat_param_t conv2d_8_bias_shift[] = CONV2D_8_BIAS_LSHIFT;
const nnom_conv2d_config_t conv2d_8_config = {
    .super = {.name = "conv2d_8"},
    .qtype = NNOM_QTYPE_PER_TENSOR,
    .weight = (nnom_tensor_t*)&tensor_conv2d_8_kernel_0,
    .bias = (nnom_tensor_t*)&tensor_conv2d_8_bias_0,
    .output_shift = (nnom_qformat_param_t *)&conv2d_8_output_shift, 
    .bias_shift = (nnom_qformat_param_t *)&conv2d_8_bias_shift, 
    .filter_size = 12,
    .kernel_size = {3, 3},
    .stride_size = {1, 1},
    .padding_size = {0, 0},
    .dilation_size = {1, 1},
    .padding_type = PADDING_SAME
};

const nnom_matrix_config_t add_2_config = {
    .super = {.name = "add_2"},
    .output_shift = ADD_2_OUTPUT_RSHIFT 
};
const int8_t tensor_conv2d_6_kernel_0_data[] = TENSOR_CONV2D_6_KERNEL_0;

const nnom_shape_data_t tensor_conv2d_6_kernel_0_dim[] = {3, 3, 12, 12};
const nnom_qformat_param_t tensor_conv2d_6_kernel_0_dec[] = TENSOR_CONV2D_6_KERNEL_0_DEC_BITS;
const nnom_qformat_param_t tensor_conv2d_6_kernel_0_offset[] = {0};
const nnom_tensor_t tensor_conv2d_6_kernel_0 = {
    .p_data = (void*)tensor_conv2d_6_kernel_0_data,
    .dim = (nnom_shape_data_t*)tensor_conv2d_6_kernel_0_dim,
    .q_dec = (nnom_qformat_param_t*)tensor_conv2d_6_kernel_0_dec,
    .q_offset = (nnom_qformat_param_t*)tensor_conv2d_6_kernel_0_offset,
    .qtype = NNOM_QTYPE_PER_TENSOR,
    .num_dim = 4,
    .bitwidth = 8
};
const int8_t tensor_conv2d_6_bias_0_data[] = TENSOR_CONV2D_6_BIAS_0;

const nnom_shape_data_t tensor_conv2d_6_bias_0_dim[] = {12};
const nnom_qformat_param_t tensor_conv2d_6_bias_0_dec[] = TENSOR_CONV2D_6_BIAS_0_DEC_BITS;
const nnom_qformat_param_t tensor_conv2d_6_bias_0_offset[] = {0};
const nnom_tensor_t tensor_conv2d_6_bias_0 = {
    .p_data = (void*)tensor_conv2d_6_bias_0_data,
    .dim = (nnom_shape_data_t*)tensor_conv2d_6_bias_0_dim,
    .q_dec = (nnom_qformat_param_t*)tensor_conv2d_6_bias_0_dec,
    .q_offset = (nnom_qformat_param_t*)tensor_conv2d_6_bias_0_offset,
    .qtype = NNOM_QTYPE_PER_TENSOR,
    .num_dim = 1,
    .bitwidth = 8
};

const nnom_qformat_param_t conv2d_6_output_shift[] = CONV2D_6_OUTPUT_RSHIFT;
const nnom_qformat_param_t conv2d_6_bias_shift[] = CONV2D_6_BIAS_LSHIFT;
const nnom_conv2d_config_t conv2d_6_config = {
    .super = {.name = "conv2d_6"},
    .qtype = NNOM_QTYPE_PER_TENSOR,
    .weight = (nnom_tensor_t*)&tensor_conv2d_6_kernel_0,
    .bias = (nnom_tensor_t*)&tensor_conv2d_6_bias_0,
    .output_shift = (nnom_qformat_param_t *)&conv2d_6_output_shift, 
    .bias_shift = (nnom_qformat_param_t *)&conv2d_6_bias_shift, 
    .filter_size = 12,
    .kernel_size = {3, 3},
    .stride_size = {1, 1},
    .padding_size = {0, 0},
    .dilation_size = {1, 1},
    .padding_type = PADDING_SAME
};

const nnom_pool_config_t max_pooling2d_4_config = {
    .super = {.name = "max_pooling2d_4"},
    .padding_type = PADDING_SAME,
    .output_shift = 0,
    .kernel_size = {2, 2},
    .stride_size = {2, 2},
    .num_dim = 2
};

const nnom_pool_config_t max_pooling2d_5_config = {
    .super = {.name = "max_pooling2d_5"},
    .padding_type = PADDING_VALID,
    .output_shift = 0,
    .kernel_size = {2, 2},
    .stride_size = {2, 2},
    .num_dim = 2
};

const nnom_matrix_config_t add_3_config = {
    .super = {.name = "add_3"},
    .output_shift = ADD_3_OUTPUT_RSHIFT 
};

const nnom_concat_config_t concatenate_config = {
    .super = {.name = "concatenate"},
    .axis = -1
};
const int8_t tensor_conv2d_9_kernel_0_data[] = TENSOR_CONV2D_9_KERNEL_0;

const nnom_shape_data_t tensor_conv2d_9_kernel_0_dim[] = {3, 3, 24, 12};
const nnom_qformat_param_t tensor_conv2d_9_kernel_0_dec[] = TENSOR_CONV2D_9_KERNEL_0_DEC_BITS;
const nnom_qformat_param_t tensor_conv2d_9_kernel_0_offset[] = {0};
const nnom_tensor_t tensor_conv2d_9_kernel_0 = {
    .p_data = (void*)tensor_conv2d_9_kernel_0_data,
    .dim = (nnom_shape_data_t*)tensor_conv2d_9_kernel_0_dim,
    .q_dec = (nnom_qformat_param_t*)tensor_conv2d_9_kernel_0_dec,
    .q_offset = (nnom_qformat_param_t*)tensor_conv2d_9_kernel_0_offset,
    .qtype = NNOM_QTYPE_PER_TENSOR,
    .num_dim = 4,
    .bitwidth = 8
};
const int8_t tensor_conv2d_9_bias_0_data[] = TENSOR_CONV2D_9_BIAS_0;

const nnom_shape_data_t tensor_conv2d_9_bias_0_dim[] = {12};
const nnom_qformat_param_t tensor_conv2d_9_bias_0_dec[] = TENSOR_CONV2D_9_BIAS_0_DEC_BITS;
const nnom_qformat_param_t tensor_conv2d_9_bias_0_offset[] = {0};
const nnom_tensor_t tensor_conv2d_9_bias_0 = {
    .p_data = (void*)tensor_conv2d_9_bias_0_data,
    .dim = (nnom_shape_data_t*)tensor_conv2d_9_bias_0_dim,
    .q_dec = (nnom_qformat_param_t*)tensor_conv2d_9_bias_0_dec,
    .q_offset = (nnom_qformat_param_t*)tensor_conv2d_9_bias_0_offset,
    .qtype = NNOM_QTYPE_PER_TENSOR,
    .num_dim = 1,
    .bitwidth = 8
};

const nnom_qformat_param_t conv2d_9_output_shift[] = CONV2D_9_OUTPUT_RSHIFT;
const nnom_qformat_param_t conv2d_9_bias_shift[] = CONV2D_9_BIAS_LSHIFT;
const nnom_conv2d_config_t conv2d_9_config = {
    .super = {.name = "conv2d_9"},
    .qtype = NNOM_QTYPE_PER_TENSOR,
    .weight = (nnom_tensor_t*)&tensor_conv2d_9_kernel_0,
    .bias = (nnom_tensor_t*)&tensor_conv2d_9_bias_0,
    .output_shift = (nnom_qformat_param_t *)&conv2d_9_output_shift, 
    .bias_shift = (nnom_qformat_param_t *)&conv2d_9_bias_shift, 
    .filter_size = 12,
    .kernel_size = {3, 3},
    .stride_size = {1, 1},
    .padding_size = {0, 0},
    .dilation_size = {1, 1},
    .padding_type = PADDING_VALID
};

const nnom_flatten_config_t flatten_config = {
    .super = {.name = "flatten"}
};
const int8_t tensor_dense_kernel_0_data[] = TENSOR_DENSE_KERNEL_0;

const nnom_shape_data_t tensor_dense_kernel_0_dim[] = {300, 96};
const nnom_qformat_param_t tensor_dense_kernel_0_dec[] = TENSOR_DENSE_KERNEL_0_DEC_BITS;
const nnom_qformat_param_t tensor_dense_kernel_0_offset[] = {0};
const nnom_tensor_t tensor_dense_kernel_0 = {
    .p_data = (void*)tensor_dense_kernel_0_data,
    .dim = (nnom_shape_data_t*)tensor_dense_kernel_0_dim,
    .q_dec = (nnom_qformat_param_t*)tensor_dense_kernel_0_dec,
    .q_offset = (nnom_qformat_param_t*)tensor_dense_kernel_0_offset,
    .qtype = NNOM_QTYPE_PER_TENSOR,
    .num_dim = 2,
    .bitwidth = 8
};
const int8_t tensor_dense_bias_0_data[] = TENSOR_DENSE_BIAS_0;

const nnom_shape_data_t tensor_dense_bias_0_dim[] = {96};
const nnom_qformat_param_t tensor_dense_bias_0_dec[] = TENSOR_DENSE_BIAS_0_DEC_BITS;
const nnom_qformat_param_t tensor_dense_bias_0_offset[] = {0};
const nnom_tensor_t tensor_dense_bias_0 = {
    .p_data = (void*)tensor_dense_bias_0_data,
    .dim = (nnom_shape_data_t*)tensor_dense_bias_0_dim,
    .q_dec = (nnom_qformat_param_t*)tensor_dense_bias_0_dec,
    .q_offset = (nnom_qformat_param_t*)tensor_dense_bias_0_offset,
    .qtype = NNOM_QTYPE_PER_TENSOR,
    .num_dim = 1,
    .bitwidth = 8
};

const nnom_qformat_param_t dense_output_shift[] = DENSE_OUTPUT_RSHIFT;
const nnom_qformat_param_t dense_bias_shift[] = DENSE_BIAS_LSHIFT;
const nnom_dense_config_t dense_config = {
    .super = {.name = "dense"},
    .qtype = NNOM_QTYPE_PER_TENSOR,
    .weight = (nnom_tensor_t*)&tensor_dense_kernel_0,
    .bias = (nnom_tensor_t*)&tensor_dense_bias_0,
    .output_shift = (nnom_qformat_param_t *)&dense_output_shift,
    .bias_shift = (nnom_qformat_param_t *)&dense_bias_shift
};
const int8_t tensor_dense_1_kernel_0_data[] = TENSOR_DENSE_1_KERNEL_0;

const nnom_shape_data_t tensor_dense_1_kernel_0_dim[] = {96, 10};
const nnom_qformat_param_t tensor_dense_1_kernel_0_dec[] = TENSOR_DENSE_1_KERNEL_0_DEC_BITS;
const nnom_qformat_param_t tensor_dense_1_kernel_0_offset[] = {0};
const nnom_tensor_t tensor_dense_1_kernel_0 = {
    .p_data = (void*)tensor_dense_1_kernel_0_data,
    .dim = (nnom_shape_data_t*)tensor_dense_1_kernel_0_dim,
    .q_dec = (nnom_qformat_param_t*)tensor_dense_1_kernel_0_dec,
    .q_offset = (nnom_qformat_param_t*)tensor_dense_1_kernel_0_offset,
    .qtype = NNOM_QTYPE_PER_TENSOR,
    .num_dim = 2,
    .bitwidth = 8
};
const int8_t tensor_dense_1_bias_0_data[] = TENSOR_DENSE_1_BIAS_0;

const nnom_shape_data_t tensor_dense_1_bias_0_dim[] = {10};
const nnom_qformat_param_t tensor_dense_1_bias_0_dec[] = TENSOR_DENSE_1_BIAS_0_DEC_BITS;
const nnom_qformat_param_t tensor_dense_1_bias_0_offset[] = {0};
const nnom_tensor_t tensor_dense_1_bias_0 = {
    .p_data = (void*)tensor_dense_1_bias_0_data,
    .dim = (nnom_shape_data_t*)tensor_dense_1_bias_0_dim,
    .q_dec = (nnom_qformat_param_t*)tensor_dense_1_bias_0_dec,
    .q_offset = (nnom_qformat_param_t*)tensor_dense_1_bias_0_offset,
    .qtype = NNOM_QTYPE_PER_TENSOR,
    .num_dim = 1,
    .bitwidth = 8
};

const nnom_qformat_param_t dense_1_output_shift[] = DENSE_1_OUTPUT_RSHIFT;
const nnom_qformat_param_t dense_1_bias_shift[] = DENSE_1_BIAS_LSHIFT;
const nnom_dense_config_t dense_1_config = {
    .super = {.name = "dense_1"},
    .qtype = NNOM_QTYPE_PER_TENSOR,
    .weight = (nnom_tensor_t*)&tensor_dense_1_kernel_0,
    .bias = (nnom_tensor_t*)&tensor_dense_1_bias_0,
    .output_shift = (nnom_qformat_param_t *)&dense_1_output_shift,
    .bias_shift = (nnom_qformat_param_t *)&dense_1_bias_shift
};

const nnom_softmax_config_t softmax_config = {
    .super = {.name = "softmax"}
};
static int8_t nnom_output_data[10] = {0};

const nnom_shape_data_t tensor_output_dim[] = {10};
const nnom_qformat_param_t tensor_output_dec[] = {SOFTMAX_OUTPUT_DEC};
const nnom_qformat_param_t tensor_output_offset[] = {0};
const nnom_tensor_t tensor_output = {
    .p_data = (void*)nnom_output_data,
    .dim = (nnom_shape_data_t*)tensor_output_dim,
    .q_dec = (nnom_qformat_param_t*)tensor_output_dec,
    .q_offset = (nnom_qformat_param_t*)tensor_output_offset,
    .qtype = NNOM_QTYPE_PER_TENSOR,
    .num_dim = 1,
    .bitwidth = 8
};

const nnom_io_config_t output_config = {
    .super = {.name = "output"},
    .tensor = (nnom_tensor_t*)&tensor_output
};
/* model version */
#define NNOM_MODEL_VERSION (10000*0 + 100*4 + 0)

/* nnom model */
static nnom_model_t* nnom_model_create(void)
{
	static nnom_model_t model;
	nnom_layer_t* layer[31];

	check_model_version(NNOM_MODEL_VERSION);
	new_model(&model);

	layer[0] = input_s(&input_1_config);
	layer[1] = model.hook(conv2d_s(&conv2d_config), layer[0]);
	layer[2] = model.active(act_relu(), layer[1]);
	layer[3] = model.hook(maxpool_s(&max_pooling2d_config), layer[1]);
	layer[4] = model.hook(conv2d_s(&conv2d_4_config), layer[2]);
	layer[5] = model.hook(conv2d_s(&conv2d_3_config), layer[3]);
	layer[6] = model.hook(conv2d_s(&conv2d_2_config), layer[3]);
	layer[7] = model.hook(maxpool_s(&max_pooling2d_1_config), layer[4]);
	layer[8] = model.hook(conv2d_s(&conv2d_1_config), layer[2]);
	layer[9] = model.hook(upsample_s(&up_sampling2d_config), layer[5]);
	layer[10] = model.mergex(add_s(&add_1_config), 2 ,layer[6] ,layer[7]);
	layer[11] = model.mergex(add_s(&add_config), 2 ,layer[8] ,layer[9]);
	layer[12] = model.hook(maxpool_s(&max_pooling2d_3_config), layer[10]);
	layer[13] = model.hook(maxpool_s(&max_pooling2d_2_config), layer[11]);
	layer[14] = model.hook(conv2d_s(&conv2d_7_config), layer[12]);
	layer[15] = model.hook(conv2d_s(&conv2d_5_config), layer[13]);
	layer[16] = model.hook(upsample_s(&up_sampling2d_1_config), layer[14]);
	layer[17] = model.hook(conv2d_s(&conv2d_8_config), layer[13]);
	layer[18] = model.mergex(add_s(&add_2_config), 2 ,layer[15] ,layer[16]);
	layer[19] = model.hook(conv2d_s(&conv2d_6_config), layer[12]);
	layer[20] = model.hook(maxpool_s(&max_pooling2d_4_config), layer[17]);
	layer[21] = model.hook(maxpool_s(&max_pooling2d_5_config), layer[18]);
	layer[22] = model.mergex(add_s(&add_3_config), 2 ,layer[19] ,layer[20]);
	layer[23] = model.mergex(concat_s(&concatenate_config), 2 ,layer[21] ,layer[22]);
	layer[24] = model.hook(conv2d_s(&conv2d_9_config), layer[23]);
	layer[25] = model.hook(flatten_s(&flatten_config), layer[24]);
	layer[26] = model.hook(dense_s(&dense_config), layer[25]);
	layer[27] = model.active(act_relu(), layer[26]);
	layer[28] = model.hook(dense_s(&dense_1_config), layer[27]);
	layer[29] = model.hook(softmax_s(&softmax_config), layer[28]);
	layer[30] = model.hook(output_s(&output_config), layer[29]);
	model_compile(&model, layer[0], layer[30]);
	return &model;
}
