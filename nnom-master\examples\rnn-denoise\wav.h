#ifndef __342_WAV_H__
#define __343_WAV_H__

typedef struct {
    char     chunk_id[4];
    uint32_t chunk_size;
    char     format[4];
    char     fmtchunk_id[4];
    uint32_t fmtchunk_size;
    uint16_t audio_format;
    uint16_t num_channels;
    uint32_t sample_rate;
    uint32_t byte_rate;
    uint16_t block_align;
    uint16_t bps;
    char     datachunk_id[4];
    uint32_t datachunk_size;
}wav_header_t;

typedef struct {
	char     id[4];
	uint32_t size;
}wav_chunk_t;

#endif
