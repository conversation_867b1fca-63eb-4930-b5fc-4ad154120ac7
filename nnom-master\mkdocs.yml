site_name: NNoM Documentation

theme:
  name: 'readthedocs'

repo_url: https://github.com/majianjia/nnom
site_description: 'A higher-level Neural Network library for microcontrollers.'
google_analytics: ['UA-141541198-1', 'majianjia.github.io/nnom']

nav:
- Overview: 'index.md'
- Guides:
  - 5 min to NNoM: 'guide_5_min_to_nnom.md'
  - Development Guide: 'guide_development.md'
  - Porting and Optimisation Guide: 'Porting_and_Optimisation_Guide.md'
- APIs: 
  - Utils (Ver < 0.4): 'api_nnom_utils.md'
  - Utils (Ver >= 0.4): 'api_nnom.md'
  - Model: 'api_model.md'
  - Construction Methods: 'api_construction.md'
  - Properties: 'api_properties.md'
  - Tensors: 'api_tensor.md'
  - Core Layers: 'api_layers.md'
  - Pooling Layers: 'api_pooling.md'
  - Activations: 'api_activations.md'
  - Merges Layers: 'api_merge.md'
  - Evaluation Methods: 'api_evaluation.md'
- Legacy Guide:
  - A Temporary Guide: 'A_Temporary_Guide_to_NNoM.md'
  - Old Readme: 'legacy_README.md'
- Chinese (中文): 
  - RT-Thread: 'rt-thread_guide.md'
  - MNIST-Simple: 'example_mnist_simple_cn.md'


