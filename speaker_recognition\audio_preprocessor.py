"""
音频预处理脚本
使用Silero VAD进行人声检测，然后切分为2秒片段
"""

import os
import torch
import torchaudio
import numpy as np
import librosa
import soundfile as sf
from pathlib import Path
from tqdm import tqdm
import json
import warnings
warnings.filterwarnings('ignore')

class AudioPreprocessor:
    def __init__(self, source_root, target_root, target_sr=8000, segment_duration=2.0, overlap=0.0, auto_clean=True):
        """
        音频预处理器

        Args:
            source_root: 源数据集根目录
            target_root: 目标数据集根目录
            target_sr: 目标采样率
            segment_duration: 片段时长（秒）
            overlap: 重叠比例 (0.0=无重叠, 0.5=50%重叠)
            auto_clean: 是否自动清理旧数据
        """
        self.source_root = Path(source_root)
        self.target_root = Path(target_root)
        self.target_sr = target_sr
        self.segment_duration = segment_duration
        self.overlap = overlap
        self.auto_clean = auto_clean

        # 设备列表和说话人列表
        self.devices = ['board', 'phone', 'ipad', 'pc']
        self.target_speakers = ['XiaoXin', 'XiaoYuan', 'XiaoSi', '<PERSON>Lai']
        self.other_speakers = [f'ID{i}' for i in range(1, 12)]
        self.all_speakers = self.target_speakers + self.other_speakers

        # 初始化Silero VAD
        self.init_vad()

        # 创建目标目录结构
        self.create_target_structure()

    def init_vad(self):
        """初始化Silero VAD模型"""
        print("正在加载Silero VAD模型...")
        try:
            # 下载并加载Silero VAD模型
            self.vad_model, utils = torch.hub.load(
                repo_or_dir='snakers4/silero-vad',
                model='silero_vad',
                force_reload=False,
                onnx=False
            )

            (self.get_speech_timestamps,
             self.save_audio,
             self.read_audio,
             self.VADIterator,
             self.collect_chunks) = utils

            print("✅ Silero VAD模型加载成功")

        except Exception as e:
            print(f"❌ VAD模型加载失败: {e}")
            print("请确保网络连接正常，或手动下载模型")
            raise

    def clean_target_directory(self):
        """清理目标目录中的旧数据"""
        if not self.target_root.exists():
            return

        # 统计要删除的文件数量
        total_files = 0
        for wav_file in self.target_root.rglob('*.wav'):
            total_files += 1

        if total_files == 0:
            print(f"📁 目标目录为空，无需清理")
            return

        print(f"🧹 发现目标目录中有 {total_files} 个旧音频文件")

        if self.auto_clean:
            print(f"   🗑️ 自动清理模式：删除所有旧文件")
            should_clean = True
        else:
            # 询问用户是否清理
            response = input(f"   是否清理这些旧文件？(y/n): ").strip().lower()
            should_clean = response in ['y', 'yes', '是']

        if should_clean:
            # 删除所有.wav文件
            for wav_file in self.target_root.rglob('*.wav'):
                wav_file.unlink()

            # 删除统计文件
            stats_file = self.target_root / 'dataset_stats.json'
            if stats_file.exists():
                stats_file.unlink()

            print(f"   ✅ 已清理 {total_files} 个旧文件")
        else:
            print(f"   ⚠️ 保留旧文件，可能会与新数据混合")
            print(f"   建议手动清理或重新运行时选择清理")

    def create_target_structure(self):
        """创建目标目录结构"""
        # 先清理旧数据
        self.clean_target_directory()

        # 创建目录结构
        self.target_root.mkdir(parents=True, exist_ok=True)

        # 为每个说话人创建目录
        categories = self.all_speakers + ['others']

        for category in categories:
            for split in ['train', 'val', 'test']:
                (self.target_root / category / split).mkdir(parents=True, exist_ok=True)

        print(f"✅ 目标目录结构已创建: {self.target_root}")

    def detect_speech_segments(self, audio_path):
        """
        使用Silero VAD检测语音片段

        Args:
            audio_path: 音频文件路径

        Returns:
            speech_timestamps: 语音时间戳列表（基于原始8kHz采样率的时间戳）
        """
        try:
            # 读取原始8kHz音频
            audio_8k, _ = librosa.load(audio_path, sr=8000, mono=True)

            # 上采样到16kHz供VAD使用
            audio_16k = librosa.resample(audio_8k, orig_sr=8000, target_sr=16000)

            # 转换为torch tensor
            wav_tensor = torch.from_numpy(audio_16k).float()

            # 获取语音时间戳（基于16kHz）
            speech_timestamps_16k = self.get_speech_timestamps(
                wav_tensor,
                self.vad_model,
                sampling_rate=16000,
                threshold=0.3,  # 降低阈值，更敏感
                min_speech_duration_ms=250,  # 降低最小语音持续时间
                min_silence_duration_ms=50   # 降低最小静音持续时间
            )

            # 调试信息：显示检测结果
            audio_duration = len(audio_8k) / 8000
            if len(speech_timestamps_16k) == 0:
                print(f"    🔍 调试 {audio_path.name}: 音频时长{audio_duration:.1f}s, 未检测到语音")
                # 如果没有检测到语音，但音频不是很短，可能需要调整参数
                if audio_duration > 1.0:  # 如果音频超过1秒但没检测到语音
                    print(f"    ⚠️  可能需要进一步调整VAD参数")
            else:
                total_speech_duration = sum([(ts['end'] - ts['start'])/16000 for ts in speech_timestamps_16k])
                print(f"    ✅ {audio_path.name}: 检测到{len(speech_timestamps_16k)}个语音段, 总时长{total_speech_duration:.1f}s")

            # 将时间戳转换回8kHz时间基准
            # 注意：Silero VAD返回的是样本索引，不是时间（秒）
            speech_timestamps_8k = []
            for timestamp in speech_timestamps_16k:
                # 从16kHz样本索引转换为8kHz样本索引
                start_8k = timestamp['start'] / 2  # 16kHz -> 8kHz，样本数减半
                end_8k = timestamp['end'] / 2
                speech_timestamps_8k.append({
                    'start': start_8k,
                    'end': end_8k
                })

            return speech_timestamps_8k

        except Exception as e:
            print(f"❌ VAD检测失败 {audio_path}: {e}")
            return []

    def extract_speech_audio(self, audio_path, speech_timestamps):
        """
        根据VAD结果提取语音片段

        Args:
            audio_path: 音频文件路径
            speech_timestamps: 语音时间戳列表

        Returns:
            speech_audio: 拼接后的语音音频
        """
        try:
            # 读取原始8kHz音频
            audio, sr = librosa.load(audio_path, sr=8000, mono=True)

            # 提取所有语音片段（时间戳是8kHz的样本索引）
            speech_segments = []
            for i, timestamp in enumerate(speech_timestamps):
                start_sample = int(timestamp['start'])  # 已经是样本索引
                end_sample = int(timestamp['end'])      # 已经是样本索引

                # 确保索引在有效范围内
                start_sample = max(0, start_sample)
                end_sample = min(len(audio), end_sample)

                if end_sample > start_sample:
                    segment = audio[start_sample:end_sample]
                    speech_segments.append(segment)
                    # 调试信息
                    segment_duration = (end_sample - start_sample) / 8000
                    print(f"      段{i+1}: 样本{start_sample}-{end_sample}, 时长{segment_duration:.2f}s")
                else:
                    print(f"      ⚠️ 段{i+1}: 无效范围 {start_sample}-{end_sample}")

            # 拼接所有语音片段
            if speech_segments:
                speech_audio = np.concatenate(speech_segments)
                return speech_audio
            else:
                return np.array([])

        except Exception as e:
            print(f"❌ 语音提取失败 {audio_path}: {e}")
            return np.array([])

    def split_audio_segments(self, audio, sr, speaker_id, device, file_prefix, output_dir):
        """
        将音频切分为2秒片段（50%重叠）

        Args:
            audio: 音频数据
            sr: 采样率
            speaker_id: 说话人ID
            device: 设备名称
            file_prefix: 文件前缀
            output_dir: 输出目录

        Returns:
            segments_count: 生成的片段数量
        """
        if len(audio) == 0:
            return 0

        # 重采样到目标采样率
        if sr != self.target_sr:
            audio = librosa.resample(audio, orig_sr=sr, target_sr=self.target_sr)
            sr = self.target_sr

        # 计算片段参数
        segment_samples = int(self.segment_duration * sr)

        if self.overlap == 0.0:
            # 无重叠模式：直接按2秒切分
            hop_samples = segment_samples
        else:
            # 有重叠模式
            hop_samples = int(segment_samples * (1 - self.overlap))

        segments_count = 0
        start = 0

        while start + segment_samples <= len(audio):
            segment = audio[start:start + segment_samples]

            # 由于已经通过VAD过滤，直接保存所有片段
            # 生成输出文件名
            output_filename = f"{file_prefix}_{device}_{segments_count:03d}.wav"
            output_path = output_dir / output_filename

            # 保存音频片段
            sf.write(output_path, segment, sr)
            segments_count += 1

            start += hop_samples

        return segments_count

    def process_device_data(self, device):
        """
        处理单个设备的数据

        Args:
            device: 设备名称 (board, phone, ipad, pc)

        Returns:
            统计信息
        """
        device_path = self.source_root / device
        if not device_path.exists():
            print(f"❌ 设备目录不存在: {device_path}")
            return {}

        print(f"\n🔄 处理设备: {device}")

        # 获取该设备下的所有说话人
        speakers = [d.name for d in device_path.iterdir() if d.is_dir()]

        stats = {'total_files': 0, 'total_segments': 0, 'by_speaker': {}}

        for speaker in tqdm(speakers, desc=f"处理{device}设备"):
            speaker_path = device_path / speaker / 'wav_cut'

            if not speaker_path.exists():
                print(f"⚠️ wav_cut目录不存在: {speaker_path}")
                continue

            # 确定输出目录（暂时都放到train中，后续再分割）
            output_dir = self.target_root / speaker / 'train'

            # 处理该说话人的所有音频文件
            audio_files = list(speaker_path.glob('*.wav'))
            speaker_segments = 0

            for audio_file in audio_files:
                try:
                    # 1. VAD检测语音片段
                    speech_timestamps = self.detect_speech_segments(audio_file)

                    if not speech_timestamps:
                        print(f"⚠️ 未检测到语音: {audio_file.name}")
                        continue

                    # 2. 提取语音音频
                    speech_audio = self.extract_speech_audio(audio_file, speech_timestamps)

                    if len(speech_audio) == 0:
                        print(f"⚠️ 语音提取为空: {audio_file.name}")
                        continue

                    # 3. 切分为2秒片段
                    file_prefix = f"{speaker}_{audio_file.stem}"
                    segments_count = self.split_audio_segments(
                        speech_audio, self.target_sr, speaker, device, file_prefix, output_dir
                    )

                    speaker_segments += segments_count
                    stats['total_files'] += 1

                except Exception as e:
                    print(f"❌ 处理文件失败 {audio_file}: {e}")
                    continue

            stats['by_speaker'][speaker] = speaker_segments
            stats['total_segments'] += speaker_segments

            print(f"  {speaker}: {speaker_segments} 个片段")

        print(f"✅ {device}设备处理完成:")
        print(f"  总文件数: {stats['total_files']}")
        print(f"  总片段数: {stats['total_segments']}")

        return stats

    def process_others_data(self):
        """
        处理others目录的数据（其他说话人）

        Returns:
            统计信息
        """
        others_path = self.source_root / 'others' / 'wav'

        if not others_path.exists():
            print(f"❌ Others目录不存在: {others_path}")
            return 0

        print("\n🔄 处理Others数据...")

        audio_files = list(others_path.glob('*.wav'))
        total_segments = 0

        output_dir = self.target_root / 'others' / 'train'

        for i, audio_file in enumerate(tqdm(audio_files, desc="处理Others音频")):
            try:
                # 1. VAD检测语音片段
                speech_timestamps = self.detect_speech_segments(audio_file)

                if not speech_timestamps:
                    continue

                # 2. 提取语音音频
                speech_audio = self.extract_speech_audio(audio_file, speech_timestamps)

                if len(speech_audio) == 0:
                    continue

                # 3. 切分为2秒片段
                file_prefix = f"others_{i:04d}_{audio_file.stem}"
                segments_count = self.split_audio_segments(
                    speech_audio, self.target_sr, 'others', 'unknown', file_prefix, output_dir
                )

                total_segments += segments_count

            except Exception as e:
                print(f"❌ 处理Others文件失败 {audio_file}: {e}")
                continue

        print(f"✅ Others数据处理完成: {total_segments}个片段")
        return total_segments

    def split_train_val_test(self, train_ratio=0.8, val_ratio=0.1, test_ratio=0.1):
        """
        将训练数据分割为训练/验证/测试集

        Args:
            train_ratio: 训练集比例 (默认0.8)
            val_ratio: 验证集比例 (默认0.1)
            test_ratio: 测试集比例 (默认0.1)
        """
        print("\n🔄 分割数据集...")

        categories = self.all_speakers + ['others']

        for category in categories:
            train_dir = self.target_root / category / 'train'
            val_dir = self.target_root / category / 'val'
            test_dir = self.target_root / category / 'test'

            if not train_dir.exists() or len(list(train_dir.glob('*.wav'))) == 0:
                continue

            # 获取所有音频文件
            audio_files = list(train_dir.glob('*.wav'))

            # 随机打乱
            np.random.shuffle(audio_files)

            # 计算分割点
            n_total = len(audio_files)
            n_train = int(n_total * train_ratio)
            n_val = int(n_total * val_ratio)

            # 分割文件
            train_files = audio_files[:n_train]
            val_files = audio_files[n_train:n_train + n_val]
            test_files = audio_files[n_train + n_val:]

            # 移动文件到验证集
            for file_path in val_files:
                new_path = val_dir / file_path.name
                file_path.rename(new_path)

            # 移动文件到测试集
            for file_path in test_files:
                new_path = test_dir / file_path.name
                file_path.rename(new_path)

            print(f"  {category}: 训练={len(train_files)}, 验证={len(val_files)}, 测试={len(test_files)}")

        print("✅ 数据集分割完成")

    def generate_statistics(self):
        """
        生成数据集统计信息

        Returns:
            统计信息字典
        """
        print("\n📊 生成统计信息...")

        stats = {}
        categories = self.all_speakers + ['others']

        for category in categories:
            stats[category] = {}
            for split in ['train', 'val', 'test']:
                split_dir = self.target_root / category / split
                if split_dir.exists():
                    count = len(list(split_dir.glob('*.wav')))
                    stats[category][split] = count
                else:
                    stats[category][split] = 0

        # 保存统计信息
        stats_file = self.target_root / 'dataset_stats.json'
        with open(stats_file, 'w', encoding='utf-8') as f:
            json.dump(stats, f, indent=2, ensure_ascii=False)

        # 打印统计信息
        print("\n=== 数据集统计 ===")
        total_train = total_val = total_test = 0

        for category in categories:
            train_count = stats[category]['train']
            val_count = stats[category]['val']
            test_count = stats[category]['test']
            total = train_count + val_count + test_count

            total_train += train_count
            total_val += val_count
            total_test += test_count

            if total > 0:  # 只显示有数据的类别
                print(f"{category:10s}: 训练={train_count:4d}, 验证={val_count:4d}, 测试={test_count:4d}, 总计={total:4d}")

        print(f"{'总计':10s}: 训练={total_train:4d}, 验证={total_val:4d}, 测试={total_test:4d}, 总计={total_train+total_val+total_test:4d}")
        print(f"\n📁 统计信息已保存到: {stats_file}")

        return stats

    def process_all(self):
        """
        处理所有数据

        Returns:
            最终统计信息
        """
        print("🚀 开始音频预处理...")
        print(f"📂 源目录: {self.source_root}")
        print(f"📁 目标目录: {self.target_root}")
        print(f"🎵 目标采样率: {self.target_sr}Hz")
        print(f"⏱️ 片段时长: {self.segment_duration}秒")
        if self.overlap == 0.0:
            print(f"🔄 切分模式: 无重叠，直接按{self.segment_duration}秒切分")
        else:
            print(f"🔄 重叠比例: {self.overlap*100}%")
        print(f"📊 数据分割: 训练80% | 验证10% | 测试10%")

        # 处理各设备数据
        all_stats = {}
        for device in self.devices:
            device_stats = self.process_device_data(device)
            if device_stats:
                all_stats[device] = device_stats

        # 处理others数据
        others_stats = self.process_others_data()
        all_stats['others'] = others_stats

        # 分割数据集
        self.split_train_val_test()

        # 生成统计信息
        final_stats = self.generate_statistics()

        print("\n🎉 音频预处理完成！")
        return final_stats


def main():
    """
    主函数
    """
    # 数据集路径配置
    source_root = r"D:\NucleiStudio\2025\final\VeriHealthi_SR_Dataset_v2.0"
    target_root = r"D:\NucleiStudio\2025\01quant\speaker_recognition\dataset"

    try:
        # 创建音频预处理器（自动清理旧数据）
        processor = AudioPreprocessor(
            source_root=source_root,
            target_root=target_root,
            auto_clean=True  # 设置为False可以手动确认清理
        )

        # 处理所有数据
        stats = processor.process_all()

        print(f"\n✅ 处理完成！数据保存在: {target_root}")

    except Exception as e:
        print(f"❌ 处理失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()