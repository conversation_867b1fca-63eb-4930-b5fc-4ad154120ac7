/*
 * 声纹识别/说话人识别 - NNOM推理代码
 * 
 * 作者: Augment Agent
 * 日期: 2025-08-02
 * 
 * 说明: 
 * - 使用NNOM框架进行声纹识别推理
 * - 输入: 198x12的MFCC特征 (2秒音频，12维MFCC，去除C0)
 * - 输出: 16类说话人分类结果
 * - 目标说话人: <PERSON><PERSON><PERSON>(0), <PERSON><PERSON><PERSON>(1), <PERSON><PERSON><PERSON>(2), <PERSON><PERSON><PERSON>(3)
 * - 其他说话人: ID1-ID11 (4-14), others(15)
 */

#include <stdio.h>
#include <stdint.h>
#include <string.h>
#include <math.h>
#include "nnom.h"
#include "speaker_weights.h"

// 模型配置
#define INPUT_HEIGHT    198     // 时间帧数 (2秒音频，10ms帧移)
#define INPUT_WIDTH     12      // MFCC维度 (去除C0后)
#define INPUT_CHANNELS  1       // 单声道
#define NUM_CLASSES     16      // 分类数量

#define INPUT_SIZE      (INPUT_HEIGHT * INPUT_WIDTH * INPUT_CHANNELS)
#define OUTPUT_SIZE     NUM_CLASSES

// 说话人标签
static const char* speaker_names[NUM_CLASSES] = {
    "XiaoXin",   // 0 - 目标说话人
    "XiaoYuan",  // 1 - 目标说话人  
    "XiaoSi",    // 2 - 目标说话人
    "XiaoLai",   // 3 - 目标说话人
    "ID1",       // 4 - 其他说话人
    "ID2",       // 5 - 其他说话人
    "ID3",       // 6 - 其他说话人
    "ID4",       // 7 - 其他说话人
    "ID5",       // 8 - 其他说话人
    "ID6",       // 9 - 其他说话人
    "ID7",       // 10 - 其他说话人
    "ID8",       // 11 - 其他说话人
    "ID9",       // 12 - 其他说话人
    "ID10",      // 13 - 其他说话人
    "ID11",      // 14 - 其他说话人
    "others"     // 15 - 未知说话人
};

// 目标说话人标志 (前4个是目标说话人)
static const uint8_t is_target_speaker[NUM_CLASSES] = {
    1, 1, 1, 1,  // XiaoXin, XiaoYuan, XiaoSi, XiaoLai
    0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0  // 其他说话人
};

// 全局变量
static nnom_model_t *model;
static int8_t input_buffer[INPUT_SIZE];
static int8_t output_buffer[OUTPUT_SIZE];

// 浮点数打印函数 (SDK兼容)
void print_float(float value) {
    int integer_part = (int)value;
    int decimal_part = (int)((value - integer_part) * 1000);
    if (decimal_part < 0) decimal_part = -decimal_part;
    uart_printf("%d.%03d", integer_part, decimal_part);
}

/**
 * 初始化NNOM模型
 * @return 0: 成功, -1: 失败
 */
int speaker_model_init(void) {
    uart_printf("🚀 初始化声纹识别模型...\n");
    
    // 创建NNOM模型
    model = nnom_model_create();
    if (model == NULL) {
        uart_printf("❌ 模型创建失败\n");
        return -1;
    }
    
    uart_printf("✅ 模型初始化成功\n");
    uart_printf("   输入尺寸: %dx%dx%d\n", INPUT_HEIGHT, INPUT_WIDTH, INPUT_CHANNELS);
    uart_printf("   输出类别: %d\n", NUM_CLASSES);
    uart_printf("   模型版本: %d\n", NNOM_MODEL_VERSION);
    
    return 0;
}

/**
 * 预处理MFCC特征
 * @param mfcc_features: 输入的MFCC特征 (float格式)
 * @param quantized_input: 输出的量化输入 (int8格式)
 */
void preprocess_mfcc(const float *mfcc_features, int8_t *quantized_input) {
    // 根据NNOM量化参数进行预处理
    // input层的量化参数: dec_bit=1, 范围约为[-50, 50]
    const float scale = 1.0f / (1 << 1);  // 2^1 = 2
    const float input_scale = 127.0f / 50.0f;  // 将[-50,50]映射到[-127,127]
    
    for (int i = 0; i < INPUT_SIZE; i++) {
        // 限制输入范围并量化
        float clipped = mfcc_features[i];
        if (clipped > 50.0f) clipped = 50.0f;
        if (clipped < -50.0f) clipped = -50.0f;
        
        // 量化到int8
        int32_t quantized = (int32_t)(clipped * input_scale);
        if (quantized > 127) quantized = 127;
        if (quantized < -128) quantized = -128;
        
        quantized_input[i] = (int8_t)quantized;
    }
}

/**
 * 后处理模型输出
 * @param raw_output: 模型原始输出 (int8格式)
 * @param probabilities: 输出的概率 (float格式)
 */
void postprocess_output(const int8_t *raw_output, float *probabilities) {
    // 根据NNOM量化参数进行后处理
    // dense层输出的量化参数: dec_bit=7, 范围[0, 1]
    const float scale = 1.0f / (1 << 7);  // 2^7 = 128
    
    // 转换为浮点数
    float logits[NUM_CLASSES];
    for (int i = 0; i < NUM_CLASSES; i++) {
        logits[i] = raw_output[i] * scale;
    }
    
    // Softmax归一化 (已经在模型中完成，这里直接复制)
    float sum = 0.0f;
    for (int i = 0; i < NUM_CLASSES; i++) {
        probabilities[i] = logits[i];
        sum += probabilities[i];
    }
    
    // 确保概率和为1
    if (sum > 0.0f) {
        for (int i = 0; i < NUM_CLASSES; i++) {
            probabilities[i] /= sum;
        }
    }
}

/**
 * 执行声纹识别推理
 * @param mfcc_features: 输入的MFCC特征 [198x12]
 * @param result: 输出的识别结果
 * @return 0: 成功, -1: 失败
 */
int speaker_inference(const float *mfcc_features, int *predicted_class, float *confidence) {
    if (model == NULL) {
        uart_printf("❌ 模型未初始化\n");
        return -1;
    }
    
    // 1. 预处理输入
    preprocess_mfcc(mfcc_features, input_buffer);
    
    // 2. 执行推理
    memcpy(nnom_input_data, input_buffer, INPUT_SIZE);
    model_run(model);
    memcpy(output_buffer, nnom_output_data, OUTPUT_SIZE);
    
    // 3. 后处理输出
    float probabilities[NUM_CLASSES];
    postprocess_output(output_buffer, probabilities);
    
    // 4. 找到最大概率的类别
    int max_class = 0;
    float max_prob = probabilities[0];
    for (int i = 1; i < NUM_CLASSES; i++) {
        if (probabilities[i] > max_prob) {
            max_prob = probabilities[i];
            max_class = i;
        }
    }
    
    *predicted_class = max_class;
    *confidence = max_prob;
    
    // 5. 打印详细结果
    uart_printf("🎯 声纹识别结果:\n");
    uart_printf("   预测说话人: %s (类别 %d)\n", speaker_names[max_class], max_class);
    uart_printf("   置信度: ");
    print_float(max_prob * 100.0f);
    uart_printf("%%\n");
    
    if (is_target_speaker[max_class]) {
        uart_printf("   ✅ 目标说话人识别成功\n");
    } else {
        uart_printf("   ⚠️  非目标说话人\n");
    }
    
    // 打印前5个最高概率
    uart_printf("   Top-5 概率:\n");
    for (int rank = 0; rank < 5 && rank < NUM_CLASSES; rank++) {
        int best_idx = 0;
        float best_prob = -1.0f;
        
        // 找到第rank高的概率
        for (int i = 0; i < NUM_CLASSES; i++) {
            if (probabilities[i] > best_prob) {
                // 检查是否已经被选过
                int already_selected = 0;
                for (int j = 0; j < rank; j++) {
                    // 这里简化处理，实际应该记录已选择的索引
                }
                if (!already_selected) {
                    best_prob = probabilities[i];
                    best_idx = i;
                }
            }
        }
        
        uart_printf("     %d. %s: ", rank + 1, speaker_names[best_idx]);
        print_float(best_prob * 100.0f);
        uart_printf("%%\n");
        
        // 标记为已选择 (简化实现)
        probabilities[best_idx] = -1.0f;
    }
    
    return 0;
}

/**
 * 测试函数 - 使用随机数据
 */
void test_speaker_recognition(void) {
    uart_printf("\n🧪 声纹识别测试\n");
    uart_printf("=" * 50);
    uart_printf("\n");
    
    // 生成测试MFCC特征 (模拟真实数据范围)
    static float test_mfcc[INPUT_SIZE];
    
    // 简单的伪随机数生成 (线性同余)
    static uint32_t seed = 12345;
    for (int i = 0; i < INPUT_SIZE; i++) {
        seed = seed * 1103515245 + 12345;
        float random_val = ((float)(seed % 10000) / 10000.0f - 0.5f) * 100.0f;  // [-50, 50]
        test_mfcc[i] = random_val;
    }
    
    uart_printf("📊 生成测试MFCC特征: %dx%d\n", INPUT_HEIGHT, INPUT_WIDTH);
    uart_printf("   特征范围: [-50.0, 50.0]\n");
    
    // 执行推理
    int predicted_class;
    float confidence;
    
    int result = speaker_inference(test_mfcc, &predicted_class, &confidence);
    
    if (result == 0) {
        uart_printf("\n✅ 推理测试完成\n");
    } else {
        uart_printf("\n❌ 推理测试失败\n");
    }
}

/**
 * 主函数
 */
int main(void) {
    uart_printf("\n🎙️  声纹识别系统启动\n");
    uart_printf("=" * 50);
    uart_printf("\n");
    
    // 初始化模型
    if (speaker_model_init() != 0) {
        uart_printf("❌ 系统初始化失败\n");
        return -1;
    }
    
    // 运行测试
    test_speaker_recognition();
    
    uart_printf("\n🏁 程序结束\n");
    return 0;
}
