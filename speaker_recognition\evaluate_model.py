'''
声纹识别模型评估程序
用于评估训练好的模型性能，包括准确率、混淆矩阵、分类报告等
支持对单个音频文件和整个测试集的评估

使用方法:
1. 直接运行: python evaluate_model.py
2. 在Python中导入使用:
   from evaluate_model import *
   main()  # 完整评估
   evaluate_single_audio(model, 'path/to/audio.npy')  # 单文件评估

输出文件:
- confusion_matrix.png: 混淆矩阵图
- confidence_distribution.png: 置信度分布图
- evaluation_summary.json: 总体指标
- detailed_predictions.json: 详细预测结果
- evaluation_results.csv: CSV格式结果

Author: AI Assistant
Date: 2025-01-31
'''

import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path
import pandas as pd
from sklearn.metrics import (classification_report, confusion_matrix, 
                           accuracy_score, precision_recall_fscore_support)
import tensorflow as tf

# 现代 TensorFlow 2.x 推荐用法
keras = tf.keras
models = tf.keras.models
utils = tf.keras.utils
to_categorical = utils.to_categorical

# 配置参数
MODEL_PATH = Path("speaker_recognition/model/speaker_model.h5")
FEATURE_DIR = Path("speaker_recognition/feature")
DATASET_DIR = Path("speaker_recognition/dataset")
RESULTS_DIR = Path("speaker_recognition/evaluation_results")
RESULTS_DIR.mkdir(exist_ok=True)

# 说话人配置（与训练时保持一致）
TARGET_SPEAKERS = ['XiaoXin', 'XiaoYuan', 'XiaoSi', 'XiaoLai']
OTHER_SPEAKERS = ['ID1', 'ID2', 'ID3', 'ID4', 'ID5', 'ID6', 'ID7', 'ID8', 'ID9', 'ID10', 'ID11']
ALL_SPEAKERS = TARGET_SPEAKERS + OTHER_SPEAKERS + ['others']  # 包含others作为未知类别
NUM_CLASSES = len(ALL_SPEAKERS)

# MFCC特征配置
N_FRAMES = 198      # 时间帧数
N_MFCC = 12         # MFCC维度（去除C0后）
INPUT_SHAPE = (N_FRAMES, N_MFCC, 1)


plt.rcParams['font.sans-serif'] = ['SimHei'] # 设置显示中文字体
plt.rcParams['axes.unicode_minus'] = False  # 设置正常显示符号

def load_test_data():
    """
    加载测试数据集
    返回: (x_test, y_test, file_paths)
    """
    print("📂 加载测试数据集...")
    
    x_test = []
    y_test = []
    file_paths = []
    
    for speaker_idx, speaker in enumerate(ALL_SPEAKERS):
        speaker_dir = FEATURE_DIR / speaker / "test"
        if not speaker_dir.exists():
            print(f"⚠️ 跳过不存在的测试目录: {speaker_dir}")
            continue
        
        print(f"   📁 加载说话人: {speaker} (类别 {speaker_idx})")
        
        feature_files = list(speaker_dir.glob('*.npy'))
        for feature_file in feature_files:
            # 加载MFCC特征 (198, 13)
            mfcc_feat = np.load(feature_file)
            
            # 去除C0系数，只保留C1-C12
            mfcc_feat = mfcc_feat[:, 1:]  # (198, 12)
            
            # 添加通道维度
            mfcc_feat = mfcc_feat.reshape(N_FRAMES, N_MFCC, 1)
            
            x_test.append(mfcc_feat)
            y_test.append(speaker_idx)
            file_paths.append(str(feature_file))
    
    # 转换为numpy数组
    x_test = np.array(x_test, dtype=np.float32)
    y_test = np.array(y_test, dtype=np.int32)
    
    print(f"✅ 测试数据加载完成:")
    print(f"   测试集: {x_test.shape}, 标签: {y_test.shape}")
    print(f"   特征范围: [{x_test.min():.3f}, {x_test.max():.3f}]")
    
    return x_test, y_test, file_paths

def preprocess_data(x_test):
    """
    数据预处理（NNOM正确方式：保持原始浮点数据）

    NNOM的正确使用方式：
    - 训练时：直接使用原始浮点MFCC数据，不进行任何归一化
    - 量化时：让NNOM根据真实激活值范围自动计算最优量化参数
    - 推理时：C代码根据NNOM生成的INPUT_OUTPUT_DEC进行量化
    """
    print(f"🔧 数据预处理（NNOM标准方式）...")

    # 预处理前的统计
    print(f"   原始MFCC数据范围: [{x_test.min():.3f}, {x_test.max():.3f}]")
    print(f"   数据形状: {x_test.shape}")
    print(f"   数据类型: {x_test.dtype}")

    # 直接返回原始数据，让NNOM处理量化
    x_test = x_test.astype(np.float32)

    # 预处理后的统计
    print(f"   预处理后范围: [{x_test.min():.3f}, {x_test.max():.3f}], 均值{x_test.mean():.3f}")

    return x_test

def load_model_and_predict(x_test):
    """
    加载模型并进行预测
    """
    print("🤖 加载训练好的模型...")
    
    if not MODEL_PATH.exists():
        raise FileNotFoundError(f"模型文件不存在: {MODEL_PATH}")
    
    # 加载模型
    model = models.load_model(str(MODEL_PATH))
    print(f"✅ 模型加载成功: {MODEL_PATH}")
    
    # 显示模型信息
    print("\n📊 模型信息:")
    model.summary()
    
    # 进行预测
    print("\n🔮 开始预测...")
    predictions = model.predict(x_test, verbose=1)
    predicted_classes = np.argmax(predictions, axis=1)
    prediction_probs = np.max(predictions, axis=1)
    
    print(f"✅ 预测完成，共处理 {len(x_test)} 个样本")
    
    return model, predictions, predicted_classes, prediction_probs

def calculate_metrics(y_true, y_pred, predictions):
    """
    计算各种评估指标
    """
    print("📊 计算评估指标...")
    
    # 基本指标
    accuracy = accuracy_score(y_true, y_pred)
    precision, recall, f1, support = precision_recall_fscore_support(y_true, y_pred, average='weighted')
    
    # 每个类别的指标
    precision_per_class, recall_per_class, f1_per_class, support_per_class = \
        precision_recall_fscore_support(y_true, y_pred, average=None)
    
    # 混淆矩阵
    cm = confusion_matrix(y_true, y_pred)
    
    # 分类报告
    class_report = classification_report(y_true, y_pred, target_names=ALL_SPEAKERS, output_dict=True)
    
    # 置信度统计
    confidence_stats = {
        'mean_confidence': np.mean(np.max(predictions, axis=1)),
        'std_confidence': np.std(np.max(predictions, axis=1)),
        'min_confidence': np.min(np.max(predictions, axis=1)),
        'max_confidence': np.max(np.max(predictions, axis=1))
    }
    
    metrics = {
        'accuracy': accuracy,
        'precision': precision,
        'recall': recall,
        'f1_score': f1,
        'precision_per_class': precision_per_class,
        'recall_per_class': recall_per_class,
        'f1_per_class': f1_per_class,
        'support_per_class': support_per_class,
        'confusion_matrix': cm,
        'classification_report': class_report,
        'confidence_stats': confidence_stats
    }
    
    return metrics

def print_evaluation_results(metrics):
    """
    打印评估结果
    """
    print("\n" + "="*80)
    print("🎯 声纹识别模型评估结果")
    print("="*80)
    
    # 总体指标
    print(f"\n📊 总体性能指标:")
    print(f"   准确率 (Accuracy):     {metrics['accuracy']:.4f} ({metrics['accuracy']*100:.2f}%)")
    print(f"   精确率 (Precision):    {metrics['precision']:.4f}")
    print(f"   召回率 (Recall):       {metrics['recall']:.4f}")
    print(f"   F1分数 (F1-Score):     {metrics['f1_score']:.4f}")
    
    # 置信度统计
    print(f"\n🎯 预测置信度统计:")
    print(f"   平均置信度: {metrics['confidence_stats']['mean_confidence']:.4f}")
    print(f"   置信度标准差: {metrics['confidence_stats']['std_confidence']:.4f}")
    print(f"   最低置信度: {metrics['confidence_stats']['min_confidence']:.4f}")
    print(f"   最高置信度: {metrics['confidence_stats']['max_confidence']:.4f}")
    
    # 每个说话人的详细指标
    print(f"\n👥 各说话人详细指标:")
    print(f"{'说话人':<12} {'精确率':<8} {'召回率':<8} {'F1分数':<8} {'样本数':<6}")
    print("-" * 50)
    
    for i, speaker in enumerate(ALL_SPEAKERS):
        precision = metrics['precision_per_class'][i]
        recall = metrics['recall_per_class'][i]
        f1 = metrics['f1_per_class'][i]
        support = metrics['support_per_class'][i]
        
        print(f"{speaker:<12} {precision:<8.4f} {recall:<8.4f} {f1:<8.4f} {support:<6}")
    
    # 目标说话人 vs 其他说话人
    target_indices = list(range(len(TARGET_SPEAKERS)))
    other_indices = list(range(len(TARGET_SPEAKERS), len(ALL_SPEAKERS)))
    
    target_precision = np.mean(metrics['precision_per_class'][target_indices])
    target_recall = np.mean(metrics['recall_per_class'][target_indices])
    target_f1 = np.mean(metrics['f1_per_class'][target_indices])
    
    other_precision = np.mean(metrics['precision_per_class'][other_indices])
    other_recall = np.mean(metrics['recall_per_class'][other_indices])
    other_f1 = np.mean(metrics['f1_per_class'][other_indices])
    
    print(f"\n🎯 目标说话人 vs 其他说话人:")
    print(f"   目标说话人平均性能: 精确率={target_precision:.4f}, 召回率={target_recall:.4f}, F1={target_f1:.4f}")
    print(f"   其他说话人平均性能: 精确率={other_precision:.4f}, 召回率={other_recall:.4f}, F1={other_f1:.4f}")

def plot_confusion_matrix(cm, save_path):
    """
    绘制混淆矩阵
    """
    plt.figure(figsize=(12, 10))
    
    # 计算百分比
    cm_percent = cm.astype('float') / cm.sum(axis=1)[:, np.newaxis] * 100
    
    # 创建标注
    annotations = []
    for i in range(cm.shape[0]):
        row = []
        for j in range(cm.shape[1]):
            row.append(f'{cm[i,j]}\n({cm_percent[i,j]:.1f}%)')
        annotations.append(row)
    
    # 绘制热力图
    sns.heatmap(cm, annot=annotations, fmt='', cmap='Blues', 
                xticklabels=ALL_SPEAKERS, yticklabels=ALL_SPEAKERS,
                cbar_kws={'label': '样本数量'})
    
    plt.title('声纹识别混淆矩阵', fontsize=16, pad=20)
    plt.xlabel('预测标签', fontsize=12)
    plt.ylabel('真实标签', fontsize=12)
    plt.xticks(rotation=45, ha='right')
    plt.yticks(rotation=0)
    plt.tight_layout()
    
    plt.savefig(save_path, dpi=300, bbox_inches='tight')
    print(f"📊 混淆矩阵已保存: {save_path}")
    plt.show()

def plot_confidence_distribution(predictions, y_true, y_pred, save_path):
    """
    绘制置信度分布图
    """
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))
    
    # 获取预测置信度
    confidences = np.max(predictions, axis=1)
    correct_mask = (y_true == y_pred)
    
    # 正确预测和错误预测的置信度分布
    correct_conf = confidences[correct_mask]
    incorrect_conf = confidences[~correct_mask]
    
    # 子图1: 置信度直方图
    ax1.hist(correct_conf, bins=30, alpha=0.7, label=f'正确预测 ({len(correct_conf)})', color='green')
    ax1.hist(incorrect_conf, bins=30, alpha=0.7, label=f'错误预测 ({len(incorrect_conf)})', color='red')
    ax1.set_xlabel('预测置信度')
    ax1.set_ylabel('样本数量')
    ax1.set_title('预测置信度分布')
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    
    # 子图2: 各说话人平均置信度
    speaker_confidences = []
    for i, speaker in enumerate(ALL_SPEAKERS):
        mask = (y_true == i)
        if np.sum(mask) > 0:
            avg_conf = np.mean(confidences[mask])
            speaker_confidences.append(avg_conf)
        else:
            speaker_confidences.append(0)
    
    bars = ax2.bar(range(len(ALL_SPEAKERS)), speaker_confidences, 
                   color=['green' if i < len(TARGET_SPEAKERS) else 'blue' for i in range(len(ALL_SPEAKERS))])
    ax2.set_xlabel('说话人')
    ax2.set_ylabel('平均置信度')
    ax2.set_title('各说话人平均预测置信度')
    ax2.set_xticks(range(len(ALL_SPEAKERS)))
    ax2.set_xticklabels(ALL_SPEAKERS, rotation=45, ha='right')
    ax2.grid(True, alpha=0.3)
    
    # 添加数值标签
    for i, bar in enumerate(bars):
        height = bar.get_height()
        ax2.text(bar.get_x() + bar.get_width()/2., height + 0.01,
                f'{height:.3f}', ha='center', va='bottom', fontsize=8)
    
    plt.tight_layout()
    plt.savefig(save_path, dpi=300, bbox_inches='tight')
    print(f"📊 置信度分布图已保存: {save_path}")
    plt.show()

def save_detailed_results(metrics, predictions, y_true, y_pred, file_paths):
    """
    保存详细的评估结果到文件
    """
    print("💾 保存详细评估结果...")

    # 保存总体指标
    results_summary = {
        'accuracy': float(metrics['accuracy']),
        'precision': float(metrics['precision']),
        'recall': float(metrics['recall']),
        'f1_score': float(metrics['f1_score']),
        'confidence_stats': {
            'mean_confidence': float(metrics['confidence_stats']['mean_confidence']),
            'std_confidence': float(metrics['confidence_stats']['std_confidence']),
            'min_confidence': float(metrics['confidence_stats']['min_confidence']),
            'max_confidence': float(metrics['confidence_stats']['max_confidence'])
        }
    }

    # 保存每个说话人的指标
    speaker_metrics = {}
    for i, speaker in enumerate(ALL_SPEAKERS):
        speaker_metrics[speaker] = {
            'precision': float(metrics['precision_per_class'][i]),
            'recall': float(metrics['recall_per_class'][i]),
            'f1_score': float(metrics['f1_per_class'][i]),
            'support': int(metrics['support_per_class'][i])
        }

    results_summary['speaker_metrics'] = speaker_metrics

    # 保存到JSON文件
    import json
    with open(RESULTS_DIR / 'evaluation_summary.json', 'w', encoding='utf-8') as f:
        json.dump(results_summary, f, indent=2, ensure_ascii=False)

    # 保存详细的预测结果
    detailed_results = []
    confidences = np.max(predictions, axis=1)

    for i in range(len(y_true)):
        result = {
            'file_path': file_paths[i],
            'true_speaker': ALL_SPEAKERS[y_true[i]],
            'predicted_speaker': ALL_SPEAKERS[y_pred[i]],
            'confidence': float(confidences[i]),
            'correct': bool(y_true[i] == y_pred[i]),
            'prediction_probs': {ALL_SPEAKERS[j]: float(predictions[i][j]) for j in range(len(ALL_SPEAKERS))}
        }
        detailed_results.append(result)

    # 保存详细结果
    with open(RESULTS_DIR / 'detailed_predictions.json', 'w', encoding='utf-8') as f:
        json.dump(detailed_results, f, indent=2, ensure_ascii=False)

    # 保存混淆矩阵
    np.save(RESULTS_DIR / 'confusion_matrix.npy', metrics['confusion_matrix'])

    # 保存CSV格式的结果（便于Excel查看）
    df_results = pd.DataFrame(detailed_results)
    df_results.to_csv(RESULTS_DIR / 'evaluation_results.csv', index=False, encoding='utf-8-sig')

    print(f"✅ 评估结果已保存到: {RESULTS_DIR}")
    print(f"   - evaluation_summary.json: 总体指标")
    print(f"   - detailed_predictions.json: 详细预测结果")
    print(f"   - evaluation_results.csv: CSV格式结果")
    print(f"   - confusion_matrix.npy: 混淆矩阵")

def analyze_error_cases(predictions, y_true, y_pred, file_paths, top_n=10):
    """
    分析错误案例
    """
    print(f"\n🔍 分析前 {top_n} 个错误案例...")

    # 找出错误预测的案例
    error_mask = (y_true != y_pred)
    error_indices = np.where(error_mask)[0]

    if len(error_indices) == 0:
        print("🎉 没有错误案例！模型表现完美！")
        return

    # 计算错误案例的置信度
    error_confidences = np.max(predictions[error_indices], axis=1)

    # 按置信度排序（置信度高但预测错误的案例更值得关注）
    sorted_indices = np.argsort(error_confidences)[::-1]

    print(f"\n❌ 共发现 {len(error_indices)} 个错误案例")
    print(f"📊 前 {min(top_n, len(error_indices))} 个高置信度错误案例:")
    print("-" * 100)
    print(f"{'序号':<4} {'文件名':<30} {'真实':<10} {'预测':<10} {'置信度':<8} {'真实概率':<8}")
    print("-" * 100)

    for i, idx in enumerate(sorted_indices[:top_n]):
        error_idx = error_indices[idx]
        file_name = Path(file_paths[error_idx]).name
        true_speaker = ALL_SPEAKERS[y_true[error_idx]]
        pred_speaker = ALL_SPEAKERS[y_pred[error_idx]]
        confidence = error_confidences[idx]
        true_prob = predictions[error_idx][y_true[error_idx]]

        print(f"{i+1:<4} {file_name:<30} {true_speaker:<10} {pred_speaker:<10} {confidence:<8.4f} {true_prob:<8.4f}")

    # 分析错误类型
    print(f"\n📈 错误类型分析:")
    error_pairs = {}
    for idx in error_indices:
        true_label = y_true[idx]
        pred_label = y_pred[idx]
        pair = (true_label, pred_label)
        error_pairs[pair] = error_pairs.get(pair, 0) + 1

    # 按错误次数排序
    sorted_errors = sorted(error_pairs.items(), key=lambda x: x[1], reverse=True)

    print(f"{'真实说话人':<12} {'预测说话人':<12} {'错误次数':<8}")
    print("-" * 35)
    for (true_idx, pred_idx), count in sorted_errors[:10]:
        true_speaker = ALL_SPEAKERS[true_idx]
        pred_speaker = ALL_SPEAKERS[pred_idx]
        print(f"{true_speaker:<12} {pred_speaker:<12} {count:<8}")

def evaluate_single_audio(model, audio_path):
    """
    评估单个音频文件
    """
    print(f"\n🎵 评估单个音频文件: {audio_path}")

    if not Path(audio_path).exists():
        print(f"❌ 文件不存在: {audio_path}")
        return

    # 加载MFCC特征
    try:
        mfcc_feat = np.load(audio_path)
        print(f"   原始特征形状: {mfcc_feat.shape}")

        # 去除C0系数
        mfcc_feat = mfcc_feat[:, 1:]  # (198, 12)

        # 添加batch和channel维度
        mfcc_feat = mfcc_feat.reshape(1, N_FRAMES, N_MFCC, 1)

        # 预处理（NNOM方式：保持原始数据）
        mfcc_feat = preprocess_data(mfcc_feat)

        # 预测
        prediction = model.predict(mfcc_feat, verbose=0)
        predicted_class = np.argmax(prediction[0])
        confidence = np.max(prediction[0])

        print(f"   预测结果: {ALL_SPEAKERS[predicted_class]}")
        print(f"   置信度: {confidence:.4f}")
        print(f"   所有类别概率:")

        # 按概率排序显示
        probs = prediction[0]
        sorted_indices = np.argsort(probs)[::-1]

        for i, idx in enumerate(sorted_indices[:5]):  # 显示前5个
            speaker = ALL_SPEAKERS[idx]
            prob = probs[idx]
            print(f"     {i+1}. {speaker}: {prob:.4f}")

    except Exception as e:
        print(f"❌ 处理文件时出错: {e}")

def check_environment():
    """
    检查运行环境
    """
    print("🔧 环境检查:")
    print("=" * 30)

    # 检查模型文件
    if MODEL_PATH.exists():
        print(f"✅ 模型文件存在: {MODEL_PATH}")
        size_mb = MODEL_PATH.stat().st_size / (1024 * 1024)
        print(f"   文件大小: {size_mb:.2f} MB")
    else:
        print(f"❌ 模型文件不存在: {MODEL_PATH}")
        print(f"   请确保模型文件位于: {MODEL_PATH}")
        return False

    # 检查特征目录
    if FEATURE_DIR.exists():
        print(f"✅ 特征目录存在: {FEATURE_DIR}")

        # 统计测试文件数量
        total_files = 0
        for speaker in ALL_SPEAKERS:
            test_dir = FEATURE_DIR / speaker / "test"
            if test_dir.exists():
                files = list(test_dir.glob('*.npy'))
                total_files += len(files)

        print(f"   总测试文件数: {total_files}")
        if total_files == 0:
            print("   ⚠️ 没有找到测试文件，请检查目录结构")
    else:
        print(f"❌ 特征目录不存在: {FEATURE_DIR}")
        print(f"   请确保特征目录位于: {FEATURE_DIR}")
        return False

    # 检查并创建结果目录
    if not RESULTS_DIR.exists():
        RESULTS_DIR.mkdir(parents=True, exist_ok=True)
        print(f"✅ 创建结果目录: {RESULTS_DIR}")
    else:
        print(f"✅ 结果目录存在: {RESULTS_DIR}")

    # 检查TensorFlow
    try:
        print(f"✅ TensorFlow版本: {tf.__version__}")
    except:
        print(f"❌ TensorFlow导入失败")
        return False

    return True

def main():
    """
    主函数 - 完整的模型评估流程
    """
    print("🎯 声纹识别模型评估程序")
    print("=" * 50)

    # 环境检查
    if not check_environment():
        print("\n❌ 环境检查失败，请检查以下项目:")
        print("   1. 模型文件是否存在")
        print("   2. 特征目录结构是否正确")
        print("   3. TensorFlow是否正确安装")
        return

    try:
        print("\n🚀 开始完整评估流程...")

        # 1. 加载测试数据
        x_test, y_test, file_paths = load_test_data()

        if len(x_test) == 0:
            print("❌ 没有找到测试数据，请检查特征文件目录")
            return

        # 2. 数据预处理
        x_test = preprocess_data(x_test)

        # 3. 加载模型并预测
        model, predictions, predicted_classes, _ = load_model_and_predict(x_test)

        # 4. 计算评估指标
        metrics = calculate_metrics(y_test, predicted_classes, predictions)

        # 5. 打印结果
        print_evaluation_results(metrics)

        # 6. 绘制图表
        print("\n📊 生成可视化图表...")
        plot_confusion_matrix(metrics['confusion_matrix'],
                            RESULTS_DIR / 'confusion_matrix.png')

        plot_confidence_distribution(predictions, y_test, predicted_classes,
                                   RESULTS_DIR / 'confidence_distribution.png')

        # 7. 保存详细结果
        save_detailed_results(metrics, predictions, y_test, predicted_classes, file_paths)

        # 8. 分析错误案例
        analyze_error_cases(predictions, y_test, predicted_classes, file_paths)

        # 9. 示例：评估单个音频文件
        print(f"\n" + "="*80)
        print("💡 单个音频文件评估示例:")

        # 如果有测试文件，展示一个例子
        if file_paths:
            print("   正在评估第一个测试文件作为示例...")
            example_file = file_paths[0]
            evaluate_single_audio(model, example_file)

        print(f"\n✅ 评估完成！")
        print(f"📁 结果已保存到: {RESULTS_DIR}")
        print(f"📊 可查看以下文件:")
        print(f"   - confusion_matrix.png: 混淆矩阵图")
        print(f"   - confidence_distribution.png: 置信度分布图")
        print(f"   - evaluation_summary.json: 总体指标")
        print(f"   - detailed_predictions.json: 详细预测结果")
        print(f"   - evaluation_results.csv: CSV格式结果")

    except Exception as e:
        print(f"❌ 评估过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
        print(f"\n💡 可能的解决方案:")
        print(f"   1. 检查模型文件是否完整")
        print(f"   2. 检查特征文件格式是否正确")
        print(f"   3. 确保有足够的内存空间")
        print(f"   4. 检查Python环境和依赖包")

if __name__ == "__main__":
    main()
