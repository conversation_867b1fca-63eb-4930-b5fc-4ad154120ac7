{"extraction_params": {"sample_rate": 8000, "audio_duration": 2.0, "expected_frames": 198, "extractor_type": "NMSIS_DSP_Compatible", "remove_c0": true}, "feature_info": {"mfcc_dims": 13, "time_frames": 198, "feature_shape": "(198, 13)", "format": "numpy数组，时间×特征"}, "extraction_stats": {"total_files": 29536, "success_files": 29536, "failed_files": 0}, "note": "NMSIS DSP兼容的完整13维MFCC特征，训练时需要去除C0: x_train[:, :, 1:]"}