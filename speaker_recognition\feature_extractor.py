#!/usr/bin/env python3
"""
与NMSIS DSP完全兼容的MFCC特征提取器 - CNN声纹识别版本
基于NMSIS DSP库的C实现，确保训练-部署完全一致
"""

import torch
import numpy as np
from scipy.fft import fft
from scipy.signal import get_window
import librosa
from pathlib import Path
from tqdm import tqdm
import json

# =============================================================================
# 配置参数 - 以宏的形式定义，方便修改
# =============================================================================

# --- 音频参数配置 (8kHz) ---
SAMPLE_RATE = 8000              # 采样率 8kHz
DURATION = 2.0                  # 音频时长 2秒
N_SAMPLES_2S = int(SAMPLE_RATE * DURATION)  # 16000样本

# --- MFCC参数配置 (NMSIS DSP兼容) ---
N_FFT = 256                     # FFT点数, 256对于8k采样率是合适的
WIN_LENGTH_MS = 25              # 帧长 25ms
HOP_LENGTH_MS = 10              # 帧移 10ms

# 根据ms计算采样点数
WIN_LENGTH = int(SAMPLE_RATE * WIN_LENGTH_MS / 1000)  # 200样本
HOP_LENGTH = int(SAMPLE_RATE * HOP_LENGTH_MS / 1000)  # 80样本

# NMSIS DSP兼容的MFCC参数
N_MELS = 26                     # Mel滤波器组数量（NMSIS标准）
N_MFCC = 13                     # MFCC系数数量（完整13维）

# 计算2秒音频的特征维度
N_FRAMES_2S = (N_SAMPLES_2S - WIN_LENGTH) // HOP_LENGTH + 1  # 198帧

# --- 说话人标签配置 ---
TARGET_SPEAKERS = ['XiaoXin', 'XiaoYuan', 'XiaoSi', 'XiaoLai']
OTHER_SPEAKERS = [f'ID{i}' for i in range(1, 12)]
ALL_SPEAKERS = TARGET_SPEAKERS + OTHER_SPEAKERS + ['others']

# 标签映射
LABEL_MAPPING = {speaker: idx for idx, speaker in enumerate(ALL_SPEAKERS)}
NUM_CLASSES = len(LABEL_MAPPING)

# --- 调试开关 ---
DEBUG_MODE = False              # 是否开启调试模式
VERBOSE = False                 # 是否显示详细信息（False=简洁模式）
SHOW_PROGRESS_ONLY = True       # 只显示进度条，不显示每个文件的详细信息

# =============================================================================
# 配置信息打印函数
# =============================================================================

def set_verbose_mode(verbose=False):
    """设置详细输出模式"""
    global VERBOSE, SHOW_PROGRESS_ONLY
    VERBOSE = verbose
    SHOW_PROGRESS_ONLY = not verbose

def print_config():
    """打印当前配置信息"""
    print("=== NMSIS MFCC特征提取器配置 ===")
    print(f"音频参数:")
    print(f"  采样率: {SAMPLE_RATE} Hz")
    print(f"  时长: {DURATION} 秒")
    print(f"  样本数: {N_SAMPLES_2S}")

    print(f"\nMFCC参数 (NMSIS兼容):")
    print(f"  FFT长度: {N_FFT}")
    print(f"  窗长: {WIN_LENGTH} ({WIN_LENGTH_MS}ms)")
    print(f"  帧移: {HOP_LENGTH} ({HOP_LENGTH_MS}ms)")
    print(f"  Mel滤波器: {N_MELS}")
    print(f"  MFCC系数: {N_MFCC}")
    print(f"  预期帧数: {N_FRAMES_2S}")

    print(f"\n说话人配置:")
    print(f"  目标说话人: {TARGET_SPEAKERS}")
    print(f"  其他说话人: {OTHER_SPEAKERS}")
    print(f"  总类别数: {NUM_CLASSES}")

    print(f"\n特征维度:")
    print(f"  提取时: ({N_FRAMES_2S}, {N_MFCC}) - 完整13维")
    print(f"  训练时: ({N_FRAMES_2S}, {N_MFCC-1}) - 去除C0")

# =============================================================================
# 特征提取器实现
# =============================================================================

class NMSISMFCCExtractor:
    """
    与NMSIS DSP完全兼容的MFCC特征提取器 - 声纹识别版本

    实现流程（严格按照riscv_mfcc_f32.c）:
    1. 信号归一化 (absmax + scale)
    2. 应用Hamming窗
    3. FFT + 幅度谱计算
    4. 恢复原始幅度
    5. Mel滤波器组
    6. 对数处理 (加1e-6)
    7. DCT变换

    针对声纹识别优化：
    - 支持2秒音频输入
    - 输出13x198的MFCC特征矩阵
    """

    def __init__(
        self,
        sample_rate=SAMPLE_RATE,
        n_fft=N_FFT,
        win_length=WIN_LENGTH,
        hop_length=HOP_LENGTH,
        n_mels=N_MELS,
        n_mfcc=N_MFCC,
        device="cpu"
    ):
        self.sample_rate = sample_rate
        self.n_fft = n_fft
        self.win_length = win_length
        self.hop_length = hop_length
        self.n_mels = n_mels
        self.n_mfcc = n_mfcc
        self.device = device

        # 生成Hamming窗（与NMSIS DSP一致）
        self.hamming_window = get_window('hamming', self.win_length).astype(np.float32)

        # 生成Mel滤波器组
        self._generate_mel_filters()

        # 生成DCT变换矩阵
        self._generate_dct_matrix()

        print(f"🎵 NMSIS MFCC特征提取器初始化完成")
        print(f"   采样率: {self.sample_rate}Hz")
        print(f"   FFT长度: {self.n_fft}")
        print(f"   窗长: {self.win_length} ({self.win_length/self.sample_rate*1000:.1f}ms)")
        print(f"   步长: {self.hop_length} ({self.hop_length/self.sample_rate*1000:.1f}ms)")
        print(f"   Mel滤波器: {self.n_mels}个")
        print(f"   MFCC系数: {self.n_mfcc}维")
        print(f"   预期帧数: {N_FRAMES_2S}帧")

    def _generate_mel_filters(self):
        """
        生成Mel滤波器组，完全按照NMSIS DSP的实现
        """
        # Mel频率转换函数
        def hz_to_mel(hz):
            return 2595 * np.log10(1 + hz / 700.0)

        def mel_to_hz(mel):
            return 700 * (10**(mel / 2595) - 1)

        # 频率范围：0Hz到Nyquist频率
        low_freq_mel = hz_to_mel(0)
        high_freq_mel = hz_to_mel(self.sample_rate / 2)

        # 在Mel尺度上均匀分布
        mel_points = np.linspace(low_freq_mel, high_freq_mel, self.n_mels + 2)
        hz_points = mel_to_hz(mel_points)

        # 转换为FFT bin索引
        fft_bins = np.floor((self.n_fft + 1) * hz_points / self.sample_rate).astype(int)
        fft_bins = np.clip(fft_bins, 0, self.n_fft // 2)

        # 生成滤波器
        self.filter_positions = []
        self.filter_lengths = []
        self.filter_coeffs = []

        for i in range(self.n_mels):
            left = fft_bins[i]
            center = fft_bins[i + 1]
            right = fft_bins[i + 2]

            start_pos = left
            length = right - left + 1

            # 三角滤波器系数，使用float32
            coeffs = np.zeros(length, dtype=np.float32)

            # 上升部分
            for j in range(center - left + 1):
                if center != left:
                    coeffs[j] = np.float32(j) / np.float32(center - left)
                else:
                    coeffs[j] = np.float32(1.0)

            # 下降部分
            for j in range(center - left + 1, length):
                if right != center:
                    coeffs[j] = np.float32(right - (left + j)) / np.float32(right - center)
                else:
                    coeffs[j] = np.float32(0.0)

            self.filter_positions.append(start_pos)
            self.filter_lengths.append(length)
            self.filter_coeffs.extend(coeffs)

    def _generate_dct_matrix(self):
        """
        生成DCT变换矩阵，完全按照NMSIS DSP的实现
        基于NMSIS源码分析：C0使用直接求和，C1-C12使用标准DCT
        """
        self.dct_matrix = np.zeros((self.n_mfcc, self.n_mels), dtype=np.float32)

        # 完全按照NMSIS DSP的DCT系数实现
        for i in range(self.n_mfcc):
            for j in range(self.n_mels):
                if i == 0:
                    # C0: 直接求和（NMSIS DCT系数第一行全是1.0）
                    self.dct_matrix[i, j] = np.float32(1.0)
                else:
                    # C1-C12: 标准DCT-II公式（与NMSIS一致）
                    self.dct_matrix[i, j] = np.float32(
                        np.cos(np.pi * i * (j + 0.5) / self.n_mels)
                    )

    def _process_frame(self, frame):
        """
        处理单帧音频，严格按照NMSIS DSP的riscv_mfcc_f32.c流程
        """
        # 确保帧长度为FFT长度，使用float32
        if len(frame) < self.n_fft:
            padded_frame = np.zeros(self.n_fft, dtype=np.float32)
            padded_frame[:len(frame)] = frame
            frame = padded_frame
        else:
            frame = frame.astype(np.float32)

        # 1. 信号归一化 (riscv_absmax_f32 + riscv_scale_f32)
        max_value = np.max(np.abs(frame)).astype(np.float32)
        if max_value != 0.0:
            normalized_frame = (frame / max_value).astype(np.float32)
        else:
            normalized_frame = frame.astype(np.float32)
            max_value = np.float32(1.0)

        # 2. 应用窗函数 (riscv_mult_f32)
        windowed_frame = (normalized_frame[:self.win_length] * self.hamming_window).astype(np.float32)

        # 3. 零填充到FFT长度
        if len(windowed_frame) < self.n_fft:
            fft_input = np.zeros(self.n_fft, dtype=np.float32)
            fft_input[:len(windowed_frame)] = windowed_frame
        else:
            fft_input = windowed_frame[:self.n_fft]

        # 4. FFT和幅度谱 (riscv_rfft_fast_f32 + riscv_cmplx_mag_f32)
        from scipy.fft import fft
        fft_result = fft(fft_input, n=self.n_fft)
        magnitude = np.abs(fft_result[:self.n_fft//2 + 1]).astype(np.float32)

        # 5. 恢复原始幅度 (riscv_scale_f32)
        if max_value != 0.0:
            magnitude = (magnitude * max_value).astype(np.float32)

        # 6. Mel滤波器组 (riscv_dot_prod_f32)
        mel_energies = np.zeros(self.n_mels, dtype=np.float32)
        coeff_idx = 0

        for i in range(self.n_mels):
            start_pos = self.filter_positions[i]
            length = self.filter_lengths[i]

            # 提取滤波器系数，使用float32
            filter_coeffs = np.array(self.filter_coeffs[coeff_idx:coeff_idx + length], dtype=np.float32)
            coeff_idx += length

            # 点积运算，使用float32
            if start_pos + length <= len(magnitude):
                mel_energies[i] = np.dot(magnitude[start_pos:start_pos + length], filter_coeffs).astype(np.float32)
            else:
                # 处理边界情况
                available_length = len(magnitude) - start_pos
                if available_length > 0:
                    mel_energies[i] = np.dot(magnitude[start_pos:], filter_coeffs[:available_length]).astype(np.float32)

        # 7. 对数处理 (riscv_offset_f32 + riscv_vlog_f32)
        log_mel = np.log(mel_energies + np.float32(1e-6)).astype(np.float32)

        # 8. DCT变换 (riscv_mat_vec_mult_f32)
        mfcc_coeffs = np.dot(self.dct_matrix.astype(np.float32), log_mel).astype(np.float32)

        return mfcc_coeffs

    def __call__(self, waveform):
        """
        从音频波形中提取MFCC特征 - 声纹识别版本

        Args:
            waveform: 音频波形，可以是torch.Tensor或numpy数组
                     对于2秒音频，应该是16000个样本

        Returns:
            torch.Tensor: MFCC特征矩阵，形状为(1, 13, 198)
                         适配CNN模型的输入格式
        """
        if isinstance(waveform, torch.Tensor):
            # 处理多维tensor，提取音频数据
            if waveform.dim() > 1:
                audio_np = waveform.squeeze().numpy()
            else:
                audio_np = waveform.numpy()
        else:
            audio_np = waveform

        # 确保是1D数组
        if audio_np.ndim > 1:
            audio_np = audio_np.flatten()

        # 计算帧数（与NMSIS DSP一致）
        num_frames = (len(audio_np) - self.win_length) // self.hop_length + 1

        # 逐帧处理
        mfcc_matrix = np.zeros((self.n_mfcc, num_frames), dtype=np.float32)

        for frame_idx in range(num_frames):
            start_sample = frame_idx * self.hop_length
            end_sample = start_sample + self.win_length

            # 提取当前帧
            frame = audio_np[start_sample:end_sample].astype(np.float32)

            # 处理当前帧
            mfcc_coeffs = self._process_frame(frame)

            # 存储结果
            mfcc_matrix[:, frame_idx] = mfcc_coeffs

        # 转换为torch.Tensor并添加batch维度
        mfcc_tensor = torch.from_numpy(mfcc_matrix).unsqueeze(0)  # (1, 13, num_frames)

        return mfcc_tensor.to(self.device)


# 为了兼容性，创建一个简化的接口
class SpeakerFeatureExtractor:
    """
    声纹识别特征提取器的简化接口
    兼容原有的调用方式
    """
    def __init__(self):
        self.extractor = NMSISMFCCExtractor()
        self.sample_rate = SAMPLE_RATE
        self.audio_duration = DURATION
        self.expected_frames = N_FRAMES_2S

        print("🎵 声纹识别MFCC特征提取器初始化")
        print(f"   音频参数: {self.sample_rate}Hz, {self.audio_duration}s")
        print(f"   预期帧数: {self.expected_frames}帧")
        print(f"   特征维度: ({self.expected_frames}, 13) - 完整13维MFCC")
        print(f"   训练时处理: 去除C0，使用[:, :, 1:] → ({self.expected_frames}, 12)")

    def extract_mfcc_from_file(self, audio_path):
        """
        从音频文件提取完整13维MFCC特征
        与NNOM keyword_spotting保持一致：特征提取时保存完整13维，训练时去除C0

        Args:
            audio_path: 音频文件路径

        Returns:
            mfcc_features: 完整13维MFCC特征矩阵 (198, 13) 时间×特征
        """
        try:
            # 读取音频文件
            audio, _ = librosa.load(audio_path, sr=self.sample_rate, mono=True)

            # 确保音频长度为2秒
            target_samples = int(self.sample_rate * self.audio_duration)
            if len(audio) != target_samples:
                if len(audio) < target_samples:
                    audio = np.pad(audio, (0, target_samples - len(audio)), 'constant')
                else:
                    audio = audio[:target_samples]

            # 提取完整13维MFCC特征（包含C0）
            mfcc_tensor = self.extractor(audio)  # (1, 13, 198)

            # 转换为numpy格式：时间×特征
            mfcc_feat = mfcc_tensor.squeeze(0).numpy().T  # (198, 13)

            if VERBOSE:
                print(f"      实际帧数: {mfcc_feat.shape[0]}, MFCC维度: {mfcc_feat.shape[1]} (完整13维)")
            return mfcc_feat.astype('float32')

        except Exception as e:
            print(f"❌ 特征提取失败 {audio_path}: {e}")
            return None

    def process_dataset(self, dataset_root, output_dir):
        """
        处理整个数据集，提取所有说话人的MFCC特征
        按照我们商量的方案：输出(198, 12)的numpy数组

        Args:
            dataset_root: 数据集根目录
            output_dir: 输出目录
        """
        dataset_root = Path(dataset_root)
        output_dir = Path(output_dir)

        # 创建输出目录
        output_dir.mkdir(parents=True, exist_ok=True)

        print("🔄 开始批量MFCC特征提取...")
        print(f"📂 源目录: {dataset_root}")
        print(f"📁 输出目录: {output_dir}")
        print(f"🎯 输出格式: (198, 13) numpy数组，完整13维MFCC")
        print(f"📝 注意: 训练时需要去除C0: x_train[:, :, 1:]")

        # 说话人列表（使用全局配置）
        speakers = ALL_SPEAKERS

        # 总体统计
        total_stats = {'total_files': 0, 'success_files': 0, 'failed_files': 0}

        # 处理每个说话人
        for speaker_idx, speaker in enumerate(speakers, 1):
            speaker_dir = dataset_root / speaker
            if not speaker_dir.exists():
                if VERBOSE:
                    print(f"⚠️ 跳过不存在的说话人: {speaker}")
                continue

            print(f"📂 [{speaker_idx:2d}/{len(speakers)}] 处理说话人: {speaker}")

            # 统计当前说话人的文件数
            speaker_stats = {'total': 0, 'success': 0, 'failed': 0}

            # 处理train, val, test三个分割
            for split in ['train', 'val', 'test']:
                split_dir = speaker_dir / split
                if not split_dir.exists():
                    continue

                # 创建输出目录
                output_split_dir = output_dir / speaker / split
                output_split_dir.mkdir(parents=True, exist_ok=True)

                # 获取所有音频文件
                audio_files = list(split_dir.glob('*.wav'))
                if len(audio_files) == 0:
                    continue

                print(f"   {split}: {len(audio_files)} 个文件")

                # 根据SHOW_PROGRESS_ONLY设置进度条显示
                if SHOW_PROGRESS_ONLY:
                    # 简洁模式：只显示进度条
                    progress_bar = tqdm(audio_files, desc=f"{speaker}-{split}", leave=False,
                                      unit="files", ncols=80)
                else:
                    # 详细模式：显示文件名
                    progress_bar = tqdm(audio_files, desc=f"{speaker}-{split}", leave=False)

                for audio_file in progress_bar:
                    # 提取完整13维MFCC特征（与NNOM keyword_spotting一致）
                    mfcc_feat = self.extract_mfcc_from_file(audio_file)

                    if mfcc_feat is not None:
                        # 保存为numpy格式
                        output_filename = audio_file.stem + '.npy'
                        output_path = output_split_dir / output_filename
                        np.save(output_path, mfcc_feat)

                        total_stats['success_files'] += 1
                        speaker_stats['success'] += 1
                    else:
                        total_stats['failed_files'] += 1
                        speaker_stats['failed'] += 1

                    total_stats['total_files'] += 1
                    speaker_stats['total'] += 1

            # 显示当前说话人的统计
            if speaker_stats['total'] > 0:
                success_rate = speaker_stats['success'] / speaker_stats['total'] * 100
                print(f"   ✅ 完成: {speaker_stats['success']}/{speaker_stats['total']} ({success_rate:.1f}%)")
            else:
                print(f"   ⚠️ 无音频文件")

        # 保存处理信息
        self._save_extraction_info(output_dir, total_stats)

        # 打印总体统计
        print(f"\n✅ MFCC特征提取完成!")
        print(f"   总文件数: {total_stats['total_files']}")
        print(f"   成功提取: {total_stats['success_files']}")
        print(f"   提取失败: {total_stats['failed_files']}")
        print(f"   成功率: {total_stats['success_files']/total_stats['total_files']*100:.1f}%")

        return total_stats

    def _save_extraction_info(self, output_dir, stats):
        """保存特征提取的参数和统计信息"""
        info = {
            'extraction_params': {
                'sample_rate': self.sample_rate,
                'audio_duration': self.audio_duration,
                'expected_frames': self.expected_frames,
                'extractor_type': 'NMSIS_DSP_Compatible',
                'remove_c0': True
            },
            'feature_info': {
                'mfcc_dims': 13,  # 完整13维MFCC
                'time_frames': self.expected_frames,
                'feature_shape': f"({self.expected_frames}, 13)",
                'format': 'numpy数组，时间×特征'
            },
            'extraction_stats': stats,
            'note': "NMSIS DSP兼容的完整13维MFCC特征，训练时需要去除C0: x_train[:, :, 1:]"
        }

        # 保存为JSON文件
        info_file = output_dir / 'extraction_info.json'
        with open(info_file, 'w', encoding='utf-8') as f:
            json.dump(info, f, indent=2, ensure_ascii=False)

        print(f"📄 提取信息已保存: {info_file}")

    def extract_dataset_features(self, dataset_root=None, output_dir=None):
        """
        专门的数据集特征提取功能
        每次提取前自动清理现有特征目录

        Args:
            dataset_root: 数据集根目录，默认使用预设路径
            output_dir: 输出目录，默认保存到feature文件夹
        """
        # 使用默认路径
        if dataset_root is None:
            dataset_root = Path("speaker_recognition/dataset")
        if output_dir is None:
            output_dir = Path("speaker_recognition/feature")

        dataset_root = Path(dataset_root)
        output_dir = Path(output_dir)

        print("🎵 开始数据集MFCC特征提取")
        print("=" * 60)
        print(f"📂 数据集目录: {dataset_root}")
        print(f"📁 特征保存目录: {output_dir}")

        # 检查数据集目录
        if not dataset_root.exists():
            print(f"❌ 数据集目录不存在: {dataset_root}")
            print("请先运行音频预处理脚本生成数据集")
            return None

        # 清理并创建输出目录
        if output_dir.exists():
            print(f"🧹 清理现有特征目录...")
            import shutil
            shutil.rmtree(output_dir)

        output_dir.mkdir(parents=True, exist_ok=True)
        print(f"📁 创建特征目录: {output_dir}")

        # 开始特征提取
        stats = self.process_dataset(dataset_root, output_dir)

        if stats:
            print("\n" + "=" * 60)
            print("✅ 数据集特征提取完成!")
            print(f"📊 处理统计:")
            print(f"   总文件数: {stats['total_files']}")
            print(f"   成功提取: {stats['success_files']}")
            print(f"   提取失败: {stats['failed_files']}")
            if stats['total_files'] > 0:
                success_rate = stats['success_files'] / stats['total_files'] * 100
                print(f"   成功率: {success_rate:.1f}%")

            print(f"\n💾 特征文件保存在: {output_dir}")
            print(f"📋 文件结构:")
            print(f"   feature/")
            print(f"   ├── XiaoXin/train/*.npy")
            print(f"   ├── XiaoXin/val/*.npy")
            print(f"   ├── XiaoXin/test/*.npy")
            print(f"   ├── ... (其他说话人)")
            print(f"   ├── extraction_info.json")
            print(f"   └── label_mapping.json")

            print(f"\n📝 使用说明:")
            print(f"1. 每个.npy文件包含一个音频的MFCC特征")
            print(f"2. 特征形状: (198, 13) - 198帧 × 13维MFCC")
            print(f"3. 训练时需要去除C0: features[:, :, 1:]")
            print(f"4. 最终训练特征形状: (198, 12)")

        return stats


def extract_features_main():
    """
    主要的特征提取入口函数
    """
    print("🎵 声纹识别数据集特征提取工具")
    print("=" * 60)

    # 打印配置信息
    print_config()

    # 创建特征提取器
    print("\n" + "=" * 60)
    print("🔧 初始化特征提取器...")
    extractor = SpeakerFeatureExtractor()

    # 执行特征提取
    print("\n" + "=" * 60)
    stats = extractor.extract_dataset_features()

    return stats


if __name__ == "__main__":
    import argparse

    parser = argparse.ArgumentParser(description='NMSIS MFCC特征提取器')
    parser.add_argument('--mode', choices=['extract', 'test'], default='extract',
                       help='运行模式: extract=数据集特征提取, test=测试单文件')
    parser.add_argument('--dataset', type=str,
                       default="speaker_recognition/dataset",
                       help='数据集根目录路径')
    parser.add_argument('--output', type=str,
                       default="speaker_recognition/feature",
                       help='特征输出目录路径')
    parser.add_argument('--verbose', '-v', action='store_true',
                       help='显示详细信息（每个文件的处理结果）')

    args = parser.parse_args()

    # 应用verbose设置
    if hasattr(args, 'verbose') and args.verbose:
        set_verbose_mode(True)

    if args.mode == 'extract':

        # 数据集特征提取模式
        print("🎵 声纹识别数据集特征提取工具")
        print("=" * 60)
        print("🧹 自动清理模式: 每次提取前清理现有特征目录")
        if args.verbose:
            print("📝 详细模式: 显示每个文件的处理结果")
        else:
            print("📊 简洁模式: 只显示进度和统计信息")

        # 打印配置信息
        if args.verbose:
            print_config()
        else:
            print(f"\n⚙️ 配置: {SAMPLE_RATE}Hz, {DURATION}s, {N_FRAMES_2S}帧, {N_MFCC}维MFCC")

        # 创建特征提取器
        print("\n" + "=" * 60)
        print("🔧 初始化特征提取器...")
        extractor = SpeakerFeatureExtractor()

        # 执行特征提取
        print("\n" + "=" * 60)
        stats = extractor.extract_dataset_features(
            dataset_root=args.dataset,
            output_dir=args.output
        )

        if stats and stats['success_files'] > 0:
            print("\n🎉 特征提取任务完成!")
        else:
            print("\n❌ 特征提取任务失败!")

    elif args.mode == 'test':
        # 测试模式
        print("🧪 NMSIS MFCC特征提取器测试模式")
        print("=" * 60)

        # 打印配置信息
        print_config()

        # 测试代码
        print("\n" + "="*50)
        print("🧪 开始测试特征提取器...")

        extractor = SpeakerFeatureExtractor()
        print("✅ NMSIS MFCC特征提取器初始化完成")

        # 如果有测试文件，进行单文件测试
        test_file_pattern = Path(args.dataset) / "XiaoXin" / "train" / "*.wav"
        test_files = list(test_file_pattern.parent.glob("*.wav")) if test_file_pattern.parent.exists() else []

        if test_files:
            test_file = test_files[0]
            print(f"\n🧪 测试文件: {test_file}")
            mfcc_feat = extractor.extract_mfcc_from_file(test_file)
            if mfcc_feat is not None:
                print(f"✅ 特征提取成功: {mfcc_feat.shape}")
                print(f"   数据类型: {mfcc_feat.dtype}")
                print(f"   数值范围: [{mfcc_feat.min():.3f}, {mfcc_feat.max():.3f}]")
                print(f"   C0 (能量): {mfcc_feat[:3, 0]}")
                print(f"   C1 (倒谱): {mfcc_feat[:3, 1]}")

                # 演示训练时的处理
                mfcc_train = mfcc_feat[:, 1:]  # 去除C0
                print(f"\n📝 训练时处理:")
                print(f"   去除C0后形状: {mfcc_train.shape}")
                print(f"   训练用特征: C1-C12")
            else:
                print("❌ 特征提取失败")
        else:
            print("⚠️ 未找到测试文件，跳过单文件测试")
            print(f"   查找路径: {test_file_pattern}")

        print("\n✅ 测试完成!")