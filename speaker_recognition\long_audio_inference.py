#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
长语音实时说话人识别推理系统
基于WebRTC VAD + 连续判定 + 滚动识别的实时处理方案
"""

import os
import sys
import numpy as np
import librosa
import webrtcvad
import soundfile as sf
from pathlib import Path
from collections import deque
import time

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# TensorFlow导入
import tensorflow as tf
from tensorflow import keras

# 导入特征提取器
from feature_extractor import SpeakerFeatureExtractor, ALL_SPEAKERS

# =============================================================================
# 系统配置参数
# =============================================================================

# 音频参数
SAMPLE_RATE = 8000              # 采样率8kHz
VAD_FRAME_MS = 30               # VAD检测帧长30ms
VAD_FRAME_SAMPLES = int(SAMPLE_RATE * VAD_FRAME_MS / 1000)  # 240样本

# 缓冲区参数
INITIAL_BUFFER_MS = 100         # 初始缓冲100ms
INITIAL_BUFFER_SAMPLES = int(SAMPLE_RATE * INITIAL_BUFFER_MS / 1000)  # 800样本
SPEECH_BUFFER_MS = 2500         # 人声缓冲2.5秒
SPEECH_BUFFER_SAMPLES = int(SAMPLE_RATE * SPEECH_BUFFER_MS / 1000)  # 20000样本

# VAD判定参数
SPEECH_CONFIRM_COUNT = 10        # 连续5次确认人声(150ms)(300ms)
SILENCE_CONFIRM_COUNT =17       # 连续8次确认静音(240ms)(510ms)

# 识别参数
RECOGNITION_DATA_MS = 2000      # 识别需要2秒数据
RECOGNITION_DATA_SAMPLES = int(SAMPLE_RATE * RECOGNITION_DATA_MS / 1000)  # 16000样本
OUTPUT_INTERVAL_MS = 1000       # 输出间隔1秒
CONFIDENCE_THRESHOLD = 0.6      # 置信度阈值

# 路径配置
CURRENT_DIR = Path(__file__).parent
MODEL_PATH = CURRENT_DIR / 'model' / 'speaker_model.h5'

# =============================================================================
# VAD状态管理器
# =============================================================================

class VADStateManager:
    """WebRTC VAD状态管理器"""
    
    def __init__(self):
        # WebRTC VAD初始化
        self.vad = webrtcvad.Vad(3)  # 最敏感模式
        
        # 状态变量
        self.speech_count = 0        # 连续人声计数
        self.silence_count = 0       # 连续静音计数
        self.current_state = "UNKNOWN"  # 当前状态
        
        # 临时缓冲区(100ms)
        self.temp_buffer = np.zeros(INITIAL_BUFFER_SAMPLES, dtype=np.float32)
        self.temp_filled = 0
        
        print("🎤 VAD状态管理器初始化完成")
        print(f"   VAD模式: 3 (最敏感)")
        print(f"   检测帧长: {VAD_FRAME_MS}ms")
        print(f"   人声确认: {SPEECH_CONFIRM_COUNT}次连续({SPEECH_CONFIRM_COUNT * VAD_FRAME_MS}ms)")
        print(f"   静音确认: {SILENCE_CONFIRM_COUNT}次连续({SILENCE_CONFIRM_COUNT * VAD_FRAME_MS}ms)")
    
    def add_audio_chunk(self, audio_chunk):
        """添加30ms音频块并进行VAD检测"""
        if len(audio_chunk) != VAD_FRAME_SAMPLES:
            raise ValueError(f"音频块长度应为{VAD_FRAME_SAMPLES}样本，实际为{len(audio_chunk)}")
        
        # 转换为int16进行VAD检测
        audio_int16 = (audio_chunk * 32767).astype(np.int16)
        is_speech = self.vad.is_speech(audio_int16.tobytes(), SAMPLE_RATE)
        
        # 更新状态计数
        if is_speech:
            self.speech_count += 1
            self.silence_count = 0
        else:
            self.silence_count += 1
            self.speech_count = 0
        
        # 更新状态
        old_state = self.current_state
        self._update_state()
        
        # 返回检测结果和状态变化
        return {
            'is_speech': is_speech,
            'current_state': self.current_state,
            'state_changed': old_state != self.current_state,
            'speech_count': self.speech_count,
            'silence_count': self.silence_count
        }
    
    def _update_state(self):
        """基于连续计数更新状态"""
        if self.speech_count >= SPEECH_CONFIRM_COUNT:
            self.current_state = "SPEECH"
        elif self.silence_count >= SILENCE_CONFIRM_COUNT:
            self.current_state = "SILENCE"
        else:
            # 保持当前状态或设为检测中
            if self.current_state == "UNKNOWN":
                self.current_state = "DETECTING"
    
    def get_state_info(self):
        """获取当前状态信息"""
        return {
            'state': self.current_state,
            'speech_count': self.speech_count,
            'silence_count': self.silence_count,
            'progress': self._get_progress()
        }
    
    def _get_progress(self):
        """获取状态确认进度"""
        if self.current_state == "SPEECH":
            return 1.0
        elif self.current_state == "SILENCE":
            return 1.0
        elif self.speech_count > 0:
            return self.speech_count / SPEECH_CONFIRM_COUNT
        elif self.silence_count > 0:
            return self.silence_count / SILENCE_CONFIRM_COUNT
        else:
            return 0.0

# =============================================================================
# 环形人声数据管理器
# =============================================================================

class CircularSpeechDataManager:
    """环形人声数据收集和管理器 - 永不清空，持续滚动"""

    def __init__(self):
        # 扩大环形缓冲区到10秒，避免频繁清空
        self.buffer_duration_ms = 10000  # 10秒
        self.buffer_samples = int(SAMPLE_RATE * self.buffer_duration_ms / 1000)  # 80000样本
        self.speech_buffer = np.zeros(self.buffer_samples, dtype=np.float32)

        # 环形缓冲区管理
        self.write_pos = 0           # 当前写入位置
        self.total_written = 0       # 总写入样本数
        self.is_collecting = False   # 是否正在收集

        # 时间戳环形缓冲区（每30ms一个时间戳）
        self.timestamp_buffer_size = self.buffer_duration_ms // VAD_FRAME_MS  # 333个时间戳
        self.timestamps = np.zeros(self.timestamp_buffer_size, dtype=np.float64)
        self.timestamp_write_pos = 0

        print("💾 环形人声数据管理器初始化完成")
        print(f"   环形缓冲区大小: {self.buffer_duration_ms}ms ({self.buffer_samples}样本)")
        print(f"   识别数据长度: {RECOGNITION_DATA_MS}ms ({RECOGNITION_DATA_SAMPLES}样本)")
        print(f"   时间戳缓冲区: {self.timestamp_buffer_size}个")

    def start_collecting(self, timestamp):
        """开始收集人声数据"""
        if not self.is_collecting:
            self.is_collecting = True
            # print(f"🎵 开始收集人声数据 @ {timestamp:.3f}s")

    def stop_collecting(self, timestamp):
        """停止收集人声数据"""
        if self.is_collecting:
            self.is_collecting = False
            # print(f"⏹️ 停止收集人声数据 @ {timestamp:.3f}s")

    def add_speech_chunk(self, audio_chunk, timestamp):
        """添加人声音频块到环形缓冲区"""
        if not self.is_collecting:
            return False

        chunk_size = len(audio_chunk)

        # 写入音频数据到环形缓冲区
        for i in range(chunk_size):
            self.speech_buffer[self.write_pos] = audio_chunk[i]
            self.write_pos = (self.write_pos + 1) % self.buffer_samples
            self.total_written += 1

        # 记录时间戳（每30ms记录一次）
        self.timestamps[self.timestamp_write_pos] = timestamp
        self.timestamp_write_pos = (self.timestamp_write_pos + 1) % self.timestamp_buffer_size

        return True

    def can_recognize(self):
        """检查是否有足够数据进行识别"""
        # 检查是否有至少2秒的数据
        available_samples = min(self.total_written, self.buffer_samples)
        return available_samples >= RECOGNITION_DATA_SAMPLES

    def get_recognition_data(self):
        """获取最新2秒数据用于识别 - 从环形缓冲区提取"""
        if not self.can_recognize():
            return None

        # 计算起始位置（从当前写入位置向前2秒）
        start_pos = (self.write_pos - RECOGNITION_DATA_SAMPLES) % self.buffer_samples

        # 提取数据
        if start_pos + RECOGNITION_DATA_SAMPLES <= self.buffer_samples:
            # 数据连续，直接复制
            return self.speech_buffer[start_pos:start_pos + RECOGNITION_DATA_SAMPLES].copy()
        else:
            # 数据跨越边界，分两部分复制
            part1_size = self.buffer_samples - start_pos
            part2_size = RECOGNITION_DATA_SAMPLES - part1_size

            result = np.zeros(RECOGNITION_DATA_SAMPLES, dtype=np.float32)
            result[:part1_size] = self.speech_buffer[start_pos:]
            result[part1_size:] = self.speech_buffer[:part2_size]
            return result

    def get_buffer_info(self):
        """获取缓冲区状态信息"""
        available_samples = min(self.total_written, self.buffer_samples)
        duration_ms = (available_samples / SAMPLE_RATE) * 1000

        return {
            'valid_samples': available_samples,
            'duration_ms': duration_ms,
            'can_recognize': self.can_recognize(),
            'is_collecting': self.is_collecting,
            'buffer_usage': available_samples / self.buffer_samples,
            'total_written': self.total_written,
            'write_position': self.write_pos
        }

    def get_recent_timestamp(self):
        """获取最近的时间戳"""
        if self.timestamp_write_pos == 0:
            return self.timestamps[self.timestamp_buffer_size - 1]
        else:
            return self.timestamps[self.timestamp_write_pos - 1]

    def reset_collection_state(self):
        """重置收集状态但不清空数据"""
        self.is_collecting = False
        # 注意：不清空缓冲区数据，保持历史数据

# =============================================================================
# 说话人识别管理器
# =============================================================================

class SpeakerRecognitionManager:
    """说话人识别管理器"""

    def __init__(self):
        # 加载模型和特征提取器
        self.feature_extractor = SpeakerFeatureExtractor()
        self.model = self._load_model()

        # 识别状态
        self.current_speaker = None
        self.current_confidence = 0.0
        self.last_recognition_time = 0
        self.stable_count = 0           # 稳定识别次数
        self.switch_detected = False    # 切换检测标志

        print("🤖 说话人识别管理器初始化完成")
        print(f"   置信度阈值: {CONFIDENCE_THRESHOLD}")
        print(f"   支持说话人: {len(ALL_SPEAKERS)}个")

    def _load_model(self):
        """加载训练好的模型"""
        if not MODEL_PATH.exists():
            raise FileNotFoundError(f"模型文件不存在: {MODEL_PATH}")

        model = keras.models.load_model(str(MODEL_PATH))
        print(f"✅ 模型加载成功: {MODEL_PATH}")
        print(f"   输入形状: {model.input_shape}")
        print(f"   输出形状: {model.output_shape}")

        return model

    def perform_recognition(self, speech_data, current_time):
        """执行说话人识别"""
        try:
            # 1. 特征提取
            features = self._extract_features(speech_data)
            if features is None:
                return None

            # 2. 模型推理
            prediction = self.model.predict(features, verbose=0)
            speaker_idx = np.argmax(prediction[0])
            confidence = np.max(prediction[0])

            # 3. 结果处理
            if confidence >= CONFIDENCE_THRESHOLD:
                predicted_speaker = ALL_SPEAKERS[speaker_idx]
            else:
                predicted_speaker = "其他说话人"

            # 4. 检测说话人切换
            if self.current_speaker is None:
                # 首次识别
                self.current_speaker = predicted_speaker
                self.current_confidence = confidence
                self.stable_count = 1
                self.switch_detected = False
            elif self.current_speaker == predicted_speaker:
                # 识别结果稳定
                self.stable_count += 1
                self.current_confidence = confidence
                self.switch_detected = False
            else:
                # 检测到可能的切换
                if self.stable_count >= 2:  # 之前的识别比较稳定
                    self.switch_detected = True
                    print(f"🔄 检测到说话人切换: {self.current_speaker} → {predicted_speaker}")

                # 更新为新的说话人
                self.current_speaker = predicted_speaker
                self.current_confidence = confidence
                self.stable_count = 1

            self.last_recognition_time = current_time

            return {
                'speaker': self.current_speaker,
                'confidence': self.current_confidence,
                'switch_detected': self.switch_detected,
                'stable_count': self.stable_count,
                'timestamp': current_time
            }

        except Exception as e:
            print(f"❌ 识别错误: {e}")
            return None

    def _extract_features(self, audio_data):
        """提取MFCC特征"""
        try:
            # 创建临时音频文件
            temp_path = "temp_recognition_audio.wav"
            sf.write(temp_path, audio_data, SAMPLE_RATE)

            # 提取特征
            features = self.feature_extractor.extract_mfcc_from_file(temp_path)

            # 清理临时文件
            if os.path.exists(temp_path):
                os.remove(temp_path)

            if features is not None:
                # 去除C0，添加batch和channel维度
                features = features[:, 1:]  # (198, 12)
                features = features.reshape(1, 198, 12, 1)  # (1, 198, 12, 1)
                return features

        except Exception as e:
            print(f"❌ 特征提取错误: {e}")

        return None

    def reset_for_switch(self):
        """重置状态用于说话人切换"""
        self.switch_detected = False
        self.stable_count = 0
        print("🔄 识别状态已重置，准备重新识别")

    def get_recognition_info(self):
        """获取当前识别信息"""
        return {
            'speaker': self.current_speaker,
            'confidence': self.current_confidence,
            'stable_count': self.stable_count,
            'switch_detected': self.switch_detected,
            'last_recognition': self.last_recognition_time
        }

# =============================================================================
# 输出控制器
# =============================================================================

class OutputController:
    """结果输出控制器"""

    def __init__(self):
        self.last_output_time = 0
        self.output_interval = OUTPUT_INTERVAL_MS / 1000.0  # 转换为秒

        print("📢 输出控制器初始化完成")
        print(f"   输出间隔: {OUTPUT_INTERVAL_MS}ms")

    def should_output(self, current_time):
        """判断是否应该输出结果"""
        return (current_time - self.last_output_time) >= self.output_interval

    def generate_output(self, current_time, vad_state, speech_info, recognition_result):
        """生成输出内容"""
        timestamp_ms = int(current_time * 1000)

        if vad_state == "SPEECH":
            if recognition_result and recognition_result['speaker']:
                if recognition_result['switch_detected']:
                    # 检测到切换，但继续使用当前识别结果
                    speaker = recognition_result['speaker']
                    output = f"{timestamp_ms} ms，说话人 ID：{speaker} (切换检测)"
                else:
                    # 正常识别结果
                    speaker = recognition_result['speaker']
                    output = f"{timestamp_ms} ms，说话人 ID：{speaker}"
            elif speech_info['can_recognize']:
                # 有足够数据但识别失败
                output = f"{timestamp_ms} ms，识别处理中..."
            else:
                # 数据积累中
                duration = speech_info['duration_ms']
                output = f"{timestamp_ms} ms，人声数据积累中...({duration:.0f}ms/{RECOGNITION_DATA_MS}ms)"

        elif vad_state == "SILENCE":
            output = f"{timestamp_ms} ms，无人声"

        else:
            # DETECTING或UNKNOWN状态
            output = f"{timestamp_ms} ms，检测中..."

        self.last_output_time = current_time
        return output

# =============================================================================
# 长语音实时推理主系统
# =============================================================================

class LongAudioSpeakerRecognitionSystem:
    """长语音说话人识别主系统"""

    def __init__(self):
        # 初始化各个组件
        self.vad_manager = VADStateManager()
        self.speech_manager = CircularSpeechDataManager()
        self.recognition_manager = SpeakerRecognitionManager()
        self.output_controller = OutputController()

        # 系统状态
        self.current_time = 0.0
        self.total_chunks_processed = 0

        print("\n" + "="*60)
        print("🎤 长语音说话人识别系统初始化完成")
        print("="*60)
        print("📋 系统配置:")
        print(f"   - VAD检测间隔: {VAD_FRAME_MS}ms")
        print(f"   - 人声确认时间: {SPEECH_CONFIRM_COUNT * VAD_FRAME_MS}ms")
        print(f"   - 静音确认时间: {SILENCE_CONFIRM_COUNT * VAD_FRAME_MS}ms")
        print(f"   - 识别数据长度: {RECOGNITION_DATA_MS}ms")
        print(f"   - 输出间隔: {OUTPUT_INTERVAL_MS}ms")
        print(f"   - 置信度阈值: {CONFIDENCE_THRESHOLD}")
        print("="*60)

    def process_audio_chunk(self, audio_chunk, timestamp):
        """处理30ms音频块 - 系统核心处理函数"""
        self.current_time = timestamp
        self.total_chunks_processed += 1

        # 1. VAD检测和状态更新
        vad_result = self.vad_manager.add_audio_chunk(audio_chunk)
        current_state = vad_result['current_state']

        # 2. 根据VAD状态管理人声数据收集
        if current_state == "SPEECH":
            # 确认为人声，开始或继续收集
            if not self.speech_manager.is_collecting:
                self.speech_manager.start_collecting(timestamp)

            # 添加音频数据到人声缓冲区
            self.speech_manager.add_speech_chunk(audio_chunk, timestamp)

        elif current_state == "SILENCE":
            # 确认为静音，停止收集并清理
            if self.speech_manager.is_collecting:
                self.speech_manager.stop_collecting(timestamp)
                # 可选：清空缓冲区或保留一段时间
                # self.speech_manager.clear_buffer()

        # 3. 尝试说话人识别
        recognition_result = None
        if (current_state == "SPEECH" and
            self.speech_manager.can_recognize()):

            speech_data = self.speech_manager.get_recognition_data()
            if speech_data is not None:
                recognition_result = self.recognition_manager.perform_recognition(
                    speech_data, timestamp
                )

                # 如果检测到说话人切换，重置识别状态但不清空缓冲区
                if (recognition_result and
                    recognition_result['switch_detected']):
                    self.recognition_manager.reset_for_switch()
                    # 注意：不再清空缓冲区，保持历史数据用于连续识别

        # 4. 输出结果
        if self.output_controller.should_output(timestamp):
            speech_info = self.speech_manager.get_buffer_info()
            output = self.output_controller.generate_output(
                timestamp, current_state, speech_info, recognition_result
            )
            print(output)

        # 5. 返回处理状态（用于调试）
        return {
            'timestamp': timestamp,
            'vad_state': current_state,
            'vad_result': vad_result,
            'speech_info': self.speech_manager.get_buffer_info(),
            'recognition_result': recognition_result
        }

    def process_audio_file(self, audio_file_path):
        """处理完整音频文件"""
        print(f"\n🎵 开始处理音频文件: {audio_file_path}")

        # 检查文件是否存在
        if not os.path.exists(audio_file_path):
            raise FileNotFoundError(f"音频文件不存在: {audio_file_path}")

        # 加载音频
        print("📂 加载音频文件...")
        audio, sr = librosa.load(audio_file_path, sr=SAMPLE_RATE, mono=True)
        total_duration = len(audio) / sr

        print(f"📊 音频信息:")
        print(f"   - 文件路径: {audio_file_path}")
        print(f"   - 时长: {total_duration:.2f}秒")
        print(f"   - 采样率: {sr}Hz")
        print(f"   - 样本数: {len(audio)}")
        print(f"   - 预计处理块数: {int(total_duration * 1000 / VAD_FRAME_MS)}")

        print("\n" + "="*60)
        print("🚀 开始实时处理...")
        print("="*60)

        # 处理音频流
        current_sample = 0
        current_time = 0.0
        chunk_count = 0

        start_time = time.time()

        while current_sample + VAD_FRAME_SAMPLES <= len(audio):
            # 提取30ms音频块
            audio_chunk = audio[current_sample:current_sample + VAD_FRAME_SAMPLES]

            # 处理音频块
            self.process_audio_chunk(audio_chunk, current_time)

            # 更新位置
            current_sample += VAD_FRAME_SAMPLES
            current_time += VAD_FRAME_MS / 1000.0
            chunk_count += 1

            # 每处理100个块显示一次进度（3秒）
            if chunk_count % 100 == 0:
                progress = (current_time / total_duration) * 100
                # print(f"📈 处理进度: {progress:.1f}% ({current_time:.1f}s/{total_duration:.1f}s)")

        processing_time = time.time() - start_time

        print("\n" + "="*60)
        print("✅ 音频处理完成")
        print("="*60)
        print(f"📊 处理统计:")
        print(f"   - 总时长: {current_time:.2f}秒")
        print(f"   - 处理块数: {chunk_count}")
        print(f"   - 处理时间: {processing_time:.2f}秒")
        print(f"   - 实时率: {current_time/processing_time:.2f}x")
        print(f"   - 平均每块: {processing_time/chunk_count*1000:.2f}ms")

        # 输出最终状态
        print(f"\n📋 最终状态:")
        vad_info = self.vad_manager.get_state_info()
        speech_info = self.speech_manager.get_buffer_info()
        recognition_info = self.recognition_manager.get_recognition_info()

        print(f"   - VAD状态: {vad_info['state']}")
        print(f"   - 人声缓冲: {speech_info['duration_ms']:.0f}ms")
        print(f"   - 当前说话人: {recognition_info['speaker']}")
        print(f"   - 识别置信度: {recognition_info['confidence']:.3f}")

        return {
            'total_duration': current_time,
            'chunks_processed': chunk_count,
            'processing_time': processing_time,
            'final_state': {
                'vad': vad_info,
                'speech': speech_info,
                'recognition': recognition_info
            }
        }

# =============================================================================
# 主函数和使用示例
# =============================================================================

def main():
    """主函数：演示长语音说话人识别系统"""
    print("🎤 长语音说话人识别系统")
    print("=" * 60)

    # 音频文件路径
    audio_file = CURRENT_DIR / "test" / "pdm_audio_2025.wav"

    # 检查音频文件
    if not audio_file.exists():
        print(f"❌ 音频文件不存在: {audio_file}")
        print("请确保音频文件位于 speaker_recognition/test/ 目录下")
        return

    try:
        # 创建系统实例
        system = LongAudioSpeakerRecognitionSystem()

        # 处理音频文件
        result = system.process_audio_file(str(audio_file))

        print(f"\n🎉 处理完成!")
        print(f"实时率: {result['final_state']['recognition']['speaker']}")

    except Exception as e:
        print(f"❌ 处理过程出错: {e}")
        import traceback
        traceback.print_exc()

def test_short_audio():
    """测试短音频文件"""
    print("🧪 测试模式：短音频文件")
    print("=" * 60)

    # 使用较短的测试音频
    test_files = [
        "test01.wav", "test02.wav", "test03.wav",
        "XiaoYuan_XiaoYuan_01_1_board_002.wav"
    ]

    for test_file in test_files:
        audio_path = CURRENT_DIR / "test" / test_file
        if audio_path.exists():
            print(f"\n🎵 测试文件: {test_file}")
            try:
                system = LongAudioSpeakerRecognitionSystem()
                result = system.process_audio_file(str(audio_path))
                print(f"✅ {test_file} 处理完成")
            except Exception as e:
                print(f"❌ {test_file} 处理失败: {e}")
            print("-" * 40)

def demo_real_time_simulation():
    """演示实时处理模拟"""
    print("🎬 实时处理模拟演示")
    print("=" * 60)

    audio_file = CURRENT_DIR / "test" / "pdm_audio_2025.wav"
    if not audio_file.exists():
        print(f"❌ 演示音频文件不存在: {audio_file}")
        return

    # 加载音频
    audio, sr = librosa.load(str(audio_file), sr=SAMPLE_RATE, mono=True)

    # 创建系统
    system = LongAudioSpeakerRecognitionSystem()

    print("🎭 模拟实时处理（每30ms处理一块）...")
    print("按 Ctrl+C 停止演示")
    print("-" * 60)

    try:
        current_sample = 0
        current_time = 0.0

        while current_sample + VAD_FRAME_SAMPLES <= len(audio):
            # 提取30ms音频块
            audio_chunk = audio[current_sample:current_sample + VAD_FRAME_SAMPLES]

            # 处理音频块
            system.process_audio_chunk(audio_chunk, current_time)

            # 模拟实时延迟（可选）
            # time.sleep(VAD_FRAME_MS / 1000.0)

            # 更新位置
            current_sample += VAD_FRAME_SAMPLES
            current_time += VAD_FRAME_MS / 1000.0

            # 限制演示时长（前30秒）
            if current_time > 30.0:
                print("\n⏰ 演示时长达到30秒，自动停止")
                break

    except KeyboardInterrupt:
        print("\n⏹️ 用户停止演示")

    print("🎬 演示结束")

if __name__ == "__main__":
    import argparse

    parser = argparse.ArgumentParser(description='长语音说话人识别系统')
    parser.add_argument('--mode', choices=['main', 'test', 'demo'], default='main',
                       help='运行模式: main=处理长音频, test=测试短音频, demo=实时模拟')
    parser.add_argument('--audio', type=str,
                       help='指定音频文件路径（可选）')

    args = parser.parse_args()

    if args.mode == 'main':
        if args.audio:
            # 处理指定音频文件
            try:
                system = LongAudioSpeakerRecognitionSystem()
                system.process_audio_file(args.audio)
            except Exception as e:
                print(f"❌ 处理失败: {e}")
        else:
            # 处理默认音频文件
            main()
    elif args.mode == 'test':
        test_short_audio()
    elif args.mode == 'demo':
        demo_real_time_simulation()

    print("\n👋 程序结束")
