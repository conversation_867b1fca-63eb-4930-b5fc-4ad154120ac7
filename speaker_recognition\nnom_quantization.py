#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
声纹识别 - NNOM量化权重导出脚本

基于NNOM原始脚本进行说话人识别模型的量化和部署

作者: Augment Agent
日期: 2025-08-02

功能:
1. 加载训练好的模型
2. 转换为NNOM兼容格式 (融合BatchNormalization)
3. 使用NNOM原始脚本进行量化
4. 导出C头文件格式的权重
"""

import os
import sys
import numpy as np
import tensorflow as tf
from tensorflow import keras

# 添加本地NNOM脚本路径
nnom_scripts_path = os.path.join(os.path.dirname(__file__), 'nnom_scripts')
if nnom_scripts_path not in sys.path:
    sys.path.append(nnom_scripts_path)

try:
    # 直接从nnom模块导入，因为已经添加了路径
    from nnom import generate_model, evaluate_model, generate_test_bin
    print("✅ NNOM工具导入成功 (使用本地脚本)")
except ImportError as e:
    print(f"❌ NNOM工具导入失败: {e}")
    print(f"   请检查路径: {nnom_scripts_path}")
    sys.exit(1)

# 导入训练模块
from train_speaker_model import load_speaker_data, TARGET_SPEAKERS, OTHER_SPEAKERS, N_FRAMES, N_MFCC, NUM_CLASSES

def fuse_bn_into_conv(conv_layer, bn_layer):
    """将BatchNormalization层融合到Conv2D层中"""
    # 获取卷积层权重和偏置
    conv_weights, conv_bias = conv_layer.get_weights()

    # 获取BN层参数
    gamma, beta, moving_mean, moving_var = bn_layer.get_weights()
    epsilon = bn_layer.epsilon

    # 计算融合后的权重和偏置
    # BN公式: y = gamma * (x - mean) / sqrt(var + epsilon) + beta
    # 融合到卷积: new_weight = gamma * old_weight / sqrt(var + epsilon)
    #            new_bias = gamma * (old_bias - mean) / sqrt(var + epsilon) + beta
    std = np.sqrt(moving_var + epsilon)
    scale = gamma / std

    # 新权重: 对每个输出通道进行缩放
    new_weights = conv_weights * scale.reshape(1, 1, 1, -1)

    # 新偏置
    new_bias = scale * (conv_bias - moving_mean) + beta

    return new_weights, new_bias

def convert_to_nnom_compatible():
    """将原始模型转换为NNOM兼容格式 (使用NNOM官方融合工具)"""
    print("🔄 转换模型为NNOM兼容格式...")

    # 加载原始模型
    original_model_path = "speaker_recognition/model/speaker_model.h5"
    if not os.path.exists(original_model_path):
        print(f"❌ 原始模型文件不存在: {original_model_path}")
        return None

    original_model = keras.models.load_model(original_model_path, compile=False)
    print(f"✅ 原始模型加载成功")

    # 打印原始模型结构以便调试
    print("📋 原始模型层结构:")
    for i, layer in enumerate(original_model.layers):
        print(f"   {i}: {layer.name} ({layer.__class__.__name__}) - 输出形状: {layer.output_shape}")

    # 训练模型现在已经使用NNOM兼容的层名，直接返回
    print("✅ 训练模型已使用NNOM兼容的层名")
    print("   NNOM将自动融合BatchNormalization层到Conv层")
    print(f"   参数数量: {original_model.count_params():,}")

    return original_model

def load_model():
    """加载训练好的模型 (优先使用NNOM兼容模型)"""
    # 优先尝试加载NNOM兼容模型
    nnom_model_path = "speaker_recognition/model/speaker_model_nnom.h5"
    if os.path.exists(nnom_model_path):
        model = keras.models.load_model(nnom_model_path, compile=False)
        print(f"✅ NNOM兼容模型加载成功: {nnom_model_path}")
        print(f"   输入形状: {model.input_shape}")
        print(f"   输出形状: {model.output_shape}")
        print(f"   参数数量: {model.count_params():,}")
        return model

    # 如果NNOM兼容模型不存在，尝试转换原始模型
    print("⚠️  NNOM兼容模型不存在，尝试转换原始模型...")
    model = convert_to_nnom_compatible()
    if model is None:
        print("❌ 模型转换失败")
        return None

    return model

def load_test_data():
    """加载测试数据用于量化"""
    print("🔄 加载测试数据...")

    try:
        # 加载完整数据集 (返回3个值: train, val, test)
        (train_data, train_labels), (val_data, val_labels), (test_data, test_labels) = load_speaker_data()

        # 增加量化校准样本数量以提高量化精度
        # 使用更多样本可以更好地反映数据分布，减少量化误差
        num_samples = min(1500, len(test_data))  # 从100增加到1500
        x_test = test_data[:num_samples]
        y_test = test_labels[:num_samples]

        # 如果测试集样本不够，补充验证集样本
        if len(x_test) < 1000 and len(val_data) > 0:
            additional_samples = min(1000 - len(x_test), len(val_data))
            print(f"   📈 测试集样本不足，补充 {additional_samples} 个验证集样本")
            x_test = np.vstack([x_test, val_data[:additional_samples]])
            y_test = np.hstack([y_test, val_labels[:additional_samples]])
            print(f"   📊 合并后样本数量: {len(x_test)}")

        print(f"✅ 测试数据加载完成:")
        print(f"   样本数量: {len(x_test)}")
        print(f"   输入形状: {x_test.shape}")
        print(f"   标签形状: {y_test.shape}")
        print(f"   数据范围: [{x_test.min():.3f}, {x_test.max():.3f}]")

        return x_test, y_test

    except Exception as e:
        print(f"❌ 数据加载失败: {e}")
        return None, None

def export_nnom_weights():
    """导出NNOM量化权重 (参考mnist-simple例子)"""
    print("\n🚀 NNOM量化权重导出")
    print("=" * 50)

    # 1. 加载NNOM兼容模型
    model = load_model()
    if model is None:
        return False

    # 2. 加载测试数据用于量化
    x_test, y_test = load_test_data()
    if x_test is None:
        return False

    # 3. 评估模型 (可选)
    print("\n📊 评估模型性能...")
    try:
        # 转换标签为one-hot编码（如果需要）
        if len(y_test.shape) == 1 or y_test.shape[1] == 1:
            from tensorflow.keras.utils import to_categorical
            y_test_onehot = to_categorical(y_test.flatten(), num_classes=16)
            print(f"   标签转换: {y_test.shape} -> {y_test_onehot.shape}")
            evaluate_model(model, x_test, y_test_onehot)
        else:
            evaluate_model(model, x_test, y_test)
    except Exception as e:
        print(f"⚠️  模型评估失败: {e}")
        print("   注意: 评估失败不影响量化过程，可以继续进行")

    # 4. 生成量化权重 (参考mnist-simple的做法)
    print("\n🔧 生成量化权重...")
    print(f"   📊 使用 {len(x_test)} 个校准样本进行量化分析...")
    print(f"   🎯 校准样本数量已从100增加到{len(x_test)}，预期量化精度提升")

    try:
        # 使用NNOM的generate_model函数，参考mnist-simple
        # generate_model(model, np.vstack((x_train, x_test)), name="weights.h")
        generate_model(model, x_test, name="speaker_weights.h", format="hwc")

        # 检查生成的文件
        generated_files = ["speaker_weights.h", "weights.h"]
        output_file = None

        for filename in generated_files:
            if os.path.exists(filename):
                output_file = filename
                break

        if output_file:
            # 创建输出目录
            output_dir = "speaker_recognition/nnom_weights"
            os.makedirs(output_dir, exist_ok=True)

            # 移动到目标位置
            target_file = os.path.join(output_dir, "speaker_weights.h")
            if output_file != target_file:
                import shutil
                shutil.move(output_file, target_file)

            print(f"✅ 权重文件生成成功: {target_file}")

            # 显示文件信息
            file_size = os.path.getsize(target_file)
            print(f"   文件大小: {file_size:,} 字节")

            # 显示文件内容预览
            with open(target_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()
                print(f"   文件行数: {len(lines)}")
                print(f"   文件内容预览:")
                for i, line in enumerate(lines[:5]):
                    print(f"      {i+1:2d}: {line.rstrip()}")
                if len(lines) > 5:
                    print(f"      ... (还有 {len(lines)-5} 行)")

            return True
        else:
            print("❌ 权重文件生成失败，未找到输出文件")
            return False

    except Exception as e:
        print(f"❌ NNOM量化失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🎙️  声纹识别 - NNOM量化权重导出")
    print("=" * 50)

    # 检查NNOM脚本路径
    if not os.path.exists(nnom_scripts_path):
        print(f"❌ NNOM脚本路径不存在: {nnom_scripts_path}")
        print("   请确保已正确复制nnom-master/scripts到本地")
        return

    # 执行量化导出
    success = export_nnom_weights()

    if success:
        print("\n🎉 量化权重导出成功!")
        print("\n📋 下一步:")
        print("   1. 将生成的 speaker_weights.h 复制到嵌入式项目中")
        print("   2. 在C代码中包含此头文件")
        print("   3. 使用NNOM库进行推理")
        print("   4. 参考 speaker_recognition/c_inference/ 中的示例代码")
    else:
        print("\n❌ 量化权重导出失败")
        print("   请检查模型文件和NNOM工具配置")

if __name__ == "__main__":
    main()
