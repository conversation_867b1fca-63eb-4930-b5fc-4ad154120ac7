#include "nnom.h"

/* Weights, bias and Q format */
#define TENSOR_CONV2D_1_KERNEL_0 {10, -9, -20, -13, -5, 2, -3, 4, -8, 9, 6, 32, 25, 10, 14, -22, -29, -14, -20, -8, -6, 2, 15, 24, 21, -29, -25, 25, 2, -12, -38, -4, -3, -15, 4, -17, -10, -10, 12, -6, 31, 30, -30, -14, 23, 42, 28, -4, 16, 3, 10, 8, -14, -15, -26, 0, 5, 13, 3, 10, -39, -41, 4, 75, 75, 3, 29, 14, -32, -26, 28, -2, -14, -30, -27, 1, -2, 13, 8, 3, 9, -8, 12, 10, 7, 19, -9, 7, 15, -5, 17, -15, -6, 9, -4, 4, -7, -11, -2, 6, 4, 0, 9, 5, -15, 13, -5, 6, 9, -27, 14, -3, -4, 7, -25, 5, 7, -2, -1, -12, 1, 5, 2, -5, -1, 24, 14, 21, 17, -21, 33, 24, 20, 0, -17, 15, 9, 14, 11, -2, -14, -19, -18, -8, 24, -44, -17, -35, -10, 2, -13, 10, -1, 13, -6, -18, 12, -1, 10, -4, -11, 5, 5, 10, 0, 2, -10, 6, 6, -2, -6, -3, 0, -3, -4, -13, -31, -30, -44, 2, 10, 0, -11, -3, -4, -5, 12, -5, -11, -5, 11, 28, 9, 1, 12, 5, 26, 14, 23, 4, -1, -4, -1, 23, 1, 16, 9, 7, -19, 0, -2, -11, -3, 12, 29, 14, 21, 23, -11, -1, 10, 8, 20, -1, 2, 28, -25, 16, 3, 7, 0, -1, 6, -2, -3, -17, 24, -11, -10, 8, -16, 32, -8, -15, -3, -13, 16, 12, -22, -15, -2, 13, 2, 8, 9, -7, -1, -7, -4, 24, -12, -12, -8, -7, 31, 2, 3, 1, -9, 29, 0, -5, -3, -15, 3, -8, 3, 15, -4, -12, -3, -2, 13, -23, 10, 7, -7, -10, 10, -2, -17, -2, 4, -4, 17, -9, -1, 14, 7, 6, 7, -6, 14, -11, -1, 15, -9, 12, -14, 8, 20, -9, 6, -23, 7, 11, -6, 7, -15, -2, -2, 4, -9, 0, -2, -11, 0, -6, -7, -22, -19, 3, 9, -13, -21, -13, 14, 24, -3, 0, -3, 5, 15, 0, -2, 6, 7, 4, 2, 5, 4, -7, -1, 6, 5, -13, -7, -4, 13, 12, -19, -8, -2, 13, 15, -13, -9, -2, 3, 2, 7, 7, 4, 1, -5, -3, 10, 11, 11, 0, 2, 3, -12, -4, -7, -11, -10, -18, -9, -14, -7, -6, -11, 0, -8, -6, 5, 0, 5, -6}

#define TENSOR_CONV2D_1_KERNEL_0_DEC_BITS {8}

#define TENSOR_CONV2D_1_BIAS_0 {103, -77, -21, -82, -62, -11, 127, -96, 36, 13, 66, 91, -61, -18, 35, -64}

#define TENSOR_CONV2D_1_BIAS_0_DEC_BITS {8}

#define CONV2D_1_BIAS_LSHIFT {1}

#define CONV2D_1_OUTPUT_RSHIFT {6}

#define TENSOR_CONV2D_2_KERNEL_0 {-8, 3, -5, -12, 16, 10, -26, -41, -38, 24, -13, 4, 10, -10, -9, -1, -9, -9, -3, -20, 22, 10, -12, 32, 2, 0, -6, -3, -6, 7, 8, -14, -4, -16, 4, -13, 26, -2, -6, 7, -24, -33, -6, 8, -15, -15, -4, 2, 15, 18, 5, 19, -11, -20, 4, -4, -35, 16, 11, 17, -11, 4, -14, 3, -5, 6, -1, -14, -11, 3, -4, 11, 5, -1, -4, 1, -17, 13, 8, -19, 2, -4, 6, 22, -13, 34, 5, 26, -16, -11, 16, 13, 6, 11, 1, -9, -5, -5, -1, 4, -6, -29, -1, 1, -34, 0, 8, 19, -11, -20, -6, 3, 9, -16, 3, 14, 20, -9, 0, -38, -27, -2, -16, -11, 9, 10, 4, -20, 14, 18, -6, 56, -38, 15, 3, 20, -19, 6, 19, 9, -3, -16, 2, -1, 9, -1, 3, -24, 0, 1, 0, -7, -21, -13, -19, -12, 17, 7, 5, -18, 6, -15, 15, -19, 6, -31, -10, 1, 21, -6, -4, 1, 5, -5, -6, 7, 13, -9, -11, -10, -3, -5, 10, -1, 14, -10, -11, 1, 13, -2, -2, 13, 7, -4, 15, -16, 10, -6, 3, 3, -32, 15, -21, -5, 3, 19, 3, -7, 0, 9, -31, -20, 15, -16, 6, -7, 12, -24, 0, 7, 18, 13, -10, 4, 7, -1, -1, 10, -32, 0, -7, -21, 15, 15, -2, -4, 0, -9, 9, 7, -5, -56, 3, -4, 6, -5, 13, -19, -49, 1, 10, 0, 1, 30, 3, -4, 6, -3, -11, -11, 19, -13, 12, -2, 0, 15, 20, 23, -25, 10, 4, 11, -4, -26, -11, 37, -14, -16, 3, -16, 18, -4, -3, 11, 8, 20, 11, 3, 21, 1, 3, 22, 20, -2, 1, 17, -18, -1, -26, 6, 22, -20, 6, -19, -4, -31, -15, 14, -26, -1, 12, -10, -11, -12, -14, 7, -2, 13, -1, -24, -24, 18, 1, 14, 2, 12, 33, -4, -64, 16, 8, -4, -12, -2, -2, -2, -13, -30, -27, 18, -5, 13, 15, 6, -21, 0, 10, 28, -13, -5, 14, -13, -16, 5, -10, 2, -16, -28, -2, -15, -22, -26, -11, 11, 6, 0, -10, -23, -30, 4, -20, 21, 14, -3, 33, 0, -42, -2, 11, -4, 21, -6, 1, -7, 6, 4, -19, 15, -4, 26, 15, -14, 17, 10, 8, 25, -22, -3, 8, -13, -19, -9, -12, 4, -2, 15, 0, 13, -37, -9, -28, -3, 3, -20, -2, -31, -16, 5, -11, -6, 19, 7, 28, -15, -16, 11, 26, -21, 42, -18, 15, -22, 6, 2, -12, 1, 10, -20, 23, -5, -9, 12, 18, 34, -12, -13, 0, -11, 1, 2, -9, 0, 6, 13, 20, 2, 10, -30, 19, -1, 6, -16, 33, 11, -23, -4, -20, 24, 26, -5, -6, -37, 7, 0, -18, 11, 9, -9, -6, -35, -16, 10, -8, 17, 17, 3, 17, 23, 5, 15, -18, 6, 4, -49, 11, 8, -15, -15, -4, -3, 0, 22, 9, -12, 11, -41, 15, 19, -5, -43, 8, -3, -1, 18, -9, 9, 15, -11, 14, -13, 16, 13, 15, -1, 1, -2, -4, -41, -19, -24, -37, 28, 23, -9, 25, 24, 7, -7, -16, -8, 13, -8, 13, -7, -21, 12, 24, -46, -17, 25, 6, -8, 3, -25, -20, 2, -23, -4, 3, -18, -7, 2, -17, -32, -39, -38, -25, -22, -9, 12, 33, 4, -45, -10, -3, -35, 10, 18, 2, -7, -5, -2, -9, -7, -17, -21, 13, 7, 20, 51, -11, -6, -2, -37, 2, 9, -5, 5, 11, -18, -8, -9, 0, -1, -7, 2, 8, -9, -17, 3, -9, 18, 20, 14, 9, -11, 8, -18, -12, -8, -9, 0, 1, 0, -4, 20, 2, 2, -13, 18, -4, 16, -12, -63, 8, -5, 7, 32, -13, 1, -16, -2, -3, 25, 12, 2, -9, 19, -9, -21, 1, -12, -11, -12, 3, 4, 1, 0, -10, -17, 3, 3, 23, 37, 0, 9, -8, -1, -12, -25, -2, 6, 4, -14, -3, -4, -15, 16, -33, -15, 14, -45, 2, 5, 6, -34, -9, 21, -13, 4, 8, 15, 31, 1, 13, -5, -6, -22, 6, -7, 8, 8, 13, -3, -14, 13, -15, -20, 6, -12, 24, 36, 3, -14, 16, 1, 16, -28, 1, 1, -3, 15, 1, -2, 5, -12, 36, -20, -24, 8, -25, -27, -1, -8, -68, -26, -17, -2, -11, 14, 9, 30, 7, -13, 11, -20, -1, 8, 29, -14, -48, -37, -1, -1, -8, -8, 3, 5, 7, -8, -16, 20, -6, -8, 15, 10, 16, -5, 25, 42, 18, 8, 7, 12, 22, 21, -10, -15, -8, 0, 16, -12, -7, -12, 2, 12, -15, 6, 5, 24, 25, 20, 16, -30, -11, 15, 19, -3, 3, -25, -15, -11, 33, -16, 4, 15, -11, 22, -11, 7, 11, -11, 8, 4, 34, -3, -57, 15, -13, -10, 1, -24, 22, 1, -22, 9, -4, -16, 20, -18, 17, -29, -4, -42, -21, -20, 8, -2, 25, 16, -8, 8, 9, 13, -17, -8, 4, -16, -14, -16, 6, -16, 9, -7, -17, -30, -66, -4, -10, -21, -3, -30, 29, -2, -4, -8, -5, 12, -23, 5, -2, -9, -5, -4, -10, -5, 11, -1, -16, -1, -3, -7, 0, -7, -31, -11, 3, 9, 13, 11, 2, 6, -1, 5, 12, 6, 12, -9, -11, -7, -38, -9, -11, -16, 4, 1, -5, -10, 15, 14, 24, 1, 0, -8, 3, -3, -23, -11, 4, -7, -9, -18, 1, 1, -13, 2, -11, -8, -3, 12, -5, 4, -28, 6, 9, 17, 12, -5, 17, 12, 6, -16, 18, -2, 9, 10, 8, -17, -11, 7, -5, 0, 8, 16, 18, 3, 6, -14, 13, -6, 9, 10, 0, 3, -26, 4, 3, -9, -9, 4, 10, -6, -17, -7, -33, -15, -6, -1, 1, 6, -29, 8, 7, -15, 2, -1, 15, 0, 24, 2, -10, 6, 5, -8, 17, 16, -28, 6, -9, 5, 7, 9, 9, -13, -10, -10, 7, 11, -16, -2, 10, 2, -4, 4, -10, -6, 1, -2, -4, -16, -42, 15, -12, -13, -9, 3, 3, 8, 11, 14, -5, 1, -6, -7, -17, -7, 4, 7, -4, -60, -34, -20, -26, 10, 12, -17, 26, -10, -24, 17, -32, -40, 0, -8, 14, -39, -19, -9, 9, 12, 10, -2, 6, 17, 15, 2, 0, -8, -18, 17, -10, -24, -26, 2, -15, -3, -7, -1, 6, -1, -13, -9, 3, -10, -18, 13, -11, -48, -27, -23, -20, 0, 21, -6, 29, -8, -18, 12, -18, -13, -1, 8, 30, -41, -4, -7, 22, 18, 29, -16, -15, 15, 29, -19, 4, 34, -5, -7, 5, -21, -23, 4, -2, 0, -4, -1, 34, 10, -18, 19, 1, -11, -5, 17, -16, -1, -2, -1, 3, -16, 19, -3, 16, 3, 0, 2, -5, -1, 22, 27, 50, 8, 9, -25, 20, 30, 5, 6, -8, -20, -16, 19, 4, 5, 14, -7, -10, -20, -23, -16, -8, 8, -18, -9, 9, 27, -35, -8, 8, -1, 11, -2, -10, -25, 1, 28, -1, 0, -5, 8, -15, -6, 25, 8, -27, -23, 28, -18, -8, 21, -14, -15, 4, 0, -1, -11, 11, -17, 2, 4, 7, 3, -2, -38, 25, 7, 7, -24, 10, -57, 4, -5, -8, 26, -33, -9, -20, -1, -3, -18, 1, -23, 15, -3, 15, -52, 26, -12, -9, -2, -10, -10, -18, 9, 36, -4, 0, -6, 6, 17, 10, -4, -5, 6, -18, 11, -4, 8, -19, -14, 15, -41, 16, 9, -8, 3, 8, -2, -7, -20, 6, 6, -38, -14, -7, 12, -6, -13, 4, -8, 5, 19, 11, -34, 13, -26, -3, 8, -13, -1, -16, 30, 32, -22, 18, 5, 3, 59, 2, 2, -2, -13, 13, -14, -5, -8, -45, -20, -7, 14, 3, -22, 10, 1, 9, 32, 12, 2, 18, 10, -20, 13, -15, 49, 7, 20, 12, -14, -18, -20, -15, 11, -9, -26, 6, -18, -18, -18, -22, 6, -15, 32, -19, -5, 49, 40, 25, -13, 2, 16, -1, 7, -5, 11, -17, -5, 26, -1, -17, -12, -1, 11, 4, -15, -4, 42, 1, 18, -23, -2, -5, 3, -25, -16, 40, -10, -18, 10, -11, 38, 6, -10, -16, 16, 7, -30, 9, -4, 19, -18, -13, 10, 20, -5, -5, -32, -3, 25, 13, 12, 3, -11, -17, 4, 8, -76, -4, -2, -33, 8, -8, 6, -10, -32, 0, 14, -36, -30, -7, -24, 17, -32, -2, -32, 0, -8, 5, 39, -36, -7, -22, 42, 4, -25, -1, 2, 28, -5, -10, -13, 2, 4, -11, -9, 10, 28, 10, 8, 7, 0, 12, -23, -23, -25, 14, -9, 3, -9, 9, -3, 8, -17, 0, -6, 4, -19, -11, 2, 1, -6, -8, -8, 0, 15, 1, 7, -11, 14, 20, -2, 5, -4, -10, 12, -23, -8, -20, 5, -7, 19, 10, -18, -8, 6, -6, -2, 0, 1, -6, -11, -29, -29, -9, -2, 3, -1, -4, 6, 16, 14, -11, -17, 2, -7, -2, 11, 3, -11, -12, -1, -5, 5, 8, 7, -1, -22, 4, 19, 7, -15, 0, 26, 12, -4, -11, 12, 3, 13, -2, -7, -9, -6, -21, -7, 1, -2, 5, 7, 12, -20, -17, -8, -1, -2, 5, -2, 19, 14, -9, 27, -11, -47, 9, -4, -5, 5, -11, 5, -9, -21, 5, 6, -11, -11, 6, 20, 5, 0, -4, 14, 15, -11, -1, 16, -13, 9, -16, 0, -10, 13, -35, -5, -1, 26, 0, 14, -10, 12, -16, -9, -5, -6, 7, 19, 12, 17, -2, -6, -7, -15, -1, -6, 0, 20, -1, -1, 14, -1, -28, 9, -15, -4, 2, 1, 23, -29, 21, -15, 17, 10, 3, 10, -4, -17, 14, 5, -2, 14, -25, 16, -2, 26, 3, -10, -7, 5, 11, -30, 5, 12, -7, 5, 4, -3, -12, -3, 1, -30, 6, -2, 2, 29, -5, -14, -3, -35, 8, -4, 22, -19, 3, -11, 6, -12, 9, -6, -23, 8, 8, 6, -34, -12, -19, -4, 16, -9, -17, 9, -8, 7, 12, -58, -16, -5, 14, -4, 21, -3, 8, 3, -2, 23, 0, 7, -1, -15, 0, -5, -9, 24, -13, -1, 6, 9, -19, 13, 25, -16, -1, -7, -12, 12, 27, -18, -57, 11, 20, 10, 0, 24, 10, 2, -16, 20, 8, -14, -1, 37, 2, -25, 0, 14, -5, -26, 0, 27, 16, -19, -16, 19, -1, -10, -8, 34, -7, -7, -13, 17, -19, -23, -17, -8, -16, -15, 0, 12, -4, 2, 20, -19, 2, 6, -8, 8, -4, 6, -11, 7, -6, 12, 5, 0, 2, -4, -8, 11, 20, -18, 4, 10, -21, -36, -3, -8, 5, -20, -12, 8, 8, -5, -7, -2, 10, 4, -12, -19, -11, -36, -6, 11, -26, -19, 13, -9, 8, 24, 2, -5, -26, -17, -2, -29, -22, -19, 8, -4, -20, 14, 9, -12, 12, 1, 4, 14, 10, 6, 26, -9, 1, -34, 8, -19, -16, -11, -12, -14, 8, -20, 1, 1, 17, 7, 4, -15, -3, -16, -17, 29, 1, -16, 19, -6, 21, -17, 9, 21, -18, 13, -10, -4, 17, -6, 6, 17, -11, -21, -25, 12, -20, 8, 1, -15, -1, 6, -9, 8, -2, -7, -1, 13, -5, 13, 12, -7, 5, -8, -12, 10, -3, 1, -7, 15, -1, -5, 19, -7, 8, -8, -26, 13, -26, 3, 8, 6, 23, -1, -4, 8, 0, -18, 1, -3, -9, -25, -12, -5, -14, -5, 9, -7, -13, 5, -13, -12, 2, -10, 7, 3, -4, -3, 17, -2, 7, 1, -9, 17, 9, 13, 0, 0, -1, -3, 21, 6, 3, -12, -51, -4, -19, 8, 1, -10, 10, -15, -8, -1, -4, -3, 2, 7, -6, -17, -9, -19, -28, -2, -2, 9, -8, -13, -7, 0, -18, -6, 1, 9, -6, -14, 0, -4, 9, 5, -16, 11, 19, 0, -5, 5, -9, -7, 0, -9, -12, -6, 3, 6, -13, -3, -5, -3, 11, -10, 6, -7, -6, 1, 6, 6, 1, 8, -30, 8, -1, 9, 9, 19, -9, 14, 21, -15, 21, -10, 8, -5, -10, -21, -29, -3, 2, 25, -31, 29, -13, 18, 21, -44, 40, -45, 4, 13, 1, -39, -4, 3, 59, -26, -38, 35, -38, -37, 35, -6, -15, -25, 35, 7, 1, 6, -18, 28, -2, -22, 7, 13, -13, -1, 7, -22, 3, -12, 7, -6, 20, -27, -5, -10, 4, 26, -18, 8, -4, -32, 0, -9, 14, -25, 30, 8, -1, -17, 0, 10, 15, 34, -3, 3, 11, -12, 14, 24, -38, -12, 3, -17, 12, 3, -12, -4, -19, -7, -16, -21, -24, -16, -12, 1, -24, -15, 14, 21, -2, 4, -15, -2, -30, -18, 4, -6, 1, -39, 13, -28, -28, -14, 1, -16, 38, -27, 4, -5, 4, 7, -1, 20, 13, -14, 44, -30, -19, 2, -3, -21, -12, -2, 13, -4, 1, 19, -2, 19, 9, -5, 20, 1, 21, -9, 6, -21, -28, 2, 10, -27, -3, -6, -11, -3, -6, 6, -16, 5, 7, -8, 4, -10, -39, 11, 9, -30, -23, 1, 12, -13, 12, 14, -10, 22, 15, 0, -2, -34, -30, 3, 2, 0, 10, -3, -9, -4, 2, 4, 3, 6, 5, -3, 3, -9, -39, 5, 12, -33, -7, 13, -12, 9, 0, 10, 4, 6, 5, -6, 9, -5, -40, 2, 2, -24, -20, -7, 1, 12, 14, 6, 4, 4, 3, -4, -3, 12, -20, -6, 2, -9, 14, -5, -29, -7, 10, -8, -2, -9, 10, -12, -14, -14, -12, 13, 10, -38, -6, -8, -10, 18, -15, -1, 13, 0, -7, -2, 0, 4, -32, -3, -4, -25, -21, -29, 1, 4, 17, -5, 19, -21, 2, -11, -3, -32, -16, -13, 1, 8, 9, 18, 18, 7, 17, -2, 35, -31, 27, -24, -7, -11, -5, 4, 24, 1, 13, -8, 10, -6, -11, 7, 5, -6, 11, -1, -10, 21, -9, 17, 20, -7, -49, 4, -11, 1, -41, -6, 39, -21, -20, 7, -14, -17, -32, -24, -14, 9, 42, -15, -29, -13, 11, -18, -7, -15, 6, -12, 4, -23, 2, 23, 6, -16, 2, -15, -13, 1, 9, -12, 24, -9, 1, -11, 8, 12, 15, 6, 18, 10, -34, 3, 2, -12, 0, 0, 0, -10, 1, -1, -23, -4, 1, 24, -36, 16, 39, -31, -25, -5, -15, -3, -7, 8, -18, 7, -3, 7, -7, -24, -14, -12, -10, 26, -6, -9, 28, -33, 6, -8, 7, -7, 24, -5, -10, -12, -32, -3, -19, 2, 12, 10, 38, 23, -9, -38, 37, 14, 15, -11, 7, -20, -17, -17, -16, -18, 3, -1, -5, 7, -22, -2, -17, 5, -9, 5, -8, 7, -10, 10, 12, -5, -4, 3, -3, -10, -32, 9, -9, 5, 13, -2, 8, -15, -24, 19, -2, -1, 19, 3, 2, -1, 1, 21, 1, 11, 9, -5, -2, -7, 2, -21, -2, 0, 4, -7, 1, 11, -12, 20, -8, -5, 0, -15, 1, -1, 0, 5, 2, 6, -5, -10, -1, 4, -15, 2, -3, -4, 8, -5, -7, -5, -7, -12, -13, 21, 5, 5, 13, 6, -7, 17, 17, 3, 18, -5, -2, 1, -11, -12, -8, -9, 9, 3, -5, 8, -1, 14, -7, 4, -17, -7, 4, -1, 13, 3, 4, 8, -12, 11, -6, -14, -35, -18, -10, -1, 2, -6, 8, 1, -23, 14, -6, 6, 0, -7, 10, -9, -4, 6, 0, 8, -19, 18, -26, 12, -2, 9, -13, -7, -15, 10, 9, -23, -7, 1, -33, 8, 4, 32, 2, -3, 30, -19, -28, 20, -4, 8, 7, 36, 33, 15, 10, -19, -6, 12, -11, 62, -21, 24, 9, -46, -30, 45, -4, -48, 15, 44, 13, 14, -50, 16, -7, -22, 7, 19, -6, 7, -11, 4, 5, -4, -5, 5, 5, -6, -8, 39, 0, -21, 22, 19, -21, -13, -1, 6, -27, 16, 24, -2, 1, -28, 1, 12, -10, 49, 5, -23, -5, -21, -26, -30, 1, -31, 36, 11, -1, -7, -32, 5, -8, 5, -10, -27, -15, 27, -5, -19, 10, 6, -14, -3, 17, -22, 11, -20, -7, -5, -6, 1, -3, -21, -13, -17, -32, 18, 11, -1, 40, -27, -20, -12, 1, 33, 24, -20, -41, -7, -1, -12, 1, -36, 8, -12, -3, -19, 17, -22, 11, 19, 9, 26, -50, 9, 8, -24, -8, -17, 6, -35, 0, -23, 10, 17, 8, 0, -28, -38, 2, -14, 3, -1, 48, 7, -2, -13, -33, 6, 5, 22, -2, -54, 23, -4, -6, 0, 7, 29, -19, -5, 6, -20, -1, -16, -4, 4, 10, 0, 5, -17, -15, 12, 8, 23, -6, 13, -2, -9, -2, -15, 7, 10, -10, 1, 6, -16, 7, -6, -31, 7, 20, -5, -3, 2, -19, -4, -7, 5, -18, -23, -14, 3, 2, 3, -17, 25, 11, -2, 18, 8, 20, 0, 17, -8, -15, -29, -11, 17, 34, 6, 9, 45, -4, 14, -20, -12, 13, 7, -5, -17, 31, -19, -4, -11, 6, 4, 16, -8, -7, -3, -18, -8, -6, -7, -7, -1, -22, -25, 8, -3, 20, 15, -14, 14, 2, 8, 37, -34, -3, -27, -44, -9, -1, 0, -16, 5, 12, -8, -6, -19, -17, 4, 27, -32, 7, 4, 21, 2, 15, 15, 8, -9, -17, 41, -23, 15, 13, -28, -15, -15, -25, 5, 20, 8, -9, 34, 17, 27, -12, 11, -21, 14, 2, 14, -19, 1, -25, 2, -21, -18, 8, 7, 0, 1, 10, -8, -5, 9, -3, -23, 4, -2, -13, 9, 24, 3, -31, -9, 22, -16, 12, 38, -47, 20, -37, -20, -31, -20, 16, -5, -3, 20, -1, 18, -28, 35, 7, -3, 6, 12, 15, 14, 6, 20, -35, -7, -16, -15, -1, 8, 1, -15, -8, -18, 17, -14, -5, -22, -24, -8, 2, 27, 1, -1, -5, -27, 35, -24, -24, 17, -16, -20, -22, -8, -39, -2, 14, -21, 2, 15, -2, -34, -37, 20, 24, -31, 49, 7, 17, 18, 30, 8, -22, -24, -2, -41, 2, 14, 26, -8, -12, 7, -12, 16, -24, 14, 31, 13, -20, -18, -9, -18, 3, -11, -12, 21, 2, 1, 17, -9, -6, 9, 14, 1, 4, 7, -16, -10, -13, 3, 42, -20, 9, -1, 14, 34, -3, 14, -27, 19, -2, -13, -17, -15, -15, -14, 16, 0, -17, 4, -5, 21, 8, -1, 9, -21, 1, 10, -25, 1, -9, -3, 5, 10, 5, 26, 22, 6, 5, -4, -11, 3, 25, -7, -11, -8, -16, -10, 41, -7, 3, -11, -12, 5, -14, 15, -20, 13, -12, -10, -29, -28, -4, -25, 10, -9, -11, -13, -16, -6, -10, -30, -16, -26, -14, 15, -20, 14, 7, -2, 23, 5, 7, 31, 38, 19, 13, -5, 1, 5, -7, -6, -12, -16, -36, -40, 38, 22, 3, 0, -13, -4, 5, -4, -6, -18, -15, -4, 28, -6, 4, -21, 15, 19, 11, 1, 11, 10, 6, 21, -25, 0, 25, 37, -23, 16, -3, 1, 31, 13, 8, 19, -10, 20, 15, -13, 33, -19, 13, 2, -7, -3, 4, -11, -23, -1, 17, -4, -34, -27, 24, -14, 11, 28, 7, -29, 2, -16, 9, 15, -6, 21, -25, -4, -14, 21, 4, -4, 2, -26, -5, 39, -23, 8, -18, -13, 2, -18, 34, -14, -4, -16, -24, -11, -13, 5, 10, -3, 13, -28, -15, 16, -7, -4, -2, 37, -5, 7, 19, -9, -16, -12, -2, -10, 26, -4, 4, 23, -43, -9, -35, 22, 11, -4, -15, -15, 29, -35, -54, -6, -26, 0, -11, -4, -8, -40, 35, -31, 11, 0, -16, -24, -18, 21, -26, -6, 22, -12, -7, -29, -28, 30, -11, 23, 17, -26, -19, -7, -18, -10, -33, -21, -9, 9, 6, -28, -29, -2, -7, -21, -5, -5, 13, 20, -38, 18, 27, 3, -14, 10, -18, 1, -3, 17, 22, -30, 13, -11, 39, 11, -3, -15, 30, -21, 10, 16, -21, 4, -40, -10, 6, -3, 10, 25, 60, 6, 13, 14, 27, -41, -19, 7, 4, -13, -3, -8, -24, 15, -30, -19, 24, 23, 1, 13, 19, -9, -2, -25, 15, -13, -3, 29, 9, 21, 6, -14, -6, 7, 11, 8, -22, -9, -34, -27, -1, -8, -22, -9, -1, -14, -27, 32, -30, -15, -18, -7, 6, -1, -44, -7, 4, 8, 21, 0, -50, -11, 14, -10, 4, -44, 36, 31, -21, -1, 19, 21, -11, 3, -10, 0, -15, 30, 7, 16, -25, -23, 10, -6, -56, -4, -14, -5, 13, -7, 17, -41, 4, 19, -17, -25, -14, -40, -47, -3, -17, 3, 7, -7, -17, 20, -14, 0, 8, -15, -8, -2, 8, 1, -25, -6, -5, 4, 11, -15, -8, -8, 20, 2, 13, -21, 1, -22, 7, 15, -5, 3, 15, -1, -5, -11, -11, -3, 4, 18, 3, 1, -8, -1, -4, -5, 18, -2, 5, 7, 26, 15, -2, 8, -12, -8, 12, -9, 1, -19, 10, 2, -25, -5, 13, 5, -5, 2, -10, 0, 7, 16, 6, -13, -2, -6, 2, 4, -10, 0, 4, -4, -2, -25, 5, 9, 12, 10, -3, 2, 2, 1, -8, -5, 23, -12, -12, -3, -19, 9, -4, 6, 9, 2, 8, -4, -12, -1, -6, -4, -9, -4, -3, -3, -15, -22, -10, 8, 1, 1, -3, 4, -1, -5, 2, -20, 0, 9, -20, -3, -9, -26, -1, 21, 15, 10, 1, 12, 1, 8, 3, -23, 26, -11, 2, 22, -12, -8, -14, 10, 3, -30, 18, 1, -19, 6, 18, -20, 21, 13, 7, -14, 11, -30, -35, 3, -10, -10, -21, -2, -20, -22, 5, -33, 1, -2, 17, -7, 4, 18, 0, 10, -49, 12, -25, -32, -25, 22, 20, -26, 30, 10, -16, 7, -7, -4, -5, -9, -7, -9, 5, -9, -2, 5, -8, -37, 7, 13, 13, -12, 18, -25, 2, 7, -7, 8, -16, -32, -3, 8, -9, -14, -4, 14, -11, 14, -12, -18, -19, -16, -29, 2, 10, -34, 5, 1, -7, -17, 25, 15, -51, 19, -14, 22, -1, -13, -1, -41, -5, 25, -3, -15, -17, -11, 13, 24, 20, -13, 15, 17, -14, 16, -43, 12, -4, -5, 6, -23, 19, 3, -3, 12, -24, 6, -4, -4, -47, 2, 10, -9, -9, 9, 8, -1, -17, 11, 2, 17, -36, 18, -7, 2, 1, -14, 8, -20, 26, 23, -25, -14, 4, 16, -17, -19, -8, -24, 17, 18, 18, -6, -7, -28, 9, 30, -34, 9, -1, 18, 16, 1, -7, -6, 11, 14, 0, -64, 29, -25, 9, 16, -54, -5, -23, 4, 3, 12, -2, 0, 3, -3, -15, 6, 10, 1, 25, 5, 14, -3, -13, 5, -2, -34, 0, -7, 1, -6, -1, -14, -13, -16, 4, 10, 9, -3, -9, 13, -6, -26, -3, 11, 10, 2, 6, -51, 27, 1, -6, -3, -27, 3, -25, 3, -9, 2, 3, 14, 6, 6, -2, 21, 10, 3, -14, -4, 22, 16, -12, -2, -28, -23, -23, -16, -10, 6, -10, 3, -15, -36, -1, 28, 26, -10, -15, -3, -8, -6, 7, 15, -9, -6, 5, -34, -4, -4, -11, 18, 21, -5, -11, 20, -24, 6, -18, 6, -17, -25, 6, 10, -10, -18, 15, 16, 26, -2, 6, 12, 28, 14, -21, 4, -6, -4, 2, -10, 13, -4, 20, -53, 18, -33, -12, -4, -28, 16, 36, -8, -1, -14, -9, 14, 15, -9, 7, -19, -1, -7, 14, -3, 1, -6, 8, 4, -17, -17, 3, -4, -10, 9, 16, 10, 17, 18, 4, -3, 14, -8, -18, -8, 7, 28, 10, -1, 11, 1, -7, -31, -4, 5, -7, -20, -31, 19, 23, -7, 1, 6, -39, 9, 30, 4, 6, 16, -64, 22, 7, -14, -4, 6, 6, -17, -17, 2, 21, -2, 1, -4, 1, -21, 4, 9, -17, -22, 10, -8, -22, -24, 13, 11, 7, 4, -6, -6, 3, -31, 29, -11, -29, -6, 14, 13, 19, -10, -13, -22, 9, -3, 35, 4, 15, 8, -51, 7, -14, -8, -3, 8, 7, -18, -13, 25, 17, 11, -7, 11, 10, -8, 0, -1, 1, -2, 14, -12, -30, 2, 13, -6, -20, 28, -4, 13, 2, -29, -12, 3, -18, -20, -1, -8, -35, -12, -9, -8, 11, 20, 13, 3, -20, -1, -46, 3, -30, -27, -11, 25, -46, 3, -16, -16, 2, -10, -2, 19, 18, -17, -13, -1, -19, -13, 24, 8, 6, -12, 18, 16, -13, -1, 3, -6, 10, 14, 7, 12, -32, 1, 15, 11, -21, 0, 9, 14, -13, -11, 21, -4, -7, 20, -35, 15, -16, 9, 3, 36, -8, 1, -28, -14, 10, -35, -3, -21, 24, -6, -23, -11, -12, 6, 11, -4, -12, -9, -1, -5, -20, 3, 5, -14, -2, 11, 21, -25, 0, 22, -3, -21, 6, 13, 15, -1, -9, -16, 47, -6, -15, -11, -9, -18, 22, 1, 4, 1, 34, -19, 2, 8, -5, -15, 4, -3, 2, 9, -7, -13, -12, -9, -4, 5, 17, 1, 0, -18, 15, 9, -2, 1, -13, -7, -4, -1, 8, 1, 1, 22, -20, -4, 0, -6, -27, 5, -18, -15, 10, 18, 16, 16, 16, 8, -9, -13, -3, -2, 2, 0, 2, -24, 1, 13, -8, 3, -4, 12, -6, -1, -21, 11, 9, 1, -5, 4, 11, 2, 10, 2, -6, 19, -12, 2, -3, 0, 11, -17, -5, -19, -18, 15, 8, 31, -9, 27, 10, 4, 11, 9, 18, 26, -10, -3, 4, -9, 2, 1, -2, -11, -3, -15, -9, -16, -20, -3, 3, -9, -10, 28, 3, 6, -10, -20, -1, 1, -8, -8, -24, 2, -6, 15, -5, -23, 4, -31, 6, 1, 1, 9, 3, 36, -35, 24, 0, 17, 18, -16, 9, 13, -10, -15, -2, 6, -17, -17, 11, 22, 3, -6, -47, 2, 1, 10, 4, 17, 8, -8, -39, -20, -21, 3, -8, -7, -7, 5, 33, -24, -6, 27, -1, 2, 1, -2, 2, 0, 20, 10, -6, -4, -8, 3, 13, -2, 1, -12, -4, 19, -34, -13, -4, 17, -6, -27, 8, -51, -11, -15, -14, 10, -6, 8, 14, 16, 8, 10, -18, -9, -7, -26, 2, 24, 12, -1, 11, -10, 10, 3, -15, 15, 3, 4, -12, 0, 2, 30, -15, -7, 26, -23, 4, -23, -8, 26, -34, 8, -21, -7, 5, -5, -15, -1, -11, -18, -26, -38, 30, -8, -42, 31, -9, -7, 10, 30, 6, 6, 22, 0, 2, 16, 18, 3, -2, -48, -17, 5, -1, 22, -5, -3, -4, -16, -25, 9, -2, -12, 16, 8, 9, -8, -15, -7, -30, 8, -14, -11, 21, 7, -29, -17, 1, -10, 8, -26, 19, 26, 18, -8, 4, 48, 1, 26, -7, 9, 11, 17, 14, -43, 28, -6, 1, 6, 3, -14, -9, 6, -20, -1, -7, 15, 7, 8, 14, 9, 26, -17, 1, 0, -12, -23, -33, -8, 4, 11, -25, 4, -29, -10, 27, 9, -6, 7, -9, -22, -4, -17, -13, -3, 11, 5, -18, -31, 2, 6, -20, 0, 11, -19, -16, -2, -3, -5, -17, -12, -19, 19, 10, -18, 1, -28, 17, -22, 2, -11, 6, -10, 10, -17, -5, -12, -14, 1, 15, 10, -20, -4, 19, -2, 3, -36, -28, 14, -34, -25, -4, 22, -1, 17, -15, -42, -6, 16, -8, 23, 4, -10, -23, 11, -24, -15, 20, 22, -34, 10, 13, -21, -9, 27, 6, -8, -8, -9, 13, 26, -19, -35, -7, 7, -10, 7, 22, -13}

#define TENSOR_CONV2D_2_KERNEL_0_DEC_BITS {7}

#define TENSOR_CONV2D_2_BIAS_0 {7, -11, -12, -22, 7, -7, -2, 21, 45, -7, 26, 18, 1, 51, 13, 39, 26, 24, 57, -19, 32, 18, 8, 42, 16, 71, 11, -3, 41, 23, 16, 49}

#define TENSOR_CONV2D_2_BIAS_0_DEC_BITS {6}

#define CONV2D_2_BIAS_LSHIFT {4}

#define CONV2D_2_OUTPUT_RSHIFT {7}

#define TENSOR_CONV2D_3_KERNEL_0 {13, -6, -7, -6, -5, -21, 2, 10, -18, 18, 3, -2, -21, -26, 28, 18, 4, -15, 8, -12, -3, 2, 10, 21, 12, -16, -14, -13, 16, -2, -12, -14, 4, -14, -5, -8, 6, -16, -3, -3, -7, -7, -15, 4, 5, 30, 12, 29, -24, 29, 19, -5, 9, 23, -20, -16, 2, -19, 3, 6, -4, 7, -2, -12, -1, -9, 4, 0, 6, -25, -2, -34, 0, -4, 4, 2, 3, -24, 23, 36, 5, -24, -2, -15, 11, 0, 22, -15, -14, 11, 9, 1, 10, -13, 8, 5, 16, -1, -5, 6, 3, -17, 13, 9, -14, 17, -8, -4, -21, -21, 28, 13, -1, -3, 8, -8, -16, 2, 15, 11, 11, -19, -10, 1, 7, 4, -4, -8, 6, -9, -5, -3, 6, -10, -4, -14, -2, 2, -13, -2, 8, 17, 14, 16, -17, 15, 10, -3, 12, 11, -14, -7, -1, -17, 6, 15, -3, 0, -5, -5, -4, -6, 6, 8, 8, -25, 2, -33, -11, 5, 0, 0, 8, -14, 18, 16, 5, -20, -9, -11, 1, -8, 21, -10, -14, 19, 9, -14, 8, -11, 5, -2, 8, -3, -5, 13, 13, -19, -4, 3, -16, 25, -9, 0, -32, -26, 24, 21, -3, -22, 10, -3, -13, 0, 11, 16, 16, -11, -9, 2, 2, 2, -5, -10, 15, -6, -24, -15, 11, -8, 4, -9, -4, -7, -14, -4, 9, 25, 7, 29, -20, 20, 23, -4, -2, 16, -10, -14, -7, -12, 2, 21, -6, -1, 2, -3, 7, -6, 15, 1, 6, -19, -6, -42, -9, 3, 2, 7, 12, -22, 11, 28, 6, -19, -11, -4, 6, -3, 16, 8, -5, 31, 15, -12, 6, -15, 8, -5, 13, 8, -11, 34, 11, -14, -13, -29, 12, 7, 22, 18, 16, -8, 1, 36, -17, -15, -21, -11, -4, 8, 6, 12, -14, 29, 50, -14, -4, 1, -29, -18, 14, -21, 5, 23, 20, 13, 3, 3, -6, 41, 3, -7, -3, -6, -26, -23, 1, 15, -21, 10, -12, -6, -36, -5, 7, 25, -14, -4, -17, 4, -5, -12, 18, 17, 19, -6, 10, 28, -9, -43, 2, 6, -30, 5, 11, 3, -4, -17, 22, -15, 0, 6, 6, 27, 11, -24, -22, 30, 22, -8, -32, -19, -9, -35, 16, 12, -9, 17, 5, -7, -6, -17, -3, 3, 8, 3, 3, -7, -8, 12, -9, -5, -10, -12, -9, -4, 13, 9, -4, 29, 38, -5, -2, 5, -11, -4, 21, -12, 0, 28, 23, 22, -9, 9, 1, 14, -12, 2, 10, -6, -40, -13, 10, 5, -20, -1, 13, 4, -28, 2, 15, 18, -10, 1, -12, 6, -7, -11, 15, 17, 16, -12, 20, 23, -5, -28, -7, 14, -19, 16, 7, 10, -15, -17, 18, -9, 1, 14, 4, 23, -3, -6, -17, 31, 5, 0, -19, -11, -14, -13, 24, 19, -11, 8, -2, -11, -15, -7, -22, 7, 2, -3, -18, -12, 1, 20, -13, -17, -29, -21, -12, 6, -1, 10, -7, 49, 38, -23, -2, 25, -13, -10, 17, -21, -9, 9, 22, 30, -23, 3, -15, 25, 3, 17, 6, -1, -38, -10, 4, 30, -11, 2, 18, 17, -23, -7, 21, 26, -13, 6, -5, 4, -4, 4, 22, 10, 23, -15, 26, 22, -13, -35, -12, 8, -41, 8, 12, 7, 10, -3, 13, -1, 0, 20, 8, 48, -3, -15, -15, 50, 16, 12, -20, 4, -21, -22, -6, 1, 15, -11, -12, 26, -20, 25, 5, 26, 21, -7, -8, -1, 38, 18, 8, 7, -28, 12, 14, 10, -28, 13, -28, 15, -21, -4, -27, 7, -21, -16, 16, 6, 23, -5, 1, -1, 11, -8, 8, 45, -3, 6, 2, 1, -2, 47, 15, 7, -27, 7, 1, -2, -3, 6, -2, 12, -15, -14, -14, 12, -19, -11, -12, 5, 23, -9, -3, 5, -21, 6, 12, 7, 3, 49, -11, -1, 5, 22, 1, 11, 4, 3, 11, -16, 3, -3, 4, -3, -5, 2, -11, -5, -15, -18, -14, 2, 16, -9, -4, 14, -10, 22, 9, 16, 8, -7, -7, -5, 24, 11, 13, 6, -23, 5, 1, 0, -25, 6, -32, 27, -20, 3, -30, 13, -14, -4, -1, 0, 20, -3, -6, -2, -2, -1, 5, 34, 2, -2, 7, -6, -8, 16, 15, 2, -24, 0, 2, -11, -9, -2, -2, 3, -4, -1, -7, 13, -19, -3, -9, 8, 15, 0, -5, 19, -27, 5, 17, 15, -11, 34, -7, 10, -1, 11, -6, 4, -5, 0, 10, -7, -7, -7, 4, -1, 6, -7, 0, -11, -23, -10, -7, 3, 7, -7, -9, 16, -18, 36, 12, 32, 13, -10, -2, 4, 38, 15, 17, 24, -20, -5, 5, 10, -15, 10, -42, 39, -22, 3, -22, 14, -13, -19, -3, -14, 22, 3, -15, -16, -22, -4, 14, 46, -3, 4, -2, 1, -5, 12, 13, -5, -21, -13, 1, -9, -10, 10, 10, 5, -14, -5, -8, 3, -14, -10, -15, 3, 12, -7, -13, 5, -37, -3, 15, 14, -8, 43, -14, 8, 19, 0, 5, 22, -2, -14, 2, -9, -2, -10, 9, -7, 15, -1, 7, -13, -9, -2, -22, -39, -6, 5, -7, -14, 29, -16, -9, 26, 3, -10, -25, -9, -15, -8, 24, -12, 8, -15, 22, 2, 19, -4, -4, -5, -34, 3, -7, 12, 8, 11, 24, -7, 2, -34, 24, -8, 17, 5, -6, 14, -17, -11, 14, 9, -20, -10, 3, 17, 7, 8, 5, -10, 2, -1, 3, 5, -12, -2, -3, 16, 29, 6, 2, -5, 10, -14, -24, 0, 3, -12, -20, -7, 14, -19, 19, -23, 27, 10, -14, -13, 14, 27, 40, -11, 33, -11, 22, 10, 13, -20, -5, 8, 21, 14, -21, -36, 1, 13, 7, 0, 21, -11, -6, 24, 3, -3, -21, -18, -6, -3, 24, 8, 17, -7, 10, 12, 13, -11, 5, 1, -22, 1, -13, 5, 11, 12, 14, -13, 12, -34, -11, -12, 10, 23, 7, 15, -14, 12, 1, -4, -18, -4, 0, 27, 9, -2, -11, -13, 3, -3, 0, 13, -11, 5, -2, 13, 20, 10, 0, -4, 2, -5, -30, -5, 1, -1, -21, -12, 1, -11, 15, -14, 9, 11, -7, 3, 10, 9, 16, -22, 20, -11, 16, 6, 5, -22, -3, -1, 10, 14, -34, -29, -1, 17, -3, 8, 25, -10, -4, 21, 0, -3, -28, -32, -11, -2, 29, 16, 19, -16, -6, 24, 30, -2, -4, 7, -32, 1, -18, 13, 16, -1, 19, -7, 6, -41, -3, -12, -7, 28, 16, 10, -9, -5, 4, 6, -13, 0, 4, 41, 12, 9, -5, -14, 23, -2, -22, 15, -17, -1, -16, 7, 26, 7, 4, 5, -7, 9, -43, -9, -3, -24, -18, -20, 4, -16, 12, -12, -1, 6, -19, -3, 12, 3, 10, -31, 17, -10, 5, 4, 22, -22, -3, -16, 22, 15, -8, -28, -14, -26, -30, 18, 7, 18, -16, 5, -19, 0, -12, 27, 10, -11, 39, 9, 12, 13, 39, 0, 6, -9, -6, 4, -21, -11, -8, -19, 6, -16, -9, -11, 16, -15, -7, -5, 6, 4, -2, -11, 11, -25, -17, -8, -10, 7, 23, 2, -11, 8, 8, -6, 4, -5, -7, 1, -3, -14, -8, 16, 19, 20, -13, -17, -9, -36, 20, -4, -15, 0, -33, -8, 8, 5, -1, -5, -6, -24, 4, -1, -7, -3, 11, -4, -2, 12, 38, -41, -9, -22, 18, 23, -4, 14, -17, -20, -4, -17, -17, 15, 2, 15, -14, 3, -20, 4, -10, 2, 15, 0, 31, 6, 13, 13, 33, 11, 12, -15, -10, -7, -18, -2, 8, -11, 4, 2, -11, -8, 9, -13, -8, -4, 14, 8, -4, -2, 10, -18, -16, -5, 0, 8, 7, -4, -1, 18, -1, 3, 16, -7, -7, 10, 11, -11, 8, 6, 22, 26, -12, 8, 1, -22, 5, -12, -16, -10, -33, 10, 3, -2, -4, 6, -14, -8, 8, 7, -12, -8, 3, 16, 1, -1, 35, -27, -3, -19, 5, 19, -5, 15, -13, -26, -9, -20, -14, 29, -2, 25, -13, 5, -14, 19, -16, -2, 28, -1, 22, -7, 14, 11, 29, 7, 6, -6, -6, -7, -16, 4, 8, -33, 4, 24, -24, -11, 7, 4, -1, 3, 27, 14, -10, 10, 11, -17, 2, 4, 8, 6, 5, -1, -7, 0, 22, 5, 26, 3, -30, -9, -2, -6, 15, -2, 26, 21, -26, 7, -3, -14, -1, -12, -19, -7, -30, -9, 8, 8, -9, -8, -21, -5, -1, 5, -8, -7, 1, 24, 1, 2, 42, -20, -14, -9, 6, 8, 4, 9, 20, -15, 31, 13, 12, 14, -18, -25, -7, -21, 15, -6, 6, 19, -3, -23, -2, 31, 16, 14, -12, -11, 10, -12, 14, 19, -8, 3, -2, -4, -11, -6, 3, -4, 23, 9, -22, -26, 6, -8, 20, -17, 30, 2, 21, -12, 11, -3, 5, -8, 2, 8, -25, -9, -9, 5, -4, 3, -3, -8, -1, -9, 3, 9, 18, -54, 14, 34, 2, -59, 21, -8, -23, -23, 25, -29, -14, -6, -20, 9, -19, -8, 26, 5, -10, -38, -31, 22, 12, -4, 16, -11, 15, 7, 32, -7, 20, -7, 10, 6, 14, 13, -20, -12, -8, -22, 5, -7, -5, -3, -1, -19, 1, 27, 17, 17, -5, -6, 1, -13, 9, 11, -8, 5, -1, -10, -5, 8, -4, 8, 3, 3, -32, -22, 0, -10, 7, -9, 22, 10, 20, -11, 14, 8, -12, 4, 10, -9, -13, -12, 17, 7, 8, -3, 6, 0, 9, -4, 4, 9, 9, -39, 8, 25, 0, -38, 2, -12, -13, -26, 10, -27, -1, -15, 0, 7, -24, -15, 22, 6, -4, -12, -13, 13, 2, -13, 14, -17, 8, 6, 15, 0, 18, -14, 6, 12, 6, 13, -42, -20, 3, -16, -2, -15, 19, -4, -10, -29, 5, 40, 21, 30, -3, 3, 22, -4, 10, 15, 6, -1, 0, 3, 3, -7, -21, 3, 20, -6, -40, -4, -10, -3, 13, -9, 32, 10, 27, -22, 20, -12, -11, -8, 4, 6, -15, -24, 16, 18, 8, -6, 6, 3, 8, 9, 9, 15, -4, -28, 11, 31, -9, -40, -4, -19, -20, -34, 4, -20, 1, -23, -7, 2, -24, -15, 20, 9, -13, -18, -14, 16, 26, -22, -5, -31, -5, 14, 17, 14, -16, 26, -9, 45, -1, 5, -16, -4, -6, 16, -7, -5, 28, 1, 10, 14, -28, 29, -11, 4, -9, 6, 8, -26, -10, -16, 12, -14, 20, -2, 39, -10, -2, 7, -8, 24, -7, 18, -6, 32, -18, -7, 0, 17, -18, -3, -3, -19, 6, -7, -3, -5, 14, 8, -12, -20, -32, -34, -14, 4, 13, -29, 0, -24, 3, 34, 8, 53, 22, 1, 27, 1, 2, -4, 8, -7, -12, 19, 7, -26, 12, -24, 7, -37, 9, 44, -3, 19, -4, 7, -4, -24, 5, 2, 15, -17, -11, 22, -4, 40, -10, 8, -12, 7, -6, 24, -7, -1, 15, 4, -1, -5, -18, 11, -2, 15, -14, -4, 16, -16, -21, -8, 21, -15, 17, 5, 18, -13, 3, 6, -11, 32, -5, 11, -17, 28, -2, 4, -1, 13, -15, 2, -6, -26, 12, 6, 10, 5, -1, 8, -6, -18, -39, -11, 0, -2, 10, -17, 2, -8, 3, 17, 25, 18, 5, -2, 9, -4, 8, -2, 3, -16, 4, -7, 1, -24, 21, -2, 7, -30, 3, 31, -1, 9, -1, 14, -10, -18, 9, 0, 12, -12, -12, 14, -18, 36, -11, 16, -6, 4, 6, 18, 1, -20, 23, 18, 1, -14, -24, 26, -10, 4, -20, -3, 19, -9, -29, -3, 24, -10, 18, 0, 18, 1, 2, 6, -15, 37, -11, 8, -13, 26, 0, -3, -15, 8, -17, 5, 6, -20, 1, -10, 10, -7, -3, 19, -1, -13, -42, -9, -5, -14, 24, -19, -1, -21, 19, 23, 23, 4, 29, 1, 9, 9, 14, -15, 10, -19, -1, -10, 14, -31, 24, 9, 10, -35, -11, 37, -13, 14, 14, 17, -19, -28, 14, 19, 9, -23, -21, -4, 2, 13, 2, 35, -28, -19, -1, 21, 3, -3, -7, -6, 9, 21, -7, -13, -40, -4, -13, 16, 9, -10, -16, 15, 40, -1, -14, -7, 11, -13, 7, -18, -21, 21, 16, 35, -25, -48, -22, 25, 9, 0, 8, -10, 9, 10, -4, 10, -38, 10, -18, 5, 13, 7, -26, -21, 5, -23, 12, -3, 7, 1, -10, -8, -27, 15, 3, 16, -34, 17, -6, 20, -3, 12, 8, 11, 14, 11, 24, -19, -13, -25, -5, -4, 8, 8, 2, -4, -14, 2, -20, -4, 5, -14, -17, 1, 5, 2, 3, 29, -13, -10, -7, 19, 0, -12, -12, 9, -1, 3, -15, -4, -26, -4, -1, 3, 8, -2, -13, 15, 29, 8, -7, 5, 10, -15, 7, -7, -9, 20, 17, 14, -29, -29, -3, 18, 4, 3, 7, 1, -10, -1, 0, -6, -24, 15, -5, -1, 11, -5, -11, -15, 3, -23, 16, 1, 3, -6, 5, -11, -9, 13, 11, 21, -26, 21, -1, 14, -14, 15, 1, 17, 4, 10, 19, -22, -5, -15, 3, -2, 3, 10, 20, -3, -19, -4, -7, -4, 11, -14, -18, -3, -7, 3, 5, 30, -19, -4, -11, 19, 13, -14, -11, -10, 9, 0, -15, 4, -33, -15, -5, 5, 11, -7, -17, 16, 26, -2, -11, 15, 12, -9, -11, -16, -18, 16, 19, 15, -45, -45, -1, 27, 11, -2, 12, 11, 6, -4, -6, -2, -19, 14, -28, 10, 11, -3, -8, -17, 0, -47, 11, -3, 3, 2, 3, -14, 10, 13, -5, 20, -36, 24, 17, 21, -13, 38, -6, 23, 8, 20, 28, -7, 3, -3, -14, -11, 5, 9, 23, -11, -17, -8, -20, -3, -6, -31, -28, 15, 10, 0, 15, -14, -5, -23, 2, 18, -7, -8, 6, 12, -15, -9, 14, 18, 8, 4, 9, -1, -8, 9, 27, -38, -13, 26, 12, 9, 24, -3, 16, 21, 28, -2, 11, -24, 10, 31, 11, -20, -23, 13, 8, 9, 5, 4, 22, 5, -6, 16, -11, -7, 11, 8, 11, -27, -10, -4, -3, 0, -3, -13, -10, 12, -8, -8, 9, -31, -11, 41, 2, 5, 3, 0, 12, 2, 7, 4, -10, 1, 13, 9, -10, -35, -2, -8, 2, -12, -33, -7, 11, -4, 17, 23, -25, 6, -2, -7, 17, -10, -6, -11, 3, 17, -2, -1, 5, 19, -19, -1, 10, 12, 6, 9, 2, -1, -17, 8, 2, -26, 0, 14, 2, 11, 8, -13, 4, 10, 7, -6, 5, -37, 9, 12, 21, -5, -19, 14, 3, -2, 7, -5, 17, 13, -7, 17, 2, -7, 13, -9, -6, -9, -4, 0, 4, -3, 4, 8, -3, 2, 6, -4, 2, -17, -5, 31, 8, -3, 3, 5, 18, 8, 9, -8, -12, 1, -5, 1, -7, -34, 2, -10, 8, -13, -25, -11, 8, 0, 14, 12, -21, 8, 2, -3, 11, 7, -11, -23, 16, 18, 6, -10, 13, 25, -22, 14, 16, -3, 12, 7, 10, 8, -22, 3, 4, -37, -11, 20, -6, 12, 22, -38, 2, 15, 13, -12, 2, -26, 2, 16, 24, -8, -9, 16, 19, -6, 4, 12, 23, -4, 4, 16, 11, -17, 4, -4, -3, -4, 9, 6, 1, -17, -1, -6, -18, 14, 23, -25, -1, -9, -6, 49, 4, -12, 11, 9, 15, 3, 17, -7, 0, 5, 3, -3, -2, -27, 13, -9, 7, -12, -18, 0, 11, -1, 18, 4, -4, 12, -19, -16, 20, -2, 6, -7, -7, 10, -23, 27, 18, -4, 7, -4, -10, 22, 12, 15, 19, -22, -3, -24, -11, 24, 10, 0, -18, -22, 9, -5, -9, 7, -8, 20, 0, 12, -17, 28, -5, 4, 0, 14, 8, 15, 14, 7, 35, -14, -12, 22, 11, 18, -6, -21, -5, 8, 8, 7, -10, -5, 5, 2, -8, 20, -21, -27, 2, -10, 4, 2, 12, 20, -22, 40, 10, -7, 0, 4, 32, -7, -5, -4, 8, 15, -10, -10, -2, -2, 22, -14, 3, 11, 14, 5, 1, 7, -18, -13, 9, 0, -2, -9, -7, 9, -19, 12, 20, -8, -7, -1, -7, 17, 3, 9, 22, -14, 6, -13, -22, 13, 6, 3, -14, -14, -4, -11, -3, 1, -4, 1, 4, 11, -8, 11, -2, 9, 4, -2, 4, 6, 2, 3, 20, -17, -6, 19, 3, 8, -2, -14, -8, 8, -3, 3, -3, -15, 11, 1, -6, 10, -16, -31, -1, -3, 1, 4, 15, 4, -3, 28, 2, -6, -11, 8, 30, -1, 1, -4, 11, 22, -1, -5, -3, 2, 22, -21, 9, 1, 8, 8, -13, 8, -26, -14, 12, -4, 11, -11, 2, 9, -12, 21, 16, -14, 6, 5, -22, 5, 11, 15, 15, -18, 12, -12, -23, 23, 4, -8, -23, -8, 5, -6, -9, -8, -13, 18, -2, 15, -6, 13, 13, 16, 25, 10, 1, -1, 10, 7, 9, -2, -4, 23, -1, 15, -1, -19, -15, 13, -4, 12, -13, -15, 8, 11, -13, 5, -15, -32, 6, -14, 3, 7, 5, -4, 12, 28, 2, -8, -4, 23, 35, 2, 4, -7, 16, 32, -12, -18, -1, -12, 11, -6, 9, 3, 1, 0, -28, 10, 21, -25, 6, 26, 8, -27, -16, 4, 10, 4, 8, -42, -6, -13, 7, 11, 2, -4, 16, -7, -17, -6, 13, 13, -13, 12, -9, -1, 12, 1, -11, -11, 16, -29, 21, -10, 3, 5, 24, -9, -19, -28, 37, -7, 5, 3, -24, 16, 34, 14, -45, -13, 20, 12, 11, 25, 19, -5, -2, 24, 18, -1, -13, -11, -35, -12, -9, -16, 21, 15, 0, -10, 7, 1, 17, 7, -2, 11, -12, -3, 31, 2, 4, -48, -38, 16, 3, 4, 30, 8, 0, 4, 24, 13, -20, 0, 17, -16, 5, 14, 21, -24, -15, 0, 5, 12, 4, -30, -11, -5, 7, 3, 1, -5, 12, 5, -24, -4, 0, 11, -20, 17, -11, -8, 16, -11, -20, -10, 2, -11, 11, -10, 3, -3, 3, -5, 0, -22, 28, -14, -6, -3, -9, 2, 22, 12, -22, -9, 24, 11, 13, 17, 16, -10, 3, 16, 10, -4, -17, -4, -34, -8, 7, -32, 22, 10, -6, -11, 11, 6, 7, 8, -10, 12, -12, -9, 31, -5, 10, -31, -16, 27, 4, -12, 30, 13, -2, -10, 20, -7, -22, -11, 12, -6, 16, 21, 16, -39, -10, -4, 12, 10, 10, -13, -5, -7, -11, 5, -10, -2, 21, 0, -13, -13, -20, 5, -22, 25, -17, -8, 25, -7, -16, -15, -4, -7, 15, -7, 21, -2, 16, -2, -6, -12, 43, 3, -5, -10, -14, -6, 35, 16, -25, -9, 16, 8, 1, 16, 16, -9, -3, 27, 15, 13, -23, 5, -37, 6, -19, -32, 31, 2, -18, 1, 19, -10, 3, 9, -14, 17, 9, 2, 20, -15, 2, -38, -5, 20, -7, -17, -1, 7, -5, -2, 16, 0, -21, -19, 9, -8, 2, 9, -3, 4, 3, 26, 16, -13, -25, -12, -6, 20, 34, -7, -6, -3, 10, 32, -6, 22, -4, -24, -27, 24, -1, 25, -6, 1, 23, 8, 3, -32, 19, -31, 18, -38, 6, 9, -32, -13, -13, 17, -19, 8, 21, -4, 10, 12, -8, -32, 20, -6, 5, -29, -9, -11, 5, 8, 0, 5, -13, 8, -13, -15, -8, 16, -24, 24, 16, 12, 0, -13, -4, 10, -3, -11, -8, 7, 17, 14, 22, -16, 29, -32, 8, 26, 4, 13, -12, 2, -3, 24, -25, -6, 16, 3, 12, 9, 10, -3, -10, 21, 13, -12, -18, -9, -17, 15, 13, 3, -10, 7, 3, 27, -7, 23, 5, -14, -16, 11, -1, 33, 3, 18, -1, -5, 0, -23, 11, -11, -3, -12, 10, 11, -19, -4, -17, 2, -20, -3, 12, 15, -5, -11, -9, -29, 7, -13, 10, -18, -9, -2, 15, 8, 3, 4, -9, 12, -10, -6, -12, 8, -22, 13, 21, -4, -6, 6, 6, 18, -1, -7, -13, -3, 2, 3, 10, -9, 18, -15, 10, 11, -5, 10, -19, 0, 6, 15, -35, -3, 1, -2, -11, 4, 21, 4, -9, 24, 6, -18, -24, -20, -16, 12, 35, 2, 0, -7, 9, 26, -2, 30, 0, -15, -15, 14, 4, 31, 4, -4, 3, 4, 8, -39, 5, -23, -32, -20, 8, 6, -20, -9, -22, 7, -24, 5, 18, 31, -9, -14, -3, -34, 3, 0, 15, 3, -20, -21, -7, 12, 0, -3, -22, 8, 14, -8, -16, 20, -20, 15, 17, -11, -4, 13, 5, 25, 0, -19, -12, 9, -8, -1, 11, -22, 41, -26, 1, 9, 4, 11, -22, -1, 13, 11, -3, -33, -2, 1, -4, 14, -2, 0, -11, 17, -19, 14, -2, -2, 21, 9, -6, 3, -17, 7, 3, -13, 13, -32, -12, 20, -17, 3, -10, -15, 5, 1, 6, -25, -1, 20, 8, 14, 5, -1, -3, 23, -4, -2, -1, -4, -1, 27, 1, 2, -22, 15, -25, 29, 14, -13, -26, 12, -4, -11, 4, -1, 13, -7, 9, -12, -29, 6, -6, 11, -3, 2, 10, 18, 4, 16, -1, -2, 2, 5, 13, -13, -5, 5, -4, 19, -7, -1, 5, -2, 2, -9, 15, 9, -3, 21, -1, -16, 2, -2, 7, 6, 7, 2, -9, 22, -27, 12, -2, -22, 9, 5, -6, 14, -16, 7, 4, -8, 12, -24, -14, 21, 2, 1, -15, -11, 3, -2, 9, -18, -5, 14, 5, 5, 1, -2, 9, 18, 2, -3, -5, -18, 1, 18, 1, 7, -14, 11, -13, 29, 5, -17, -19, 10, -5, -7, 2, 5, 12, 5, 4, -6, -11, 2, -7, 6, -1, -7, -2, 12, 2, 11, -1, -8, -2, -1, 27, -3, -5, -2, -5, 20, 6, -9, 11, 0, 1, -7, 9, 7, -3, 11, -8, -14, -2, -4, 4, 20, 8, 3, -14, 26, -15, 16, 0, -26, 23, 10, -18, 5, -19, 1, -2, -6, 14, -18, -8, 23, -4, -1, -16, -17, 14, 16, 2, -12, -2, 17, 8, 9, -1, -2, 4, 21, 3, 0, 7, -24, 2, 30, -23, 3, -16, 12, -25, 37, 12, -18, -22, 5, -2, -11, 6, -8, 16, 15, 1, 7, -19, 6, -4, 0, -8, -15, -5, 1, 3, 10, -3, -8, -14, 11, 11, -10, -2, -15, -2, 20, 1, -14, 15, 2, -7, -8, 4, 3, 7, 11, 12, -13, 14, -19, 2, -5, -21, 19, 7, 11, -9, 1, -20, 12, -47, -33, 34, -54, 6, -7, 0, 6, 40, -10, 22, 8, 12, -7, -20, -6, 4, 21, 19, -2, -18, -7, -9, 23, 7, 3, -4, 4, -13, -13, 0, 17, -27, -37, -12, -32, -6, 30, -12, 21, 26, -1, -3, -5, -1, -29, -11, -26, 38, 39, 13, 13, -25, 9, -48, 33, -16, 28, -13, 5, 5, -20, 35, 3, -1, -23, 9, 0, -11, 34, -1, 22, 14, -27, 33, -14, -35, 7, -21, 14, 4, 22, -4, -8, 8, -5, 14, 6, -9, 23, 4, -1, -26, -5, -23, -3, -45, -26, 32, -40, -1, 1, 5, 8, 26, -9, 14, 8, 9, -5, -18, -5, 10, 9, 10, -6, -15, 3, -9, 38, 0, -3, -2, 3, 10, 0, 0, 2, -31, -15, -2, -21, -9, 20, 3, 0, 10, -1, 0, -6, -13, -11, -14, -9, 24, 28, 3, 10, -11, 15, -35, 37, -7, 27, -5, 3, 16, -25, 34, -9, 4, -19, 3, -5, 3, 38, 9, 9, 17, -21, 18, -11, -34, 7, -27, 7, 4, 32, -16, -2, 7, -7, 10, -2, -20, 24, 6, 4, -32, -5, -34, 11, -36, -17, 29, -51, 8, -14, -9, 11, 37, 1, 24, 2, -4, -15, -19, -1, 11, 6, -8, -1, -26, -8, -13, 34, 12, -6, 3, 6, 23, -8, -1, 1, -20, -24, 1, -9, -11, 16, 10, 14, 23, -7, -11, -13, -23, -22, -12, 1, 19, 26, -4, 11, -15, 17, -55, 15, -13, 28, -7, -3, 7, -31, 42, 11, -8, -29, 2, 4, 12, 43, 11, 12, 23, -14, 8, -26, -32, 5, -47, 7, -16, 16, 37, 10, -17, -23, -8, -24, -15, 24, 4, -17, 23, 8, -5, -15, -7, -19, 28, -42, 31, -2, 29, 7, 2, 42, 7, -18, 15, -16, 0, -16, -20, 5, -1, -9, -21, 2, -14, 23, -1, -17, -16, 7, 13, -19, -18, 1, 4, -5, 0, 8, 6, 12, 6, 11, 3, 10, 27, 4, 0, 4, -18, -22, -4, 6, 23, 10, 4, -21, 3, 14, 7, -11, -19, -17, -16, 9, 5, 1, 7, 27, 4, -41, -17, -1, 23, 0, 23, -10, -1, 0, -10, 27, -5, -8, 1, -8, 42, 6, 3, -19, -4, -10, -8, 12, 6, -18, 5, -2, -8, -3, -4, -9, 24, -27, 12, -3, 16, 2, 9, 34, 29, -8, 3, 2, 6, -16, -4, -7, -8, -4, -19, 2, 4, 26, 14, -17, -17, 6, 4, -19, -4, -16, 12, -7, -15, 4, 2, 12, 9, -4, -6, 12, 22, -2, 1, 3, -14, -6, -3, 5, 4, 0, -3, -10, 8, -3, 5, -9, -17, -8, -16, -6, 14, 2, 15, 12, 9, -18, -14, 7, 26, -5, 15, -13, 6, 0, -2, 20, -18, -7, 6, 7, 58, 20, 8, -28, -9, -19, -5, 10, -15, -10, -8, 15, -22, -5, 3, -14, 12, -46, 15, 3, 27, 2, -10, 35, 35, -12, 1, -17, -7, -13, -34, -6, 3, 1, -9, 5, 1, 26, 10, -14, -15, 2, 13, -12, 0, -25, 3, 3, -18, 5, -4, 5, 2, -17, -19, 3, 26, -6, -3, 0, -21, 2, -2, 14, -4, 5, -18, -11, 12, 12, -12, -10, -22, 2, -20, -12, 31, -9, 5, 10, 10, -23, -6, 22, 10, -1, -1, 12, -3, 5, -2, 25, -25, -14, -2, 7, 6, 28, -12, 3, 9, -21, 22, 19, 32, 11, 2, -13, 8, -39, -6, -18, 14, -4, 20, -33, 25, 28, -5, 33, 18, -22, -6, -15, 3, -4, 5, -14, 23, 13, -10, 13, -6, 14, 33, 7, 5, -19, -5, -42, -6, -5, -13, -54, -12, 3, 16, 9, 9, -26, 4, 11, 21, -15, -15, 0, -13, -16, 19, 11, 2, 34, -24, 17, 10, 17, 13, 7, -2, -34, 21, -19, 6, 22, 0, -34, 3, 9, -9, 10, 15, 23, 8, 9, 9, 2, -11, 21, -25, -15, 25, -4, 23, 16, 3, 6, 3, -15, 6, 14, 25, 17, 9, -5, 8, -27, -18, -12, 16, -1, 15, -37, 14, 20, -1, 24, 24, -13, -7, -10, 0, 0, 15, -21, 34, -2, -15, 11, -12, 14, 17, 7, 8, -11, -1, -31, 5, -8, -9, -39, -5, 9, 17, 8, 3, -15, -7, 6, 13, -7, -19, 11, -10, -8, 19, -2, 3, 25, -12, 9, 3, 20, -7, 14, -1, -30, 15, -23, 20, 24, -3, -16, 3, -8, -9, 13, -1, 23, 3, 20, 5, 6, -16, 28, -24, -9, 14, 12, 21, 20, -8, -8, 16, -28, 16, 16, 10, 19, 11, -6, -4, -24, -6, -16, 15, 14, 9, -37, 7, 15, -21, 32, 24, -23, 1, -7, 8, 7, 9, -34, 35, 8, -16, 17, -3, -4, 27, 3, 8, 1, 12, -30, 8, -3, -7, -58, -7, 5, 10, 1, -14, -22, -20, 7, 5, -12, -10, 16, -17, -8, 16, 2, 10, 17, -15, 17, -6, 33, -27, -5, 18, -16, 23, -43, 37, 24, 14, -21, 11, 4, -15, 11, -5, 22, -7, 15, -23, 8, -16, 27, -31, -7, 9, 2, 13, 10, 4, 31, -2, -5, -24, 5, 13, 10, 26, -18, 0, 7, -4, 11, 9, 24, 10, 2, -19, 18, -13, 41, 3, -25, 3, 21, -3, 32, 3, -27, 5, -19, 23, -26, 12, -46, 3, 13, 4, -12, -1, 22, 3, 15, 0, -6, 19, -8, 12, -10, 17, -12, -18, 1, 3, -19, -3, 16, 2, -2, -10, -25, 23, 17, 56, -4, 20, -28, 5, -10, -35, -10, 26, -18, -16, 0, 8, 27, 1, -8, 5, -9, -21, -27, 0, -18, -4, 8, 25, 2, 14, -3, 16, -12, 11, 14, 1, 23, -5, -8, -4, 3, 10, 11, 14, -21, -6, 10, -8, -3, 7, 5, 10, 11, -11, 16, -6, 26, 9, -27, 4, 12, 4, 23, 7, -19, 11, -6, 24, -9, -8, -45, 3, 10, 0, 4, -7, 8, 1, 0, -8, -18, 3, 7, 14, -19, 3, -11, -14, -3, -2, -16, 7, 5, 8, 2, -17, -13, 18, 14, 39, -9, 12, -26, 0, -13, -20, -20, 13, -18, -2, -3, 14, 14, -8, -4, 13, -10, -23, -23, -7, -13, -13, 10, 22, -4, 3, -12, 14, -8, 26, 12, 13, 31, -16, -7, 1, -11, 13, 17, 9, -37, 7, 14, -14, -10, -1, 3, 13, 8, -44, 23, 5, 25, 21, -28, 16, 12, 1, 15, 9, -26, 10, -11, 34, -8, -11, -45, -12, 2, 13, 5, -22, -2, 6, 1, -17, -6, -3, 35, 9, -16, -6, -20, -12, 2, 8, 6, -8, 19, 25, 1, -12, -7, 25, -8, 46, -11, 10, -23, -12, -17, -5, -16, 13, -22, 8, -4, 26, 14, -29, 0, 15, -24, -31, -32, -6, -1, 5, 15, 13, -13, 7, -3, 20, -29, 30, 11, 2, -28, 2, 21, -7, 26, -4, -19, 11, 10, 0, -46, 5, 3, -1, 31, 7, 13, 18, 12, -23, 27, 12, 5, -5, 1, -4, -2, -10, -14, -10, -32, 0, -12, -17, -7, -3, -20, -9, -3, -4, -8, 16, -15, -8, 0, 19, 18, -6, 2, -1, -27, -9, 18, -7, 11, 6, -11, 2, -11, -5, 1, 3, -28, -9, 15, 24, -4, 6, 3, 0, -6, 2, 17, -13, 0, 3, 0, 13, 17, -5, -7, 1, -20, -7, 5, 14, -18, 13, 26, 9, -3, -7, -9, 21, 13, 7, -25, -13, 21, -3, 16, -4, -13, 9, 4, -3, -32, 15, 0, 3, 29, -5, 19, 15, 4, -21, 16, -7, 5, -7, 7, -14, -6, -8, -4, -4, -13, 0, -10, -14, -22, -7, -16, -14, 4, 3, -10, 14, -3, -7, -5, 19, 9, 0, -3, 4, -29, -8, 16, 0, 8, 7, -13, 16, -2, -18, 1, 5, -6, 2, -9, 20, -8, 5, -5, -5, 6, -7, 8, -23, 8, 4, 14, 14, 15, 0, -9, -9, 7, -10, -2, 0, -20, 21, 20, 11, -4, -5, -16, 21, 15, 0, -10, -7, 23, -2, 35, 2, -9, -2, 3, 10, -25, 15, 6, 5, 32, 2, 24, 38, -1, -15, 21, -28, 11, -1, 8, -5, -2, -9, 13, -9, -15, 16, -13, -20, -30, -6, -20, 0, 5, 0, 3, 10, -8, -3, -2, 19, 3, 6, -4, -7, -34, -8, 10, 1, 1, 0, -13, -2, 0, -20, 9, -9, -1, -9, -6, 20, -16, 12, -15, 5, 5, 15, 6, -13, 19, -3, 8, 9, 14, -7, -11, -10, -12, -2, 9, -1, -10, 19, 27, 1, 2, -2, -13, -5, -8, 30, -13, -16, 38, -27, -6, 14, -25, 8, 11, 6, -15, -47, -12, -17, 12, -7, 9, -11, 1, -5, -17, 18, -20, 15, 14, -23, 3, 9, 30, 2, 14, 50, -1, 1, 14, -42, -16, 3, -8, 14, -6, 12, 6, -14, -21, -5, 7, 19, 2, 0, -38, 12, -5, 10, 4, -11, 16, -4, 30, 15, 26, 14, -15, -18, -18, -13, 16, -14, 14, 16, 22, -15, -31, 12, 18, -12, -10, 13, 22, 13, 21, 14, 16, -12, 25, -33, 1, -30, -6, -48, 14, -34, -13, -7, -3, 18, -13, -19, 30, 6, -6, 5, -21, 6, 3, 5, -4, -37, -11, -6, 0, -8, 5, -7, -6, -2, -12, 4, -5, 15, 3, -22, 6, 2, 23, 3, 12, 30, 4, -3, -5, -28, -27, 5, -16, 24, 0, 8, 1, -12, -21, -1, -7, 12, 6, 6, -40, 3, -8, 3, 2, -12, 5, -5, 29, 3, 16, -1, -13, -19, -13, 0, -1, -12, 23, 6, 15, 0, -30, 3, -13, -8, 3, 19, 11, 9, 15, 17, 0, -6, 6, -7, 13, -20, 16, -31, 3, 2, 8, -12, -6, 19, -15, -33, 18, -8, -21, -6, -25, 19, 24, 7, -12, -37, -9, -23, -26, -25, -7, -14, 1, 9, -16, -10, -4, 5, -3, -16, -1, 9, 19, -12, 16, 28, -15, -10, 0, -16, -41, 1, -7, 32, 3, 10, 1, -6, -7, 22, 12, 21, 4, -8, -43, 5, 10, 0, 12, -2, -11, 4, 37, 7, 15, 28, -21, -16, -11, -5, -24, -17, 16, -6, 23, -4, -11, 0, 5, -16, -9, 31, 13, 11, 28, 21, -17, 10, 4, -23, -1, -41, -12, -44, 29, -9, -4, 10, -29, 12, 6, -3, 5, -6, 33, -20, 1, -9, -23, -13, 13, 10, -9, 30, -2, -2, 1, -6, 2, 47, -21, 9, 6, -15, -9, -2, -6, 16, 5, 4, -20, 11, -1, 14, 16, -3, -9, -11, 0, -6, -11, -11, 0, 7, 26, -3, 1, 16, -13, -5, 25, -9, -33, -9, -16, 4, 1, -3, 4, 10, 9, 0, -23, 15, 20, 22, -4, 5, -9, -32, -24, 29, -17, -12, -17, 19, 10, 5, 6, 4, 1, -14, 8, 5, 6, 13, -17, 11, -14, -1, 27, -2, 7, 5, -21, 9, 11, -8, 3, -9, 27, -17, 11, -14, -7, -16, 2, 21, -12, 32, 2, -1, 2, -11, 10, 37, -9, -2, 2, -9, -6, -4, -17, 23, 7, 15, -7, 7, 4, 1, 6, -1, -6, -14, 16, -9, 0, -11, 0, 7, 16, -1, 1, 9, -3, 1, 18, 0, -28, -12, -12, 5, 1, -1, 4, 20, 3, 6, 9, 10, 17, 13, -12, 4, -7, -27, -13, 3, -13, -6, -23, 14, 15, -4, 9, 0, -13, -20, 3, 7, -4, 9, -4, 16, -7, 0, 15, -2, 6, 8, -19, 10, 21, -23, 12, -10, 44, -5, 26, -6, -7, -14, 8, 29, -6, 37, -4, 3, 3, -26, 10, 35, 1, -3, -5, -14, -6, 3, -26, 13, 19, 8, 4, -4, 20, 2, 0, -6, -12, -1, 15, -11, -7, -16, 11, 4, 6, -6, 9, -4, -10, -4, 24, 11, -16, -21, -8, 9, 5, 3, -22, 20, 5, -7, -8, 14, 28, 5, -24, 3, -19, -10, -6, 13, -5, 1, -23, 12, 7, -10, 7, 6, -9, -22, 6, -3, -10, 13, -7, 14, -10, 4, 1, -1, 1, 5, -11, 18, 14, 1, 20, -10, 17, -23, -11, 12, 0, -20, -5, 17, 8, 0, 6, -13, 19, 2, 23, -11, 15, -5, -2, -2, 14, 12, 13, -9, 7, 10, -29, 3, 7, 14, -22, -23, -6, -4, -1, 4, 17, -13, 3, 22, 39, -2, 16, -6, -18, -2, -17, 9, 37, -5, -32, 9, -10, 1, 5, -16, -19, -16, -15, 9, 8, -6, 33, -5, 18, 1, 14, -3, 1, -31, 19, -24, 4, -25, -7, -4, -16, 9, -13, -4, -33, -13, -1, -12, 16, 39, -30, -1, -5, -11, 2, 14, 20, -4, 9, -3, 8, -29, -2, 13, -1, -22, -10, 15, 7, -8, 13, -16, 13, -2, 5, -4, 15, 7, -5, 7, 15, 17, 11, -8, 3, 17, -10, -2, -2, 5, -21, -16, -13, 3, 1, -8, -2, -7, 4, -3, 18, -5, 0, 1, -3, -2, -8, -2, 32, 3, -28, -1, 2, 1, -3, -6, -8, -11, -14, 2, 19, 9, 23, 9, 9, 8, 17, -3, -6, -22, 19, -27, 3, -20, -2, -4, 5, 1, -14, -1, -19, -20, 19, -15, 15, 31, -15, -10, 5, -4, 5, 7, 38, -8, 14, -6, 10, -33, 6, 18, -10, -14, -2, 16, 20, 2, 1, -20, 8, 0, -1, 5, 15, 10, -8, 25, 15, 33, -6, 3, 17, 27, -5, 1, -6, 7, -11, -18, -15, -2, -7, -14, 0, -12, -1, -4, 17, -9, 2, 0, -2, -12, 1, -14, 26, 23, -19, -3, 1, -2, -5, -9, -9, -10, -19, 19, 39, 3, 8, -2, 3, 10, 10, -12, -4, -28, 4, -30, 29, -19, 13, -16, -1, -2, -29, 18, -12, -5, 15, -26, 9, 32, -9, -20, -16, 10, 17, -15, 3, 1, -3, 9, 8, 0, -2, -7, 11, 10, 24, -6, -6, 3, 2, 17, 14, 30, 4, -25, 13, -17, -12, 13, -6, 13, -8, -3, -16, -4, 6, -2, 13, -17, -1, 14, 16, -2, -9, 11, 10, -13, -4, -11, 9, 31, -16, -14, 11, 13, -2, -3, 23, 4, -10, 6, 1, 3, -23, -12, -20, -1, 6, 14, 0, 14, 4, 9, 34, -2, 12, 5, 4, -16, 0, -13, 11, 4, 7, -14, -4, 7, 6, 7, 8, -5, -14, -9, -1, 17, -15, 8, 17, 7, 17, -14, -1, -3, -3, -5, -2, -4, -1, -11, 13, 10, 15, -10, -11, -3, 0, 11, 14, 18, -6, -26, 13, -6, -10, 21, -1, 9, -9, -7, -8, -2, 2, -3, 6, -13, -3, 7, 6, 3, 0, 8, 6, -4, 0, -5, -4, 24, -15, -13, 7, 15, -9, -4, 23, 7, -10, -6, 1, 5, -22, -11, -17, 4, 5, 11, -6, 5, 8, -2, 19, 8, 10, -1, 10, -3, 8, -13, 6, 1, 10, -16, -9, 8, 2, 5, 8, -2, -8, -9, -9, 18, -11, 14, 8, 1, 13, -9, -4, -10, -4, 1, 7, 4, -6, -18, 11, 10, 28, -1, -18, -1, -7, 14, 15, 23, -13, -32, 17, 9, -4, 18, -1, 18, -9, -7, -5, 4, -1, 6, 7, -12, -10, 15, 12, 2, -5, 8, 16, -12, 2, 2, -1, 22, -16, -13, 9, 11, -17, -24, 30, 10, -10, 5, 7, 3, -20, -15, -18, -11, -3, 17, -14, 10, 12, -5, 25, 12, 9, 2, 10, -5, 2, -10, 11, -2, 17, -16, -3, 0, 15, 1, 11, -6, -11, -9, -3, 12, -15, 20, 1, -10, 14, 5, 14, 5, 2, 7, -6, -4, 17, 13, 1, 25, 9, -13, 1, 7, 13, -8, -18, -2, -2, 5, -11, 17, -28, 1, -10, 11, -27, 22, -8, 9, 43, 9, 46, 19, 0, -2, 25, -12, -17, 1, 8, -4, 10, 11, 3, 9, -10, 26, 6, 3, 15, -9, 15, -18, -17, -3, 7, -10, -22, -5, 0, 0, 7, -7, 15, -6, -37, 13, 64, 14, 2, 18, -15, 2, 15, 29, 13, 5, -7, 2, 13, -6, 9, -14, 26, 7, -7, -38, 0, 15, 10, 2, 4, 4, 8, 7, 4, 2, -3, -4, -13, -5, 15, 3, -7, 24, 0, -16, -3, 11, 14, 0, -20, -1, -2, -1, -5, 7, -20, 5, -7, 1, -22, 8, -5, 13, 24, -4, 31, 15, 8, -5, 11, -13, -26, 2, 0, -7, -5, 3, -6, 14, -11, 22, 4, 0, 16, 5, 8, -23, -22, 5, -1, 1, -17, 2, -4, 6, 2, -15, 3, -7, -16, 12, 47, 10, 1, 18, -20, -7, 1, 18, -12, 2, -10, -16, -8, -1, 6, -6, 14, 5, -3, -29, -3, 14, 7, 2, 6, 13, -11, 9, 3, -6, 3, -10, -20, -1, 23, 18, -4, 21, 14, -6, 11, 10, 35, 5, -6, 1, 3, -6, -5, -3, -20, 8, -24, 0, -18, 3, -15, 12, 27, 1, 25, 16, 2, -2, 22, -3, -16, 3, -7, -4, -5, 0, 1, 20, -9, 18, 0, -13, 16, -1, 10, -42, -22, -5, 12, -4, -17, 10, 2, 20, 16, 9, -6, 1, -27, 17, 65, 9, 5, 24, -29, 12, -4, 32, -23, 6, -21, 0, -12, 2, 24, -20, 19, 8, 1, -41, -2, 18, 15, 9, 2, 22, -14, -11, 0, -11, -22, 10, 5, -11, -4, -11, 32, 0, -7, 14, -5, 7, 5, 17, -20, 32, -16, -11, -16, 7, 18, -21, -13, 4, -1, -4, 5, 5, -21, -5, -16, 10, 3, -2, -13, 1, -15, -19, -10, 4, -11, -2, -3, 21, 12, 34, -16, 27, -6, -11, 12, -16, 20, -5, 26, -23, 16, -8, -6, 11, -22, 13, -52, 26, -10, -17, -12, -7, -33, 12, 4, -5, -10, -1, -7, 16, 4, -13, 3, 20, 13, -5, 28, 5, 19, 29, 16, -2, 26, -20, 3, 24, -14, -4, -6, -7, -21, 13, -4, 9, 2, 1, 19, -9, -19, 18, -1, 22, 3, 14, -20, 15, 1, -3, -6, 2, 2, -16, 2, 9, -10, -1, -11, 7, -15, 0, -26, 0, -7, 9, 4, -13, -8, -14, -17, -6, -23, 3, 4, 29, 5, 18, -10, 10, -14, 0, 12, -10, 3, -5, 17, -8, 11, -14, 0, 25, 5, 17, -40, 19, -5, -4, -15, -3, -27, -4, 4, -21, -6, 2, -13, 20, 1, -7, 11, 4, 10, -4, 6, 5, 27, 29, 16, -3, 10, -18, 11, 24, 1, -3, -16, -8, -17, 16, 0, 1, -4, 6, 40, -10, -33, 35, 21, 9, 3, 22, -17, 26, 0, -10, -4, 2, 8, -3, -13, -2, -42, -3, -3, 2, -13, -2, -15, 16, 8, 5, -11, -26, 10, -4, -23, -11, -45, -4, 11, 28, -7, 29, -15, 16, -17, -2, 19, -8, 2, -11, 23, -2, 2, 6, -5, 24, 0, 16, -42, 41, 4, -15, -19, -21, -23, -14, 1, -28, -10, 17, -11, 14, -2, -16, -2, 10, -8, -13, 3, 4, 17, 33, 10, 10, 11, -13, 28, -19, -1, -21, 12, -6, 5, -15, 9, 22, -16, 10, 4, 15, 1, -22, -4, -23, -21, -7, 10, -36, 15, -4, 10, -15, 42, 13, 9, -30, 40, -6, 12, 18, 6, 9, -1, 18, 13, -5, 15, 4, 26, 8, 33, -13, 11, -17, -36, 13, -7, -32, 21, -2, 0, -3, 1, -3, 32, -18, -15, -1, -9, -6, -20, 8, 0, 10, 20, 8, 23, -6, -22, -16, -3, -45, 9, 26, -4, -5, -23, 3, -12, 4, 17, 42, 6, 34, -38, -38, 17, 0, -15, -25, -13, 8, -26, -7, 7, -5, 7, -5, -4, -7, 13, 10, -17, 5, -1, 12, 13, -32, -9, -16, -17, -11, 11, -31, -8, -3, 2, -9, 33, 25, 14, -25, 26, -4, -9, 11, 17, 1, 17, 14, 18, -5, 20, -9, -6, -13, 25, 8, 1, -19, -28, 27, -10, -9, 12, 11, -10, -12, 9, -1, 26, 3, -12, 5, -7, -17, -17, 13, 3, 14, 27, 0, 26, -17, 2, 3, 5, -34, 2, 27, -3, -6, -22, 2, -14, 18, 10, 26, 11, 21, -10, -23, 19, -1, -7, -17, -11, -6, 0, 2, 8, 5, 24, -21, -17, -4, 7, -2, -12, -1, -6, 13, 16, -25, -16, -23, -2, -17, 0, -32, -13, 9, -4, -18, 35, 17, 17, -18, 40, 2, 8, 23, 12, 7, -4, 2, 11, -25, 15, -14, 3, 0, 29, 11, 19, -28, -37, 30, 6, -17, 16, 0, -10, -18, -7, 12, 28, -27, -14, 6, 1, 4, -2, 19, -2, 7, 15, 17, 47, -15, -4, 6, 6, -49, 12, 34, 11, 4, -20, 6, 10, 10, 20, 30, 31, 30, -9, -38, 33, 3, -9, -31, 8, -8, -31, 14, 23, -26, 1, 19, 7, 17, -17, 18, 7, -23, 10, 29, 8, 10, -8, -9, 27, 32, 19, -4, -23, -8, -8, 18, 28, -7, 23, -20, 16, -2, -24, -33, -12, -14, -9, -37, 20, 11, 7, 16, 15, 16, -6, 1, -10, -9, 0, 3, -21, -24, 21, 0, 2, -13, -18, 15, 9, -6, 4, 0, -16, -11, 5, -5, -17, -26, 9, -14, -30, 25, -30, -2, 11, 13, 1, 25, -23, -7, 11, -6, 2, -3, -2, 10, -11, -30, 11, -1, -3, 2, -46, 13, 8, 26, 34, 21, 16, -28, -2, 18, 0, 3, -22, 17, 4, -11, 10, 3, -4, 3, -2, -14, 11, 18, 17, -4, -18, 1, -5, 14, 26, -6, 12, -2, 6, -5, -5, -19, -1, -9, -9, -22, 2, -5, 9, 5, 16, 6, 3, -3, -16, -7, -9, -4, -11, -15, 21, -2, -11, -2, -9, 13, 22, -9, 2, -1, -15, -11, 1, -5, -4, -16, 1, -8, -26, 20, -21, 0, 11, 14, 6, 21, -15, -5, 12, 3, -12, 0, 15, 10, -8, -35, 12, -19, -10, -9, -21, 18, 5, 18, 33, 31, 27, -20, -10, 21, -5, -10, -15, 25, 11, -29, 3, 16, -4, -3, 10, -14, 12, 27, 14, 1, -22, 16, -8, 17, 26, -5, 8, -3, 24, 13, -23, -25, -7, -16, -6, -44, 13, -16, 1, 7, 13, 11, 0, 7, -15, -9, -14, -12, -14, -20, 17, -12, -1, -3, -7, 13, 26, -1, -1, 4, -12, 1, 2, -21, -24, -39, -33, -28, -30, 9, -37, 4, -18, 15, 2, 14, -5, 0, 3, 4, -17, -13, -3, 16, 10, -36, 18, -15, -13, -6, -18, 30, 0, 27, 32, 6, -6, 4, 33, 9, -16, -4, -19, -16, -3, -4, -7, -6, -14, 0, -14, -1, -21, 16, -5, 8, -11, 15, -22, 19, -15, -13, 1, 23, -5, 13, -18, -5, -16, -19, -14, 4, -19, -25, 21, 27, -33, -1, -7, 26, 5, 21, -2, -7, 25, 27, 2, 3, 2, 12, 15, -7, -26, 4, 18, 0, 14, 11, 27, 25, -20, 1, 26, -14, -45, 16, -17, -23, -23, 24, -35, -10, -12, -9, 14, -4, -18, 1, 12, 4, -14, 9, -3, -2, 3, -9, 2, 19, -14, 41, 0, 5, -24, 1, 27, 16, -11, 10, -17, -15, -2, -4, -2, -3, -11, 5, -8, 2, 3, 12, -3, -3, -3, 16, -17, 12, -14, -12, 0, 19, -8, 8, -19, -12, -13, -19, -1, -2, -6, -21, 19, 17, -20, 0, 0, 16, 10, 15, 5, -9, 14, 17, -6, 0, -3, 20, 8, -4, -12, 11, 13, 0, 9, 3, 12, 10, -9, 0, 32, -16, -29, 26, -25, -27, -6, 22, -29, -8, -1, 0, 1, -4, -11, -5, 20, 13, -6, 14, -7, -2, 11, -5, -4, 15, -19, 22, -7, 14, -30, 0, 39, 17, -15, 3, -30, -18, 5, -12, -7, -3, -4, -8, -12, 6, 2, 26, -6, -3, -4, 20, -14, 6, -13, -2, 9, 21, -20, 6, -10, -2, -17, -9, -3, 10, 6, -16, 27, 11, -31, 0, -9, 20, 7, 11, -7, -18, -3, 37, 5, -21, -8, 14, 8, -5, -15, 22, 17, -11, 15, 13, 20, 8, 2, 8, 28, -18, -24, 32, -19, -39, -15, 19, -35, 0, -20, -11, -3, -6, -30, 4, 13, 2, -16, 6, 7, 1, 11, 8, -4, 22, -17, 32, -20, 25, 4, 15, -7, -21, 25, -17, 18, -4, -33, 1, 2, 22, -7, 10, -1, -17, 0, -12, 11, 3, -10, -9, -5, 2, -18, -25, 1, 8, -18, -3, 45, -9, 0, 9, -11, -27, 9, 14, 16, 12, -24, -18, -31, -10, -24, -37, 16, -17, -13, 2, 0, 4, -25, -16, -27, 5, 28, 23, -4, 14, 5, -1, 5, -7, -1, -23, 9, -6, 10, 21, 26, 9, -28, 7, -34, 1, -17, 3, -12, -32, 1, 13, 4, -28, 3, -14, 2, 13, -3, 31, -3, 15, 7, -13, 29, -10, -7, 9, -6, -18, 25, -5, 18, -7, -19, 0, 14, -2, 2, 18, -22, -4, -5, -7, 14, 3, -3, 3, -11, -5, -4, -14, -2, 9, 3, -3, 29, -13, -9, -5, -20, -25, 15, 8, -11, 0, -38, -9, -22, -25, -21, -24, -12, 1, -26, 8, 9, -6, -18, -9, -16, -12, 13, 11, -8, -3, 17, 4, 14, 1, -14, -16, 3, -5, 23, 39, 0, -10, -28, 4, -4, -9, -16, 3, -22, -11, 5, -8, 5, -34, -12, -7, 4, 9, -12, 9, 0, -12, 25, -23, 13, -20, -21, 1, -12, -20, 29, 6, 5, -17, -4, -1, 17, 24, 10, 11, -20, 8, 11, 9, 22, 7, 9, 13, -24, -14, -10, -15, 9, -4, 7, 10, 56, -3, -18, -10, 0, -27, 17, 19, -22, -25, -41, 2, -16, 3, -22, -27, 9, 15, -16, 5, -5, 5, 3, 11, -39, -5, 7, 19, -10, 4, 23, 6, 18, -6, -1, -30, -8, -15, 21, 49, -16, -51, -20, 10, -15, 3, -11, 1, -24, 1, 6, -22, -5, -8, -12, -2, 6, 28, 13, 40, 11, -5, 23, 2, 36, 39, 10, 9, -6, 27, 3, 8, 16, -26, 9, 0, 27, -30, -17, -7, 13, 3, 6, 0, 8, 18, 13, -25, 2, 24, 5, -3, 15, 26, -6, -28, 8, -5, -18, 14, 9, -32, -15, 30, -24, -7, 6, 22, -8, -12, -28, -47, -7, -22, -18, -1, 14, 20, -7, 11, -3, 8, -2, 4, -31, -8, -27, -6, 7, -7, -5, -21, -24, -3, 35, 14, 30, 24, -4, 31, -19, 4, -3, -17, 2, -18, 3, -7, 20, 25, -11, 7, -22, -8, 7, -27, 37, 18, 0, -24, 33, 38, 5, 8, -9, 16, -2, -5, 11, -13, 17, -9, 22, -8, -6, -5, 9, -12, -7, -3, 23, 5, -5, -32, -12, 13, 13, 12, 5, 30, 5, -18, 3, 3, -8, 14, 19, -33, -13, 21, -28, -10, -8, 8, -21, -12, -11, -44, -17, -31, -11, -13, 10, 6, -7, 4, 14, 5, -2, -1, -19, -5, -19, -4, 16, -17, -4, -11, -23, -6, 18, 16, 19, 34, -23, 18, -11, 5, -5, -3, -8, -24, -12, -5, 24, 0, -7, 6, -23, -15, 10, -44, 21, 13, 1, -14, 16, 31, 12, 34, -10, -9, 6, -15, 15, -11, 15, -15, 6, -14, -21, -5, 4, -8, -7, 6, 20, 16, -15, -32, -5, 17, -9, -2, 12, 39, 1, -42, -1, 7, -8, 24, -2, -41, -32, 6, -47, -15, -12, 10, -7, -20, -15, -37, -16, -22, -13, -11, -8, 9, -10, 8, 13, 8, 1, 1, -17, 7, -11, -5, 12, -14, -5, -11, -38, -18, 9, 19, 21, 29, -22, 8, -36, 3, 12, -17, -16, -8, -8, -17, 11, 20, -7, 21, -23, -28, 1, -32, 16, 14, -5, -15, 8, -4, 13, -28, 11, -4, 2, 0, -8, 2, 8, -1, -6, -7, -6, -11, 14, 3, 9, 5, -11, 9, -2, 0, 5, 22, -21, 10, 8, 31, -3, 18, -24, -15, -2, -23, 6, 19, -34, -19, -45, -1, 5, -11, -18, 0, 3, 11, -13, 8, 2, 16, -35, 23, -7, 21, -7, -25, -25, 16, 10, 12, 21, 3, -8, -2, 5, -12, -2, 36, -19, -13, -22, -6, 4, 3, -17, -26, 13, 5, -8, -9, -3, -23, -37, -2, 4, 7, 26, -17, 13, 17, 0, -1, 4, 19, -6, -4, 11, -21, 11, 2, 2, 11, -10, 3, 3, 2, 2, -5, -10, 3, 12, 2, 8, 5, -8, -5, -6, 0, 8, 14, -28, 10, 4, 17, 7, 11, -23, -3, 0, -9, 15, 22, -30, -8, -21, -13, 6, -11, -7, 6, 8, 10, -13, 6, 7, 18, -35, 1, 0, 11, 2, 2, -22, 12, 7, 3, 13, 3, -18, -6, -6, -3, -9, 27, 0, -11, -1, -17, 13, -5, -15, -10, 8, 3, -7, -1, 1, -9, -30, -24, 20, 11, 39, 2, 28, 7, -12, -2, -3, 7, 0, 5, 2, -34, 19, 7, -4, 24, -24, 14, 4, 3, -20, -4, -19, -10, 7, 12, 3, 3, -8, 5, -4, -7, 8, 4, -20, 27, 3, 35, 6, 4, -26, 7, -5, -20, 19, 23, -41, 2, -27, -15, -15, -7, -15, 10, 5, 14, -19, 1, -1, 25, -21, -10, 17, 1, 10, -6, -8, 15, -1, 3, 9, -9, -29, 10, -5, -10, -3, 30, -3, -1, -16, -23, -10, -8, 0, -17, 18, 5, -9, 12, 10, -7, -35, -17, 16, 0, 26, 8, 32, -8, -39, 1, 6, 2, -29, 1, 5, 14, -1, -11, 24, -9, 12, -13, -10, 11, 9, -8, -15, -1, 19, -9, -8, -38, 3, 21, -15, -16, 8, 35, -30, 15, 7, 13, -16, 8, 27, 2, -28, -31, 4, 39, -12, -23, -38, -5, -25, -10, -18, 5, -1, -8, 19, -25, 5, 18, 15, -2, -2, 18, 20, -3, -1, -13, 9, -15, 3, 3, 11, -2, 9, -33, 16, 3, 24, 26, 29, 10, 4, 16, 9, -22, 21, -33, -4, 0, -12, 0, -8, -25, -33, 5, 6, -2, 4, -5, 29, -13, -2, -17, 10, 8, 6, 17, 6, -10, 17, -6, 2, -7, 1, 8, 7, 6, -5, -2, 12, -12, -4, -27, -7, 2, -14, -10, 2, 18, -20, 16, 6, 16, -13, 14, 6, -2, -19, -26, -3, 24, -3, -33, -31, -3, -27, -3, -14, 2, -2, -12, 17, -16, -11, 20, 16, 2, 13, 19, 9, 7, -1, -14, 2, -10, -1, 3, 11, 2, 3, -23, 9, -1, 6, 15, 32, 15, 4, 6, 0, -18, 10, -25, 4, 6, -14, 0, 3, -1, -15, -5, -15, 3, 11, -19, 56, -8, 2, -15, 20, -1, 14, 19, 4, -12, 23, 16, 3, -15, 0, 14, 7, 3, -19, 5, 17, -20, 10, -36, -13, 2, -20, -22, 4, 13, -11, 25, -6, 18, -25, 28, 9, -8, -4, -31, -1, 48, -1, -35, -25, -9, -34, -3, -2, 4, -6, -9, 16, -36, -11, 26, 11, 2, 0, 11, 14, 14, -9, -12, -6, -18, 1, -2, 9, -7, 13, -42, 16, 5, 14, 12, 34, -2, 14, 11, 18, -36, 10, -11, 0, 14, -7, -9, 7, -3, -18, -3, -6, -6, -5, -42, 64, -13, 2, -30, 11, 19, 4, -28, -11, -5, 16, -35, 13, -11, -30, -23, 2, -16, 1, -27, -22, -16, -18, 17, 42, -26, -10, -24, 13, 33, -11, -1, 37, 27, 47, 12, 8, -24, -3, -14, -4, -12, -26, -44, -11, -10, -12, -18, 4, -3, 23, 4, 12, 5, -4, 7, 16, -7, -14, 6, -27, 16, -11, 18, -6, 8, 17, -11, -34, -24, -37, -16, 22, 11, 0, 1, -25, 14, 19, -29, 14, -10, 0, -14, -4, 6, 1, -7, -12, 17, -9, 27, 4, -11, -3, 20, -26, -5, 9, -4, 3, 10, -3, -21, -12, 6, 14, -12, -4, -4, -26, -27, 10, -14, 7, -15, -9, -6, -8, 12, 38, -17, -15, -19, 0, 3, -6, 8, 34, 9, 27, 14, -5, -12, -8, -22, -7, -18, -24, -22, -22, -19, -15, -12, 17, -7, 11, 3, 2, 7, -9, 8, 15, 3, -19, -2, -13, 20, -14, 22, -3, 5, 9, -1, -16, 9, -22, -18, 4, 21, -6, 7, -9, 14, 19, -17, -6, 2, -11, -21, -24, 1, -7, 9, -1, 15, -10, 18, 4, -4, 2, 7, -7, -1, 1, 0, -5, 6, -24, -17, -3, 13, 12, -18, -5, 12, -24, -42, 0, 1, 2, -17, -19, -16, 0, 14, 41, -23, -22, -12, -13, -16, -12, 13, 38, 15, 26, -5, -6, -19, -5, -20, 0, -20, -10, -8, -48, -7, -10, -10, 20, -17, 24, 17, 17, 13, -37, 11, 2, 7, -20, 2, -10, 32, 5, 17, -10, 10, 2, -7, -8, 13, -38, 13, 3, 16, -21, 7, -11, 14, 34, -32, -7, -8, 13, -28, -9, -5, 13, 5, 6, 0, -1, 21, 6, -1, 0, -3, -48, 6, -8, -8, 3}

#define TENSOR_CONV2D_3_KERNEL_0_DEC_BITS {4}

#define TENSOR_CONV2D_3_BIAS_0 {50, 42, 14, -33, 11, 6, 28, 94, -92, -32, -55, -59, 39, 91, 76, -40, -67, -16, 23, 12, 17, -7, -17, 15, 50, -3, 20, 54, 92, 10, 47, 48}

#define TENSOR_CONV2D_3_BIAS_0_DEC_BITS {4}

#define CONV2D_3_BIAS_LSHIFT {3}

#define CONV2D_3_OUTPUT_RSHIFT {6}

#define TENSOR_DENSE_KERNEL_0 {-36, -48, -21, 9, -40, -17, -24, -74, 18, -40, -25, 40, 33, -96, -49, 11, -71, 6, -18, -48, -31, 20, -10, -5, -6, 11, 41, -22, 60, 38, -22, -54, -32, 37, 26, 29, -78, 46, -69, 37, -61, -30, 23, 52, -28, 31, -41, -44, 2, -40, -34, 6, -80, -2, 48, -34, 22, 59, -45, 55, 18, 9, 11, -38, -41, -18, 50, 22, 9, -28, 23, 8, -11, -1, -63, -32, 42, -40, -13, -48, 42, -52, -42, 26, -66, -36, -19, 18, -58, -18, -83, -25, -8, 7, 18, -13, 55, -29, -34, -22, 32, -44, -52, 47, -36, -4, 13, -17, 46, 57, 8, 16, 40, 31, 45, -17, 43, 16, -31, -47, -32, -39, 50, -53, -15, 10, 35, 45, 3, -20, -42, -65, -49, -83, 29, -41, -14, 17, -9, 43, -48, 0, 19, -64, 1, 36, -51, -4, 23, -23, 30, 53, -34, -19, -93, -11, 1, -3, -57, 10, -19, -71, 1, -39, 7, -12, -40, 35, 6, 0, -25, -7, -56, 25, 51, -45, -22, 11, 50, 14, -55, -25, -59, -72, -13, 56, 35, -9, -18, -35, 18, 22, -50, -50, -35, 44, 61, -37, -56, 47, 11, -50, -52, 47, 18, 38, -6, -35, 14, -72, 2, -11, 11, -63, -50, 33, 26, -53, 53, 34, 16, -55, -40, 26, -51, 31, 34, -15, 21, -3, -39, -64, 18, -45, 18, 37, 28, -15, 56, 46, 52, -49, 42, 1, 37, -41, -71, 49, -1, -17, -20, -5, 0, 41, 19, 13, 18, 13, 36, 28, -14, 11, 15, 4, 27, -29, 29, -32, 18, -33, -54, -6, -32, 43, 7, -65, -20, 0, 33, 34, 3, -23, 0, 34, 28, 30, 8, 31, -5, -32, 30, 15, 30, -24, -17, 7, 46, 43, -15, -11, 11, -23, -55, -45, 43, 5, -30, 34, -31, 36, 13, -18, -53, -9, -17, -19, -78, -2, -19, -17, 7, -45, -39, -31, 36, 7, -18, -82, -35, 47, -21, 9, 9, -7, -12, 43, -28, -14, -2, -81, 31, 19, 34, 16, -20, 20, 3, -2, 47, -30, -24, -54, 26, -10, -2, -48, 29, -58, 8, 26, 46, 32, -14, -38, -60, -12, -44, -9, -26, -33, -61, -31, -56, -4, -24, 20, -59, -13, -48, 34, -48, -21, -50, -62, 19, -28, 24, 26, 41, 38, 18, -34, -47, 43, -33, -4, -32, 21, 48, 2, 34, 34, -2, -4, -17, -49, -58, 13, -60, -37, -23, 55, 26, -45, -36, 7, 25, -66, -40, -44, 8, -4, 31, -39, 14, -14, 0, 27, -48, 1, 30, 20, 39, 17, -3, -30, -25, -33, -8, 35, -25, -28, -52, -3, -71, 7, -25, 53, -24, -37, -10, -10, 47, 17, -5, -19, -17, 17, 35, -32, 16, -54, 28, -15, 27, 16, 34, -31, 16, -13, -23, 0, 21, -5, 14, 36, -43, -4, 21, -12, -67, -5, -44, -28, -39, 25, 34, 3, -16, -71, -27, -37, -24, 3, -29, -85, 25, 5, 20, 17, -28, -22, -26, -10, -24, 51, 20, -61, -1, 40, -2, 20}

#define TENSOR_DENSE_KERNEL_0_DEC_BITS {5}

#define TENSOR_DENSE_BIAS_0 {6, 2, 8, 11, 5, 6, 6, 4, -2, 0, -3, -4, -11, 5, -11, -6}

#define TENSOR_DENSE_BIAS_0_DEC_BITS {5}

#define DENSE_BIAS_LSHIFT {0}

#define DENSE_OUTPUT_RSHIFT {5}


/* output q format for each layer */
#define INPUT_OUTPUT_DEC 1
#define INPUT_OUTPUT_OFFSET 0
#define CONV2D_1_OUTPUT_DEC 3
#define CONV2D_1_OUTPUT_OFFSET 0
#define BATCH_NORMALIZATION_1_OUTPUT_DEC 3
#define BATCH_NORMALIZATION_1_OUTPUT_OFFSET 0
#define RE_LU_1_OUTPUT_DEC 3
#define RE_LU_1_OUTPUT_OFFSET 0
#define MAX_POOLING_1_OUTPUT_DEC 3
#define MAX_POOLING_1_OUTPUT_OFFSET 0
#define CONV2D_2_OUTPUT_DEC 3
#define CONV2D_2_OUTPUT_OFFSET 0
#define BATCH_NORMALIZATION_2_OUTPUT_DEC 3
#define BATCH_NORMALIZATION_2_OUTPUT_OFFSET 0
#define RE_LU_2_OUTPUT_DEC 3
#define RE_LU_2_OUTPUT_OFFSET 0
#define MAX_POOLING_2_OUTPUT_DEC 3
#define MAX_POOLING_2_OUTPUT_OFFSET 0
#define CONV2D_3_OUTPUT_DEC 1
#define CONV2D_3_OUTPUT_OFFSET 0
#define BATCH_NORMALIZATION_3_OUTPUT_DEC 1
#define BATCH_NORMALIZATION_3_OUTPUT_OFFSET 0
#define RE_LU_3_OUTPUT_DEC 1
#define RE_LU_3_OUTPUT_OFFSET 0
#define DROPOUT_1_OUTPUT_DEC 1
#define DROPOUT_1_OUTPUT_OFFSET 0
#define AVERAGE_POOLING_FINAL_OUTPUT_DEC 1
#define AVERAGE_POOLING_FINAL_OUTPUT_OFFSET 0
#define FLATTEN_OUTPUT_DEC 1
#define FLATTEN_OUTPUT_OFFSET 0
#define DENSE_OUTPUT_DEC 1
#define DENSE_OUTPUT_OFFSET 0
#define SOFTMAX_OUTPUT_DEC 7
#define SOFTMAX_OUTPUT_OFFSET 0

/* bias shift and output shift for none-weighted layer */

/* tensors and configurations for each layer */
static int8_t nnom_input_data[2376] = {0};

const nnom_shape_data_t tensor_input_dim[] = {198, 12, 1};
const nnom_qformat_param_t tensor_input_dec[] = {1};
const nnom_qformat_param_t tensor_input_offset[] = {0};
const nnom_tensor_t tensor_input = {
    .p_data = (void*)nnom_input_data,
    .dim = (nnom_shape_data_t*)tensor_input_dim,
    .q_dec = (nnom_qformat_param_t*)tensor_input_dec,
    .q_offset = (nnom_qformat_param_t*)tensor_input_offset,
    .qtype = NNOM_QTYPE_PER_TENSOR,
    .num_dim = 3,
    .bitwidth = 8
};

const nnom_io_config_t input_config = {
    .super = {.name = "input"},
    .tensor = (nnom_tensor_t*)&tensor_input
};
const int8_t tensor_conv2d_1_kernel_0_data[] = TENSOR_CONV2D_1_KERNEL_0;

const nnom_shape_data_t tensor_conv2d_1_kernel_0_dim[] = {5, 5, 1, 16};
const nnom_qformat_param_t tensor_conv2d_1_kernel_0_dec[] = TENSOR_CONV2D_1_KERNEL_0_DEC_BITS;
const nnom_qformat_param_t tensor_conv2d_1_kernel_0_offset[] = {0};
const nnom_tensor_t tensor_conv2d_1_kernel_0 = {
    .p_data = (void*)tensor_conv2d_1_kernel_0_data,
    .dim = (nnom_shape_data_t*)tensor_conv2d_1_kernel_0_dim,
    .q_dec = (nnom_qformat_param_t*)tensor_conv2d_1_kernel_0_dec,
    .q_offset = (nnom_qformat_param_t*)tensor_conv2d_1_kernel_0_offset,
    .qtype = NNOM_QTYPE_PER_TENSOR,
    .num_dim = 4,
    .bitwidth = 8
};
const int8_t tensor_conv2d_1_bias_0_data[] = TENSOR_CONV2D_1_BIAS_0;

const nnom_shape_data_t tensor_conv2d_1_bias_0_dim[] = {16};
const nnom_qformat_param_t tensor_conv2d_1_bias_0_dec[] = TENSOR_CONV2D_1_BIAS_0_DEC_BITS;
const nnom_qformat_param_t tensor_conv2d_1_bias_0_offset[] = {0};
const nnom_tensor_t tensor_conv2d_1_bias_0 = {
    .p_data = (void*)tensor_conv2d_1_bias_0_data,
    .dim = (nnom_shape_data_t*)tensor_conv2d_1_bias_0_dim,
    .q_dec = (nnom_qformat_param_t*)tensor_conv2d_1_bias_0_dec,
    .q_offset = (nnom_qformat_param_t*)tensor_conv2d_1_bias_0_offset,
    .qtype = NNOM_QTYPE_PER_TENSOR,
    .num_dim = 1,
    .bitwidth = 8
};

const nnom_qformat_param_t conv2d_1_output_shift[] = CONV2D_1_OUTPUT_RSHIFT;
const nnom_qformat_param_t conv2d_1_bias_shift[] = CONV2D_1_BIAS_LSHIFT;
const nnom_conv2d_config_t conv2d_1_config = {
    .super = {.name = "conv2d_1"},
    .qtype = NNOM_QTYPE_PER_TENSOR,
    .weight = (nnom_tensor_t*)&tensor_conv2d_1_kernel_0,
    .bias = (nnom_tensor_t*)&tensor_conv2d_1_bias_0,
    .output_shift = (nnom_qformat_param_t *)&conv2d_1_output_shift, 
    .bias_shift = (nnom_qformat_param_t *)&conv2d_1_bias_shift, 
    .filter_size = 16,
    .kernel_size = {5, 5},
    .stride_size = {1, 1},
    .padding_size = {0, 0},
    .dilation_size = {1, 1},
    .padding_type = PADDING_VALID
};

const nnom_pool_config_t max_pooling_1_config = {
    .super = {.name = "max_pooling_1"},
    .padding_type = PADDING_VALID,
    .output_shift = 0,
    .kernel_size = {2, 1},
    .stride_size = {2, 1},
    .num_dim = 2
};
const int8_t tensor_conv2d_2_kernel_0_data[] = TENSOR_CONV2D_2_KERNEL_0;

const nnom_shape_data_t tensor_conv2d_2_kernel_0_dim[] = {3, 3, 16, 32};
const nnom_qformat_param_t tensor_conv2d_2_kernel_0_dec[] = TENSOR_CONV2D_2_KERNEL_0_DEC_BITS;
const nnom_qformat_param_t tensor_conv2d_2_kernel_0_offset[] = {0};
const nnom_tensor_t tensor_conv2d_2_kernel_0 = {
    .p_data = (void*)tensor_conv2d_2_kernel_0_data,
    .dim = (nnom_shape_data_t*)tensor_conv2d_2_kernel_0_dim,
    .q_dec = (nnom_qformat_param_t*)tensor_conv2d_2_kernel_0_dec,
    .q_offset = (nnom_qformat_param_t*)tensor_conv2d_2_kernel_0_offset,
    .qtype = NNOM_QTYPE_PER_TENSOR,
    .num_dim = 4,
    .bitwidth = 8
};
const int8_t tensor_conv2d_2_bias_0_data[] = TENSOR_CONV2D_2_BIAS_0;

const nnom_shape_data_t tensor_conv2d_2_bias_0_dim[] = {32};
const nnom_qformat_param_t tensor_conv2d_2_bias_0_dec[] = TENSOR_CONV2D_2_BIAS_0_DEC_BITS;
const nnom_qformat_param_t tensor_conv2d_2_bias_0_offset[] = {0};
const nnom_tensor_t tensor_conv2d_2_bias_0 = {
    .p_data = (void*)tensor_conv2d_2_bias_0_data,
    .dim = (nnom_shape_data_t*)tensor_conv2d_2_bias_0_dim,
    .q_dec = (nnom_qformat_param_t*)tensor_conv2d_2_bias_0_dec,
    .q_offset = (nnom_qformat_param_t*)tensor_conv2d_2_bias_0_offset,
    .qtype = NNOM_QTYPE_PER_TENSOR,
    .num_dim = 1,
    .bitwidth = 8
};

const nnom_qformat_param_t conv2d_2_output_shift[] = CONV2D_2_OUTPUT_RSHIFT;
const nnom_qformat_param_t conv2d_2_bias_shift[] = CONV2D_2_BIAS_LSHIFT;
const nnom_conv2d_config_t conv2d_2_config = {
    .super = {.name = "conv2d_2"},
    .qtype = NNOM_QTYPE_PER_TENSOR,
    .weight = (nnom_tensor_t*)&tensor_conv2d_2_kernel_0,
    .bias = (nnom_tensor_t*)&tensor_conv2d_2_bias_0,
    .output_shift = (nnom_qformat_param_t *)&conv2d_2_output_shift, 
    .bias_shift = (nnom_qformat_param_t *)&conv2d_2_bias_shift, 
    .filter_size = 32,
    .kernel_size = {3, 3},
    .stride_size = {1, 1},
    .padding_size = {0, 0},
    .dilation_size = {1, 1},
    .padding_type = PADDING_VALID
};

const nnom_pool_config_t max_pooling_2_config = {
    .super = {.name = "max_pooling_2"},
    .padding_type = PADDING_VALID,
    .output_shift = 0,
    .kernel_size = {2, 1},
    .stride_size = {2, 1},
    .num_dim = 2
};
const int8_t tensor_conv2d_3_kernel_0_data[] = TENSOR_CONV2D_3_KERNEL_0;

const nnom_shape_data_t tensor_conv2d_3_kernel_0_dim[] = {3, 3, 32, 32};
const nnom_qformat_param_t tensor_conv2d_3_kernel_0_dec[] = TENSOR_CONV2D_3_KERNEL_0_DEC_BITS;
const nnom_qformat_param_t tensor_conv2d_3_kernel_0_offset[] = {0};
const nnom_tensor_t tensor_conv2d_3_kernel_0 = {
    .p_data = (void*)tensor_conv2d_3_kernel_0_data,
    .dim = (nnom_shape_data_t*)tensor_conv2d_3_kernel_0_dim,
    .q_dec = (nnom_qformat_param_t*)tensor_conv2d_3_kernel_0_dec,
    .q_offset = (nnom_qformat_param_t*)tensor_conv2d_3_kernel_0_offset,
    .qtype = NNOM_QTYPE_PER_TENSOR,
    .num_dim = 4,
    .bitwidth = 8
};
const int8_t tensor_conv2d_3_bias_0_data[] = TENSOR_CONV2D_3_BIAS_0;

const nnom_shape_data_t tensor_conv2d_3_bias_0_dim[] = {32};
const nnom_qformat_param_t tensor_conv2d_3_bias_0_dec[] = TENSOR_CONV2D_3_BIAS_0_DEC_BITS;
const nnom_qformat_param_t tensor_conv2d_3_bias_0_offset[] = {0};
const nnom_tensor_t tensor_conv2d_3_bias_0 = {
    .p_data = (void*)tensor_conv2d_3_bias_0_data,
    .dim = (nnom_shape_data_t*)tensor_conv2d_3_bias_0_dim,
    .q_dec = (nnom_qformat_param_t*)tensor_conv2d_3_bias_0_dec,
    .q_offset = (nnom_qformat_param_t*)tensor_conv2d_3_bias_0_offset,
    .qtype = NNOM_QTYPE_PER_TENSOR,
    .num_dim = 1,
    .bitwidth = 8
};

const nnom_qformat_param_t conv2d_3_output_shift[] = CONV2D_3_OUTPUT_RSHIFT;
const nnom_qformat_param_t conv2d_3_bias_shift[] = CONV2D_3_BIAS_LSHIFT;
const nnom_conv2d_config_t conv2d_3_config = {
    .super = {.name = "conv2d_3"},
    .qtype = NNOM_QTYPE_PER_TENSOR,
    .weight = (nnom_tensor_t*)&tensor_conv2d_3_kernel_0,
    .bias = (nnom_tensor_t*)&tensor_conv2d_3_bias_0,
    .output_shift = (nnom_qformat_param_t *)&conv2d_3_output_shift, 
    .bias_shift = (nnom_qformat_param_t *)&conv2d_3_bias_shift, 
    .filter_size = 32,
    .kernel_size = {3, 3},
    .stride_size = {1, 1},
    .padding_size = {0, 0},
    .dilation_size = {1, 1},
    .padding_type = PADDING_VALID
};

const nnom_pool_config_t average_pooling_final_config = {
    .super = {.name = "average_pooling_final"},
    .padding_type = PADDING_VALID,
    .output_shift = 0,
    .kernel_size = {45, 4},
    .stride_size = {1, 1},
    .num_dim = 2
};

const nnom_flatten_config_t flatten_config = {
    .super = {.name = "flatten"}
};
const int8_t tensor_dense_kernel_0_data[] = TENSOR_DENSE_KERNEL_0;

const nnom_shape_data_t tensor_dense_kernel_0_dim[] = {32, 16};
const nnom_qformat_param_t tensor_dense_kernel_0_dec[] = TENSOR_DENSE_KERNEL_0_DEC_BITS;
const nnom_qformat_param_t tensor_dense_kernel_0_offset[] = {0};
const nnom_tensor_t tensor_dense_kernel_0 = {
    .p_data = (void*)tensor_dense_kernel_0_data,
    .dim = (nnom_shape_data_t*)tensor_dense_kernel_0_dim,
    .q_dec = (nnom_qformat_param_t*)tensor_dense_kernel_0_dec,
    .q_offset = (nnom_qformat_param_t*)tensor_dense_kernel_0_offset,
    .qtype = NNOM_QTYPE_PER_TENSOR,
    .num_dim = 2,
    .bitwidth = 8
};
const int8_t tensor_dense_bias_0_data[] = TENSOR_DENSE_BIAS_0;

const nnom_shape_data_t tensor_dense_bias_0_dim[] = {16};
const nnom_qformat_param_t tensor_dense_bias_0_dec[] = TENSOR_DENSE_BIAS_0_DEC_BITS;
const nnom_qformat_param_t tensor_dense_bias_0_offset[] = {0};
const nnom_tensor_t tensor_dense_bias_0 = {
    .p_data = (void*)tensor_dense_bias_0_data,
    .dim = (nnom_shape_data_t*)tensor_dense_bias_0_dim,
    .q_dec = (nnom_qformat_param_t*)tensor_dense_bias_0_dec,
    .q_offset = (nnom_qformat_param_t*)tensor_dense_bias_0_offset,
    .qtype = NNOM_QTYPE_PER_TENSOR,
    .num_dim = 1,
    .bitwidth = 8
};

const nnom_qformat_param_t dense_output_shift[] = DENSE_OUTPUT_RSHIFT;
const nnom_qformat_param_t dense_bias_shift[] = DENSE_BIAS_LSHIFT;
const nnom_dense_config_t dense_config = {
    .super = {.name = "dense"},
    .qtype = NNOM_QTYPE_PER_TENSOR,
    .weight = (nnom_tensor_t*)&tensor_dense_kernel_0,
    .bias = (nnom_tensor_t*)&tensor_dense_bias_0,
    .output_shift = (nnom_qformat_param_t *)&dense_output_shift,
    .bias_shift = (nnom_qformat_param_t *)&dense_bias_shift
};

const nnom_softmax_config_t softmax_config = {
    .super = {.name = "softmax"}
};
static int8_t nnom_output_data[16] = {0};

const nnom_shape_data_t tensor_output0_dim[] = {16};
const nnom_qformat_param_t tensor_output0_dec[] = {SOFTMAX_OUTPUT_DEC};
const nnom_qformat_param_t tensor_output0_offset[] = {0};
const nnom_tensor_t tensor_output0 = {
    .p_data = (void*)nnom_output_data,
    .dim = (nnom_shape_data_t*)tensor_output0_dim,
    .q_dec = (nnom_qformat_param_t*)tensor_output0_dec,
    .q_offset = (nnom_qformat_param_t*)tensor_output0_offset,
    .qtype = NNOM_QTYPE_PER_TENSOR,
    .num_dim = 1,
    .bitwidth = 8
};

const nnom_io_config_t output0_config = {
    .super = {.name = "output0"},
    .tensor = (nnom_tensor_t*)&tensor_output0
};
/* model version */
#define NNOM_MODEL_VERSION (10000*0 + 100*4 + 3)

/* nnom model */
static nnom_model_t* nnom_model_create(void)
{
	static nnom_model_t model;
	nnom_layer_t* layer[14];

	check_model_version(NNOM_MODEL_VERSION);
	new_model(&model);

	layer[0] = input_s(&input_config);
	layer[1] = model.hook(conv2d_s(&conv2d_1_config), layer[0]);
	layer[2] = model.active(act_relu(), layer[1]);
	layer[3] = model.hook(maxpool_s(&max_pooling_1_config), layer[2]);
	layer[4] = model.hook(conv2d_s(&conv2d_2_config), layer[3]);
	layer[5] = model.active(act_relu(), layer[4]);
	layer[6] = model.hook(maxpool_s(&max_pooling_2_config), layer[5]);
	layer[7] = model.hook(conv2d_s(&conv2d_3_config), layer[6]);
	layer[8] = model.active(act_relu(), layer[7]);
	layer[9] = model.hook(avgpool_s(&average_pooling_final_config), layer[8]);
	layer[10] = model.hook(flatten_s(&flatten_config), layer[9]);
	layer[11] = model.hook(dense_s(&dense_config), layer[10]);
	layer[12] = model.hook(softmax_s(&softmax_config), layer[11]);
	layer[13] = model.hook(output_s(&output0_config), layer[12]);
	model_compile(&model, layer[0], layer[13]);
	return &model;
}
