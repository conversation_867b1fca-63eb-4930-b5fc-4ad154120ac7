#include "nnom.h"

/* Weights, bias and Q format */
#define TENSOR_CONV2D_1_FUSED_KERNEL_0 {15, 17, 8, 22, -37, 10, 13, 9, 15, -44, 39, 3, -5, 5, -26, 35, 2, 18, -12, -25, 12, -11, 31, 0, -5, 42, 17, -5, -3, -39, -1, 2, 9, 21, -1, -25, 14, 0, 8, 38, -69, -13, 14, 16, 78, -54, 0, 12, -7, 54, -36, 30, 44, -12, 16, -8, 18, -18, -25, -2, 46, 17, -12, -23, 11, 58, 6, -27, -9, 2, 57, 0, 6, -28, 24, -7, 40, 49, 74, 51, -9, 63, 55, 71, 88, -7, -34, -42, -34, -17, -1, -71, -55, -97, -114, 5, -3, -9, 4, -28, -19, 6, 13, -46, 65, -12, -2, -10, -10, 55, 14, -18, -46, 21, 16, 43, -28, 28, -41, 44, -15, 13, 4, -15, -6, 42, 8, -16, 14, 20, 73, 0, -39, 46, 6, 29, -61, -4, 20, -64, -102, 106, -32, -9, 40, -84, -11, 69, -73, -16, -50, -75, -12, -21, 44, -16, -26, -14, -36, 5, 20, 16, 29, -5, -26, 72, 63, 26, -24, 8, 31, 51, 76, 55, 26, 5, 11, 5, -35, -29, 32, -27, 20, -47, 6, 33, -55, 36, -16, 18, 23, -39, 22, -2, 35, 13, -5, 5, -14, -4, 25, 71, 23, -58, -72, -37, -18, -35, 37, 22, -88, -105, -29, 119, 126, 105, 95, 34, -95, -105, -6, -27, 17, 1, 21, -22, -30, 14, 4, -5, -68, -54, -40, -44, -34, 17, 2, 3, 6, 18, -26, -52, -70, -16, -7, 95, 87, 61, 68, 23, 27, -12, 24, -11, 63, 41, 14, 19, -19, -31, 34, 34, 27, 26, 16, -2, -40, 27, -32, 77, 24, -43, -29, 4, -49, 24, -2, 35, -11, 29, -69, 21, -10, 39, -50, -39, 31, -7, 63, -44, -33, 8, -7, 15, -47, 26, -16, 29, -23, 26, -15, -4, -16, 1, -31, -16, -4, -16, 8, -34, 4, 10, -3, 0, -18, -12, 7, -4, 5, -10, -31, -9, -2, -9, -17, -1, 0, 8, 19, -3, 26, -23, -9, 34, -19, 22, -31, -7, 30, -40, -7, -18, 22, 16, -22, 9, -20, 16, 21, -24, 10, 26, 35, 2, -40, 6, 31, 32, 5, -36, 1, 20, 21, -12, -40, -6, -3, -5, -6, -29, -16, 5, -6, -29, -6, 52, -6, 3, 30, 29, 8, -14, 8, 15, 3, 6, -3, -51, -20, -6, 27, 1, -26, 10, 32, 37, 7, -19, 25, 34}

#define TENSOR_CONV2D_1_FUSED_KERNEL_0_DEC_BITS {6}

#define TENSOR_CONV2D_1_FUSED_BIAS_0 {-14, 50, -126, -84, 81, -83, -12, -1, -15, -48, 31, 69, -107, 4, 8, -92}

#define TENSOR_CONV2D_1_FUSED_BIAS_0_DEC_BITS {8}

#define CONV2D_1_FUSED_BIAS_LSHIFT {0}

#define CONV2D_1_FUSED_OUTPUT_RSHIFT {8}

#define TENSOR_CONV2D_2_FUSED_KERNEL_0 {-2, 23, -6, 1, 5, 4, 8, -8, 9, 9, -15, 22, -3, -13, 1, 37, 9, 10, 22, 3, 5, -13, -9, -8, -11, 3, 1, -7, -8, 15, 9, 9, 7, 10, 7, -11, 27, 17, -13, 5, -10, -35, 19, 8, -2, 31, 7, 10, -4, 7, 2, 13, 13, -14, 32, -7, -27, 5, -3, 15, -6, 0, -2, 9, 6, -13, -6, -3, 4, -19, -26, -13, -1, 15, 8, -6, -9, 11, 8, -12, -21, 1, -23, -5, 12, -7, -38, -37, -46, 8, -7, 13, -5, 12, 1, 28, 4, -12, 9, 9, -8, -12, 11, 12, -12, 21, -28, 23, -10, -22, -6, 1, 2, -19, -1, -3, -13, -12, -1, 10, -13, 0, -10, -8, 1, -21, 38, 15, -33, -12, -59, 16, -6, 4, -43, -36, -51, -6, -38, -18, 9, 5, 9, -13, 7, -9, -1, 23, 8, 11, -6, 20, 20, -3, 17, -9, -8, -7, 3, -2, 7, -13, 22, 7, -2, -18, 20, 15, -10, 16, -6, 12, -42, -7, -3, 6, -6, 12, 17, -13, 29, 7, -3, 7, 0, -20, 6, 18, -44, 10, -4, -39, -10, 9, 0, 11, -18, 1, -7, -12, -5, -10, 2, -1, -21, -7, -8, 0, 21, 5, 2, 4, 16, 15, 0, 10, 3, -12, -3, 18, -30, -26, 10, 2, -2, -8, -21, -16, 6, 1, -5, 2, -4, -20, -16, 13, -32, 27, 22, -42, 2, -9, -3, 18, -2, -7, -5, 3, -18, -15, 6, 11, -52, 2, 11, -9, 17, 10, 17, -6, -1, -1, 12, 13, -8, 2, -3, 12, -41, -13, 1, 10, 18, 17, -5, -21, 13, 4, -10, 3, 10, -28, -24, 13, -40, -7, 20, -19, -11, -9, -20, -4, 7, -2, -38, 8, -2, -21, 5, 32, -6, -18, -20, -1, -5, 1, -34, 9, 7, -19, -15, -23, -2, -8, 28, -15, 2, -17, 10, -12, -7, 19, 13, -9, -24, 23, 0, -27, -1, 26, 7, 6, -6, -28, 7, -9, -17, -18, 1, -16, 2, -17, -45, -2, 0, -27, -3, 6, 0, -16, -17, 8, -12, 4, -2, 3, -17, 24, -18, -9, -6, 19, 4, 2, 6, -14, 12, -4, 5, 16, 50, -8, -1, 8, -14, 0, -6, 18, -14, -11, 0, -7, -1, -31, -3, 0, 25, -29, 18, 4, -40, -17, 0, -24, 14, 29, 2, -11, -18, 8, 28, 4, 5, 15, 11, -18, -33, -13, 13, -5, 19, -1, 15, 18, 24, 23, 3, 12, 55, -19, 21, 11, 5, 15, 10, 9, 3, 1, 7, 37, 0, -5, 27, 4, 21, 5, -12, -9, 3, -15, -3, -28, 18, 3, -19, 9, 26, 18, 3, -15, -6, -7, 12, 21, -38, -5, -24, -9, -15, 1, 2, -8, 14, -5, 22, 24, 22, -13, 4, -11, 0, 18, 9, 6, 6, -7, -1, -5, -3, -28, 19, 2, 11, -12, 14, 8, 1, -10, -19, -19, 10, -26, -17, 22, 1, 2, 8, -8, -11, -8, 3, 25, 2, -21, 1, -6, -9, 11, -4, -5, -3, 13, 29, 13, -10, 3, 0, 6, 9, 18, -17, -12, -1, -9, 11, 2, 18, -22, 17, 11, 9, -20, 11, -2, 5, -27, -14, -11, -26, 2, -21, 18, 13, -11, 0, -10, -9, -14, 9, 35, -8, -14, -17, 10, -8, -29, -15, 0, 2, 3, 1, 13, 5, 3, -6, -2, -45, -13, -49, -10, -8, 10, 23, -5, 25, -28, 14, -11, -3, -8, -20, -5, -16, -1, -4, 16, 7, -21, 7, -15, -9, 23, 2, -6, 29, -16, -12, -34, 4, -10, -9, 0, -4, -30, -15, -6, 2, 13, 28, 0, 17, 16, -9, 13, -18, 2, -21, -4, 22, 11, 10, 9, -4, -18, -6, -10, -10, 14, -1, 6, 3, 20, -2, 15, 21, -17, 2, -20, -25, 9, 15, 18, 41, 7, -26, -36, 16, -11, -15, 0, -4, -4, -5, -7, 4, 2, 12, -1, -36, -4, -22, -3, -5, -3, -6, -7, -1, 19, 11, 15, 9, -48, 1, -20, -19, -5, -3, 10, 9, 4, 5, 0, 17, -16, 14, -5, -8, 15, 31, -12, 25, 8, -16, 9, 3, -15, 13, 3, -1, 7, 22, -2, -7, 8, 28, 16, -36, 1, -13, 11, -18, -1, -11, 4, -20, 2, 0, 7, 0, -30, 6, 6, 35, 14, -20, -8, -14, 5, -1, -17, -7, 18, 0, -8, -23, 25, -16, 30, -41, 35, -2, -29, 7, -42, -5, -19, -13, -12, 8, -3, -11, -31, 13, -2, 26, -14, 9, 2, 19, -19, 8, 19, 17, 22, -2, -18, -13, 8, 19, -29, 15, 6, -5, -9, 8, 4, -18, -33, 1, -33, 2, -11, 1, 23, -8, -10, -15, -9, 9, 11, 6, -36, 10, 7, -16, -7, 17, 5, 3, -9, 7, 6, 31, -19, 25, -11, 11, 13, -12, -14, 19, 19, -4, -9, -7, 20, 6, 0, 11, -11, -52, 23, -44, -27, -7, -2, -18, -26, -34, -7, 7, 4, 1, -4, 21, -1, 42, 13, -24, 0, -4, 3, 4, -25, 12, 23, -7, 22, -4, -8, 19, 31, 0, -2, -10, 3, -13, 1, 0, 22, -9, -17, -12, 9, 10, 34, 18, -35, 4, -5, -35, -5, -28, 18, -16, 16, 4, 22, -1, 18, 2, 3, 0, 14, 9, 6, -29, 12, -49, -6, -15, 5, -14, -1, 3, 2, 15, 30, -22, -44, 12, -16, -2, 3, -31, -9, -6, 9, -6, -7, 9, 18, 1, -6, 11, -42, 5, 7, -14, 7, -39, 2, 4, 6, 5, 10, -2, 15, 0, -14, 6, -6, -11, 2, -14, -2, -38, 1, -6, -14, 2, 0, 1, -4, 3, 5, -20, -55, 12, -14, -6, 9, -38, -25, 10, -8, -4, -3, -3, -2, 4, -1, 22, -8, 2, 2, 20, -6, -19, 25, -6, 11, 4, -6, 25, 2, -19, -11, 2, -29, -3, 7, -3, -6, -9, 11, -13, -9, 3, -19, 5, -17, 2, -9, -38, -35, 5, 3, -1, 5, -13, -6, -15, 20, 3, -9, 22, -20, 24, 35, 4, 22, 1, 24, 9, -31, -1, -22, -33, -3, 3, -25, -17, 3, -4, -50, 1, 7, -10, -5, -10, -12, -14, -5, 8, -5, -1, -12, -4, 1, 0, 19, 9, -7, -7, 0, 12, -7, 9, 12, -12, -11, 8, 20, 9, -5, 21, 10, 0, -4, -19, 27, -1, -17, -13, 16, -3, -23, 11, -24, 1, 20, 10, -20, 6, 5, -7, 4, -25, 0, 11, -22, -3, 0, 7, -2, 12, 5, -8, 29, 13, -18, -8, 3, -15, 6, 16, -32, -3, -17, -21, 12, -2, -1, -10, 26, 6, 23, -5, -17, -18, 34, 10, -23, 10, -1, 18, 8, -21, 23, 10, 4, 24, 3, -12, 16, 11, -1, -12, -14, 16, -2, 25, -5, -3, 10, -3, -17, 16, -8, 0, -14, 19, 15, 8, 2, 11, 2, -12, -7, 3, 8, -12, 3, -50, 14, -6, 23, 7, 5, 3, 12, -3, 4, -12, -15, 3, -3, -1, -11, 4, -16, 9, -14, 7, 0, 13, 2, -2, 6, -13, 6, 13, -7, -10, 7, -14, 15, 9, -7, 5, 3, 1, -6, -4, -16, -14, 27, -15, -9, 8, -9, -25, 9, 0, 24, -2, -15, -10, -7, 0, -1, 2, -13, -1, -15, 4, 13, 12, 1, 11, -12, 2, 6, -12, -11, 2, 2, -13, -1, 14, -6, 5, 9, -15, -3, 17, 1, -15, 12, 16, -8, 1, -4, -13, 11, -4, 7, -3, -26, -6, 6, -8, 7, -2, -26, -4, 13, -4, 15, 3, -22, -5, -13, -4, 5, -9, -13, 1, -6, 1, 9, 0, 0, 4, 3, -8, -5, -9, 3, -1, -1, -27, 8, 20, 4, -10, 9, 5, -8, 13, -18, -18, 3, -42, 18, 10, 8, -5, 19, -14, -12, -25, -3, -18, -7, 14, -37, -31, 25, 22, 1, 5, -4, 21, -2, -2, -1, -7, -4, 14, 7, -32, -19, -10, 6, 19, 11, -12, 10, -4, -32, 17, -7, 23, -4, -18, -1, 9, -14, 5, -12, -11, -9, -7, -2, -5, 22, 10, -9, -11, -16, 0, 2, -13, -14, 10, 20, 7, 0, 14, 3, 19, 9, -4, -7, 14, -4, -18, -14, -16, -16, -15, 17, 14, 13, 5, 6, 12, 27, 9, -6, 21, 5, 3, -12, 4, -2, 20, 2, -7, 8, -2, -11, -7, -8, 16, -17, -10, 19, -5, -22, 2, -34, -10, 17, 15, -1, 19, 13, 40, 4, -7, -19, -14, 3, 6, 11, -12, -9, -9, 13, 3, -3, 23, 5, 11, 5, 9, -34, -15, 0, 1, 12, 14, 11, 22, 13, -8, -6, 18, 34, 7, 13, -18, 6, -6, 14, -4, -12, -1, -10, -3, 26, 0, 1, -4, 3, -39, -5, 2, 0, -19, -10, 3, 18, -15, -13, -9, 17, 16, -11, -57, 0, -20, -4, -4, 4, -4, 3, -6, 5, -6, 3, 1, 13, 17, -20, 4, -3, 20, 3, -11, -31, 9, -4, -14, -11, -7, -7, 1, 21, 10, -8, 0, 12, -16, -5, 2, 7, -2, -18, 2, 6, -16, -24, 2, 12, 7, -6, -10, 6, -2, -9, -21, -3, 2, 5, -4, -23, -1, 1, -4, 15, 12, -62, 17, -4, 0, -5, -4, -12, -1, 0, -2, -10, -6, -24, 2, 11, 16, -8, 3, 12, -24, -11, 10, -9, 7, -33, 15, 20, -35, -27, 2, 1, 12, -11, -9, 5, -2, 6, 2, 5, -23, -9, 15, 13, -43, -10, -4, 14, -3, -37, -28, -14, -19, 5, 7, 1, 5, 14, 0, -15, -6, 1, 11, 5, -2, -9, 13, -5, -4, -10, -5, 9, -3, -24, -9, -3, -11, 9, 8, -17, -10, 18, 2, 4, -17, -17, 15, 9, 7, -2, 9, -9, -2, -11, -10, -7, -4, -13, -20, -5, -18, -3, -1, 8, 11, 15, -1, 5, 8, 0, -3, 6, 7, 4, 1, -1, -10, -4, 7, 15, -2, -19, 7, 4, -9, 9, 1, -3, -8, 0, 9, 2, 1, -10, -5, 2, 11, -1, 13, 4, -9, -8, -11, -2, 3, 1, -11, 0, -13, 4, -2, -5, 14, -4, 9, -11, 12, 6, -8, 3, 6, 0, 7, 6, -5, 11, 2, -6, 4, -24, 8, 7, -5, 13, -1, -11, -4, 14, 13, 9, -2, 3, -7, 6, 10, -15, 11, 5, -7, -8, -2, -8, 12, 3, -1, -13, 41, 30, -10, 38, 3, -1, 7, -29, -23, -28, -15, -11, -3, 34, -10, 17, -7, -17, 1, 37, -21, 23, -3, -10, 34, -16, -29, 9, -27, 32, 38, 32, 17, 19, 40, -1, 37, -2, 21, -13, 2, -8, -34, 0, -17, -28, -1, -24, -18, 11, -18, -3, 5, -16, 0, 9, -25, 13, -5, 14, 11, -12, -4, -12, -15, -9, -2, 3, -83, 3, -34, 2, 8, 28, -2, 5, 0, -11, 2, 7, 14, -2, -24, -8, -13, -19, -4, -22, 3, -11, 6, 13, 4, 25, -6, -15, -11, 13, 0, -2, 6, 44, -55, -34, -5, 24, 6, 8, 1, 3, -14, -27, 9, 3, 1, 8, -40, -13, -28, 6, -13, 37, 12, 4, -10, -18, 6, -48, -9, -16, -16, -5, -7, -9, -44, -24, -15, 5, 6, 18, 22, -2, -10, -30, 20, -42, 6, -13, -15, -1, 2, 14, 15, 20, 16, 11, 8, 6, -48, 1, 12, -2, 13, 2, 5, 22, -10, 17, -6, 10, 7, 3, 5, -11, 25, 6, -30, -28, 4, 18, 5, 21, 0, 14, 4, -3, 24, 8, -4, -16, -12, -17, 21, -13, 16, 3, -14, -4, -3, 8, -8, -16, -1, -11, 0, 12, -4, 9, -9, -12, -2, -14, -16, 9, 8, 11, -12, 0, -14, -11, 10, -27, 8, -21, 5, -5, -9, -8, -7, 3, -5, 1, 8, 1, 1, -1, -2, -6, 6, -28, 17, 15, -8, -13, 6, -11, 1, -10, 0, -5, -41, -6, -2, 5, 14, 14, -8, -15, 2, -10, 18, -14, 9, 14, -16, -13, -27, 2, 4, -26, 24, -12, 21, -26, -19, -49, -8, -1, -2, 10, 7, -7, -19, 13, 5, -8, 6, -2, 4, -6, 13, 12, 18, 22, -21, 1, 1, -5, -19, 0, -1, 23, -20, 9, 3, -10, 23, 23, -13, -3, 11, -6, 12, 3, 11, -8, -15, 3, -22, 27, -12, -7, 19, -10, -12, -8, -18, -20, -15, -17, -16, -6, 17, 15, -3, 12, -6, -17, 23, 7, 16, 2, -13, 8, 9, 4, 5, -7, -13, 20, -7, -1, 21, -21, 2, -27, -22, -1, -14, 12, 3, -14, 0, -20, 33, 1, 11, 21, -4, 31, 20, -18, -9, -25, -12, -8, 1, -15, 6, 5, 18, 3, -9, 8, 13, -5, 12, -9, 10, 16, -21, 21, 3, 35, -5, 1, -6, 26, 4, 13, 11, 16, -7, 1, 4, -8, -31, 9, 22, -10, 9, -27, 19, 5, -35, 5, -41, -3, 18, 10, -20, -21, -11, -28, -8, -24, -1, 8, 4, -1, 8, -30, 15, 10, -17, 18, 29, 34, -1, 8, -1, -26, -14, -5, 3, -4, 12, 10, 29, 8, 5, -6, 17, 13, -9, -17, -17, 9, 0, 11, 20, 13, -25, -16, -25, -20, 25, 6, 5, 15, 21, -6, -8, -13, 2, 8, -6, 9, -10, -11, 9, 14, 8, 12, 10, 23, -4, -12, 19, -38, 2, 1, 4, -13, 5, 9, 21, -14, -13, 14, 16, -12, 11, -12, -21, 5, 1, -7, -13, -2, 0, -5, 12, 7, 20, -30, 14, 21, 12, 10, -9, -20, -7, 1, -24, 9, -24, -16, -3, 1, -15, 0, 2, -38, -4, 2, 0, -7, 23, -5, 1, -35, 10, -2, -1, -16, -2, 8, -19, -20, -2, 9, -9, 12, -19, 11, -21, -27, -32, 16, -3, -17, -23, 4, 15, -17, 5, 7, -15, 35, -8, -6, -12, -7, -30, 11, -23, -11, -6, 14, 1, -6, -8, 13, -20, 0, -29, -15, 28, 1, 7, -2, -11, 1, 22, 38, -4, 18, 18, 2, -20, -14, -19, 8, -13, -26, 29, -21, -15, 24, 5, 0, 15, 46, -30, 0, -37, 1, -17, -4, 25, 19, -2, 13, -11, -3, -11, 6, -14, 12, 4, -6, 5, -9, 11, 25, 14, -8, -10, -3, 29, -17, -18, -5, 23, 10, -32, -23, -27, -17, -7, 28, 1, -6, 33, -7, -6, 6, -2, -21, -9, 33, -11, -10, -22, -26, 3, -10, 21, 36, -6, -1, 13, 15, 5, -14, -8, 10, 12, -16, -22, -22, -1, 21, 23, 33, -5, -13, 16, -3, -11, -8, 28, 18, -22, 8, -12, -31, -5, 23, -3, -9, 30, -7, -33, -12, 6, 1, -14, 14, -9, -9, -26, -28, -7, 26, 2, 7, -5, 5, 12, -32, -3, 11, 15, 24, 10, 9, -6, 9, -22, 31, 18, -8, -37, -19, 16, -1, 22, -2, 7, 1, -4, 20, -17, 7, 6, 10, -5, -12, 1, -26, 30, 3, -5, 38, -5, 35, 5, 1, -11, -12, -16, 25, 0, -22, -31, -29, 4, 20, 8, 4, 30, -9, -4, 6, 0, 5, -10, 8, 14, 7, -22, -25, 3, -6, 16, -2, 3, 13, -7, -14, 3, 0, 8, 18, -6, -15, 5, -33, -6, 2, -18, -2, 19, 17, 13, 1, -45, -5, -4, -1, 14, -38, -24, -39, 4, 6, -13, 30, 31, -37, 11, -9, 2, 5, 1, -27, 1, 21, -34, -2, -2, 14, -20, 13, 25, -5, -6, 0, -26, 12, 2, -15, -45, 5, -9, -25, -9, 3, 7, 7, 20, -14, 10, -11, -34, -15, -19, -26, -2, -19, -5, -24, -7, 9, -6, 0, -8, 11, 2, 8, -6, -26, -13, 13, 39, -7, -13, 7, -3, 7, -13, 11, 14, 30, 4, -2, 8, -16, 10, -16, -15, 9, -1, 0, 10, 1, 2, -4, -21, -10, -19, 12, -6, -17, -7, -27, -19, 6, -5, -6, -3, -29, 23, 1, -19, 13, -4, -2, -16, -24, -17, 1, 20, 15, -14, 5, -3, -16, 8, -4, 1, 26, -8, 1, 6, -12, 14, 3, -7, 12, 12, 0, 22, 6, 3, -16, 9, 5, 5, 6, -10, 4, 4, -12, -3, 18, -5, 5, 5, -8, 24, -2, -16, -7, -3, -9, -2, -6, -12, 5, 17, 9, -8, -6, 6, 0, 3, 11, -11, 9, 9, 13, -15, -24, -5, -13, -12, 6, -13, 6, 12, -21, 16, -20, -24, 2, 0, -10, -14, -15, -22, -25, -31, 3, -57, -3, -13, 8, -11, -13, 13, 2, -19, 31, -22, -4, 17, -31, 2, -32, 2, 3, 15, 23, -4, 30, -24, 34, 14, -12, -13, -14, -44, 34, 31, 33, 20, -3, -4, 8, -15, 11, -4, 14, -8, 15, 25, 24, -13, -3, -26, 49, -10, -5, -4, -23, 24, 9, 21, 11, -22, -13, -23, -10, -8, 15, 18, -10, -8, -18, -22, 6, -6, 29, -29, 26, -23, -19, 1, 34, -41, 12, 12, -6, -36, -9, 27, -1, -11, 33, -11, 6, -43, 4, -9, -7, -18, -7, -18, -11, 1, -30, 18, -19, 30, 1, -1, 11, -1, -11, -1, -24, 10, 25, 13, 12, -4, -5, 20, -35, 10, 11, -12, 40, -20, 22, -2, 6, -56, -11, -15, 4, -33, -29, 7, 14, 1, -18, 20, -16, -6, -19, -53, 2, -43, -2, -7, -28, 5, 10, 25, 25, -4, 4, -4, -11, 11, -8, 4, -14, 4, 7, 3, -20, -9, -8, 17, -2, -33, -5, 24, 10, 11, 11, 2, 14, -14, 10, -2, -13, -3, 6, -1, 2, -6, 1, -2, 4, 9, -13, 8, 5, -18, -16, -27, -29, 2, -8, 10, -14, 4, 5, -4, -15, -6, 0, -22, -2, 22, -12, 8, -34, 3, 11, 3, 12, -46, 3, -18, 6, -2, -8, 3, 10, -1, 7, -6, -3, 9, -9, -11, 19, 5, 0, 3, 0, -28, 12, -19, 5, -19, -22, -17, -8, -12, -5, 12, 20, -7, -3, -11, 10, 2, -9, -1, -10, 14, 28, 10, -15, 14, 15, -4, 16, -24, 4, -1, 12, -11, -15, 14, 13, -11, 19, -4, -7, -4, -13, -4, -3, 15, -13, 20, 8, 4, -18, 1, -22, -9, -4, -28, -6, -2, 6, -24, 3, -7, -2, -15, 9, -15, 0, -27, -7, -9, 8, 10, -10, 5, 13, 7, 4, -27, -17, -10, 28, -1, 15, -26, 42, 0, -7, 21, -5, -39, 6, 19, 10, 22, -26, 10, 3, 5, -12, 17, 0, 12, 17, -22, 30, 14, -3, 0, -20, 22, 14, -20, 6, 11, -20, -19, -31, -19, 8, -2, -24, 7, 11, 6, 15, -21, -5, 13, 19, -16, 11, -19, 6, 7, 11, -12, 7, -42, -9, -24, 27, 20, -22, 12, 7, 17, -8, -7, -21, 0, 13, -2, 3, 13, 0, 15, -23, -7, -10, 3, -1, -6, -17, -30, 5, -23, 15, 26, 2, -8, 2, 14, -7, -10, -6, 10, -9, 1, -18, -47, -3, 4, 20, -50, 4, -9, -15, -25, 4, -12, -5, 14, -17, -2, 3, -9, 20, 22, 18, 11, -11, -2, 16, -11, -15, -7, 9, -13, -15, -8, 14, -23, 2, 3, 0, -3, -13, 9, 26, -13, -16, 20, -7, -13, 4, -5, 8, 11, -10, -5, -18, -19, -1, 4, -19, 30, 10, 2, -4, -21, -6, -20, -1, 1, 6, -12, -2, -4, -1, -3, 17, -21, -21, -1, 8, -4, -2, 0, 2, -10, -9, 0, 0, -9, -8, 6, -7, -6, -1, 16, -9, 11, 14, 0, -6, 13, 1, -8, 0, -22, -8, 4, -14, 5, 4, -13, -18, -21, 12, -3, -5, -1, 1, -6, 15, 12, 16, 2, 22, 3, -14, -1, -3, 17, -3, 7, -5, -8, -14, -10, 13, 2, 2, 7, -13, -14, 2, 14, -15, -5, 10, 2, -3, 12, -1, -2, 17, -1, 9, 1, -16, 10, -11, 5, -13, 7, 11, -3, 6, -17, 7, 6, -2, 18, 10, 10, -49, 34, -17, 4, -19, 32, 3, 16, 38, 0, 44, -13, -15, -12, -29, 15, 14, 37, 3, -51, 5, -22, -7, -6, -7, -29, 3, 2, -12, 51, -28, -57, -8, -13, -4, 15, -14, 27, -20, -7, 5, -33, 3, 11, -42, 27, 7, 2, 11, 11, -18, 14, -18, -4, 10, -11, 10, 16, 13, -13, -17, -2, -26, -33, 8, 9, 25, 28, -9, 5, -8, 5, 1, -17, -17, -39, -3, -4, 14, 30, 13, -26, -2, -5, -11, -6, -6, 1, 31, 29, 7, 7, -35, -1, -7, 2, 12, 7, -6, 3, 2, 11, 17, 7, 2, -12, 0, -34, 10, 3, -37, -24, 9, -19, -6, 25, -2, 9, 47, -9, -23, -30, -7, -52, 26, -34, -12, 28, -22, -42, -7, 16, 9, -8, 31, 2, 22, 20, 28, -12, 1, -4, -11, 21, -45, 26, -6, 28, 55, -1, -3, 19, 27, 19, 21, -24, 10, 2, -18, 2, 9, 5, 0, -2, 14, -29, 3, -17, -1, 23, 8, -26, -32, -1, -23, -6, -31, 22, 6, -31, 33, -15, -12, -4, -3, -3, 11, -33, 10, 16, 13, 16, -5, 6, -10, 1, -3, -7, -9, -5, 20, 11, 13, -52, 3, -3, -13, -8, -4, -26, 2, 5, 3, -24, 5, 16, -5, 3, -5, -11, -12, -6, -26, 6, -13, 42, -27, -22, -3, -2, 1, 0, -28, -6, 9, -11, -17, 3, -14, 10, -18, 17, -5, 7, 15, -4, 0, 16, 17, 17, 26, -44, -31, -2, 17, 0, 1, -13, -8, 12, -9, 7, -6, -22, -8, -8, 12, -11, 0, 4, -24, 3, -22, 12, -20, 7, -1, -31, -7, 10, 6, -7, 29, -13, -16, 21, -23, -1, -41, 6, -2, 15, -8, 16, -14, -11, -47, 15, 21, 1, 3, -46, -19, -2, -5, -29, -5, 21, 6, -3, 4, 3, 1, 16, 13, -10, 25, 38, -30, -10, 5, -28, 28, 3, 19, 7, -20, 6, -31, 2, 1, -16, -27, -10, 16, 3, -24, -3, 15, 4, -17, 19, -16, 20, -3, 15, 3, -8, -2, 2, -16, -19, -6, 10, -12, -7, 20, 24, 19, -15, -5, -16, -1, -7, 0, 14, -11, -5, -26, 4, 30, -15, 23, -27, 5, 0, -4, -11, 15, 9, 5, 5, 23, 5, 4, 13, 7, -10, -8, 13, -1, -21, -39, -11, -16, -17, -22, 1, -22, 1, 7, 2, 19, -23, 9, -20, 7, -20, -12, -10, 3, 12, 16, 4, 5, -10, 32, -9, 37, -39, 6, -24, 28, -4, 32, -31, 8, -6, -4, -1, 33, 0, -27, -8, -21, 19, -5, 14, -42, -9, 7, -27, -1, -2, 6, -24, -1, 4, -18, -30, -12, 20, -1, 9, -8, -13, 6, -1, 21, 13, 2, 14, 10, -55, 13, 15, 22, 35, 1, 24, -55, -7, -18, 8, 3, -16, -23, -63, 6, 8, -11, 26, -16, 5, -16, -17, -4, -7, 10, -9, -3, -1, -1, 9, 6, -8, -38, -6, -10, -7, 13, 13, 8, 4, 6, -11, -13, 18, 5, 8, -7, -1, -39, -5, 5, -10, 0, 14, -19, 9, -1, -3, 12, -9, -16, -15, 1, 23, 12, 38, -17, -42, -22, 12, 2, -2, -8, -20, 0, -6, -2, 20, -12, -10, 8, 16, 7, 5, -11, 2, -5, 4, 2, 10, 6, 0, 5, -27, 5, 6, -49, 18, -12, -33, 1, 3, 10, -19, 15, -17, 14, -5, 34, 32, 19, 10, -14, 42, 27, -28, -2, -1, 6, 16, 9, 10, 33, -1, -17, -4, -2, -25, -24, 3, -3, 4, -16, -2, 13, -6, -41, 7, -13, -16, 12, 0, 20, -17, 2, -30, -1, 20, -20, 32, 23, -21, -13, 0, 4, -12, 17, 19, 16, -2, -29, 31, -8, -16, -23, -17, 4, -10, -12, 2, 12, 0, -1, -7, 6, -8, -26, -22, -16, -1, 20, -28, 10, 1, -11, -29, 9, -10, 3, -10, -18, -6, -27, -13, 9, 9, -9, -9, 5, -12, -24, 4, 25, -1, 20, -5, -25, -9, -21, 8, -15, -7, 12, -1, -5, -28, -11, -12, 16, 2, 1, 7, 2, 3, -19, 10, -9, 13, 8, 9, 1, 12, 10, -3, 7, 22, -20, -18, -11, 9, -20, -35, -9, -7, -25, -33, 12, -20, -5, 5, 1, -3, -3, -10, -16, -13, 14, 26, 31, -11, 1, 20, -20, -10, -7, -25, 5, 3, -17, 2, -19, -9, -30, -17, -21, 3, -25, -5, 8, 13, -3, 3, -23, -2, -16, -28, 20, -13, -38, 19, 1, -6, -13, -43, -14, -22, 1, -12, 15, -13, 13, -8, -15, -15, 11, 16, 22, -2, -11, 20, -16, -28, -7, -18, 6, 2, -16, -1, 6, -15, -8, -9, 3, -5, 0, 8, -24, -12, -2, -10, -4, 2, -12, -20, 14, 11, -8, 17, 6, 14, -4, -15, 2, 0, 8, -11, 24, -2, 11, -2, 12, 14, 5, 17, -16, 11, 8, 19, 24, 1, 13, -12, -12, -7, 19, 25, -12, -16, -10, -6, -12, -8, 4, -13, -17, -13, -7, 0, 27, 2, -4, 7, 2, 8, -28, 32, 20, -6, 6, 4, 1, 5, -1, 2, -1, -19, 21, 9, 25, 15, 28, -2, -14, -17, 7, -4, -15, 8, 16, -6, -5, 4, -6, -1, -16, 19, 5, -12, 12, -19, -6, -13, 10, -5, -10, -22, -11, -1, -19, 17, 8, -2, 14, 1, 20, 2, -12, -3, -22, 5, -10, -1, -2, -7, 7, -6, -6, 10, 18, -15, 2, 7, 23, -20, -1, -5, -9, -6, -12, -6, 10, 17, -16, 15, 15, 2, 0, 3, 4, -2, -7, -18, 7, -19, -9, -2, -21, 14, -3, 9, 17, -14, 18, -9, -23, 2, 5, -3, 4, -9, -11, -4, 11, -5, 9, 9, 23, -11, 13, -3, 10, 14, -1, -11, -5, -18, -13, 0, 4, 10, -41, 20, 14, 8, 4, 2, -6, 1, 27, -19, 0, -19, -6, -5, -30, 6, -27, 12, 12, -30, -21, -19, 13, 1, 13, -9, 3, -3, 3, 1, 4, 4, -16, -17, 11, -22, -21, -8, 7, 12, 6, -4, -14, 24, -2, 5, -8, 15, -5, 5, 11, 2, 6, -4, -3, 3, 9, -4, 3, -17, 25, -6, 21, -14, 5, 30, 11, 20, 6, 0, 2, 5, 17, -5, -18, -1, -9, -16, 7, 12, -21, 8, 9, -2, 4, -12, 11, -31, -31, -3, -12, 11, 21, 17, -2, -1, -27, -10, 3, -32, -10, 18, 7, -1, 1, 0, 9, 12, -11, -11, -1, -22, -1, -23, -10, 36, -15, -2, 5, 0, -18, -7, 3, -15, -7, -8, 14, -11, -15, 13, 1, -2, -2, -19, 19, 12, -16, 4, 4, 13, 27, 13, -21, -4, -16, -35, -12, -19, 0, 20, 11, -8, -20, 3, -25, 11, 7, -2, 16, -23, 5, -19, 3, -3, -8, -22, 15, -6, -3, 2, 0, -44, 9, -16, 11, -22, -13, 19, 34, 13, 7, 14, -42, -2, 5, 0, -6, 20, -6, 1, -4, -1, 8, -10, -17, 13, -1, -2, -9, -12, -11, -21, 20, -11, 3, -21, -19, -18, -1, 47, -14, 15, -8, 5, -16, -8, -16, -28, -14, -32, 21, -4, -22, 20, 9, 15, 0, -1, -24, 6, -7, 15, -3, -3, -10, 35, -5, 6, -17, 6, 15, 17, 7, 26, -8, -1, -26, 5, -11, 16, 9, -2, 10, -8, -5, 11, 5, 9, -5, 40, 13, 20, -27, -52, -6, -22, -2, -38, 6, -13, -11, 18, 6, 7, -11, -4, -20, 17, 14, -7, 17, -5, 3, 3, 26, 23, -5, 14, 10, 28, -13, 24, 7, 1, -12, 2, 16, 9, 0, 9, 11, -18, -1, 3, -16, -12, 15, 3, -1, 2, -7, -38, -12, 18, -22, -56, 21, 4, -27, -6, 21}

#define TENSOR_CONV2D_2_FUSED_KERNEL_0_DEC_BITS {7}

#define TENSOR_CONV2D_2_FUSED_BIAS_0 {12, -61, 20, -10, 28, 39, 12, -4, 7, -36, 51, 48, 15, 35, -79, -2, 13, 54, 31, -1, 54, 16, 29, -110, 13, 15, 45, 33, 46, 31, 17, -101}

#define TENSOR_CONV2D_2_FUSED_BIAS_0_DEC_BITS {7}

#define CONV2D_2_FUSED_BIAS_LSHIFT {0}

#define CONV2D_2_FUSED_OUTPUT_RSHIFT {6}

#define TENSOR_CONV2D_3_FUSED_KERNEL_0 {4, 13, -2, 4, 5, 13, 5, 3, -1, 13, -14, -30, 9, -10, -13, 4, 10, 23, 2, 2, -5, 11, -16, -13, 23, 7, 15, -25, 13, 13, -24, -4, -1, -12, 10, 10, 16, 3, 11, 10, -3, -10, -18, -9, -14, 5, 6, -9, -10, -1, -11, 6, 2, -4, 9, -1, -3, 1, 25, -12, 5, 8, -8, 21, 6, 5, 0, 17, 19, 10, 5, 1, 13, -23, 9, -7, -16, -3, 12, 21, 5, -3, 35, -7, -3, -15, 26, 7, 2, -2, 7, 1, -15, 8, -2, 0, 12, 10, 14, 0, -6, 5, 4, 2, -1, 6, -15, -11, 0, -2, -1, -4, 1, 20, -6, 0, 6, 12, -15, -1, 17, 8, 10, -15, 7, 17, -11, -8, 1, -9, 10, -5, 4, -5, 7, 5, 2, -21, -8, 1, -11, 5, 6, -13, -2, 11, -11, 5, -4, 5, 10, 5, -8, 6, 19, -10, 1, -2, -6, 4, 6, 8, 4, 3, 12, 11, -2, -8, 14, -22, 14, 6, -19, -9, -6, 22, 9, -14, 17, -4, 4, 3, 21, 19, -1, 0, 0, -3, -18, 12, -10, -10, 12, 3, 11, 7, -4, 9, 4, -5, -5, 2, -14, -8, -4, -15, -11, -10, 2, 21, -5, 7, 7, 24, -6, -9, 18, -2, 22, -23, 10, 24, -30, -12, 11, -13, 21, 10, 1, -4, 3, -3, 7, -28, -6, 8, -13, 2, 0, -16, -13, 12, -5, 13, -4, 9, 18, -3, -11, 1, 24, -10, 1, 9, -2, -1, 2, -2, 11, 13, 11, 9, 16, -11, 8, -21, 17, -3, -18, -10, -7, 20, 4, -7, 15, -17, 8, 13, 21, 17, -4, -6, 1, -9, -17, 4, -5, -10, 21, 2, 8, 17, -4, -12, -36, -6, -1, 14, 6, 8, 7, -4, -15, 3, 2, 32, 1, -6, -18, 9, -6, 18, 6, -12, 16, 15, -7, -14, 30, -1, 2, 58, 18, 3, -12, 4, -19, -5, -10, -25, 21, -9, -1, -6, -15, -5, -25, 8, -10, -6, 4, 6, 9, 4, -25, 0, 23, 26, 3, 2, 21, -11, 17, 39, 14, 5, 16, -9, -8, 2, 23, 2, -4, -7, 9, 4, -33, -3, -32, -10, -52, 13, 18, 17, 24, 8, -10, -48, 22, -20, 33, 5, 21, 10, 9, -3, 6, 12, 0, -9, -35, -10, -5, 12, 3, -5, 10, -2, 5, -3, 12, 22, -8, 11, -10, -7, -15, 9, 1, -6, 4, 15, 2, 4, 20, 0, 4, 42, 4, 4, -7, 6, -14, -1, 12, -3, 1, -22, 2, 2, -7, 4, -4, 5, -12, 6, 1, -13, -7, 9, -12, 0, 14, 18, 8, 3, 10, -3, -1, 29, 7, 20, 9, -12, -2, -5, 18, 0, -3, -11, 13, 9, -23, 15, -20, -20, -33, 29, 9, 14, 20, 8, -6, -42, 21, -21, 10, 6, 16, 9, 11, -9, 15, 8, -4, -16, -27, -5, -18, 8, -6, -6, 9, -1, -11, -1, 10, 31, -7, 9, -15, 8, -6, 8, -5, -3, -4, 11, 6, 4, 30, 1, 13, 56, 10, 0, 2, 11, -27, 5, -10, -5, 8, -14, -1, -10, -3, 6, -13, 22, -21, 5, -2, -10, 1, 21, -17, -13, 19, 31, 5, 6, 13, -9, -2, 40, 23, 9, 10, -9, 5, -2, 23, -5, 4, -7, -1, -3, -25, 18, -35, -7, -59, 18, 22, 14, -5, 30, -18, -51, 18, -20, 1, -6, 9, -8, -5, -8, 6, -16, 20, 40, -25, 24, 9, -15, 6, 34, -3, 14, -5, -20, 13, -19, 10, 53, -16, -8, 2, -21, -16, 3, -25, 18, 8, 38, -11, 15, 2, -1, -2, -18, -31, -2, -12, 1, -31, -9, 4, -1, 23, 25, -8, 3, 10, 3, 24, -5, 1, -17, -8, -4, -7, -12, 9, -3, 16, 5, 2, 4, 7, 18, 6, -7, -13, 26, -24, -15, -20, -28, 24, -7, -16, 16, -5, 9, 18, 3, 16, 0, 2, 6, -5, -21, -17, 25, -25, 8, 19, -4, 19, -5, 4, -7, 5, -15, 22, 39, -22, 22, 9, -11, 14, 24, 5, 8, -8, 6, 9, -24, 14, 32, -4, -4, 2, -14, -1, 7, -15, 20, 0, 13, -7, 8, 17, 1, -6, -15, -23, 17, -10, -7, -20, -12, -2, 7, 8, 16, -6, 7, 7, 2, 23, -8, 11, -15, -2, 2, 0, -10, 4, -6, 3, -8, 12, 0, 0, 8, 6, -11, -21, 30, -31, -19, -5, -30, 12, -4, -14, 4, -8, 22, 15, 0, 0, 2, 7, 7, -12, -36, -6, 22, -1, 3, 18, -9, 5, -16, 10, -9, 4, -12, 21, 40, -22, 28, 25, -7, -12, 24, 6, -2, -13, 10, -2, -13, 3, 13, -4, -25, 9, -26, 4, -3, -24, -1, 8, 26, -2, 18, 22, 8, -14, -29, -33, 23, -10, -15, -39, -18, 0, 15, 15, 16, -7, 15, 11, -14, 33, -2, -8, -16, 12, -2, 1, 2, 12, 2, 20, -13, 2, 10, -1, 3, 5, -25, -25, 37, -29, -15, -6, -25, 1, 5, -25, 15, -6, 14, 17, 5, 6, 15, 4, 11, -6, -48, -1, 20, -15, 11, 13, -11, 11, -23, 0, 22, -20, -7, 18, 18, 8, 9, 17, 5, -7, -7, 11, 13, -28, 5, -13, -34, 7, -11, -7, -6, -14, -17, 17, 25, 30, -23, 18, 22, 4, 12, -6, -37, -10, -34, -3, 6, 18, -8, 16, 12, -11, 32, 5, 14, -4, -4, -6, -26, 37, 3, 12, -11, 21, -14, 4, 5, 7, -9, 3, -1, -3, 13, 3, -22, -4, -23, -7, 4, -17, 12, -3, 2, 8, 25, 9, 16, -5, 0, -2, 14, 24, -9, 13, 4, -2, -28, -15, 25, -2, 4, -9, -5, -20, -4, -5, 19, -14, -14, 4, 15, 24, 6, -1, -8, -9, -5, -5, 24, -8, 2, -20, -31, 0, 1, 3, 1, -3, -10, 5, 21, 17, -16, 13, 18, 9, 2, -11, -29, -8, -16, -2, -2, 15, -12, 7, 11, -10, 15, 0, 2, -4, -5, 1, -24, 13, -4, 4, 1, 13, 4, 5, -4, 10, -6, 19, 3, 2, 3, 11, -12, -15, 1, -3, -12, 2, 10, -3, 9, 9, 25, 10, 16, -13, -6, 8, 4, 26, -18, 2, 7, 10, -34, -14, 12, -11, 17, -13, -2, -3, 9, -10, 25, 4, -12, 9, 15, 31, -12, -9, -6, -17, -8, -11, 30, -2, -2, -13, -32, 14, -5, -10, -9, -19, -24, 10, 36, 22, -16, 16, 11, 6, 4, -9, -39, -11, -10, -4, -4, 23, -12, 16, 20, -14, 12, 5, 18, -1, -2, 8, -22, 17, 11, 19, -7, 8, -4, 3, -4, 9, -16, 19, 7, 15, 11, 2, -14, -12, 1, 7, 5, 4, 13, -7, 4, 10, 28, 12, 7, -2, 4, 11, -4, 38, -26, 4, 5, 15, -42, -8, 9, 5, 28, -20, -5, -7, 16, -3, -1, 6, 14, -18, -12, -1, 5, 28, -13, 26, 16, 16, -25, 2, -18, 5, 23, -4, 4, 11, -11, -1, 2, 25, -3, 7, 9, -6, -13, -42, 25, 2, -3, -14, -16, -11, 1, -4, 15, -6, -10, 20, 9, 6, -22, 20, 2, -7, 15, -19, -3, -14, -13, 4, 7, -15, 2, 31, 29, -3, 4, -6, -19, -8, -5, -6, 8, -6, -2, 13, 6, 8, 0, 3, 11, 12, -14, 16, -30, -1, -6, 0, 3, -28, -16, -8, -1, 22, 18, -13, -1, -22, -2, 1, 22, 3, 4, 11, 15, -13, -15, 1, 9, 16, -10, 21, 9, 19, -23, 1, -16, 7, 16, -15, 3, 6, 3, 8, 4, 9, -8, 0, 15, 0, -23, -23, 17, 4, -3, -8, -10, -3, -9, -5, 20, -10, -11, 17, 6, -5, -5, 9, 6, -11, 5, -14, -8, -7, 0, -4, -7, -16, 3, 17, 19, -3, 5, -9, -15, -2, -3, -1, -3, -6, -1, 7, 9, 7, 14, 10, 11, 7, -1, 3, -11, -3, -4, -13, 1, -36, -17, -14, 9, 9, 6, -17, -10, -10, -11, 3, 26, -11, 12, 3, 22, -15, -11, -4, 16, 9, -18, 20, 9, 9, -20, -5, -9, 19, 20, -19, 7, 7, 4, 10, 14, 18, -2, -1, 18, -2, -24, -14, 23, 14, 2, -9, 5, -9, -11, -8, 17, -18, -10, 19, 4, -1, -15, 18, 4, -11, 7, -7, -5, -20, 11, -1, -1, -10, -2, 23, 10, -12, -2, 1, -10, 8, 5, -6, 14, -2, -4, 1, 14, -3, 1, 8, 4, 8, -1, 3, -11, 2, -11, -24, 2, -40, -17, -33, 10, 25, 10, 4, -21, -11, -6, 7, 9, -15, -26, -8, 2, 18, -15, -2, 14, 12, -7, 28, 19, -17, -5, 20, -44, 0, 8, 1, 6, 36, -3, 4, 7, -16, 30, -43, 49, 11, -14, 8, 29, 9, -5, 12, 2, -4, -8, -3, 3, -28, -19, -15, -1, 4, 2, 5, 3, 4, 1, -2, 18, 12, -12, 17, 26, -9, -14, 13, -14, 16, 10, 2, -44, 14, -29, 10, -39, -16, 17, -45, 9, 2, 0, -7, 8, 2, -4, -13, 15, -4, 18, -6, 3, -4, -13, 2, 1, -19, -1, 3, -3, -4, -11, -5, -5, 8, -18, 10, 8, 9, -20, -4, 18, 11, -5, 7, 9, -7, -11, 15, -36, 4, 5, 18, 10, 19, -10, -3, 0, -8, 24, -27, 35, 6, -2, 0, 18, 12, 5, 7, 8, -2, -16, -10, 1, -22, -20, -15, -2, 1, 8, 1, -3, 7, 0, 2, 5, 16, -10, 3, 8, -11, -15, 12, -11, -3, 17, -5, -32, 8, -17, 1, -10, 13, 24, -41, 0, -5, 8, -14, 9, -5, -11, -2, 7, -19, 7, -9, 1, -13, -19, -5, 7, -11, 1, 3, 6, -4, -6, -5, 3, 0, -9, -3, 6, 20, -25, -15, 24, 23, -9, 18, 14, 9, -16, 16, -39, -1, 7, 18, 3, 18, -22, 0, -9, 6, 22, -16, 29, 7, -11, -17, 15, 20, 0, -5, 3, -22, -15, -17, -5, -13, -22, -4, 2, -14, 3, 1, 7, 1, 0, 13, 19, 8, -27, -3, 13, -3, -10, 19, -17, -5, -3, -14, -31, 20, -13, -14, -20, -21, 36, -47, -16, 6, 7, -6, 17, -8, -21, -6, 19, -16, 16, 17, 7, -23, -27, 2, 0, -23, -8, 1, 18, -5, -18, -16, -23, 19, 3, -6, -9, 11, -11, 19, 17, 1, 4, -10, -18, -16, 4, -12, 33, 7, 0, 19, 33, -7, 8, 17, -18, 8, 20, 10, -11, 3, 17, -1, -5, 2, -25, -9, -21, 9, 1, -3, 20, 12, 6, 11, 13, 34, 9, -25, -11, -4, -3, -8, 21, -17, -6, 1, -12, -22, 2, -20, -3, -17, 20, 3, -17, 8, 0, 3, -42, 5, -16, -16, 9, -15, 24, 5, 2, -6, 14, 22, -13, 19, 23, 7, 11, -3, -5, 8, 8, -8, 6, 24, 13, -8, 14, -9, -43, 11, 13, 8, -8, 15, -8, 9, 6, 9, -3, 0, 0, -17, 3, -12, 16, -9, -2, 8, 21, -2, 1, 8, -11, 7, 9, 10, -18, 0, 17, -4, -11, -1, -35, 3, -9, 8, 2, -14, 6, -3, -4, -5, 17, 29, 5, -11, -15, -13, -6, -5, 22, -19, 1, -3, 9, -5, 5, -12, 6, 1, 17, 0, -17, 10, 0, 0, -30, 6, -11, -23, -2, -8, 26, 12, 3, -2, 12, 16, -8, 4, 20, -4, 10, -11, -11, 4, 1, 10, 11, 16, 15, -15, 18, -16, -26, 24, 8, 3, -4, 10, -18, 4, -13, 9, 13, -4, -12, -20, 4, -6, 16, -4, -15, 26, 14, -11, -7, 8, -3, -6, 13, 6, -18, -6, 15, -1, -12, 1, -42, -6, -14, 4, 6, -9, 3, -2, -3, -3, 19, 46, 13, -14, -21, -10, -9, 6, 24, -16, -10, -12, 2, -7, 8, -26, 14, 0, 14, -4, -17, 23, -11, -11, -30, 7, 4, -18, -3, -22, 22, 18, 7, 3, 5, 4, -21, -6, 26, 3, 2, -21, -4, 3, 23, 6, 9, 16, 15, -23, 10, -15, -34, 14, -6, -8, -4, -9, -25, 15, 10, 13, 17, -17, 0, -10, 3, 15, 27, -3, 14, -1, 17, -10, -5, -1, -9, 20, -5, -6, 27, 0, 8, -4, -11, -7, 1, 6, 0, 5, 3, 3, 12, 3, 34, 0, 20, -4, 12, -16, 4, -15, -17, -18, 42, -5, -15, -3, 3, -9, -9, -31, 15, -10, 7, -13, -14, 7, -5, 4, -26, 9, 10, 6, 3, -6, 40, -21, -26, -11, 18, 16, 3, 6, 10, -55, 21, -10, -15, 3, 32, 8, -14, -12, 14, 16, -5, -5, -18, 15, -6, 0, -7, -2, -23, 9, 14, 2, 3, -4, -1, -13, -11, 14, 17, -4, 14, -2, 15, 1, 2, -8, -7, 16, 1, -2, 2, 9, 11, -15, -16, -3, -5, -2, 7, 4, 0, 8, 8, -2, 19, 1, 9, 5, 7, -17, -2, -6, -14, -13, 35, -8, -11, -5, 3, -5, -16, -19, 11, -2, 3, -13, -11, 2, -16, 1, -18, -7, 3, 0, -3, -7, 26, -2, -26, -12, 8, 7, 0, -3, 4, -36, 19, -9, -22, -6, 20, 12, -11, -7, -6, -2, 7, -2, -18, 17, -10, 5, -7, -18, -36, 19, 21, -2, -3, -8, 4, -14, -18, 8, 26, 2, 21, 4, 15, -4, 3, 9, -3, -2, -5, -10, 10, 2, 15, -7, -16, 3, 12, 16, 7, 0, -4, 14, 9, -9, 22, -2, -9, 10, -5, -14, -3, -16, -9, -16, 42, 0, -8, -9, -1, -9, -16, -32, 26, 1, 1, -19, -15, 18, -18, 22, -22, -10, -6, 13, 3, -9, 21, -8, -32, -9, 7, 12, 7, -7, 8, -56, 19, -14, -10, -9, 22, 15, -15, 3, 1, 7, 3, 6, -21, -11, 2, -6, 21, -8, 15, 11, -5, 2, -8, -13, 1, -6, 0, 11, -1, 5, 10, -17, 10, -16, 20, 31, -13, 34, 11, -26, 4, 8, 1, -11, -4, -23, 8, 5, 9, 26, 9, 12, 1, 2, -6, 21, 3, 6, 16, 0, -1, -10, 14, -3, -4, -14, 14, -8, -6, 15, -17, 16, 37, 16, 12, -4, -28, 9, -2, 6, -4, 11, 13, -5, 0, 0, 8, 12, 12, 19, -2, 19, -17, 1, -1, -43, 7, -19, -19, 7, 17, -12, 11, 11, 11, -20, 19, -11, -68, -7, 11, 3, 14, -6, 12, 6, -3, -2, -14, -12, 8, -10, 9, 3, -4, -5, 9, -11, 14, 2, 24, 14, -7, 18, 12, -16, 2, 4, -9, 2, -14, -11, -9, 0, 6, 12, 9, 2, 2, 5, 1, 9, -2, 6, 17, -5, 3, -12, 4, 0, 6, -3, 14, 0, -9, 11, -10, 18, 31, 4, 13, -1, -32, 4, -16, 10, -12, 11, 3, -9, -6, -3, 24, 10, 1, 18, -1, 10, 0, 1, -8, -13, -1, -19, -19, 11, 5, -3, 9, 5, 16, -19, -2, -13, -57, 5, 13, -4, 16, -5, 14, -1, -2, 6, -9, -20, 4, -16, 11, 11, -1, 4, 20, -18, 8, -4, 21, 27, 0, 27, 14, -24, 3, 11, -15, 7, -1, -12, -9, 16, 23, 7, 7, 6, 5, 1, 1, -7, 3, 5, 0, -2, 0, -2, 15, -4, 6, -12, 11, -3, 4, 8, -21, 0, 36, 12, 7, 9, -27, 5, -9, -1, -15, 3, 3, -17, -1, -18, 20, 10, -8, 28, 7, 8, -2, 2, -8, -34, 9, -28, -11, -9, 15, -7, -4, 0, 33, -9, -9, -22, -57, 28, -9, -12, -14, 17, -19, 19, 17, 18, 8, -2, -33, -8, 7, -3, 25, -12, -7, 1, 21, -18, -11, 5, 16, -3, 26, 26, -6, 0, 3, -19, -6, 7, -9, 0, 1, -5, -1, 16, 16, 1, 7, -4, -9, -44, -6, -11, -6, 4, -1, 2, 26, 17, -2, 19, 33, -3, 15, 21, -27, 5, -1, 10, 5, -1, 2, -20, 9, 23, -3, -6, 15, 19, -3, -30, 6, -25, 9, -10, 10, 7, 11, 37, -16, 29, -8, 4, 8, -17, -8, 5, -1, -15, -14, 4, 14, 12, -9, 6, -14, 7, -22, 27, 10, 11, -2, -7, -22, -1, 3, 8, 14, -11, -8, 22, 3, -3, -17, 7, 7, -10, 11, 28, -10, 8, 6, -8, -6, 16, -7, -2, -1, -12, -8, 19, 10, 6, 1, -18, 5, -35, -14, -2, -18, 5, -2, 10, 23, 3, -4, 13, 29, -10, 11, 18, -21, 4, 2, 16, -4, 4, -4, -7, 8, 12, -6, 2, 11, 14, -1, 1, 3, -22, 8, -15, 3, 4, 1, 14, -9, 16, 7, 5, 9, -12, -4, 13, -5, -9, -1, -8, -12, 9, 1, 8, -6, 11, -15, 42, 5, 14, 4, 3, -24, 12, -3, -6, 17, -22, -13, 31, 7, 0, -21, 10, 13, -7, 4, 31, -8, 14, 3, -7, -14, 23, -21, 7, 5, -8, -27, 34, 5, 2, 9, -16, -7, -35, -22, -15, -9, -7, -7, 16, 25, 19, 0, 15, 36, -6, 30, 22, -18, 10, 4, 2, 5, 3, -25, 2, -3, 16, -2, 20, 1, 26, -10, -2, -2, -30, 0, -27, -9, 11, 10, 16, -11, 30, 4, 8, 12, -14, 0, 8, 6, 2, -10, -1, 13, -19, -4, 4, 18, -9, -25, 9, -11, 8, -16, 1, 3, 0, 7, 10, 12, -8, -17, -11, 11, -10, 5, 0, 2, -17, 3, 11, -1, -2, -4, 0, 9, 2, -31, 4, 36, 16, -2, 14, 18, 36, -1, -9, -10, -13, 3, -2, -2, 1, 0, -7, 28, -3, 15, -11, -34, -5, 11, 10, 18, 24, 0, -17, -2, -21, -22, -9, 30, -32, 4, 6, 26, -11, -2, -11, -11, 32, -13, -19, 0, 11, 4, -39, 17, -26, 14, -13, -24, 3, 24, -21, -2, 30, 10, -10, -1, -18, 2, 2, 16, -13, -11, 3, -13, 4, -6, -11, -4, 9, 11, 17, 13, -10, -22, -8, 12, 0, 4, 7, -2, -12, 3, 8, -3, 1, -4, 7, 0, -8, -19, 4, 37, 14, 1, -4, 11, 28, -4, -9, 4, -7, -3, 1, 5, -18, -2, 10, 14, -7, 24, -3, -29, 1, 16, 14, -3, 8, 0, -13, -3, -13, -12, -6, 9, -30, -11, 2, 21, 0, 6, -6, -5, 27, -16, -9, 9, -11, -2, -23, 13, -10, 5, -10, 5, 12, 20, -3, -10, 23, 2, -1, 7, -27, 12, 4, 14, -15, -9, 18, -12, 17, -8, -36, 15, -1, 5, 19, 15, 1, -14, -5, 3, 0, 10, 1, 15, -32, 1, 6, 20, 6, -21, 11, 15, -27, -20, -2, 36, 20, -4, -5, 15, 45, 3, -17, 16, 1, 6, -12, 1, -5, -8, -11, 16, -17, 28, -17, -20, 5, 10, -7, -10, 15, 1, -7, -12, -10, 1, 8, 16, -17, -11, -4, 12, -6, 11, -21, -3, 35, -5, -5, 9, -1, -9, -40, 20, -25, 22, -7, -16, 15, 28, 4, -20, 28, 7, 5, 0, 25, 0, 19, 2, -8, -19, -11, 2, 3, 19, 3, -9, -12, -7, -8, 7, 11, 16, 1, -27, -27, 14, -27, 29, 15, 34, 20, -3, -10, -9, -9, 4, 3, -4, 7, 1, 28, 6, 30, 0, -8, 5, -22, -1, -23, -6, 20, -5, -1, 10, -8, -25, -6, 4, 9, 30, 7, 15, 18, -4, -17, 3, 27, -1, 8, -5, -16, 30, 14, -7, 26, 2, 27, -8, 9, -14, -7, 9, -10, 4, -18, -8, -12, 3, -10, 3, 17, 28, 2, 4, 40, -14, -24, -1, 2, 12, 15, -11, 12, -2, -13, -17, -1, 6, -6, 8, 2, -5, -3, -7, 7, 1, 14, 6, -9, -34, -2, 0, -12, 19, 15, 31, 15, -4, -14, -5, -6, -7, -3, -2, 8, -9, 25, -1, 30, -7, -18, 3, -7, 2, -13, -16, 18, -9, -2, 6, -2, -38, 5, -1, 14, 40, 10, 12, 17, -7, -26, 3, 13, -10, 6, -9, -4, 13, 14, -6, 28, -6, 14, 1, -1, 3, -2, 0, -8, 2, -22, -12, -11, 4, -16, 7, 18, 25, 10, 6, 29, -9, -23, -5, -2, 12, 11, 4, 17, 3, -19, -25, -1, 3, -24, 3, 20, -6, -6, -10, -15, -8, 3, 22, 4, -38, 2, 2, -17, 26, 15, 27, 22, -6, -35, -7, 8, -4, 10, 0, 7, -14, 20, -13, 39, -19, -26, 14, -18, -2, -14, -34, 9, -20, 0, 14, 4, -35, -4, -4, -1, 37, 18, 12, 20, -2, -21, -4, 13, -12, 19, -19, -5, 19, 20, -1, 53, -8, 15, -8, -10, -19, 11, -12, -8, -8, -28, -9, -8, -8, -1, 14, 31, 39, 10, 1, 19, 0, -26, -20, -20, 17, 13, 1, -13, -4, -25, 12, 0, 25, -6, -2, -1, 10, -7, 10, 16, -2, 28, 1, -8, 15, 33, -28, -6, -1, 4, -9, -10, 16, -4, 12, 9, -17, 11, 11, 16, -13, -31, 0, 10, 10, -8, 3, 16, 18, 21, 14, -21, -4, 35, -2, -8, -10, 10, -18, -22, -1, 2, 13, -21, 4, 2, -18, 16, 18, 0, -18, 1, -15, -19, -2, 41, -4, -12, -18, -37, 18, -25, 15, 12, 3, -5, -15, -54, 13, 3, -20, -16, -10, 0, -12, -18, 7, 24, 9, 8, -1, 8, 4, -9, 5, -12, 3, -7, 26, -3, -6, 5, 10, 0, 17, 11, 6, 23, -4, -2, 20, 9, -27, -12, -11, 4, -10, -4, 4, -3, 1, 4, -7, 9, 5, 8, -4, -15, -14, 4, 15, -14, -3, 12, 12, 4, 17, -13, 5, 30, 9, -1, 1, 3, -11, -28, 8, -7, 9, -24, 2, 0, -18, 24, 21, 6, -11, 0, -14, 0, -11, 21, 2, -1, -20, -27, 3, -23, 18, 18, -14, -2, -1, -28, -3, 6, -20, -14, -13, -10, 3, -15, 3, 21, 2, 4, 1, 18, 7, -22, -4, -4, -12, -6, 40, -9, -2, -4, 27, 13, 30, 16, 7, 28, -16, -8, 37, 26, -23, -6, -17, -2, -19, -10, 9, -1, 3, 10, -13, 10, 13, 12, -8, -11, -16, 7, 37, -16, -4, 9, 3, 6, 23, -15, 16, 26, 7, 4, 7, 11, -12, -17, 9, -3, 15, -21, 6, -1, -27, 27, 14, 15, -13, -1, -16, -13, -35, 16, 14, -12, -18, -16, -11, -26, 27, 20, -1, -12, -5, -33, 2, -6, -40, -12, -19, -7, -1, 4, 6, 23, -3, 18, -33, 2, 17, 34, -26, 17, 20, -10, 26, -32, 0, 12, 7, 6, -25, 20, -29, 19, 6, -8, 0, 5, -13, 18, 8, -5, -4, -17, 60, -4, -4, -6, -1, -5, 13, -5, -2, -8, -2, -15, -15, 7, -15, -4, 16, -14, 0, 15, -26, 30, 13, 30, -12, 10, -3, 1, -11, -19, -11, -15, 6, 24, -17, 1, -8, 13, 22, -11, 24, 22, 14, -58, -27, 17, -11, -8, -1, -17, -11, 20, -6, 7, -25, 4, -2, 6, 4, -12, -12, -2, -5, -11, -1, -7, -42, 16, 1, 8, 8, 25, -13, 7, 12, 0, 25, -27, 2, 11, 14, 13, -20, 10, -32, 17, 5, -1, 10, 13, 5, 17, -7, 5, -7, -16, 41, 13, -15, -2, 6, -4, 2, 11, -11, 1, -10, -12, -7, 1, -11, 11, 10, -11, -2, 14, -15, 16, 8, 16, -8, 22, 0, 2, -9, -17, -15, -12, 0, -1, -7, -10, -3, 21, 18, -11, 9, 22, 11, -41, -18, -2, -16, -7, 18, -23, -10, 3, -16, 8, -13, -6, -17, 1, 14, -6, -12, -4, -11, -13, -14, -31, -36, 15, -6, 6, 10, 28, -21, -3, 8, -10, 31, -23, 10, 9, 10, 11, -17, -1, -40, 11, 19, -8, 8, 15, 3, 28, 8, 1, -16, -40, 49, -3, -3, 3, -9, -4, 22, 2, -7, -10, -11, -18, -18, -17, -19, 21, 1, -8, -10, 0, -24, 24, 11, 19, 10, 16, -4, 1, 9, -10, -19, -13, -1, 3, 0, -19, -8, 19, 5, -1, 18, 24, 2, -59, -13, 11, -22, -11, 23, -19, -15, 5, -15, 15, -9, -24, -12, 3, 17, -21, -12, 2, -15, -12, -4, -28, -41, 15, 4, 25, 13, -19, -3, -1, 20, -5, 1, 17, -3, -15, -20, 7, -4, -15, -6, 10, 4, 6, 1, 22, -3, -12, -10, 30, -3, 6, -14, 7, 8, -17, -4, 12, 4, -2, -5, -11, -10, 11, 22, -15, 5, -6, -20, 9, -3, 20, -8, -16, 10, -9, -27, -13, -1, 5, 13, -3, 9, 10, -13, 3, 1, 4, 14, 6, 5, -5, -5, -8, -12, 24, 0, -3, -15, -1, 2, -31, 0, 10, 18, 17, -1, 12, -15, 8, -16, 4, -6, -4, 7, 17, -20, -1, 7, -1, -6, 21, 11, -9, -2, -3, 12, -8, -1, 10, -2, -13, -23, -3, 1, -2, -4, 5, 3, 2, -6, 10, 0, -3, -2, 8, -5, -4, -6, -5, 10, -6, -4, 13, -2, 5, -13, -17, -14, -2, 16, -7, -3, -14, -13, 11, 11, 4, -17, 1, 17, -5, -24, -8, 0, 6, 16, -12, 5, 8, -19, 1, -3, 4, 8, 1, 6, 4, -11, 4, -19, 6, 0, -1, -20, 9, 8, -16, 2, 1, 3, 12, -3, 24, -4, 1, -18, 11, -15, 0, 1, 4, -18, -4, 8, -2, -2, 25, 9, -10, 4, 2, 16, -13, 0, 11, 4, -3, -19, 2, 0, -6, -3, 15, 16, 7, 7, 7, 11, -1, -7, 28, -2, -1, -12, -4, 5, -36, 0, 9, 4, 1, -4, -21, -16, 2, 13, -12, -13, -12, -9, 7, 8, 11, -15, 2, 24, 7, -18, -20, 3, 16, 5, 2, 10, 18, -37, -3, -3, 9, 14, 0, 2, -10, 4, 8, -22, 6, -4, -16, -20, 18, 6, -5, -4, -8, 2, 20, -4, 27, 3, -11, -10, 20, -4, 1, -2, 2, -29, 13, 14, -1, 10, -7, -1, 13, 1, 14, 20, -6, -6, -2, -14, -6, -10, -5, 2, -5, 10, 3, 11, -8, -12, 19, -12, -5, 4, 3, -11, 3, 0, -2, 15, -17, -12, 1, 15, -7, 23, 13, 11, 6, 3, -7, -9, 17, -7, 21, -6, 0, -3, -5, 0, -2, -31, -8, 3, 2, -7, 11, -5, 7, -27, 23, -14, 22, 26, -6, -15, 3, 37, -18, 15, 12, -33, -2, 0, 5, -17, -7, -22, -3, 24, 42, 1, -10, -5, 0, -12, 0, -16, 5, 13, -6, -33, 13, 11, 32, 22, -5, 5, 10, 1, 5, 17, -9, -18, 7, -12, 2, -7, -7, -1, -2, 12, -3, 22, -6, -12, 16, -14, 9, 0, -5, -2, -2, 6, -1, 20, -20, -11, -13, 23, -3, 5, -2, 11, 6, -2, 0, -4, 12, -10, 22, -9, -6, -4, -7, 1, -4, -9, 0, -5, -2, -17, -2, -4, 8, -22, 6, -12, 16, 18, -12, 1, 4, 9, -17, 20, 14, -23, -8, -11, 3, -5, -3, -19, -11, 8, 19, 4, -8, 0, 3, -6, -3, -17, -3, 18, -8, -28, 7, 2, 22, 8, -13, 5, 14, 6, 5, 10, -2, -17, -16, -5, -1, -12, -2, -4, 3, 18, -4, 9, -14, -25, 32, -9, 10, 11, -7, -10, 10, 8, 3, 17, -10, -7, -12, 29, -7, 19, -6, 7, 9, -8, -5, -8, 7, -2, 23, -9, 5, -1, 4, 0, -2, -10, -9, -7, 8, -17, 4, -10, 15, -24, 4, -4, 21, 28, -4, 6, -5, 27, -14, 4, 5, -36, -20, -4, -4, -14, -2, -14, -21, 19, 42, -4, -7, -2, -5, -19, 11, -12, 4, 11, -15, -29, 7, 9, 30, 5, 0, -21, -18, 5, 4, -4, 37, 6, 19, -12, 15, 9, 16, -14, 23, 1, -6, 15, -14, -27, 20, -9, 14, 10, 12, 4, -20, 2, 5, -17, 11, 8, -3, -7, -6, 30, 12, -3, 25, -3, 1, 5, -17, -2, 2, -9, 7, 8, -8, -9, -13, 1, 0, -1, 7, 19, 24, -18, 1, -11, 19, 8, 8, -23, 16, -23, -7, -10, -24, 0, 26, -8, -12, 16, 0, 6, 10, -12, 5, -1, -8, -10, 5, -7, 1, -24, 8, 8, 8, -13, -18, -10, 12, 11, -12, -20, 5, -23, -12, 4, 3, -12, 15, 2, 4, -6, 7, 1, 12, -13, 10, 4, -12, 5, 4, -20, 21, -1, 10, 3, 9, -6, -11, 10, 7, -9, 18, 3, 5, -11, 0, 18, 9, -10, 3, -2, -9, 6, 3, -1, 10, -6, 11, 13, -8, -11, -7, 5, 13, -2, -5, 4, 15, -24, 0, -1, 7, 8, 1, -13, 6, -18, -9, -7, -12, 3, 5, -10, -14, 13, 2, 5, 20, -7, 4, -11, -2, 2, 16, -8, 7, -26, 1, -6, 9, -14, -13, 5, 0, 7, -13, -29, -8, -8, -16, 16, 2, -9, 27, 9, 8, -8, 5, -3, 22, -18, 23, 0, -17, 9, -4, -29, 26, -5, 13, 6, 22, -2, -12, 10, 1, -9, 1, 12, -2, 0, 7, 34, 1, -9, 12, 4, -7, 18, -8, -5, 8, -14, 26, 13, -4, -2, 2, -3, 9, -8, -7, 19, 25, -22, -8, 9, 18, 2, -6, -12, 11, -1, -6, 2, -22, 11, 28, -7, -21, 17, -13, -7, 17, -16, 11, -17, -7, 10, 18, 2, -3, -25, 5, -1, 14, -20, -8, 5, -8, 4, -11, -16, -32, 19, 34, -8, 4, -5, 4, -27, -8, 6, -18, 8, -4, 0, 1, -4, 15, 12, -6, 11, -14, 25, 13, -7, -2, -24, -2, -9, 5, 27, -19, -8, -27, 9, -12, 12, 4, 19, -8, -9, 20, -2, 15, -10, 13, -1, 17, -2, 1, 7, 1, 17, -6, 12, 6, -16, -31, -5, 25, 37, -42, 30, -19, 34, -23, 23, 2, 18, 12, -14, 7, 1, 21, 6, 17, -35, 32, -2, 18, 7, -6, -3, -27, -9, -3, 10, 4, 10, -8, 3, -1, 7, -34, -8, 25, 43, -18, 3, 27, -20, 13, 6, -3, -14, -17, -5, -16, 7, 5, -8, -2, 1, 2, 13, -22, 21, -1, 20, -1, -10, -9, -12, 7, 5, 4, 16, -12, 0, -24, 2, -16, 0, 19, 16, -7, 0, 10, 0, 10, -11, 6, 12, 12, -9, -6, 3, 4, 17, -15, 9, -5, -1, -12, 6, 13, 27, -27, 27, -19, 15, -15, 13, 7, 6, 7, -14, 3, 14, 12, -4, 29, -19, 17, -3, -8, 10, 2, -3, -33, 11, -6, 16, -8, 28, -1, 4, 1, 17, -40, -8, 23, 34, -28, 12, 48, -22, 24, -10, -3, -21, -27, 1, -9, 22, -6, -3, 15, 4, 3, 7, -26, 16, 0, 33, 15, -19, 0, -14, 6, -3, -15, 21, -24, -6, -27, 6, -19, -18, 27, 16, -7, -9, 14, 5, 18, 6, 2, 7, 22, -9, 1, 3, 6, 19, -13, 12, -10, -2, -29, 23, 1, 37, -42, 21, -14, 21, -8, 25, 19, -9, 23, -16, 5, 4, 19, 0, 39, -8, 23, 5, 4, -15, -1, -3, -44, 11, 9, 20, -19, 24, -13, 10, -11, 18, -56, -16, 17, 34, -2, 12, 4, -18, -8, -29, 7, 5, 6, -3, 13, -11, 27, -5, 3, 14, -25, 15, -12, 9, 7, 5, -3, 0, -3, 1, 10, 17, 3, -8, 2, 20, 6, -8, 16, 26, -7, -11, 5, 6, 23, -23, 19, 32, -28, 9, -17, -7, 18, -14, 6, 35, 11, -6, 7, -23, 8, 6, 26, 0, 23, -23, -8, -19, 24, 10, -10, 6, 13, -5, 1, 15, -15, -22, -13, 3, -21, -2, -8, -2, -7, -8, 2, 2, -11, 17, 8, -1, -1, -5, -17, 22, 15, -1, 6, -5, 1, 3, 9, -13, -7, -19, 2, 7, -1, 0, 4, -2, 18, 4, 2, 11, -10, 6, -13, 11, 4, 10, 2, -4, -7, -7, 8, 13, 5, -7, 5, 14, 13, 0, 7, 19, -4, -8, -1, 11, 8, -23, 12, 33, -21, 2, -10, -9, 1, -15, 15, 27, 3, -1, -2, -16, 6, 15, 13, -10, 17, -13, -5, -16, 15, 10, -8, 4, 9, -6, -12, 18, -13, -6, -2, 11, -9, -1, -1, 2, -12, -8, 9, -3, 1, 21, 6, -1, 8, -4, -14, 3, 1, -2, -1, -2, -4, 10, 10, -7, -2, -16, 28, 6, -2, 16, 10, 9, 15, -9, 4, 16, -10, 13, -15, 20, 0, -2, 1, 14, -14, -9, 5, 21, 5, -7, 8, 14, 10, 6, 9, 35, -9, -20, 18, 10, 9, -26, 17, 23, -34, -4, -18, -6, 3, -15, 6, 28, 4, 3, 13, -8, 6, 12, 13, 5, 15, -11, -15, -18, 19, 4, 11, 12, 16, -3, 0, 14, -13, -11, -11, -2, -2, -4, 5, 1, -6, -11, -9, 0, -7, 24, 19, 6, 12, 9, -11, -3, 21, -16, -5, 7, -16, 2, 15, 2, 1, 30, 3, -23, 24, -14, -10, 11, 20, 4, -7, 10, -15, -26, -10, 2, -15, 5, -7, -4, 14, -15, 10, -17, 18, 7, 15, -1, -25, -19, -26, -28, -9, 22, 18, -10, 3, 4, 23, 24, 12, 23, -8, 2, -5, -12, 24, -25, 1, -5, 8, -22, -7, -7, -4, 26, -11, 7, 4, 17, 6, -7, -24, -4, -4, 1, 1, -12, 12, 8, 22, 17, 17, 34, -12, 7, 22, -7, 2, -2, 2, -6, -23, -35, -7, 19, -42, 13, 20, -6, 7, -31, -17, 4, 9, 4, 4, 35, 4, -9, 11, -7, 0, 15, 6, 0, -3, 12, -9, -21, 6, 15, -19, 3, 1, -6, 12, -17, 3, -15, 12, 4, 3, 0, -26, -25, -20, -13, 1, 24, 14, 4, 6, 6, 18, 24, 12, 14, 1, 17, -2, -4, 7, -19, 9, -3, 5, -22, -1, -4, -13, 33, -9, -1, 3, 8, 18, 5, -15, 1, -3, -8, 11, -4, 6, 7, 9, 16, 21, 29, -3, 4, 19, -1, -9, -20, 1, -7, -15, -30, -6, 15, -30, 3, 4, -15, 10, -24, -24, 10, 10, 8, 6, 28, 12, -24, 19, 1, -19, 10, -8, 14, -6, 16, -2, -29, -12, 19, 8, -2, 11, -2, 22, -22, 2, -6, 21, 0, -10, 7, -35, -25, -26, -15, 1, 25, 26, 2, 2, -3, 12, 22, 7, 9, 9, 15, 9, -6, 13, -14, 19, -17, 15, -18, -4, 6, -14, 44, -6, -1, -1, 13, 2, 0, -19, -4, 6, -12, 3, -5, -7, 11, 24, 11, 37, 33, 8, 10, 18, -8, -10, -21, -8, -5, -10, -29, -10, 11, -32, 2, -2, -3, 12, -16, 4, 15, -13, 14, -21, 9, 3, -3, -8, 12, 2, -16, 9, 18, -5, 4, 1, -8, -1, -17, 7, 2, 1, 9, 20, -2, 10, -23, -7, -22, 17, -11, 1, 2, 5, 3, 1, 3, 7, -5, 2, -10, 5, -15, 6, -7, 11, 3, 10, 19, -8, -24, -7, 0, -9, 11, 14, 11, 19, -18, 11, -5, 32, 1, -4, -2, -6, -4, -21, -5, 10, -9, 5, -24, -3, -3, -4, -15, 1, 5, 2, -12, 0, -20, -13, -17, -10, -2, -2, 8, 39, -16, 8, 21, 6, -24, 7, 7, -9, 15, -20, 5, 2, -5, 4, 1, -6, -7, -2, 22, 5, -4, 5, -10, 2, -6, 1, 0, 2, 12, 10, -1, -1, -13, -11, -8, 14, -11, -9, 6, 3, 1, -3, -1, 3, -2, 6, -11, 2, -13, 1, -4, 12, -5, 13, 19, -3, -17, 8, -2, -13, 4, 1, 13, 19, -13, 1, -13, 22, 1, -8, -1, -5, -4, -15, -8, 1, -3, -2, -8, -3, 5, -3, -8, -2, 1, 4, -6, 3, -22, -4, -10, -11, -2, -2, 12, 15, -18, 4, 14, 2, -17, 9, 16, -11, 16, -25, 10, 8, -3, -4, 3, -10, -17, 5, 26, 0, -4, 4, -6, 4, -16, 3, 4, -3, 9, 14, -1, 7, -17, -13, -18, 15, -6, -2, 8, -16, 13, -2, -2, 7, -1, -2, -21, 3, -16, 8, 2, 14, 3, 24, 17, -6, -38, 2, 5, -11, 12, 7, 3, 27, -6, 2, -17, 25, -8, 2, -4, -13, 9, -9, -10, 9, 3, -8, -7, -11, -1, -1, -5, 5, 7, 18, -9, 5, -20, -13, -9, -22, -9, 2, 13, 13, -27, 2, 13, -2, -16, 11, -2, -26, 3, 14, -10, -23, 15, -9, -3, 10, 5, 3, 9, 5, 11, -9, -11, 3, -22, 15, 0, 4, 23, -24, -4, 30, -7, -9, 2, 8, 8, 22, 8, 13, 24, 20, 14, -16, 1, 8, -15, -15, -5, 14, -19, -16, 0, -8, -13, -3, -11, 7, 12, -3, 1, -2, 5, -29, -41, -16, -3, 5, 0, -5, -13, 13, 33, -23, -30, 1, -1, -33, -23, -14, -8, -8, -11, -37, -2, 16, 3, 21, 18, 20, 35, 21, -18, 2, 2, -6, 1, 28, 11, 24, 51, -10, 2, -11, 4, 4, -8, -20, 4, 5, -7, 7, -2, -3, 10, 0, 12, -6, -1, -1, -16, 9, -3, -2, 14, -21, -10, 28, -1, -9, 2, 19, 0, 16, 18, 5, 23, 6, 16, -12, 4, 9, -5, -16, -5, 15, -5, -18, 10, -7, -7, 10, 7, 4, 6, 0, -11, -12, 0, -16, -32, -2, -8, 8, 4, 2, 9, -2, 25, -25, -6, 15, 1, -19, -4, -22, -3, -8, -10, -11, -4, 2, 4, 18, 18, 18, 22, 13, -22, 6, 9, 5, -11, 12, 12, 22, 31, -6, -7, -2, 4, -4, -15, -16, 8, -13, -3, 1, -1, 15, 13, 4, 16, -1, 1, 11, -16, 11, -15, -26, 13, -34, -2, 31, 12, -21, 7, 24, 14, 14, 26, 0, 13, 5, 6, -15, 0, 15, -9, -11, -13, 9, -9, -30, 15, -10, -1, 13, 14, 4, 6, 9, -13, -9, 4, -8, -27, -5, -14, 4, 0, 1, 6, 0, 23, -31, 6, 13, 8, -13, -13, -24, 2, -16, -14, -23, 3, -8, -10, 33, 37, 11, 24, 25, -27, 14, -12, 8, -14, 19, 3, 30, 39, 16, 27, -17, -27, 0, 18, -1, 3, -34, 20, 21, -5, 36, -2, -10, 8, 1, -8, 11, -34, -1, -1, 3, 0, -6, -2, 2, 4, -4, -19, 45, -10, 4, -7, -14, -17, 0, -9, -5, -13, -6, -4, -12, 7, 10, -6, 2, -21, 25, 5, -6, -21, 7, 14, -15, 19, -19, -11, -36, 33, 9, -8, 30, -8, -6, 11, -15, 0, 2, -6, 6, -7, 10, -23, -14, 23, 16, -11, -11, -22, 4, -3, 22, -14, -4, 13, -25, 10, -10, -16, 17, 36, -17, 9, 12, -10, 6, 13, -24, -14, 6, 7, -4, 12, -22, 16, 4, 1, 25, 11, -2, -17, 2, -2, 9, -14, 11, -2, -1, 5, -5, 4, -2, 6, -3, -4, 40, -2, 12, -11, -20, -11, 9, -11, -8, 1, 0, -3, -8, 3, 19, 5, 5, -29, 19, 7, 2, -17, 3, 5, -3, 12, -19, -3, -38, 12, 3, -7, 20, 5, 6, 9, -13, 6, 18, 1, 5, -4, 1, -16, -8, 34, 18, 5, 6, -18, 7, -2, 22, -6, -10, 17, -20, -28, -10, -18, 0, 26, -15, 10, 4, 5, 3, 16, -32, -16, -3, 17, -4, 32, -26, 20, 18, -12, 31, 21, 3, -26, -12, -5, 8, -25, 12, -21, -14, 5, -8, -17, 0, 7, -7, -13, 27, -10, 5, 0, -39, -2, 12, -3, -24, 11, -5, 1, 11, -7, 28, 20, 14, -34, 32, 11, 2, -19, 18, 7, -3, 11, -10, -17, -31, 14, 6, -19, 4, -11, 9, 10, -9, 7, 30, -2, 5, 9, 13, 6, -32, 21, 7, 9, 21, -19, 7, -6, 34, -13, -9, 12, -18, -6, -6, -23, 1, 27, -9, 8, -6, 4, 3, -15, 4, -16, 32, 5, 2, 10, 2, -2, 10, -22, 14, -9, -11, 24, -14, -2, -12, 8, -9, -5, 16, 13, 20, 24, -11, 13, -7, 26, 2, 7, 17, 22, -4, -17, 8, 9, 2, -29, -2, 2, -3, -8, -16, 5, -20, 1, 7, -21, 18, 10, -8, -10, 18, 21, 4, -13, 33, 8, -2, 2, -9, -14, 5, 9, -15, -9, 14, -5, 3, -1, -9, -11, 16, 12, 5, -3, -5, -21, 2, 18, 29, -28, 22, 7, 24, 11, -14, -15, 7, 8, -11, -17, 28, -7, 12, -22, 18, -17, 21, 1, 1, 5, -6, -5, 8, -3, 2, -8, -7, 14, -27, 6, 8, 3, -2, -4, 1, 7, 19, 16, 1, 10, 6, 19, 6, 0, 14, 25, -7, 2, 5, 5, 9, -9, 6, 11, 5, -6, -9, 4, -7, -5, -2, -19, 25, 23, -2, -26, 1, 32, 0, 0, 31, 1, 1, 3, -2, -15, 5, 5, -10, -10, 5, 7, -1, -7, -12, -9, 10, 3, 4, -8, 1, -23, -3, 7, 21, -12, 19, 11, 8, 5, -8, -10, 25, 4, -11, -13, 23, -22, 19, -23, 20, -8, 19, -5, 23, 5, 1, -7, 14, -4, 8, -17, -6, 15, -22, 7, 21, -24, -7, -2, 19, 8, 17, 28, 1, 11, 6, 17, 4, 3, 21, 22, -8, 5, -8, 3, 18, -23, 4, 6, 14, -3, -19, -5, -20, 2, 1, -11, 23, 11, -12, -25, 21, 39, 0, 4, 19, 2, 15, 7, -7, -27, 7, 13, 0, -19, 13, 15, -19, -14, 0, -6, 17, 8, 0, -26, 3, -26, 0, 22, 42, -8, 30, 13, 7, 14, -15, -16, 15, 9, -11, -28, 12, 2, -13, -4, 10, 35, -1, 2, -35, -15, 0, -9, -1, 24, 8, 21, -10, -18, 18, -36, 3, 43, -17, 22, -14, 4, -27, -8, 18, -27, 16, -10, 6, 4, 0, -3, 5, 4, -21, 10, -28, 6, 15, -31, 22, -5, 0, 15, 10, 4, -1, 14, -28, 24, 4, 23, -9, -23, -16, 15, -21, 3, 10, -8, -7, 10, -20, 18, 24, -6, -31, -12, -45, 6, 24, -47, 6, -22, 21, 17, -27, 12, -14, -28, -22, 34, 1, 8, -16, -15, -1, 3, 11, 12, 7, -12, -8, 10, -7, -2, 3, 16, 5, 15, -17, -13, -7, 10, -10, 12, 17, 26, 3, -7, 17, -22, 3, 36, -12, 9, -16, 11, -15, 9, 3, -28, 9, -8, 8, 0, -2, -1, 4, 9, -8, 29, -32, 1, 8, -20, 8, 1, 1, 15, -5, 6, -4, 7, -10, 15, 1, 8, -1, -20, -8, 9, -9, -12, 7, -3, 0, 10, -18, 25, 12, 0, -21, 0, -28, 12, 20, -20, 8, -8, 5, 19, -7, 11, 12, -10, -16, 11, 4, 6, -10, -1, -3, 7, -1, -7, 18, -4, -3, 13, -15, 11, 1, 19, -12, 0, -33, -24, -7, 5, -9, 24, -2, 29, -6, -22, 23, -39, 1, 51, -23, 7, -16, 2, -32, 4, -8, -25, 15, -24, -10, 0, 1, 6, 3, -3, -12, 3, -43, -6, 21, 2, 14, 0, 8, 18, 2, 1, 2, 11, -10, 17, -7, 19, -6, -23, -7, 9, -18, -18, 15, -4, -5, 12, -20, 4, 18, -10, -16, -7, -48, 11, 38, -16, 3, 10, 2, 33, -19, 16, 0, -5, -26, 18, -2, 8, -19, -19, -7, 18, 22, -23, 13, -12, -15, -16, -37, 24, 18, 9, 5, 12, 4, 9, -19, -1, -18, -17, -4, 24, -2, -8, 11, -26, -13, 4, -4, 12, 14, -4, -3, 8, -15, 3, 13, -4, -8, -20, -27, -13, -2, -16, 37, -1, 30, -4, 28, -6, 4, 0, -2, -6, 24, -14, 18, -3, 4, 12, 0, -3, -1, -6, 8, 8, 23, -21, 18, 4, 4, 27, -1, -16, -7, 3, 3, 13, -12, -1, 8, -12, 12, -10, 8, -5, -6, 23, 27, 5, 14, 7, -20, -16, -24, -14, -2, 26, -4, -31, 12, -10, -7, 7, -23, 14, 12, 4, 3, 11, 4, -1, -8, -8, -8, -13, -18, 8, 7, -3, -1, -7, -6, 6, 6, 8, 7, 10, -9, 11, -5, 1, -1, 7, -20, -11, -24, -8, 5, -10, 17, 0, 15, -12, 16, -13, 15, 6, -17, -5, 15, 0, 8, 0, -8, 5, 1, -2, 2, -3, 9, 7, 14, -13, 16, -2, 14, 6, 8, -11, -1, 5, 3, 0, -2, -7, 12, -18, 11, -2, 15, -18, -14, 9, 12, 16, 18, -4, -11, -13, -17, -12, 2, 25, -1, -28, 4, 5, 2, 5, -25, 25, 25, 4, 1, 11, 4, -11, -26, 0, -15, -25, -20, 8, 5, -4, 3, -2, -17, 9, 8, 10, 10, 6, -6, 7, -6, 8, -5, -6, -24, -34, -14, -11, 9, -7, 20, -8, 29, -21, 30, -15, 5, 5, -12, 6, 20, 2, 15, 3, -9, 3, -2, 7, -2, 9, 9, 12, 15, -21, 21, -5, -5, 6, 9, -15, 14, 9, 5, -2, 4, -5, 7, 4, -3, 5, 18, -18, 0, 14, 32, 13, 29, -9, -14, -2, -18, -4, -6, 25, -7, -35, 5, 3, -1, -1, 14, -13, -11, 15, -5, -3, -4, -24, 6, 4, -9, 7, 28, 26, -5, 15, 8, -33, -6, 0, 4, -8, -32, 24, 4, -13, 14, -18, -1, 9, 32, -2, 10, 9, 15, -10, -9, 13, 7, 5, 13, 15, 1, -19, 7, 23, -12, 31, 1, -7, -8, -16, -6, 15, -14, 7, -8, 18, -27, 11, -24, -19, 13, 7, -5, -13, 9, 7, -23, -8, 17, 1, 6, 11, -7, -17, -12, 12, -33, -5, 8, -1, -43, -6, 21, -1, -5, -24, 3, 5, -20, -5, 37, 17, 18, -12, -4, -11, -16, 15, -10, -10, -6, -11, 4, 7, -3, 4, 24, 21, -1, 14, 3, -26, -9, 3, -3, -4, -26, 11, -9, -6, 5, -23, -3, 4, 17, -3, -2, 16, 13, -2, -6, 9, -6, 13, 5, 26, -11, -22, 15, 17, -6, 27, -3, 2, -8, -24, -19, 1, -8, 5, -4, 12, -18, 17, -17, -12, 18, 1, -10, 0, -3, 0, -22, -8, 11, 0, 17, 12, -15, -6, -10, 0, -19, 1, 4, 6, -47, -17, 0, -9, -13, -7, -2, 12, -20, 2, 28, 12, 12, -19, 3, -11, -1, 12, -15, -12, 1, -25, 26, 8, -15, 13, 15, 31, -1, 26, 13, -19, -9, 3, 6, -4, -31, 2, -6, -12, 7, -23, -4, -14, 19, 16, 3, 12, 11, 4, 2, 19, -1, -2, 2, 40, -10, -19, 13, 24, 1, 34, 14, 0, 5, -22, -28, 5, -21, 6, 0, 7, -19, 23, -19, -16, 21, 13, -21, 0, 2, -6, -14, -14, 15, -6, 12, 15, -18, -7, -8, 7, -18, 5, 8, 0, -37, -10, 10, -10, 1, -13, 4, 17, -9, 5, 23, 6, 4, -10, -18, -6, -12, 7, 11, 3, 12, -15, 2, 9, 18, 17, 35, 6, 11, -27, -30, 20, -6, 4, 10, 4, 17, 1, -2, -6, -34, 8, 18, -18, 35, 0, 10, -3, 2, 14, 20, 1, -17, -18, 9, -4, -4, -15, -14, -25, 12, -5, -15, 4, -30, 31, 15, 14, -15, 29, 5, -22, -25, -5, 38, 0, -6, -12, 6, 3, -1, 10, 1, 37, 6, -18, 14, 2, -29, 6, 5, 30, -11, 18, -12, -2, 12, 17, -2, -3, 5, 3, -3, -30, 0, -8, 11, 26, -2, -15, -15, -6, -10, 17, 3, 7, 8, -9, -4, 24, 4, 6, 36, -3, 10, -22, -14, 11, 0, 5, 19, 5, 5, -5, -2, -4, -27, 2, 6, -16, 31, -3, 7, -4, 4, 13, 18, -6, -12, 4, -9, -8, 12, -16, -9, -11, 17, 3, -10, 2, -21, 32, 8, 0, -16, 13, 5, -18, -33, -15, 28, 1, -6, 0, -4, 1, -3, 15, 6, 29, -2, -24, 3, 3, -23, 5, 25, 28, -7, 33, -12, -3, 15, 18, 3, 4, -9, -6, -10, -36, 6, -23, -4, 17, 6, -16, -37, 3, -15, 11, 7, 7, 8, 4, -13, 27, -2, 8, 43, -3, 6, -22, -15, 28, -12, 6, 21, 5, 9, -7, 5, -18, -34, -11, 8, -13, 28, -10, 6, 1, -3, 16, 10, 4, -5, -3, -8, -3, 7, -28, -19, -1, 25, 3, -1, 10, -21, 24, 18, 1, -13, 22, 9, -20, -34, -19, 37, -1, -14, 6, -2, 7, -3, 13, 6, 46, 6, -11, 18, 4, -26, -2, 14, 40, -13, 26, -17, 5, 21, 22, 19, -9, -10, 4, -15, -31, 13, -15, 11, 19, 2, -10, 8, 4, -11, 6, 10, -17, -27, 22, -29, -15, 7, -1, -24, -21, 3, 3, 19, 10, 8, 11, -2, 2, 8, -38, -6, 16, 20, 0, 0, 31, -1, 10, -2, -2, 4, 9, 14, 5, -17, 14, -8, -25, 11, 9, 28, 0, -8, -11, 3, -1, 11, 6, -9, -7, -1, -29, -14, 4, 2, -18, -22, 13, -2, 8, -31, -4, 31, -3, -25, -21, -6, -6, -31, -40, 4, -5, -4, -32, 44, -15, 33, 18, 15, 24, -13, 20, 12, -24, 3, 14, 2, -3, 3, -5, 8, 1, 12, 11, -15, 0, 9, -14, -17, 24, -19, -5, -2, 0, -20, -4, 15, 7, 4, 10, -3, -2, -11, -4, 7, -24, 0, 7, 24, -1, -12, 27, -1, 3, 8, 2, 1, 11, -3, 8, -10, 8, -10, -19, -1, 6, 22, 1, -16, -9, -7, 9, 8, 7, -3, -5, 14, -21, -10, 9, 12, -23, -14, 6, 5, 7, -12, 14, 15, -9, -17, -5, 9, -11, -15, -32, 5, -3, -10, -28, 28, -6, 15, 15, -6, 25, -2, 16, 18, -15, 1, 11, 20, 9, 7, -21, 14, 16, 19, 5, -8, -9, 7, -23, -19, 13, -27, 1, -9, 4, -23, -10, 12, 14, 15, 1, 15, 1, -12, -11, 4, -20, -8, -7, 25, 4, -20, 43, -9, -3, 4, -3, -5, -5, 0, 13, -23, 6, 2, -24, -10, 10, 40, 13, -15, -22, -2, 6, 8, 10, -11, 3, 13, -36, 2, 15, 11, -18, -28, 8, 1, -4, -9, 21, 10, -8, -9, 1, -4, -2, -23, -29, 3, 9, -9, -16, 25, -1, 19, -1, 10, 36, 2, 27, 34, -22, 13, -4, 14, 32, 1, -23, 17, 20, 4, 7, -15, -16, -16, 15, 0, 20, -32, 11, -16, -20, -17, 16, 15, 6, 0, 11, 2, -26, 4, -9, 6, -22, -3, 16, -3, 8, -1, 14, 12, -12, 4, -9, 5, 0, 35, 4, 13, -4, 10, 28, -6, 7, 14, 5, -25, -12, 18, -12, 4, -10, 11, -2, -5, 15, 16, -9, -25, -18, -25, -6, 8, 4, -3, -39, -3, -9, -14, -14, 49, -10, -26, 17, -13, -22, 0, 41, -7, -4, -5, 19, -3, 5, 3, 0, -3, -1, 7, 2, 16, -10, 13, 3, 13, -4, 8, 2, -12, -16, -10, -2, -6, 8, -18, 8, -20, -13, -22, 10, 20, 7, 2, 20, 13, -27, 6, -6, -6, -13, 9, 13, -5, 4, -7, 17, 8, -13, 13, -7, 4, 7, 21, 4, 23, -4, 10, 17, 0, 7, 14, -5, -19, -14, 18, -3, 3, -8, 3, 0, 3, 17, 14, -6, -3, 3, -13, -5, -2, -3, 2, -22, -9, -9, -2, -3, 39, -1, -14, 6, -12, -18, 0, 14, 13, -18, -6, 11, -1, 7, 4, 0, -6, 1, 2, 8, 16, -7, -3, -1, 20, 6, 2, -1, -11, -12, 1, -2, -1, 12, -42, -3, -21, -1, -25, 7, 30, 11, -1, 24, 10, -35, 14, -12, -12, -2, 9, -2, 5, 18, -26, 19, 12, -13, 3, -10, 18, 6, 17, 6, 26, -15, 21, 25, -5, -12, 25, -11, -22, -10, 7, 1, -4, -18, 17, 8, -10, 13, 18, -5, -9, -3, -11, -6, -17, 0, 6, -23, -1, -1, -1, 8, 58, -2, -14, 20, -23, -17, 0, 17, 13, -9, -8, 8, 16, 11, 15, -1, 5, 14, -5, 14, 17, 9, -8, 4, 10, 0, 10, 5, -51, -4, 8, 1, 27, 0, -4, 26, 8, -8, 44, 16, -4, -2, -25, -24, 17, 28, 13, 4, -5, -29, -1, -10, 15, -30, 0, 4, -11, 16, 11, -18, -13, 5, -31, 5, -4, 34, 3, -8, -30, 9, -8, 5, -12, -12, -26, -1, 0, 23, 28, 11, 7, -1, -2, -3, -22, -8, 15, -6, -13, -11, 1, 31, 6, -5, -7, 6, -20, 32, 6, -10, -9, 25, -26, 3, 5, 4, -21, 0, 52, -2, 19, -2, -20, -31, 0, -22, 20, 13, 9, 26, -20, -3, 11, -1, -30, -6, 4, -3, 19, 7, -10, 20, -5, -6, 43, 19, -6, -7, -29, -33, 10, 30, 8, -12, -5, -19, 0, -7, 15, -14, 12, -1, -1, 11, 10, -15, -11, -3, -26, 3, -5, 16, 8, -19, -30, 15, -12, -1, -9, -9, -3, -4, -18, 32, 15, -1, 17, -2, -6, -3, -16, -16, 25, -4, -3, 0, -4, 29, 2, -12, -6, 1, -19, 22, -3, -2, 0, 6, -18, 14, -5, 23, -7, 3, 26, -4, 4, 6, -15, -35, 1, -17, -1, 14, 13, 31, -18, 10, 3, -2, -50, -13, -1, -23, 25, 22, 4, 17, -3, -22, 46, 13, -19, -7, -45, -25, 16, 42, -5, -18, -20, -16, -15, -14, 17, -15, 21, 10, 0, 16, 1, -28, -16, -2, -13, 5, 0, 18, 4, -22, -10, -3, 1, 7, -13, -13, -11, -6, -19, 14, 20, -6, 8, -19, -12, 3, -9, -7, 23, -9, -3, -5, -11, 30, 1, -18, 4, 0, -32, 33, 4, 6, -8, 1, -24, 22, -3, 23, -8, 1, 46, -8, 22, 2, -19, -29, 3, -16, -4, 13, 15, 10, -11, 2, 7, -20, -24, -8, 24, -10, 3, -22, 18, -15, 30, -12, 32, -4, -17, 1, -20, -26, 11, -10, 6, -1, 4, 8, -19, 0, 10, -1, 16, -4, -15, 16, 14, 5, -27, 9, -32, -6, -17, 11, -20, -2, 2, -16, 12, -6, 3, 17, -12, 0, -11, -2, -31, 3, 38, 20, -16, -1, 1, 8, 2, 28, 4, 15, -12, -4, -17, 4, -3, -6, -29, 12, 12, -8, 4, 22, 1, -16, -17, 3, 21, -9, 28, -11, 7, -11, 6, -6, 10, 9, -29, 28, -15, 9, 2, 41, 8, -10, -13, -3, 16, -9, 6, -14, 10, -5, 5, -19, 34, 1, -10, 10, -12, -30, 17, -3, 11, -6, -1, 17, -15, -1, 9, 0, 19, -11, -9, 13, -6, 10, -18, 2, -20, -11, -21, 4, 1, -5, -2, -12, 19, -3, 19, 5, -11, -2, -6, -5, -21, 11, 6, 15, -23, 2, 7, -4, -15, 17, 2, 9, -14, -4, -5, -18, 0, 0, -35, -2, -3, -13, -2, 7, 4, -14, -2, 5, 17, 2, 25, -7, 8, -13, 9, -17, 9, 5, -13, 28, -4, 11, 6, 43, 11, -8, -9, -4, 20, -11, -6, -14, 45, -12, 15, -10, 41, -1, -27, 7, -8, -17, 14, -23, 8, -12, -6, 12, -16, -1, 12, -8, 33, -17, -13, 17, -3, 18, -39, 6, -5, -23, -25, -1, 7, -10, 3, -17, 11, -1, 25, -7, -12, -8, 6, -17, -12, 6, 7, 10, -22, 10, 3, -1, 9, 18, -15, 20, -16, 0, -30, -15, 2, -16, -40, 3, -7, -9, 32, 8, 7, -24, -19, 11, 23, 1, 38, -12, 8, -7, 23, -4, 7, 8, -25, 24, -7, 11, 16, 43}

#define TENSOR_CONV2D_3_FUSED_KERNEL_0_DEC_BITS {4}

#define TENSOR_CONV2D_3_FUSED_BIAS_0 {-54, -27, -68, -84, 29, 41, -39, 75, -57, -27, 26, 17, -30, 13, 13, 15, -17, 5, -99, -70, 40, -17, 8, -81, 34, 1, 37, -35, -31, 9, 29, 62}

#define TENSOR_CONV2D_3_FUSED_BIAS_0_DEC_BITS {4}

#define CONV2D_3_FUSED_BIAS_LSHIFT {1}

#define CONV2D_3_FUSED_OUTPUT_RSHIFT {7}

#define TENSOR_DENSE_KERNEL_0 {-34, -46, 28, 22, 20, -32, 24, 62, 6, 8, -28, -24, 5, -47, -37, -17, -49, 24, -46, -56, 37, -18, 45, 6, 45, -26, -13, -20, 28, -2, 10, -20, -68, -62, -68, 36, -42, 37, -78, 24, -22, 15, -35, -30, 12, -68, -28, -87, 23, 30, -17, -37, 36, 37, 59, -1, 47, 25, -19, 11, 4, 11, -51, 41, 40, -39, -31, 54, -52, -14, -53, -31, -23, -16, -23, 19, 15, -13, -39, 34, -37, -22, 49, -19, -27, 18, -11, -32, 23, 49, 17, -15, -20, -17, -36, -39, -23, 33, -36, 27, 27, 15, -14, 7, -69, -22, 60, -40, -65, 30, -19, -8, -16, 1, 39, 8, 38, 26, 44, -43, 21, -62, 6, 20, -73, -7, 37, -21, 12, -37, 20, -26, -55, 7, 35, -1, -46, 23, 48, 4, -25, 12, 19, 1, -13, -44, 34, 3, 50, 19, 6, -31, -50, -20, -22, 6, 64, 44, -19, -45, 6, -17, 2, 24, 38, 20, 16, -32, 2, -41, -46, -9, -55, -20, 1, 42, -56, -20, -5, -56, -46, -20, -22, -22, 24, -27, 5, -58, -36, 57, -21, -66, -57, -42, 20, -61, 57, -33, -19, -48, 44, 39, 39, -4, -17, 46, 19, -34, -43, -6, -2, 31, -25, -6, -49, 15, -11, 22, 36, -55, 16, -65, 27, -46, -9, 60, -65, 33, -10, 49, 12, -72, 23, -33, -77, 38, -64, 60, -23, 39, 43, -8, 6, 47, -6, -44, -39, -5, -20, -1, 13, 0, -76, -34, 55, -52, -34, 20, -49, 38, 53, -14, -49, -44, 5, 25, -35, 35, 6, -40, -36, 20, 35, -34, 11, 15, 24, 15, -58, -15, -44, -36, 53, -22, -37, -67, -13, -41, 44, 13, 19, -55, 29, -19, 5, -3, -21, 23, -8, 21, 16, -37, 10, 26, 32, 14, -19, 30, -47, 36, 36, 24, 14, -54, -21, -51, -30, -15, 30, 21, 29, -33, 23, -21, 42, 15, 30, -14, -6, -52, 20, 29, -50, -19, -49, -5, 8, 16, 5, -16, 30, 42, -41, 29, -59, -57, -8, 43, 2, 19, -4, -32, -55, -5, 14, 38, -59, -53, -48, 8, -24, -29, 13, -16, -5, 8, 19, -54, -55, -55, -22, 0, -13, 31, 21, -29, 12, 0, -32, -28, -19, 31, -21, -32, 24, -44, -3, -60, -24, 10, -15, -8, -54, 30, 31, -13, -74, 48, 35, -54, -40, -19, -37, 22, -42, -28, 37, 15, -37, 9, -22, -57, 11, -9, 40, 44, -31, -30, -1, -24, -2, -9, 49, -14, 33, 49, 19, 38, 31, 12, -13, 23, -29, -74, 17, -4, 18, -7, -35, -26, -34, -5, 36, -25, -66, 14, -31, 7, 10, -30, -29, 0, 17, -31, 23, -4, 18, -38, 39, -46, -73, 33, -21, -44, -13, 45, -50, 62, -18, -44, -38, -31, -16, 7, 8, 42, 15, 38, -14, 28, -39, -21, 33, -13, -17, -21, -3, -7, 40, -4, -4, -3, 9, -24, -15, -18, -48, -29, -39, -11, 15, 39, 33, -59, 11, -2, 28, 39, 32, 28, -46, -35}

#define TENSOR_DENSE_KERNEL_0_DEC_BITS {5}

#define TENSOR_DENSE_BIAS_0 {8, 1, 3, 3, -2, 8, 8, 6, -8, -5, -9, 2, -12, 3, 2, 4}

#define TENSOR_DENSE_BIAS_0_DEC_BITS {5}

#define DENSE_BIAS_LSHIFT {0}

#define DENSE_OUTPUT_RSHIFT {-4}


/* output q format for each layer */
#define INPUT_OUTPUT_DEC 2
#define INPUT_OUTPUT_OFFSET 0
#define CONV2D_1_FUSED_OUTPUT_DEC 0
#define CONV2D_1_FUSED_OUTPUT_OFFSET 0
#define MAX_POOLING_1_OUTPUT_DEC 0
#define MAX_POOLING_1_OUTPUT_OFFSET 0
#define CONV2D_2_FUSED_OUTPUT_DEC 1
#define CONV2D_2_FUSED_OUTPUT_OFFSET 0
#define MAX_POOLING_2_OUTPUT_DEC 1
#define MAX_POOLING_2_OUTPUT_OFFSET 0
#define CONV2D_3_FUSED_OUTPUT_DEC -2
#define CONV2D_3_FUSED_OUTPUT_OFFSET 0
#define GLOBAL_AVERAGE_POOLING_OUTPUT_DEC -2
#define GLOBAL_AVERAGE_POOLING_OUTPUT_OFFSET 0
#define DENSE_OUTPUT_DEC 7
#define DENSE_OUTPUT_OFFSET 0

/* bias shift and output shift for none-weighted layer */

/* tensors and configurations for each layer */
static int8_t nnom_input_data[2376] = {0};

const nnom_shape_data_t tensor_input_dim[] = {198, 12, 1};
const nnom_qformat_param_t tensor_input_dec[] = {2};
const nnom_qformat_param_t tensor_input_offset[] = {0};
const nnom_tensor_t tensor_input = {
    .p_data = (void*)nnom_input_data,
    .dim = (nnom_shape_data_t*)tensor_input_dim,
    .q_dec = (nnom_qformat_param_t*)tensor_input_dec,
    .q_offset = (nnom_qformat_param_t*)tensor_input_offset,
    .qtype = NNOM_QTYPE_PER_TENSOR,
    .num_dim = 3,
    .bitwidth = 8
};

const nnom_io_config_t input_config = {
    .super = {.name = "input"},
    .tensor = (nnom_tensor_t*)&tensor_input
};
const int8_t tensor_conv2d_1_fused_kernel_0_data[] = TENSOR_CONV2D_1_FUSED_KERNEL_0;

const nnom_shape_data_t tensor_conv2d_1_fused_kernel_0_dim[] = {5, 5, 1, 16};
const nnom_qformat_param_t tensor_conv2d_1_fused_kernel_0_dec[] = TENSOR_CONV2D_1_FUSED_KERNEL_0_DEC_BITS;
const nnom_qformat_param_t tensor_conv2d_1_fused_kernel_0_offset[] = {0};
const nnom_tensor_t tensor_conv2d_1_fused_kernel_0 = {
    .p_data = (void*)tensor_conv2d_1_fused_kernel_0_data,
    .dim = (nnom_shape_data_t*)tensor_conv2d_1_fused_kernel_0_dim,
    .q_dec = (nnom_qformat_param_t*)tensor_conv2d_1_fused_kernel_0_dec,
    .q_offset = (nnom_qformat_param_t*)tensor_conv2d_1_fused_kernel_0_offset,
    .qtype = NNOM_QTYPE_PER_TENSOR,
    .num_dim = 4,
    .bitwidth = 8
};
const int8_t tensor_conv2d_1_fused_bias_0_data[] = TENSOR_CONV2D_1_FUSED_BIAS_0;

const nnom_shape_data_t tensor_conv2d_1_fused_bias_0_dim[] = {16};
const nnom_qformat_param_t tensor_conv2d_1_fused_bias_0_dec[] = TENSOR_CONV2D_1_FUSED_BIAS_0_DEC_BITS;
const nnom_qformat_param_t tensor_conv2d_1_fused_bias_0_offset[] = {0};
const nnom_tensor_t tensor_conv2d_1_fused_bias_0 = {
    .p_data = (void*)tensor_conv2d_1_fused_bias_0_data,
    .dim = (nnom_shape_data_t*)tensor_conv2d_1_fused_bias_0_dim,
    .q_dec = (nnom_qformat_param_t*)tensor_conv2d_1_fused_bias_0_dec,
    .q_offset = (nnom_qformat_param_t*)tensor_conv2d_1_fused_bias_0_offset,
    .qtype = NNOM_QTYPE_PER_TENSOR,
    .num_dim = 1,
    .bitwidth = 8
};

const nnom_qformat_param_t conv2d_1_fused_output_shift[] = CONV2D_1_FUSED_OUTPUT_RSHIFT;
const nnom_qformat_param_t conv2d_1_fused_bias_shift[] = CONV2D_1_FUSED_BIAS_LSHIFT;
const nnom_conv2d_config_t conv2d_1_fused_config = {
    .super = {.name = "conv2d_1_fused"},
    .qtype = NNOM_QTYPE_PER_TENSOR,
    .weight = (nnom_tensor_t*)&tensor_conv2d_1_fused_kernel_0,
    .bias = (nnom_tensor_t*)&tensor_conv2d_1_fused_bias_0,
    .output_shift = (nnom_qformat_param_t *)&conv2d_1_fused_output_shift, 
    .bias_shift = (nnom_qformat_param_t *)&conv2d_1_fused_bias_shift, 
    .filter_size = 16,
    .kernel_size = {5, 5},
    .stride_size = {1, 1},
    .padding_size = {0, 0},
    .dilation_size = {1, 1},
    .padding_type = PADDING_VALID
};

const nnom_pool_config_t max_pooling_1_config = {
    .super = {.name = "max_pooling_1"},
    .padding_type = PADDING_VALID,
    .output_shift = 0,
    .kernel_size = {2, 1},
    .stride_size = {2, 1},
    .num_dim = 2
};
const int8_t tensor_conv2d_2_fused_kernel_0_data[] = TENSOR_CONV2D_2_FUSED_KERNEL_0;

const nnom_shape_data_t tensor_conv2d_2_fused_kernel_0_dim[] = {3, 3, 16, 32};
const nnom_qformat_param_t tensor_conv2d_2_fused_kernel_0_dec[] = TENSOR_CONV2D_2_FUSED_KERNEL_0_DEC_BITS;
const nnom_qformat_param_t tensor_conv2d_2_fused_kernel_0_offset[] = {0};
const nnom_tensor_t tensor_conv2d_2_fused_kernel_0 = {
    .p_data = (void*)tensor_conv2d_2_fused_kernel_0_data,
    .dim = (nnom_shape_data_t*)tensor_conv2d_2_fused_kernel_0_dim,
    .q_dec = (nnom_qformat_param_t*)tensor_conv2d_2_fused_kernel_0_dec,
    .q_offset = (nnom_qformat_param_t*)tensor_conv2d_2_fused_kernel_0_offset,
    .qtype = NNOM_QTYPE_PER_TENSOR,
    .num_dim = 4,
    .bitwidth = 8
};
const int8_t tensor_conv2d_2_fused_bias_0_data[] = TENSOR_CONV2D_2_FUSED_BIAS_0;

const nnom_shape_data_t tensor_conv2d_2_fused_bias_0_dim[] = {32};
const nnom_qformat_param_t tensor_conv2d_2_fused_bias_0_dec[] = TENSOR_CONV2D_2_FUSED_BIAS_0_DEC_BITS;
const nnom_qformat_param_t tensor_conv2d_2_fused_bias_0_offset[] = {0};
const nnom_tensor_t tensor_conv2d_2_fused_bias_0 = {
    .p_data = (void*)tensor_conv2d_2_fused_bias_0_data,
    .dim = (nnom_shape_data_t*)tensor_conv2d_2_fused_bias_0_dim,
    .q_dec = (nnom_qformat_param_t*)tensor_conv2d_2_fused_bias_0_dec,
    .q_offset = (nnom_qformat_param_t*)tensor_conv2d_2_fused_bias_0_offset,
    .qtype = NNOM_QTYPE_PER_TENSOR,
    .num_dim = 1,
    .bitwidth = 8
};

const nnom_qformat_param_t conv2d_2_fused_output_shift[] = CONV2D_2_FUSED_OUTPUT_RSHIFT;
const nnom_qformat_param_t conv2d_2_fused_bias_shift[] = CONV2D_2_FUSED_BIAS_LSHIFT;
const nnom_conv2d_config_t conv2d_2_fused_config = {
    .super = {.name = "conv2d_2_fused"},
    .qtype = NNOM_QTYPE_PER_TENSOR,
    .weight = (nnom_tensor_t*)&tensor_conv2d_2_fused_kernel_0,
    .bias = (nnom_tensor_t*)&tensor_conv2d_2_fused_bias_0,
    .output_shift = (nnom_qformat_param_t *)&conv2d_2_fused_output_shift, 
    .bias_shift = (nnom_qformat_param_t *)&conv2d_2_fused_bias_shift, 
    .filter_size = 32,
    .kernel_size = {3, 3},
    .stride_size = {1, 1},
    .padding_size = {0, 0},
    .dilation_size = {1, 1},
    .padding_type = PADDING_VALID
};

const nnom_pool_config_t max_pooling_2_config = {
    .super = {.name = "max_pooling_2"},
    .padding_type = PADDING_VALID,
    .output_shift = 0,
    .kernel_size = {2, 1},
    .stride_size = {2, 1},
    .num_dim = 2
};
const int8_t tensor_conv2d_3_fused_kernel_0_data[] = TENSOR_CONV2D_3_FUSED_KERNEL_0;

const nnom_shape_data_t tensor_conv2d_3_fused_kernel_0_dim[] = {3, 3, 32, 32};
const nnom_qformat_param_t tensor_conv2d_3_fused_kernel_0_dec[] = TENSOR_CONV2D_3_FUSED_KERNEL_0_DEC_BITS;
const nnom_qformat_param_t tensor_conv2d_3_fused_kernel_0_offset[] = {0};
const nnom_tensor_t tensor_conv2d_3_fused_kernel_0 = {
    .p_data = (void*)tensor_conv2d_3_fused_kernel_0_data,
    .dim = (nnom_shape_data_t*)tensor_conv2d_3_fused_kernel_0_dim,
    .q_dec = (nnom_qformat_param_t*)tensor_conv2d_3_fused_kernel_0_dec,
    .q_offset = (nnom_qformat_param_t*)tensor_conv2d_3_fused_kernel_0_offset,
    .qtype = NNOM_QTYPE_PER_TENSOR,
    .num_dim = 4,
    .bitwidth = 8
};
const int8_t tensor_conv2d_3_fused_bias_0_data[] = TENSOR_CONV2D_3_FUSED_BIAS_0;

const nnom_shape_data_t tensor_conv2d_3_fused_bias_0_dim[] = {32};
const nnom_qformat_param_t tensor_conv2d_3_fused_bias_0_dec[] = TENSOR_CONV2D_3_FUSED_BIAS_0_DEC_BITS;
const nnom_qformat_param_t tensor_conv2d_3_fused_bias_0_offset[] = {0};
const nnom_tensor_t tensor_conv2d_3_fused_bias_0 = {
    .p_data = (void*)tensor_conv2d_3_fused_bias_0_data,
    .dim = (nnom_shape_data_t*)tensor_conv2d_3_fused_bias_0_dim,
    .q_dec = (nnom_qformat_param_t*)tensor_conv2d_3_fused_bias_0_dec,
    .q_offset = (nnom_qformat_param_t*)tensor_conv2d_3_fused_bias_0_offset,
    .qtype = NNOM_QTYPE_PER_TENSOR,
    .num_dim = 1,
    .bitwidth = 8
};

const nnom_qformat_param_t conv2d_3_fused_output_shift[] = CONV2D_3_FUSED_OUTPUT_RSHIFT;
const nnom_qformat_param_t conv2d_3_fused_bias_shift[] = CONV2D_3_FUSED_BIAS_LSHIFT;
const nnom_conv2d_config_t conv2d_3_fused_config = {
    .super = {.name = "conv2d_3_fused"},
    .qtype = NNOM_QTYPE_PER_TENSOR,
    .weight = (nnom_tensor_t*)&tensor_conv2d_3_fused_kernel_0,
    .bias = (nnom_tensor_t*)&tensor_conv2d_3_fused_bias_0,
    .output_shift = (nnom_qformat_param_t *)&conv2d_3_fused_output_shift, 
    .bias_shift = (nnom_qformat_param_t *)&conv2d_3_fused_bias_shift, 
    .filter_size = 32,
    .kernel_size = {3, 3},
    .stride_size = {1, 1},
    .padding_size = {0, 0},
    .dilation_size = {1, 1},
    .padding_type = PADDING_VALID
};

const nnom_global_pool_config_t global_average_pooling_config = {
    .super = {.name = "global_average_pooling"},
    .output_shift = 0,
};
const int8_t tensor_dense_kernel_0_data[] = TENSOR_DENSE_KERNEL_0;

const nnom_shape_data_t tensor_dense_kernel_0_dim[] = {32, 16};
const nnom_qformat_param_t tensor_dense_kernel_0_dec[] = TENSOR_DENSE_KERNEL_0_DEC_BITS;
const nnom_qformat_param_t tensor_dense_kernel_0_offset[] = {0};
const nnom_tensor_t tensor_dense_kernel_0 = {
    .p_data = (void*)tensor_dense_kernel_0_data,
    .dim = (nnom_shape_data_t*)tensor_dense_kernel_0_dim,
    .q_dec = (nnom_qformat_param_t*)tensor_dense_kernel_0_dec,
    .q_offset = (nnom_qformat_param_t*)tensor_dense_kernel_0_offset,
    .qtype = NNOM_QTYPE_PER_TENSOR,
    .num_dim = 2,
    .bitwidth = 8
};
const int8_t tensor_dense_bias_0_data[] = TENSOR_DENSE_BIAS_0;

const nnom_shape_data_t tensor_dense_bias_0_dim[] = {16};
const nnom_qformat_param_t tensor_dense_bias_0_dec[] = TENSOR_DENSE_BIAS_0_DEC_BITS;
const nnom_qformat_param_t tensor_dense_bias_0_offset[] = {0};
const nnom_tensor_t tensor_dense_bias_0 = {
    .p_data = (void*)tensor_dense_bias_0_data,
    .dim = (nnom_shape_data_t*)tensor_dense_bias_0_dim,
    .q_dec = (nnom_qformat_param_t*)tensor_dense_bias_0_dec,
    .q_offset = (nnom_qformat_param_t*)tensor_dense_bias_0_offset,
    .qtype = NNOM_QTYPE_PER_TENSOR,
    .num_dim = 1,
    .bitwidth = 8
};

const nnom_qformat_param_t dense_output_shift[] = DENSE_OUTPUT_RSHIFT;
const nnom_qformat_param_t dense_bias_shift[] = DENSE_BIAS_LSHIFT;
const nnom_dense_config_t dense_config = {
    .super = {.name = "dense"},
    .qtype = NNOM_QTYPE_PER_TENSOR,
    .weight = (nnom_tensor_t*)&tensor_dense_kernel_0,
    .bias = (nnom_tensor_t*)&tensor_dense_bias_0,
    .output_shift = (nnom_qformat_param_t *)&dense_output_shift,
    .bias_shift = (nnom_qformat_param_t *)&dense_bias_shift
};
static int8_t nnom_output_data[16] = {0};

const nnom_shape_data_t tensor_output0_dim[] = {16};
const nnom_qformat_param_t tensor_output0_dec[] = {DENSE_OUTPUT_DEC};
const nnom_qformat_param_t tensor_output0_offset[] = {0};
const nnom_tensor_t tensor_output0 = {
    .p_data = (void*)nnom_output_data,
    .dim = (nnom_shape_data_t*)tensor_output0_dim,
    .q_dec = (nnom_qformat_param_t*)tensor_output0_dec,
    .q_offset = (nnom_qformat_param_t*)tensor_output0_offset,
    .qtype = NNOM_QTYPE_PER_TENSOR,
    .num_dim = 1,
    .bitwidth = 8
};

const nnom_io_config_t output0_config = {
    .super = {.name = "output0"},
    .tensor = (nnom_tensor_t*)&tensor_output0
};
/* model version */
#define NNOM_MODEL_VERSION (10000*0 + 100*4 + 3)

/* nnom model */
nnom_model_t* nnom_model_create(void)
{
	static nnom_model_t model;
	nnom_layer_t* layer[9];

	check_model_version(NNOM_MODEL_VERSION);
	new_model(&model);

	layer[0] = input_s(&input_config);
	layer[1] = model.hook(conv2d_s(&conv2d_1_fused_config), layer[0]);
	layer[2] = model.hook(maxpool_s(&max_pooling_1_config), layer[1]);
	layer[3] = model.hook(conv2d_s(&conv2d_2_fused_config), layer[2]);
	layer[4] = model.hook(maxpool_s(&max_pooling_2_config), layer[3]);
	layer[5] = model.hook(conv2d_s(&conv2d_3_fused_config), layer[4]);
	layer[6] = model.hook(global_avgpool_s(&global_average_pooling_config), layer[5]);
	layer[7] = model.hook(dense_s(&dense_config), layer[6]);
	layer[8] = model.hook(output_s(&output0_config), layer[7]);
	model_compile(&model, layer[0], layer[8]);
	return &model;
}
