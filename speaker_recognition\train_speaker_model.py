'''
声纹识别训练程序
基于NNOM框架，适配keyword_spotting的导出格式
支持16类说话人识别（4个目标说话人 + 11个其他说话人 + 1个未知类别）

Author: AI Assistant
Date: 2025-01-31
'''

import matplotlib.pyplot as plt
import os
import numpy as np
from pathlib import Path

# 现代 TensorFlow 2.x 推荐用法
import tensorflow as tf

# 创建简洁的别名以保持代码可读性
keras = tf.keras
layers = tf.keras.layers
models = tf.keras.models
optimizers = tf.keras.optimizers
callbacks = tf.keras.callbacks

# 常用层的别名
Input = layers.Input
Conv2D = layers.Conv2D
BatchNormalization = layers.BatchNormalization
ReLU = layers.ReLU
MaxPool2D = layers.MaxPool2D
AvgPool2D = layers.AveragePooling2D
Dropout = layers.Dropout
Flatten = layers.Flatten
Dense = layers.Dense
Softmax = layers.Softmax
GlobalAveragePooling2D = layers.GlobalAveragePooling2D

# 模型相关
Model = models.Model
load_model = models.load_model
save_model = models.save_model

# 回调函数
EarlyStopping = callbacks.EarlyStopping
ReduceLROnPlateau = callbacks.ReduceLROnPlateau

# 工具函数
utils = tf.keras.utils
to_categorical = utils.to_categorical

from sklearn.utils.class_weight import compute_class_weight

# 导入NNOM相关函数
import sys
nnom_path = Path(__file__).parent.parent / "nnom-master" / "scripts"
if str(nnom_path) not in sys.path:
    sys.path.append(str(nnom_path))

# NNOM工具函数
NNOM_AVAILABLE = False
try:
    # 尝试直接导入
    sys.path.insert(0, str(nnom_path))
    import nnom_utils
    generate_model = nnom_utils.generate_model
    generate_test_bin = nnom_utils.generate_test_bin
    NNOM_AVAILABLE = True
    print("✅ NNOM工具导入成功")
except ImportError as e:
    print("⚠️ 警告: 无法导入NNOM工具")
    print(f"   错误: {e}")
    print(f"   路径: {nnom_path}")
    print("   将跳过NNOM权重导出功能")

# === 配置参数 ===
MODEL_DIR = Path("speaker_recognition/model")
MODEL_DIR.mkdir(exist_ok=True)  # 确保目录存在

MODEL_PATH = MODEL_DIR / 'speaker_model.h5'
WEIGHTS_HEADER = MODEL_DIR / 'speaker_weights.h'
TRAINING_HISTORY_PATH = MODEL_DIR / 'training_history.png'

# 说话人配置
TARGET_SPEAKERS = ['XiaoXin', 'XiaoYuan', 'XiaoSi', 'XiaoLai']
OTHER_SPEAKERS = ['ID1', 'ID2', 'ID3', 'ID4', 'ID5', 'ID6', 'ID7', 'ID8', 'ID9', 'ID10', 'ID11']
NUM_CLASSES = len(TARGET_SPEAKERS) + len(OTHER_SPEAKERS) + 1  # +1 for unknown

# MFCC特征配置
N_FRAMES = 198      # 时间帧数
N_MFCC = 12         # MFCC维度（去除C0后）
INPUT_SHAPE = (N_FRAMES, N_MFCC, 1)  # (时间, 频率, 通道)

def load_speaker_data():
    """
    加载声纹识别数据
    返回: (x_train, y_train), (x_val, y_val), (x_test, y_test)
    """
    feature_dir = Path("speaker_recognition/feature")
    
    if not feature_dir.exists():
        raise FileNotFoundError(f"特征目录不存在: {feature_dir}")
    
    print("📂 加载声纹识别特征数据...")
    
    x_train, y_train = [], []
    x_val, y_val = [], []
    x_test, y_test = [], []
    
    # 所有说话人列表（包括others作为未知类别）
    all_speakers = TARGET_SPEAKERS + OTHER_SPEAKERS + ['others']

    for speaker_idx, speaker in enumerate(all_speakers):
        speaker_dir = feature_dir / speaker
        if not speaker_dir.exists():
            print(f"⚠️ 跳过不存在的说话人: {speaker}")
            continue
        
        print(f"   📁 加载说话人: {speaker} (类别 {speaker_idx})")
        
        # 加载各个分割的数据
        for split, (x_list, y_list) in [('train', (x_train, y_train)), 
                                       ('val', (x_val, y_val)), 
                                       ('test', (x_test, y_test))]:
            split_dir = speaker_dir / split
            if not split_dir.exists():
                continue
                
            feature_files = list(split_dir.glob('*.npy'))
            for feature_file in feature_files:
                # 加载MFCC特征 (198, 13)
                mfcc_feat = np.load(feature_file)
                
                # 去除C0系数，只保留C1-C12
                mfcc_feat = mfcc_feat[:, 1:]  # (198, 12)
                
                # 添加通道维度
                mfcc_feat = mfcc_feat.reshape(N_FRAMES, N_MFCC, 1)
                
                x_list.append(mfcc_feat)
                y_list.append(speaker_idx)
    
    # 转换为numpy数组
    x_train = np.array(x_train, dtype=np.float32)
    y_train = np.array(y_train, dtype=np.int32)
    x_val = np.array(x_val, dtype=np.float32)
    y_val = np.array(y_val, dtype=np.int32)
    x_test = np.array(x_test, dtype=np.float32)
    y_test = np.array(y_test, dtype=np.int32)
    
    print(f"✅ 数据加载完成:")
    print(f"   训练集: {x_train.shape}, 标签: {y_train.shape}")
    print(f"   验证集: {x_val.shape}, 标签: {y_val.shape}")
    print(f"   测试集: {x_test.shape}, 标签: {y_test.shape}")
    print(f"   特征范围: [{x_train.min():.3f}, {x_train.max():.3f}]")
    
    return (x_train, y_train), (x_val, y_val), (x_test, y_test)

def normalize_data(x_train, x_val, x_test):
    """
    数据预处理（NNOM正确方式：不进行归一化）

    NNOM的正确使用方式：
    - 训练时：直接使用原始浮点MFCC数据，不进行任何归一化
    - 量化时：让NNOM根据真实激活值范围自动计算最优量化参数
    - 推理时：C代码根据NNOM生成的INPUT_OUTPUT_DEC进行量化

    这样确保量化参数是基于真实数据分布计算的，而不是人为扭曲的分布
    """
    def process_data(data):
        # 只统计数据范围，不进行任何归一化
        print(f"      原始MFCC数据范围: [{data.min():.3f}, {data.max():.3f}]")
        print(f"      数据形状: {data.shape}")
        print(f"      数据类型: {data.dtype}")

        # 直接返回原始数据，让NNOM处理量化
        return data.astype(np.float32)
    
    print(f"🔧 数据预处理（NNOM方式：保持原始浮点数据）...")

    # 处理数据（不进行归一化）
    print("   训练集:")
    x_train = process_data(x_train)
    print("   验证集:")
    x_val = process_data(x_val)
    print("   测试集:")
    x_test = process_data(x_test)

    print(f"✅ 数据预处理完成，保持原始MFCC范围供NNOM自动量化")

    return x_train, x_val, x_test

def build_speaker_cnn(num_classes):
    """
    构建声纹识别CNN模型 - 内存优化版本
    基于keyword_spotting架构，适配声纹识别任务
    去掉第4层卷积以减少内存占用和参数量

    输入: (198, 12, 1) - 198帧，12维MFCC，1通道
    输出: num_classes个类别的概率分布

    网络结构:
    - Conv2D(16) + BN + ReLU + MaxPool → (97, 8, 16)
    - Conv2D(32) + BN + ReLU + MaxPool → (47, 6, 32)
    - Conv2D(32) + BN + ReLU + Dropout → (45, 4, 32)
    - AvgPool2D(45,4) + Flatten + Dense(num_classes) → (num_classes,)

    重要说明 - NNOM BatchNormalization层命名约定:
    - BatchNormalization层名必须包含完整描述性名称
    - NNOM使用字符串匹配来融合BN层到Conv2D层
    - 命名格式：'batch_normalization_{序号}' 避免与conv层名冲突
    - 例如：'batch_normalization_1' 会被融合到前一个Conv2D层
    """
    inputs = Input(shape=INPUT_SHAPE, name='input')

    # 第一层卷积 - 提取局部时频特征 (使用NNOM兼容的层名)
    x = Conv2D(16, kernel_size=(5, 5), strides=(1, 1), padding='valid', name='conv2d_1')(inputs)
    x = BatchNormalization(name='batch_normalization_1')(x)
    x = ReLU(name='re_lu_1')(x)  # NNOM兼容名称
    x = MaxPool2D((2, 1), strides=(2, 1), padding="valid", name='max_pooling_1')(x)  # NNOM兼容名称

    # 第二层卷积 - 增加特征深度 (使用NNOM兼容的层名)
    x = Conv2D(32, kernel_size=(3, 3), strides=(1, 1), padding="valid", name='conv2d_2')(x)
    x = BatchNormalization(name='batch_normalization_2')(x)
    x = ReLU(name='re_lu_2')(x)  # NNOM兼容名称
    x = MaxPool2D((2, 1), strides=(2, 1), padding="valid", name='max_pooling_2')(x)  # NNOM兼容名称

    # 第三层卷积 - 深层特征提取（最后一层卷积） (使用NNOM兼容的层名)
    x = Conv2D(32, kernel_size=(3, 3), strides=(1, 1), padding="valid", name='conv2d_3')(x)
    x = BatchNormalization(name='batch_normalization_3')(x)
    x = ReLU(name='re_lu_3')(x)  # NNOM兼容名称
    x = Dropout(0.3, name='dropout_1')(x)  # 增加dropout防止过拟合

    # 全尺寸平均池化 - 兼容CMSIS-NN，等效于全局平均池化 (使用NNOM兼容的层名)
    x = AvgPool2D((45, 4), strides=(1, 1), padding="valid", name='average_pooling_final')(x)  # NNOM兼容名称
    x = Flatten(name='flatten')(x)
    x = Dense(num_classes, name='dense')(x)

    # 输出层
    predictions = Softmax(name='softmax')(x)

    model = Model(inputs=inputs, outputs=predictions, name='speaker_recognition_cnn')

    return model

def train_model(x_train, y_train, x_val, y_val, num_classes,
                batch_size=96, epochs=150, learning_rate=0.001):
    """
    训练声纹识别模型
    增加了类别权重平衡、早停和学习率调度
    """
    print("🚀 开始训练声纹识别模型...")

    # 计算类别权重以处理数据不平衡
    print("⚖️ 计算类别权重...")
    class_weights = compute_class_weight(
        'balanced',
        classes=np.unique(y_train),
        y=y_train
    )
    class_weight_dict = dict(enumerate(class_weights))

    print("📊 类别权重:")
    for class_idx, weight in class_weight_dict.items():
        print(f"   类别 {class_idx}: {weight:.3f}")

    # 构建模型
    model = build_speaker_cnn(num_classes)

    # 编译模型
    model.compile(
        loss='categorical_crossentropy',
        optimizer=optimizers.Adam(learning_rate=learning_rate),
        metrics=['accuracy']
    )

    # 显示模型结构
    model.summary()

    # 转换标签为one-hot编码
    y_train_onehot = to_categorical(y_train, num_classes)
    y_val_onehot = to_categorical(y_val, num_classes)

    # 设置回调函数
    callbacks = [
        EarlyStopping(
            monitor='val_accuracy',         # 监控验证准确率
            patience=25,                    # 25个epoch没有改善就停止
            restore_best_weights=True,      # 恢复最佳权重
            verbose=1,
            mode='max'                      # 最大化验证准确率
        ),
        ReduceLROnPlateau(
            monitor='val_accuracy',         # 监控验证准确率
            factor=0.7,                     # 学习率乘以0.7
            patience=12,                    # 12个epoch没改善就降低学习率
            min_lr=1e-6,                    # 最小学习率
            verbose=1,
            mode='max'                      # 最大化验证准确率
        )
    ]

    print(f"📊 训练参数:")
    print(f"   批次大小: {batch_size}")
    print(f"   最大训练轮数: {epochs}")
    print(f"   初始学习率: {learning_rate}")
    print(f"   类别数: {num_classes}")
    print(f"   早停耐心值: 15")
    print(f"   学习率调度耐心值: 8")
    print(f"   使用类别权重平衡: ✅")

    # 训练模型
    history = model.fit(
        x_train, y_train_onehot,
        batch_size=batch_size,
        epochs=epochs,
        verbose=1,
        validation_data=(x_val, y_val_onehot),
        class_weight=class_weight_dict,     # 使用类别权重
        callbacks=callbacks,                # 使用回调函数
        shuffle=True
    )

    # 保存模型
    save_model(model, str(MODEL_PATH))
    print(f"💾 模型已保存: {MODEL_PATH}")

    # 保存训练历史
    history_dict = history.history
    np.save(str(MODEL_DIR / 'training_history.npy'), history_dict)
    print(f"📈 训练历史已保存: {MODEL_DIR / 'training_history.npy'}")

    # 清理会话
    del model
    keras.backend.clear_session()

    return history

def evaluate_and_export():
    """
    评估模型并导出NNOM权重
    """
    print("📊 评估模型性能...")
    
    # 重新加载数据和模型
    (x_train, y_train), (x_val, y_val), (x_test, y_test) = load_speaker_data()
    x_train, x_val, x_test = normalize_data(x_train, x_val, x_test)
    
    # 转换标签
    y_test_onehot = to_categorical(y_test, NUM_CLASSES)
    
    # 加载训练好的模型
    model = load_model(str(MODEL_PATH))
    
    # 评估模型
    test_loss, test_acc = model.evaluate(x_test, y_test_onehot, verbose=0)
    print(f"✅ 测试集性能: 损失={test_loss:.4f}, 准确率={test_acc:.4f}")
    
    # 生成测试数据（用于MCU验证）
    if NNOM_AVAILABLE:
        try:
            test_data_path = MODEL_DIR / 'speaker_test_data.bin'
            generate_test_bin(x_test * 127, y_test_onehot, str(test_data_path))
            print(f"💾 测试数据已导出: {test_data_path}")
        except Exception as e:
            print(f"⚠️ 测试数据导出失败: {e}")
    else:
        print("⚠️ 跳过测试数据导出（NNOM不可用）")

    # 导出NNOM权重
    if NNOM_AVAILABLE:
        try:
            # 切换到模型目录进行导出
            import os
            original_dir = os.getcwd()
            os.chdir(str(MODEL_DIR))
            generate_model(model, x_test[:100], name=str(WEIGHTS_HEADER.name))
            os.chdir(original_dir)
            print(f"🎯 NNOM权重已导出: {WEIGHTS_HEADER}")
        except Exception as e:
            print(f"⚠️ NNOM权重导出失败: {e}")
    else:
        print("⚠️ 跳过NNOM权重导出（NNOM不可用）")
    
    return model, test_acc

def plot_training_history(history):
    """
    绘制训练历史
    """
    acc = history.history['accuracy']
    val_acc = history.history['val_accuracy']
    loss = history.history['loss']
    val_loss = history.history['val_loss']
    
    epochs_range = range(len(acc))
    
    plt.figure(figsize=(12, 4))
    
    plt.subplot(1, 2, 1)
    plt.plot(epochs_range, acc, 'r', label='Training Accuracy')
    plt.plot(epochs_range, val_acc, 'b', label='Validation Accuracy')
    plt.legend(loc='lower right')
    plt.title('Training and Validation Accuracy')
    plt.xlabel('Epochs')
    plt.ylabel('Accuracy')
    
    plt.subplot(1, 2, 2)
    plt.plot(epochs_range, loss, 'r', label='Training Loss')
    plt.plot(epochs_range, val_loss, 'b', label='Validation Loss')
    plt.legend(loc='upper right')
    plt.title('Training and Validation Loss')
    plt.xlabel('Epochs')
    plt.ylabel('Loss')
    
    plt.tight_layout()
    plt.savefig(str(TRAINING_HISTORY_PATH), dpi=150, bbox_inches='tight')
    print(f"📈 训练历史图已保存: {TRAINING_HISTORY_PATH}")
    plt.show()

def main():
    """
    主训练流程
    """
    print("🎵 声纹识别模型训练程序")
    print("=" * 60)
    
    # 配置GPU内存增长
    physical_devices = tf.config.experimental.list_physical_devices("GPU")
    if physical_devices:
        tf.config.experimental.set_memory_growth(physical_devices[0], True)
        print("🔧 GPU内存增长已启用")
    
    try:
        # 1. 加载数据
        (x_train, y_train), (x_val, y_val), (x_test, y_test) = load_speaker_data()
        
        # 2. 数据预处理
        x_train, x_val, x_test = normalize_data(x_train, x_val, x_test)
        
        # 3. 数据增强（可选）
        print("🔄 应用数据增强...")
        x_train_aug = np.vstack((x_train, x_train * 0.8))  # 音量变化
        y_train_aug = np.hstack((y_train, y_train))
        print(f"   增强后训练集: {x_train_aug.shape}")
        
        # 4. 训练模型
        history = train_model(x_train_aug, y_train_aug, x_val, y_val, NUM_CLASSES)
        
        # 5. 评估和导出
        model, test_acc = evaluate_and_export()
        
        # 6. 绘制训练历史
        plot_training_history(history)
        
        print("\n" + "=" * 60)
        print("🎉 训练完成!")
        print(f"📊 最终测试准确率: {test_acc:.4f}")
        print(f"� 模型保存目录: {MODEL_DIR}")
        print(f"�💾 模型文件: {MODEL_PATH}")
        print(f"🎯 权重文件: {WEIGHTS_HEADER}")
        print(f"📈 训练历史: {TRAINING_HISTORY_PATH}")
        print(f"📊 训练历史数据: {MODEL_DIR / 'training_history.npy'}")
        print("=" * 60)
        
    except Exception as e:
        print(f"❌ 训练过程出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    main()
