01:29:06 **** Incremental Build of configuration Release for project qemu ****
make -j6 all 
Building file: D:/NucleiStudio/2025/01quant/VeriHealthi_QEMU_SDK_v3.4/galaxy_sdk/main.c
Building file: D:/NucleiStudio/2025/01quant/VeriHealthi_QEMU_SDK_v3.4/galaxy_sdk/speaker_inference.c
Invoking: GNU RISC-V Cross C Compiler
riscv64-unknown-elf-gcc -march=rv32imafc_xxldsp -mabi=ilp32f -mtune=nuclei-300-series -mcmodel=medlow -mno-save-restore -O2 -ffunction-sections -fdata-sections -fno-common -Werror -Wall -g -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\include\arch\riscv\n309" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\include" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\config\include" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\drivers\include" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\external\riscv_dsp\include" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\external\nnom\inc\layers" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\external\nnom\port" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\external\nnom\inc" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\external\riscv_dsp\PrivateInclude" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\include" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\include" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\osal\include" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\prebuilts\bluetooth\health\include" -std=gnu11 -MMD -MP -MF"galaxy_sdk/main.d" -MT"galaxy_sdk/main.o" -c -o "galaxy_sdk/main.o" "D:/NucleiStudio/2025/01quant/VeriHealthi_QEMU_SDK_v3.4/galaxy_sdk/main.c"
Invoking: GNU RISC-V Cross C Compiler
riscv64-unknown-elf-gcc -march=rv32imafc_xxldsp -mabi=ilp32f -mtune=nuclei-300-series -mcmodel=medlow -mno-save-restore -O2 -ffunction-sections -fdata-sections -fno-common -Werror -Wall -g -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\include\arch\riscv\n309" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\bsp\include" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\config\include" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\drivers\include" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\external\riscv_dsp\include" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\external\nnom\inc\layers" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\external\nnom\port" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\external\nnom\inc" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\external\riscv_dsp\PrivateInclude" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\include" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\os\include" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\osal\include" -I"D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\prebuilts\bluetooth\health\include" -std=gnu11 -MMD -MP -MF"galaxy_sdk/speaker_inference.d" -MT"galaxy_sdk/speaker_inference.o" -c -o "galaxy_sdk/speaker_inference.o" "D:/NucleiStudio/2025/01quant/VeriHealthi_QEMU_SDK_v3.4/galaxy_sdk/speaker_inference.c"
D:/NucleiStudio/2025/01quant/VeriHealthi_QEMU_SDK_v3.4/galaxy_sdk/main.c: In function 'task_speaker_recognition':
D:/NucleiStudio/2025/01quant/VeriHealthi_QEMU_SDK_v3.4/galaxy_sdk/main.c:62:35: error: void value not ignored as it ought to be
   62 |     speaker_error_t test_result = speaker_test();
      |                                   ^~~~~~~~~~~~
make: *** [galaxy_sdk/subdir.mk:32: galaxy_sdk/main.o] Error 1
make: *** Waiting for unfinished jobs....
D:/NucleiStudio/2025/01quant/VeriHealthi_QEMU_SDK_v3.4/galaxy_sdk/speaker_inference.c: In function 'print_float':
D:/NucleiStudio/2025/01quant/VeriHealthi_QEMU_SDK_v3.4/galaxy_sdk/speaker_inference.c:68:5: error: implicit declaration of function 'uart_printf' [-Werror=implicit-function-declaration]
   68 |     uart_printf("%d.%03d", integer_part, decimal_part);
      |     ^~~~~~~~~~~
D:/NucleiStudio/2025/01quant/VeriHealthi_QEMU_SDK_v3.4/galaxy_sdk/speaker_inference.c: In function 'build_speaker_model':
D:/NucleiStudio/2025/01quant/VeriHealthi_QEMU_SDK_v3.4/galaxy_sdk/speaker_inference.c:83:19: error: implicit declaration of function 'nnom_qformat' [-Werror=implicit-function-declaration]
   83 |                   nnom_qformat(INPUT_OUTPUT_DEC, INPUT_OUTPUT_OFFSET));
      |                   ^~~~~~~~~~~~
D:/NucleiStudio/2025/01quant/VeriHealthi_QEMU_SDK_v3.4/galaxy_sdk/speaker_inference.c:83:19: error: passing argument 2 of 'Input' makes pointer from integer without a cast [-Werror=int-conversion]
   83 |                   nnom_qformat(INPUT_OUTPUT_DEC, INPUT_OUTPUT_OFFSET));
      |                   ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
      |                   |
      |                   int
In file included from D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\external\nnom\inc/nnom_layers.h:54,
                 from D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\external\nnom\inc/nnom.h:343,
                 from D:/NucleiStudio/2025/01quant/VeriHealthi_QEMU_SDK_v3.4/galaxy_sdk/speaker_inference.c:19:
D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\external\nnom\inc/layers/nnom_input.h:50:56: note: expected 'void *' but argument is of type 'int'
   50 | nnom_layer_t *Input(nnom_3d_shape_t input_shape, void *p_buf);
      |                                                  ~~~~~~^~~~~
D:/NucleiStudio/2025/01quant/VeriHealthi_QEMU_SDK_v3.4/galaxy_sdk/speaker_inference.c:86:52: error: incompatible type for argument 4 of 'Conv2D'
   86 |     conv1 = Conv2D(16, kernel(5, 5), stride(1, 1), PADDING_SAME,
      |                                                    ^~~~~~~~~~~~
      |                                                    |
      |                                                    int
In file included from D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\external\nnom\inc/nnom_layers.h:46:
D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\external\nnom\inc/layers/nnom_conv2d.h:76:94: note: expected 'nnom_3d_shape_t' {aka 'struct _nnom_3d_shape_t'} but argument is of type 'int'
   76 | nnom_layer_t *Conv2D(uint32_t filters, nnom_3d_shape_t k, nnom_3d_shape_t s, nnom_3d_shape_t d,  nnom_padding_t pad_type,
      |                                                                              ~~~~~~~~~~~~~~~~^
D:/NucleiStudio/2025/01quant/VeriHealthi_QEMU_SDK_v3.4/galaxy_sdk/speaker_inference.c:87:20: error: incompatible type for argument 5 of 'Conv2D'
   87 |                    &tensor_conv2d_1_fused_kernel_0, &tensor_conv2d_1_fused_bias_0)(input);
      |                    ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
      |                    |
      |                    const nnom_tensor_t * {aka const struct _nnom_tensor_t *}
D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\external\nnom\inc/layers/nnom_conv2d.h:76:113: note: expected 'nnom_padding_t' but argument is of type 'const nnom_tensor_t *' {aka 'const struct _nnom_tensor_t *'}
   76 | nnom_layer_t *Conv2D(uint32_t filters, nnom_3d_shape_t k, nnom_3d_shape_t s, nnom_3d_shape_t d,  nnom_padding_t pad_type,
      |                                                                                                  ~~~~~~~~~~~~~~~^~~~~~~~
D:/NucleiStudio/2025/01quant/VeriHealthi_QEMU_SDK_v3.4/galaxy_sdk/speaker_inference.c:87:53: error: passing argument 6 of 'Conv2D' from incompatible pointer type [-Werror=incompatible-pointer-types]
   87 |                    &tensor_conv2d_1_fused_kernel_0, &tensor_conv2d_1_fused_bias_0)(input);
      |                                                     ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~
      |                                                     |
      |                                                     const nnom_tensor_t * {aka const struct _nnom_tensor_t *}
D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\external\nnom\inc/layers/nnom_conv2d.h:77:63: note: expected 'const nnom_weight_t *' {aka 'const struct _nnom_weights *'} but argument is of type 'const nnom_tensor_t *' {aka 'const struct _nnom_tensor_t *'}
   77 |                                          const nnom_weight_t *w, const nnom_bias_t *b);
      |                                          ~~~~~~~~~~~~~~~~~~~~~^
D:/NucleiStudio/2025/01quant/VeriHealthi_QEMU_SDK_v3.4/galaxy_sdk/speaker_inference.c:86:13: error: too few arguments to function 'Conv2D'
   86 |     conv1 = Conv2D(16, kernel(5, 5), stride(1, 1), PADDING_SAME,
      |             ^~~~~~
D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\external\nnom\inc/nnom_layers.h:107:15: note: declared here
  107 | nnom_layer_t *Conv2D(uint32_t filters, nnom_3d_shape_t k, nnom_3d_shape_t s, nnom_3d_shape_t d, nnom_padding_t pad,
      |               ^~~~~~
D:/NucleiStudio/2025/01quant/VeriHealthi_QEMU_SDK_v3.4/galaxy_sdk/speaker_inference.c:88:13: error: called object is not a function or function pointer
   88 |     conv1 = ReLU()(conv1);
      |             ^~~~
D:/NucleiStudio/2025/01quant/VeriHealthi_QEMU_SDK_v3.4/galaxy_sdk/speaker_inference.c:91:52: error: incompatible type for argument 4 of 'Conv2D'
   91 |     conv2 = Conv2D(32, kernel(3, 3), stride(1, 1), PADDING_SAME,
      |                                                    ^~~~~~~~~~~~
      |                                                    |
      |                                                    int
D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\external\nnom\inc/layers/nnom_conv2d.h:76:94: note: expected 'nnom_3d_shape_t' {aka 'struct _nnom_3d_shape_t'} but argument is of type 'int'
   76 | nnom_layer_t *Conv2D(uint32_t filters, nnom_3d_shape_t k, nnom_3d_shape_t s, nnom_3d_shape_t d,  nnom_padding_t pad_type,
      |                                                                              ~~~~~~~~~~~~~~~~^
D:/NucleiStudio/2025/01quant/VeriHealthi_QEMU_SDK_v3.4/galaxy_sdk/speaker_inference.c:92:20: error: incompatible type for argument 5 of 'Conv2D'
   92 |                    &tensor_conv2d_2_fused_kernel_0, &tensor_conv2d_2_fused_bias_0)(conv1);
      |                    ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
      |                    |
      |                    const nnom_tensor_t * {aka const struct _nnom_tensor_t *}
D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\external\nnom\inc/layers/nnom_conv2d.h:76:113: note: expected 'nnom_padding_t' but argument is of type 'const nnom_tensor_t *' {aka 'const struct _nnom_tensor_t *'}
   76 | nnom_layer_t *Conv2D(uint32_t filters, nnom_3d_shape_t k, nnom_3d_shape_t s, nnom_3d_shape_t d,  nnom_padding_t pad_type,
      |                                                                                                  ~~~~~~~~~~~~~~~^~~~~~~~
D:/NucleiStudio/2025/01quant/VeriHealthi_QEMU_SDK_v3.4/galaxy_sdk/speaker_inference.c:92:53: error: passing argument 6 of 'Conv2D' from incompatible pointer type [-Werror=incompatible-pointer-types]
   92 |                    &tensor_conv2d_2_fused_kernel_0, &tensor_conv2d_2_fused_bias_0)(conv1);
      |                                                     ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~
      |                                                     |
      |                                                     const nnom_tensor_t * {aka const struct _nnom_tensor_t *}
D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\external\nnom\inc/layers/nnom_conv2d.h:77:63: note: expected 'const nnom_weight_t *' {aka 'const struct _nnom_weights *'} but argument is of type 'const nnom_tensor_t *' {aka 'const struct _nnom_tensor_t *'}
   77 |                                          const nnom_weight_t *w, const nnom_bias_t *b);
      |                                          ~~~~~~~~~~~~~~~~~~~~~^
D:/NucleiStudio/2025/01quant/VeriHealthi_QEMU_SDK_v3.4/galaxy_sdk/speaker_inference.c:91:13: error: too few arguments to function 'Conv2D'
   91 |     conv2 = Conv2D(32, kernel(3, 3), stride(1, 1), PADDING_SAME,
      |             ^~~~~~
D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\external\nnom\inc/nnom_layers.h:107:15: note: declared here
  107 | nnom_layer_t *Conv2D(uint32_t filters, nnom_3d_shape_t k, nnom_3d_shape_t s, nnom_3d_shape_t d, nnom_padding_t pad,
      |               ^~~~~~
D:/NucleiStudio/2025/01quant/VeriHealthi_QEMU_SDK_v3.4/galaxy_sdk/speaker_inference.c:93:13: error: called object is not a function or function pointer
   93 |     conv2 = ReLU()(conv2);
      |             ^~~~
D:/NucleiStudio/2025/01quant/VeriHealthi_QEMU_SDK_v3.4/galaxy_sdk/speaker_inference.c:96:52: error: incompatible type for argument 4 of 'Conv2D'
   96 |     conv3 = Conv2D(32, kernel(3, 3), stride(1, 1), PADDING_SAME,
      |                                                    ^~~~~~~~~~~~
      |                                                    |
      |                                                    int
D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\external\nnom\inc/layers/nnom_conv2d.h:76:94: note: expected 'nnom_3d_shape_t' {aka 'struct _nnom_3d_shape_t'} but argument is of type 'int'
   76 | nnom_layer_t *Conv2D(uint32_t filters, nnom_3d_shape_t k, nnom_3d_shape_t s, nnom_3d_shape_t d,  nnom_padding_t pad_type,
      |                                                                              ~~~~~~~~~~~~~~~~^
D:/NucleiStudio/2025/01quant/VeriHealthi_QEMU_SDK_v3.4/galaxy_sdk/speaker_inference.c:97:20: error: incompatible type for argument 5 of 'Conv2D'
   97 |                    &tensor_conv2d_3_fused_kernel_0, &tensor_conv2d_3_fused_bias_0)(conv2);
      |                    ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
      |                    |
      |                    const nnom_tensor_t * {aka const struct _nnom_tensor_t *}
D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\external\nnom\inc/layers/nnom_conv2d.h:76:113: note: expected 'nnom_padding_t' but argument is of type 'const nnom_tensor_t *' {aka 'const struct _nnom_tensor_t *'}
   76 | nnom_layer_t *Conv2D(uint32_t filters, nnom_3d_shape_t k, nnom_3d_shape_t s, nnom_3d_shape_t d,  nnom_padding_t pad_type,
      |                                                                                                  ~~~~~~~~~~~~~~~^~~~~~~~
D:/NucleiStudio/2025/01quant/VeriHealthi_QEMU_SDK_v3.4/galaxy_sdk/speaker_inference.c:97:53: error: passing argument 6 of 'Conv2D' from incompatible pointer type [-Werror=incompatible-pointer-types]
   97 |                    &tensor_conv2d_3_fused_kernel_0, &tensor_conv2d_3_fused_bias_0)(conv2);
      |                                                     ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~
      |                                                     |
      |                                                     const nnom_tensor_t * {aka const struct _nnom_tensor_t *}
D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\external\nnom\inc/layers/nnom_conv2d.h:77:63: note: expected 'const nnom_weight_t *' {aka 'const struct _nnom_weights *'} but argument is of type 'const nnom_tensor_t *' {aka 'const struct _nnom_tensor_t *'}
   77 |                                          const nnom_weight_t *w, const nnom_bias_t *b);
      |                                          ~~~~~~~~~~~~~~~~~~~~~^
D:/NucleiStudio/2025/01quant/VeriHealthi_QEMU_SDK_v3.4/galaxy_sdk/speaker_inference.c:96:13: error: too few arguments to function 'Conv2D'
   96 |     conv3 = Conv2D(32, kernel(3, 3), stride(1, 1), PADDING_SAME,
      |             ^~~~~~
D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\external\nnom\inc/nnom_layers.h:107:15: note: declared here
  107 | nnom_layer_t *Conv2D(uint32_t filters, nnom_3d_shape_t k, nnom_3d_shape_t s, nnom_3d_shape_t d, nnom_padding_t pad,
      |               ^~~~~~
D:/NucleiStudio/2025/01quant/VeriHealthi_QEMU_SDK_v3.4/galaxy_sdk/speaker_inference.c:98:13: error: called object is not a function or function pointer
   98 |     conv3 = ReLU()(conv3);
      |             ^~~~
D:/NucleiStudio/2025/01quant/VeriHealthi_QEMU_SDK_v3.4/galaxy_sdk/speaker_inference.c:101:11: error: called object is not a function or function pointer
  101 |     gap = GlobalAvgPool()(conv3);
      |           ^~~~~~~~~~~~~
D:/NucleiStudio/2025/01quant/VeriHealthi_QEMU_SDK_v3.4/galaxy_sdk/speaker_inference.c:104:32: error: passing argument 2 of 'Dense' from incompatible pointer type [-Werror=incompatible-pointer-types]
  104 |     dense = Dense(NUM_CLASSES, &tensor_dense_kernel_0, &tensor_dense_bias_0)(gap);
      |                                ^~~~~~~~~~~~~~~~~~~~~~
      |                                |
      |                                const nnom_tensor_t * {aka const struct _nnom_tensor_t *}
In file included from D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\external\nnom\inc/nnom_layers.h:49:
D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\external\nnom\inc/layers/nnom_dense.h:57:62: note: expected 'const nnom_weight_t *' {aka 'const struct _nnom_weights *'} but argument is of type 'const nnom_tensor_t *' {aka 'const struct _nnom_tensor_t *'}
   57 | nnom_layer_t *Dense(size_t output_unit, const nnom_weight_t *w, const nnom_bias_t *b);
      |                                         ~~~~~~~~~~~~~~~~~~~~~^
D:/NucleiStudio/2025/01quant/VeriHealthi_QEMU_SDK_v3.4/galaxy_sdk/speaker_inference.c:104:56: error: passing argument 3 of 'Dense' from incompatible pointer type [-Werror=incompatible-pointer-types]
  104 |     dense = Dense(NUM_CLASSES, &tensor_dense_kernel_0, &tensor_dense_bias_0)(gap);
      |                                                        ^~~~~~~~~~~~~~~~~~~~
      |                                                        |
      |                                                        const nnom_tensor_t * {aka const struct _nnom_tensor_t *}
D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\external\nnom\inc/layers/nnom_dense.h:57:84: note: expected 'const nnom_bias_t *' {aka 'const struct _nnom_bias *'} but argument is of type 'const nnom_tensor_t *' {aka 'const struct _nnom_tensor_t *'}
   57 | nnom_layer_t *Dense(size_t output_unit, const nnom_weight_t *w, const nnom_bias_t *b);
      |                                                                 ~~~~~~~~~~~~~~~~~~~^
D:/NucleiStudio/2025/01quant/VeriHealthi_QEMU_SDK_v3.4/galaxy_sdk/speaker_inference.c:104:13: error: called object is not a function or function pointer
  104 |     dense = Dense(NUM_CLASSES, &tensor_dense_kernel_0, &tensor_dense_bias_0)(gap);
      |             ^~~~~
D:/NucleiStudio/2025/01quant/VeriHealthi_QEMU_SDK_v3.4/galaxy_sdk/speaker_inference.c:107:14: error: called object is not a function or function pointer
  107 |     output = Softmax()(dense);
      |              ^~~~~~~
D:/NucleiStudio/2025/01quant/VeriHealthi_QEMU_SDK_v3.4/galaxy_sdk/speaker_inference.c:109:21: error: passing argument 2 of 'Output' makes pointer from integer without a cast [-Werror=int-conversion]
  109 |                     nnom_qformat(DENSE_OUTPUT_DEC, DENSE_OUTPUT_OFFSET))(output);
      |                     ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
      |                     |
      |                     int
In file included from D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\external\nnom\inc/nnom_layers.h:59:
D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\external\nnom\inc/layers/nnom_output.h:37:58: note: expected 'void *' but argument is of type 'int'
   37 | nnom_layer_t *Output(nnom_3d_shape_t output_shape, void *p_buf);
      |                                                    ~~~~~~^~~~~
D:/NucleiStudio/2025/01quant/VeriHealthi_QEMU_SDK_v3.4/galaxy_sdk/speaker_inference.c:108:14: error: called object is not a function or function pointer
  108 |     output = Output(shape(NUM_CLASSES, 1, 1),
      |              ^~~~~~
D:/NucleiStudio/2025/01quant/VeriHealthi_QEMU_SDK_v3.4/galaxy_sdk/speaker_inference.c:112:22: error: passing argument 1 of 'new_model' from incompatible pointer type [-Werror=incompatible-pointer-types]
  112 |     return new_model(input, output);
      |                      ^~~~~
      |                      |
      |                      nnom_layer_t * {aka struct _nnom_layer_t *}
D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\external\nnom\inc/nnom.h:392:39: note: expected 'nnom_model_t *' {aka 'struct _nnom_model *'} but argument is of type 'nnom_layer_t *' {aka 'struct _nnom_layer_t *'}
  392 | nnom_model_t *new_model(nnom_model_t *m);
      |                         ~~~~~~~~~~~~~~^
D:/NucleiStudio/2025/01quant/VeriHealthi_QEMU_SDK_v3.4/galaxy_sdk/speaker_inference.c:112:12: error: too many arguments to function 'new_model'
  112 |     return new_model(input, output);
      |            ^~~~~~~~~
D:\NucleiStudio\2025\01quant\VeriHealthi_QEMU_SDK_v3.4\galaxy_sdk\modules\external\nnom\inc/nnom.h:392:15: note: declared here
  392 | nnom_model_t *new_model(nnom_model_t *m);
      |               ^~~~~~~~~
D:/NucleiStudio/2025/01quant/VeriHealthi_QEMU_SDK_v3.4/galaxy_sdk/speaker_inference.c: In function 'speaker_model_init':
D:/NucleiStudio/2025/01quant/VeriHealthi_QEMU_SDK_v3.4/galaxy_sdk/speaker_inference.c:130:30: error: 'COST_LABEL' undeclared (first use in this function)
  130 |     if (model_compile(model, COST_LABEL, COST_LABEL) != NN_SUCCESS) {
      |                              ^~~~~~~~~~
D:/NucleiStudio/2025/01quant/VeriHealthi_QEMU_SDK_v3.4/galaxy_sdk/speaker_inference.c:130:30: note: each undeclared identifier is reported only once for each function it appears in
D:/NucleiStudio/2025/01quant/VeriHealthi_QEMU_SDK_v3.4/galaxy_sdk/speaker_inference.c: In function 'speaker_inference':
D:/NucleiStudio/2025/01quant/VeriHealthi_QEMU_SDK_v3.4/galaxy_sdk/speaker_inference.c:208:12: error: called object 'nnom_input_data' is not a function or function pointer
  208 |     memcpy(nnom_input_data(model, 0), input_buffer, INPUT_SIZE);
      |            ^~~~~~~~~~~~~~~
In file included from D:/NucleiStudio/2025/01quant/VeriHealthi_QEMU_SDK_v3.4/galaxy_sdk/speaker_inference.c:20:
D:/NucleiStudio/2025/01quant/VeriHealthi_QEMU_SDK_v3.4/galaxy_sdk/speaker_weights.h:74:15: note: declared here
   74 | static int8_t nnom_input_data[2376] = {0};
      |               ^~~~~~~~~~~~~~~
D:/NucleiStudio/2025/01quant/VeriHealthi_QEMU_SDK_v3.4/galaxy_sdk/speaker_inference.c:213:27: error: called object 'nnom_output_data' is not a function or function pointer
  213 |     memcpy(output_buffer, nnom_output_data(model, 0), OUTPUT_SIZE);
      |                           ^~~~~~~~~~~~~~~~
D:/NucleiStudio/2025/01quant/VeriHealthi_QEMU_SDK_v3.4/galaxy_sdk/speaker_weights.h:290:15: note: declared here
  290 | static int8_t nnom_output_data[16] = {0};
      |               ^~~~~~~~~~~~~~~~
D:/NucleiStudio/2025/01quant/VeriHealthi_QEMU_SDK_v3.4/galaxy_sdk/speaker_weights.h: At top level:
D:/NucleiStudio/2025/01quant/VeriHealthi_QEMU_SDK_v3.4/galaxy_sdk/speaker_weights.h:313:22: error: 'nnom_model_create' defined but not used [-Werror=unused-function]
  313 | static nnom_model_t* nnom_model_create(void)
      |                      ^~~~~~~~~~~~~~~~~
cc1.exe: all warnings being treated as errors
make: *** [galaxy_sdk/subdir.mk:60: galaxy_sdk/speaker_inference.o] Error 1
"make -j6 all" terminated with exit code 2. Build might be incomplete.

01:29:08 Build Failed. 35 errors, 0 warnings. (took 1s.717ms)

